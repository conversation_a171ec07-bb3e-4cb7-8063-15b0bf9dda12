describe('单据模版字段配置字段用例', function() {
  const expenseName = '测试报销单'
  const T_CustomField = {
    文本: {
      name: '文本1',
      type: '文本'
    },
    数字: {
      name: '数字1',
      type: '数字'
    },
    金额: {
      name: '金额1',
      type: '金额'
    },
    日期: {
      name: '日期1',
      type: '日期'
    },
    日期范围: {
      name: '日期范围1',
      type: '日期范围'
    },
    员工: {
      name: '员工1',
      type: '员工'
    },
    部门: {
      name: '部门1',
      type: '部门'
    },
    自定义档案: {
      name: '自定义档案1',
      type: '自定义档案',
      source: '项目'
    },
    枚举: {
      name: '枚举1',
      type: '枚举',
      source: '航班舱型'
    },
    城市: {
      name: '消费城市',
      type: '城市'
    },
    业务对象: {
      name: '业务对象1',
      type: '业务对象',
      source: 'LIU新增自定义档案收款信息/测试数字1'
    }
  }

  function generateText(count) {
    let result = ''
    for (let i = 0; i < count; i++) {
      result = result + 'a'
    }
    return result
  }
  function addField(field) {
    cy.contains('添加字段').click()
    cy.contains(field).click()
    cy.get('.modal-footer > .ant-btn-primary').click()
    cy.contains(field).click()
  }

  function checkDescription(field) {
    cy.get('[data-cy="ekb-description"] .meta_name_item').should($lis => {
      cy.expect($lis.eq(0), '字段名称').to.contain(field.name)
      cy.expect($lis.eq(1), '字段类型').to.contain(field.type)
      if (field.source) {
        cy.expect($lis.eq(2), '引用数据').to.contain(field.source)
      }
    })
  }

  function checkRequired() {
    cy.get('[data-cy="ekb-check-required"]').should('contain', '这个是必填项')
    cy.get('[data-cy="ekb-check-required"]')
      .find('input')
      .should('be.checked')
  }
  function checkFistPart(field, isRef, isDate) {
    const label = !isRef ? `请输入${field}` : `请选择${field}`
    cy.get('@rightForm')
      .contains('显示名称')
      .parents('.ant-form-item')
      .find('input')
      .as('rightInputName')
    cy.get('@rightInputName').should('have.value', field)
    cy.get('@rightInputName').clear()
    // cy.get('.ant-form-explain').should('have.text', '显示名称不能为空')
    cy.get('@rightInputName').type(`自定义${field}`)
    if (isDate) {
      return
    }
    cy.get('@rightForm')
      .contains('提示文字')
      .parents('.ant-form-item')
      .find('input')
      .as('placeholderInput')
    cy.get('@placeholderInput').should('have.value', label)
  }

  function checkEditable() {
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('手动填写')
      cy.expect($lis.eq(0), 'first item').to.have.class('active')
      cy.expect($lis.eq(1), 'first item').to.contain('系统计算')
    })
  }

  function saveTemplate() {
    cy.get('[data-cy=specificRight-newActionsPart] > .ant-btn-primary').click()
  }

  const searchSelectExpense = function(expenseName) {
    cy.get('[data-cy="specificLeft-listPart"]')
      .contains(expenseName)
      .click()
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .find('.ant-tabs-tab-active')
      .should('have.text', '基本设置')
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
  }

  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"customSpecification"}'
        )
        cy.visit('/web/app.html#/custom-specification')
        cy.wait(3000)
        searchSelectExpense(expenseName)
        cy.wait(3000)
      })
  })

  it('文本字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const textField = T_CustomField['文本']
    addField(textField.name)
    checkDescription(textField)
    checkFistPart(textField.name)
    // cy.get('.ant-form-explain').should('have.text', '显示名称不能超过1000个字')

    cy.get('@rightForm')
      .contains('文本类型')
      .parents('.ant-row')
      .find('.ant-radio-wrapper-checked span')
      .should('have.text', `单行文本`)

    checkEditable()

    cy.get('.ant-input-number-input-wrap')
      .first()
      .find('input')
      .type(2)

    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(1)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
    })

    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()

    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(3)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`从关联的申请单的${textField.name}取值`)
      cy.expect($lis.eq(2), 'third item').to.contain('计算公式')
    })
  })
  it('数字字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const numberField = T_CustomField['数字']
    addField(numberField.name)
    checkDescription(numberField)
    checkFistPart(numberField.name)
    checkEditable()
    cy.get('.ant-input-number-input-wrap')
      .first()
      .find('input')
      .should('have.value', '-1000000000')
    cy.get('.ant-input-number-input-wrap')
      .last()
      .find('input')
      .should('have.value', '10000000000')
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('固定值')
      cy.expect($lis.eq(1), 'second item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('#ConditionalInput')
      .find('input')
      .should('have.value', '0')

    // cy.get('#ConditionalInput')
    //   .find('input')
    //   .clear()
    //
    // cy.get('#ConditionalInput')
    //   .find('.ant-form-explain')
    //   .should('have.text', '固定值不能为空')

    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(4)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`固定值`)
      cy.expect($lis.eq(2), 'third item').to.contain(`从关联的申请单的${numberField.name}取值`)
      cy.expect($lis.eq(3), 'fourth item').to.contain(`计算公式`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('金额字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const amountField = T_CustomField['金额']
    addField(amountField.name)
    checkDescription(amountField)
    checkFistPart(amountField.name)
    checkRequired()
    checkEditable()
    cy.get('.ant-input-number-input-wrap')
      .first()
      .find('input')
      .should('have.value', '0.01')
    cy.get('.ant-input-number-input-wrap')
      .last()
      .find('input')
      .should('have.value', '10000000000')
    // cy.get('.ant-input-number-input-wrap')
    //   .first()
    //   .find('input')
    //   .clear()
    // cy.get('[data-cy="number-range"]')
    //   .parents('ant-form-item-control')
    //   .find('ant-form-explain')
    //   .should('have.text', '限制数值的最大值和最小值不能为空')
    // cy.get('.ant-input-number-input-wrap')
    //   .first()
    //   .find('input')
    //   .type(2)
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(1)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })

    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()

    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(5)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`启用个人费用标准`)
      cy.expect($lis.eq(2), 'second item').to.contain(`计算公式`)
      cy.expect($lis.eq(3), 'third item').to.contain(`从关联的申请单的${amountField.name}取值`)
      cy.expect($lis.eq(4), 'fourth item').to.contain(`从明细中选取下述字段求和`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('日期字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const dateField = T_CustomField['日期']
    addField(dateField.name)
    checkDescription(dateField)
    checkFistPart(dateField.name, undefined, true)
    checkEditable()
    cy.get('[data-cy="ekb-check-withTime"]').should('contain', '需填写时间（小时、分钟）')
    cy.get('[data-cy="ekb-check-withTime"]')
      .find('input')
      .should('not.be.checked')

    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('当前日期（即制单日期）')
      cy.expect($lis.eq(1), 'second item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(6)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`当前日期（即制单日期）`)
      cy.expect($lis.eq(2), 'second item').to.contain(`送审日期（多次送审取最后一次）`)
      cy.expect($lis.eq(3), 'third item').to.contain(`首次送审日期`)
      cy.expect($lis.eq(4), 'fourth item').to.contain(`计算公式`)
      cy.expect($lis.eq(5), 'fifth item').to.contain(`从关联的申请单的${dateField.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('日期范围字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const dateRangeField = T_CustomField['日期范围']
    addField(dateRangeField.name)
    checkDescription(dateRangeField)
    checkFistPart(dateRangeField.name, undefined, true)
    checkEditable()
    cy.get('[data-cy="ekb-check-withTime"]').should('contain', '需填写时间（小时、分钟）')
    cy.get('[data-cy="ekb-check-withTime"]')
      .find('input')
      .should('not.be.checked')
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('当前日期（即制单日期）')
      cy.expect($lis.eq(1), 'second item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(3)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`当前日期（即制单日期）`)
      cy.expect($lis.eq(2), 'fourth item').to.contain(`从关联的申请单的${dateRangeField.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('员工字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const staffField = T_CustomField['员工']
    addField(staffField.name)
    checkDescription(staffField)
    checkFistPart(staffField.name, true)
    checkRequired()
    checkEditable()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(3)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(4)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(3), 'fourth item').to.contain(`从关联的申请单的${staffField.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('部门字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const departField = T_CustomField['部门']
    addField(departField.name)
    checkDescription(departField)
    checkFistPart(departField.name, true)
    checkRequired()
    checkEditable()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(4)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(3), 'fourth item').to.contain('提交人的默认部门')
      cy.expect($lis.eq(3), 'third will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(5)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(3), 'fourth item').to.contain('提交人的默认部门')
      cy.expect($lis.eq(4), 'fifth item').to.contain(`从关联的申请单的${departField.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('自定义档案字段', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const dimensionField = T_CustomField['自定义档案']
    addField(dimensionField.name)
    checkDescription(dimensionField)
    cy.get('[data-cy="ekb-radio-group-type"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('下拉框（适合候选值较少）')
      cy.expect($lis.eq(1), 'second item').to.contain('搜索（适合候选值较多）')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    checkFistPart(dimensionField.name, true)
    checkRequired()
    checkEditable()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(3)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(4)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(3), 'fourth item').to.contain(`从关联的申请单的${dimensionField.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('城市', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const field = T_CustomField['城市']
    addField(field.name)
    checkDescription(field)
    checkFistPart(field.name, true)
    checkEditable()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(1)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="ekb-check-multiple"]').should('contain', '支持多选')
    cy.get('[data-cy="ekb-check-multiple"]')
      .find('input')
      .should('not.be.checked')
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(2)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain(`从关联的申请单的${field.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('枚举', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const field = T_CustomField['枚举']
    addField(field.name)
    checkDescription(field)
    checkFistPart(field.name, true)
    checkRequired()
    checkEditable()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(3)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
    cy.get('[data-cy="enterWay"]>.ant-radio-group>.item')
      .last()
      .click()
    cy.get('[data-cy="ekb-radio-group-defaultValue"]>.ant-radio-group>.ant-radio-wrapper').should($lis => {
      cy.expect($lis).to.have.length(4)
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(0), 'first item').to.contain('无')
      cy.expect($lis.eq(1), 'second item').to.contain('上次选择')
      cy.expect($lis.eq(2), 'third item').to.contain('固定值')
      cy.expect($lis.eq(3), 'fourth item').to.contain(`从关联的申请单的自定义${field.name}取值`)
      cy.expect($lis.eq(0), 'first item will be checked').to.have.class('ant-radio-wrapper-checked')
    })
  })
  it('业务对象', function() {
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting').as('fieldSettingPanel')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    const field = T_CustomField['业务对象']
    addField(field.name)
    checkDescription(field)
    checkFistPart(field.name, true)
    checkRequired()
    cy.get('[data-cy="ekb-check-isLinkageAssignment"]').should('contain', '启用联动赋值')
    cy.get('[data-cy="ekb-check-required"]')
      .find('input')
      .should('be.checked')
  })
})
