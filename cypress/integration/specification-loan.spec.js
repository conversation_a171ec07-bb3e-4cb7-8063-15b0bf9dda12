/**************************************************
 * 单据模板-报销单
 * 1.新建借款单T_newLoan
 * 2.编辑借款单T_newLoan为T_editLoan，验证T_editLoan内配置的默认字段
 * 3.移动借款单T_editLoan到T_moveGroup分组
 * 4.删除借款单T_editLoan
 *
 输入
 **************************************************/

describe('借款单模版', function() {
  const T_specificGroup = 'Cy_group_1'
  const T_moveGroup = '借款单模板'
  const loanName = 'Cy_借款单' + Math.floor(Math.random() * 1000)
  const T_newLoan = {
    基本设置: {
      单据模板名称: loanName,
      审批流程: '默认借款流程',
      打印模板: '借款单（竖版）'
    }
  }
  const T_editLoan = {
    基本设置: {
      单据模板名称: loanName,
      审批流程: '默认借款流程',
      打印模板: '借款单（横版）'
    },
    字段设置: {
      默认字段: {
        标题: {
          字段名称: '标题',
          字段类型: '文本',
          文本类型: '多行文本',
          显示名称: '标题',
          提示文字: '请输入标题',
          // 系统计算: {
          //   计算规则: '从关联的申请单的标题取值',
          //   '数据互联的「联动赋值」 高于 「自动计算」': true
          // },
          手动填写: {
            限制字数: [0, 15],
            默认值: '无',
            '业务对象的「联动赋值」 高于 「手动填写」': true
          }
        },
        提交人: {
          字段名称: '提交人',
          字段类型: '员工',
          显示名称: '提交人',
          提示文字: '请选择提交人'
        },
        借款日期: {
          字段名称: '借款日期',
          字段类型: '日期',
          显示名称: '借款日期',
          '需填写时间（小时、分钟）': true,
          // 系统计算: {
          //   计算规则: '当前日期（即制单日期）',
          //   '数据互联的「联动赋值」 高于 「自动计算」': true
          // },
          手动填写: {
            默认值: '无',
            '业务对象的「联动赋值」 高于 「手动填写」': true
          }
        },
        还款日期: {
          字段名称: '还款日期',
          字段类型: '日期',
          显示名称: '还款日期',
          '需填写时间（小时、分钟）': true,
          // 系统计算: {
          //   计算规则: '当前日期（即制单日期）',
          //   '数据互联的「联动赋值」 高于 「自动计算」': true
          // },
          手动填写: {
            默认值: '默认制单日期+1月',
            '业务对象的「联动赋值」 高于 「手动填写」': true
          }
        },
        借款部门: {
          字段名称: '借款部门',
          字段类型: '部门',
          显示名称: '借款部门',
          提示文字: '请选择借款部门',
          这个是必填项: true,
          // 系统计算: {
          //   计算规则: '从关联的申请单的费用承担部门取值'
          // },
          手动填写: {
            只可选择最末级: true,
            使用字段依赖性: ['提交人', '部门主管'],
            默认值: '固定值',
            固定值: '测试部'
          }
        },
        收款信息: {
          字段名称: '收款信息',
          字段类型: '收款信息',
          显示名称: '收款信息',
          提示文字: '请选择收款信息',
          '业务对象的「联动赋值」 高于 「手动填写」': false
        },
        描述: {
          字段名称: '描述',
          字段类型: '文本',
          文本类型: '多行文本',
          显示名称: '描述',
          提示文字: '请输入描述',
          手动填写: {
            限制字数: [0, 150],
            '业务对象的「联动赋值」 高于 「手动填写」': true,
            默认值: '无'
          }
        },
        附件: {
          字段名称: '附件',
          字段类型: '附件'
        }
      }
    }
  }

  const testCheckBox = function(label, checked) {
    cy.get('@rightForm')
      .contains(label)
      .find('input')
      .each(input => {
        if (input[0].checked !== checked) {
          cy.get('@rightForm')
            .contains(label)
            .click()
        }
      })
  }

  const testManualVal = function(manualVal, label) {
    cy.get('@rightForm')
      .contains(label)
      .click()
    Object.keys(manualVal).forEach(function(key) {
      switch (key) {
        case '限制字数':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .each(function(input, i) {
              cy.get(input)
                .clear({ force: true })
                .type(manualVal[key][i])
            })
          break
        case '只可选择最末级':
          testCheckBox(key, manualVal[key])
          break
        case '使用字段依赖性':
          testCheckBox(key, !!manualVal[key])
          if (!!manualVal[key]) {
            const depVal = manualVal[key][0]
            const roleVal = manualVal[key][1]
            cy.get('@rightForm')
              .find('#dependence')
              .as('dependence')
              .find('.ant-select-selection')
              .should('be.visible')
              .click({ force: true })
            cy.get('@dependence')
              .find('.ant-select-search input')
              .should('be.visible')
              .type(depVal)
            cy.get('@dependence')
              .find('.ant-select-dropdown')
              .contains(depVal)
              .click({ force: true })
            cy.get('@rightForm')
              .find('#roleDef')
              .as('roleDef')
              .find('.ant-select-selection')
              .should('be.visible')
              .click({ force: true })
            cy.get('@roleDef')
              .find('.ant-select-search input')
              .should('be.visible')
              .type(roleVal)
            cy.get('@roleDef')
              .find('.ant-select-dropdown')
              .contains(roleVal)
              .click({ force: true })
          }
          break
        case '默认值':
          if (manualVal[key] === '固定值') {
            const defVal = manualVal['固定值']
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(manualVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other .ant-select')
              .should('be.visible')
              .click({ force: true })
            cy.get('.ant-select-tree-dropdown')
              .last()
              .as('selectTreeDropdown')
              .find('.ant-select-dropdown-search input')
              .type(defVal)
            cy.get('@selectTreeDropdown')
              .find('.ant-select-tree')
              .contains(defVal)
              .click({ force: true })
          } else {
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(manualVal[key])
              .click()
          }
          break
        case '业务对象的「联动赋值」 高于 「手动填写」':
          testCheckBox(key, manualVal[key])
          break
      }
    })
  }

  const testAutoVal = function(autoVal, label) {
    cy.get('@rightForm')
      .contains(label)
      .click()
    Object.keys(autoVal).forEach(function(key) {
      switch (key) {
        case '计算规则':
          if (autoVal[key] === '从明细中选取下述字段求和') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other')
              .as('radioValContainer')
              .should('be.visible')
              .find('.ant-select')
              .click({ force: true })
            cy.get('@radioValContainer')
              .find('.ant-select-dropdown')
              .contains(value)
              .click({ force: true })
          } else if (autoVal[key] === '启用个人费用标准') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other .ant-select')
              .should('be.visible')
              .click({ force: true })
            cy.get('.ant-select-dropdown')
              .last()
              .contains(value)
              .click({ force: true })
          } else if (autoVal[key] === '固定值') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other #ConditionalInput')
              .should('be.visible')
              .find('input')
              .clear({ force: true })
              .type(value, { force: true })
          } else if (autoVal[key] === '计算公式') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other')
              .as('radioOther')
              .should('be.visible')
            cy.get('@radioOther')
              .find('.content_obj .font')
              .then(el => {
                el.text(value)
              })
          } else {
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
          }
          break
        case '业务对象的「联动赋值」 高于 「手动填写」':
          testCheckBox(key, autoVal[key])
          break
      }
    })
  }

  const testField = function(fieldVal, label) {
    cy.get('@simulator')
      .find('.simulator-item')
      .contains(label)
      .click()
    cy.wait(1000)
    Object.keys(fieldVal).forEach(function(key) {
      if (
        key === '这个是必填项' ||
        key === '需填写时间（小时、分钟）' ||
        key === '「报销总额」>申请单「申请金额」时有风险提示' ||
        key === '「报销金额」>申请单「申请余额」时有风险提示' ||
        key === '「报销金额」>申请单「申请余额」时禁止提交' ||
        key === '添加明细时，先选费用类型' ||
        key === '业务对象的「联动赋值」 高于 「手动填写」'
      ) {
        testCheckBox(key, fieldVal[key])
        return
      }

      switch (key) {
        case '字段名称':
          cy.get('@rightForm')
            .contains(key)
            .parents('.meta_name_item')
            .find('.meta_name_val')
            .should('have.text', fieldVal[key])
          break
        case '字段类型':
          cy.get('@rightForm')
            .contains(key)
            .parents('.meta_name_item')
            .find('.meta_name_val')
            .should('have.text', fieldVal[key])
          break
        case '文本类型':
          if (fieldVal[key] === '多行文本') {
            cy.get('@rightForm')
              .contains('多行文本')
              .click()
          }
          break
        case '显示名称':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(fieldVal[key])
          break
        case '提示文字':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(fieldVal[key])
          break
        case '展示方式':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .contains(fieldVal[key])
            .click()
          break
        case '手动填写':
          testManualVal(fieldVal[key], key)
          break
        case '系统计算':
          testAutoVal(fieldVal[key], key)
          break
        default:
          throw new Error('Did not find ' + key)
      }
    })
  }

  const testBasicSetting = function(basicInfo) {
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-content')
      .find('.ant-tabs-tabpane-active')
      .as('activePanelBasic')
    cy.get('.icon-wrapper .icon').should('have.length', 32)
    cy.get('.color-wrapper .color-btn').should('have.length', 38)
    Object.keys(basicInfo).forEach(function(key, index) {
      switch (key) {
        case '单据模板名称':
          cy.get('@activePanelBasic')
            .contains('单据模板名称')
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(basicInfo[key])
          break
        case '审批流程':
          cy.get('@activePanelBasic')
            .contains('审批流程')
            .parents('.ant-form-item')
            .find('.ant-select')
            .as('selectFlow')
            .click()
          cy.get('@selectFlow')
            .parent()
            .contains(basicInfo[key])
            .click()
          break
        case '打印模板':
          cy.get('@activePanelBasic')
            .contains('打印模板')
            .parents('.ant-form-item')
            .find('.ant-select')
            .as('selectPrint')
            .click()
          cy.get('@selectPrint')
            .parent()
            .contains(basicInfo[key])
            .click()
          break
      }
    })
  }

  const testDefaultField = function(defaultField) {
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting')
      .as('fieldSettingPanel')
      .should('be.visible')
    cy.get('@fieldSettingPanel')
      .find('#custom-simulator')
      .as('simulator')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    Object.keys(defaultField).forEach(function(key) {
      testField(defaultField[key], key)
    })
  }

  const searchSelectExpense = function(expenseName) {
    cy.get('[data-cy="specificLeft-actionPart"] .ant-input')
      .clear()
      .type(expenseName)
    cy.get('[data-cy="specificLeft-listPart"] .highlight')
      .contains(expenseName)
      .click()

    cy.get('[data-cy="specificRight-titlePart"]')
      .contains(expenseName)
      .should('be.visible')
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .find('.ant-tabs-tab-active')
      .should('have.text', '基本设置')
  }

  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"customSpecification"}'
        )
        cy.visit('/web/app.html#/custom-specification')
        cy.wait(3000)
      })
  })

  afterEach(() => {
    cy.logout()
    cy.wait(1000)
  })

  it('创建借款单', function() {
    const basicInfo = T_newLoan['基本设置']
    cy.get('[data-cy="specificLeft-titlePart"] .ant-btn').click()
    cy.get('.add-card')
      .contains('新建单据模板')
      .click()
    cy.get('#custom-addTemplateModal')
      .as('addTemplateModal')
      .should('be.visible')
    cy.get('@addTemplateModal')
      .find('#name')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .contains(T_specificGroup)
      .click()
    cy.get('@addTemplateModal')
      .find('.template-list')
      .contains('借款单')
      .click()
    cy.get('@addTemplateModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()

    cy.get('[data-cy="specificRight-titlePart"]')
      .contains('添加借款单模板')
      .should('be.visible')
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .find('.ant-tabs-tab-active')
      .should('have.text', '基本设置')
    testBasicSetting(basicInfo)
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting')
      .as('fieldSettingPanel')
      .should('be.visible')
    cy.get('@fieldSettingPanel')
      .find('#custom-simulator')
      .as('simulator')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    cy.get('@simulator')
      .find('.simulator-item.simulator-default')
      .contains('提交人')
      .click()
    cy.get('@rightForm')
      .find('.meta_name_item')
      .first()
      .find('.meta_name_val')
      .should('have.text', '提交人')

    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('保 存')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('编辑借款单', function() {
    const basicInfo = T_editLoan['基本设置']
    const defaultField = T_editLoan['字段设置']['默认字段']

    const loanName = T_newLoan['基本设置']['单据模板名称']

    searchSelectExpense(loanName)

    testBasicSetting(basicInfo)

    testDefaultField(defaultField)
    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('保 存')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('移动借款单', function() {
    const basicInfo = T_editLoan['基本设置']
    const loanName = basicInfo['单据模板名称']
    searchSelectExpense(loanName)
    cy.get('[data-cy="specificRight-titlePart"]')
      .contains('移动')
      .click()
    cy.get('.group-content')
      .as('groupContent')
      .should('be.visible')
    cy.get('@groupContent')
      .contains(T_moveGroup)
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('删除借款单', function() {
    const basicInfo = T_editLoan['基本设置']
    const name = basicInfo['单据模板名称']
    searchSelectExpense(name)
    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('删 除')
      .click()
    cy.get('.del-confirm-modal')
      .as('delModal')
      .should('be.visible')
    cy.get('@delModal')
      .find('.content input')
      .clear()
      .type(name)
    cy.get('@delModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })
})
