const T_groupName = '档案关系默认分组'
const T_sourceType = '部门'
const T_purposeType = '自定义档案'

const T_sourceName = '132'
const T_purposeName = '132(反)'


async function getDepartment() {
  const session = JSON.parse(localStorage.getItem('session'))
  const corpId = session.user.corpId
  return new Promise((resolve, reject) => {
    cy.request('GET', `/api/flow/v2/referables/$organization.Department?withVisibility=true&corpId=${corpId}`).then(
      response => {
        expect(response.status).to.equal(200)
        resolve(response.body)
      }
    )
  })
}

function selectedStaff(order) {
  const $el = `.list-view > :nth-child(${order})`
  cy.get($el).click()
  cy.get('[data-cy="SelectStaffsModal@Confirm"').click()
}

function checkStaff(order) {
  const $el = order === 1 ? `[data-cy="TagSelector@Input"]:first` : `[data-cy="TagSelector@Input"]:last`
  cy.get($el).click()
  selectedStaff(order)
  cy.get(`${$el} .ant-tag`).should('have.length', 1)
}

function selectDepartment(i) {
  cy.get('.ant-tree-switcher').click()
  cy.get(`.ant-tree-child-tree > :nth-child(${i}) > .ant-tree-checkbox > .ant-tree-checkbox-inner`).click()
  cy.get('.ant-modal-footer > div > .ant-btn-primary').click()
}

function checkDepartment(order) {
  // const { items } = await getDepartment()
  // const selectedDepart = items[0]
  const $el = order === 1 ? `[data-cy="TagSelector@Input"]:first` : `[data-cy="TagSelector@Input"]:last`
  cy.get($el).click()
  selectDepartment(1)
  cy.get(`${$el} .ant-tag`).should('have.length', 1)
  return
}

function selectDimension() {
  cy.contains(T_groupName).trigger('click')
  cy.contains(`${T_sourceName}与${T_purposeName}`).trigger('click')
  cy.get('[data-cy="DimensionTable"]')
    .find('tbody:first tr td:nth-child(2)')
    .should('have.text', T_sourceName)

  cy.get('[data-cy="DimensionTable"]')
    .find('tbody:first tr td:nth-child(3)')
    .should('have.text', T_purposeName)
}

describe('DimensionTable Test', function() {
  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"dimensionMap","isGuided":true}'
        )
        cy.visit('/app.html#/dimension-map')
      })
    cy.wait(1000)
  })

  it('create a  relation', function() {
    selectDimension()

    cy.get('[data-cy="DimensionTable@BatchAdd"]').click()

    if (T_sourceType === '员工') {
      checkStaff(1)
    }

    if (T_sourceType === '部门' || T_sourceType === '自定义档案') {
      checkDepartment(1)
    }
    if (T_purposeType === '部门' || T_purposeType === '自定义档案') {
      checkDepartment(2)
    }

    if (T_purposeType === '员工') {
      checkStaff(2)
    }

    cy.get('[data-cy="AddScopeModal@Confirm"').click()
    //保存成功
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('edit a  relation', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@Edit"]:first').click()
    cy.get('[data-cy="TagSelector@Input"]:first .ant-tag').should('have.length', 1)
    cy.get('[data-cy="TagSelector@Input"]:last .ant-tag').should('have.length', 1)
    cy.get('[data-cy="TagSelector@Input"]:last').click()
    if (T_purposeType === '部门' || T_purposeType === '自定义档案') {
      selectDepartment(2)
    }

    if (T_purposeType === '员工') {
      selectedStaff(3)
    }

    cy.get('[data-cy="TagSelector@Input"]:last .ant-tag').should('have.length', 2)
    cy.get('[data-cy="AddScopeModal@Confirm"]').click()
    //保存成功
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('delete a  relation', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@Delete"]')
      .first()
      .click()
    cy.get('.ant-popover-buttons > .ant-btn-primary').click({ force: true })
    //删除
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('switch dimension', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@SwitchDimension"]').click()
    cy.get('.ant-dropdown ul li:last').click()

    cy.get('[data-cy="DimensionTable"]')
      .find('tbody:first tr td:nth-child(2)')
      .should('have.text', T_purposeName)

    cy.get('[data-cy="DimensionTable"]')
      .find('tbody:first tr td:nth-child(3)')
      .should('have.text', T_sourceName)
  })
})
