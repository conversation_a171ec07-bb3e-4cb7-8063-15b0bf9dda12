import { toCompactDateTime } from '../utils/DateFormat'
import { dataGet } from '../utils/utils'

const goToBudget = name => {
  cy.log('进入预算')
  dataGet('budgetListItem')
    .contains(name)
    .click()
    .invoke('text')
    .as('budgetName')
}

const deleteBudget = isDel => {
  cy.log('click 删除')
  dataGet('deleteBudgetBtn').click()
  cy.log('input budgetName')
  cy.get('@budgetName').then(data => {
    dataGet('deleteBudgetInp')
      .type(data)
      .should($ele => {
        expect($ele.val()).to.equal(data)
      })
  })
  if (isDel) {
    cy.log('Confirm 删除')
    dataGet('delBudgetConfirmBtn').click()
  } else {
    cy.log('cancel 删除')
    dataGet('delBudgetCancle').click()
  }
}

describe('Budget Test', () => {
  beforeEach(() => {
    cy.login().initLanguage()
    cy.log('Go to budget-manage')
    cy.visit('/app.html#/budget-manage')
    dataGet('budgetTitle').should($ele => {
      expect($ele.text()).to.equal(i18n.get('预算控制'))
    })
  })
  it('create new budget', () => {
    cy.log('Create new budget')
    dataGet('newBudget').click()
    cy.wait()
    dataGet('newBudgetTitle').should($ele => {
      expect($ele.text()).to.equal(i18n.get('新建预算'))
    })
    dataGet('newBudgetName')
      .type(`周期累计控制 ${toCompactDateTime(new Date())}`)
      .should($ele => {
        expect($ele.val()).to.include('周期累计控制')
      })
    dataGet('newBudgetCtrl')
      .eq(1)
      .click()
    dataGet('newBudgetConfirm').click()
    dataGet('saveAndPulishBtn').click()
  })
  it('delete budget', () => {
    goToBudget('周期累计控制')
    deleteBudget(false)
    deleteBudget(true)
  })
})
