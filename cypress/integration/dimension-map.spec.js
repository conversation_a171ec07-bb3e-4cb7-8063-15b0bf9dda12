/**************************************************
 * 档案关系测试用例
 * 1.新建档案分组，名称为T_groupName
 * 2.编辑分组，修改名称为T_groupName_edit
 * 3.新建档案关系，选择分组为T_groupName_edit，关系设置类型为T_sourceType和T_purposeType，名称为T_sourceName和T_purposeName
 * 4.为档案关系T_sourceType和T_purposeType，添加关系
 * 5.搜索名称为T_searchDimen的档案关系，选中此档案关系，在右上角搜索框输入T_searchContent，搜索其内容
 * 6.编辑关系
 * 7.删除关系
 * 8.切换关系维度
 * 9.搜索档案关系，输入搜索内容为T_search
 * 10.编辑此档案关系，修改关系设置名称为T_sourceName_edit
 * 11.移动此档案关系到T_moveGroup分组
 * 12.删除档案关系
 * 13.删除档案分组
 * 
输入 
**************************************************/

const T_groupName = 'Cy_Group'
const T_groupName_edit = 'Cy_Group_edit'

const T_sourceName = 'Cy_sourceName'
const T_sourceName_edit = 'Cy_source_Edit'
const T_purposeName = 'Cy_purposeName'
const T_sourceType = '员工'
const T_purposeType = '部门'

const T_search = 'Cy'
const T_moveGroup = '档案关系默认分组'

const T_searchDimen = 'Cy_sourceName与Cy_purposeName'
const T_searchContent = '2-20'

function selectedStaff(order) {
  const $el = `.list-view > :nth-child(${order})`
  cy.get($el).click()
  cy.get('[data-cy="SelectStaffsModal@Confirm"').click()
}

function checkStaff(order) {
  const $el = order === 1 ? `[data-cy="TagSelector@Input"]:first` : `[data-cy="TagSelector@Input"]:last`
  cy.get($el).click()
  selectedStaff(order)
  cy.get(`${$el} .ant-tag`).should('have.length', 1)
}

function selectDepartment(i) {
  cy.get('.ant-tree-switcher').click()
  cy.get(`.ant-tree-child-tree > :nth-child(${i}) > .ant-tree-checkbox > .ant-tree-checkbox-inner`).click()
  cy.get('.ant-modal-footer > div > .ant-btn-primary').click()
}

function checkDepartment(order) {
  // const { items } = await getDepartment()
  // const selectedDepart = items[0]
  const $el = order === 1 ? `[data-cy="TagSelector@Input"]:first` : `[data-cy="TagSelector@Input"]:last`
  cy.get($el).click()
  selectDepartment(1)
  cy.get(`${$el} .ant-tag`).should('have.length', 1)
  return
}

function selectDimension() {
  cy.contains(T_groupName).trigger('click')
  cy.contains(`${T_sourceName}与${T_purposeName}`).trigger('click')
  cy.get('[data-cy="DimensionTable"]')
    .find('tbody:first tr td:nth-child(2)')
    .should('have.text', T_sourceName)

  cy.get('[data-cy="DimensionTable"]')
    .find('tbody:first tr td:nth-child(3)')
    .should('have.text', T_purposeName)
}

describe('Dimension-map Test', function() {
  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"dimensionMap","isGuided":true}'
        )
        cy.visit('/web/app.html#/dimension-map')
        cy.wait(1000)
      })
  })

  afterEach(() => {
    cy.logout()
    cy.wait(1000)
  })

  it('createDimensionGroup', function() {
    cy.get('[data-cy="dimensionHeader"] .ant-btn').trigger('mouseover')

    cy.get('[data-cy="dimensionHeaderMenu"]').as('dropdownMenu')
    cy.get('@dropdownMenu').should('be.visible')
    cy.get('@dropdownMenu')
      .last()
      .contains(window.i18n.get('新建分组'))
      // .find('li:nth-child(1)')
      .click()

    cy.get('.add-role-group-modal').as('addModel')
    cy.get('@addModel')
      .find('#name')
      .type(T_groupName)
    cy.get('@addModel')
      .find('.modal-footer-v2 > .ant-btn-primary')
      .click()

    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('editDimensionGroup', function() {
    // cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title').as('hoverGroupItem')
    cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title')
      .contains(T_groupName)
      .parents('.ant-menu-submenu-title')
      .as('hoverGroupItem')
    cy.get('@hoverGroupItem').trigger('mouseover')
    cy.get('@hoverGroupItem')
      .find('.ant-dropdown-trigger.icon-menu')
      .should('be.visible')
      .trigger('mouseover')

    cy.get('[data-cy="dimensionListGroupMenu"]')
      .last()
      .as('dropdownMenu')
      .should('be.visible')
    cy.get('@dropdownMenu')
      .contains(window.i18n.get('编辑'))
      .click()

    cy.get('.add-role-group-modal').as('editModel')
    cy.get('@editModel')
      .find('#name')
      .as('groupName')
      .type('_edit_edit_edit_edit_edit')
    cy.get('@groupName')
      .parent()
      .parent()
      .should('have.class', 'has-error')
    cy.get('@groupName')
      .clear()
      .type(T_groupName_edit)

    cy.get('@editModel')
      .find('.modal-footer-v2 > .ant-btn-primary')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('createDimension', function() {
    cy.get('[data-cy="dimensionHeader"] .ant-btn').trigger('mouseover')

    cy.get('[data-cy="dimensionHeaderMenu"]').as('dropdownMenu')
    cy.get('@dropdownMenu').should('be.visible')
    cy.get('@dropdownMenu')
      .contains(window.i18n.get('新建档案关系'))
      .click()

    cy.get('[data-cy="dimensionMapEdit"]').as('addDimenModel')
    cy.get('@addDimenModel')
      .find('#roleGroupId')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .should('be.visible')
      .contains(T_groupName_edit)
      .click()
    cy.get('@addDimenModel')
      .find('[data-cy="roleMapSelect"] #sourceType')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .should('be.visible')
      .contains(T_sourceType)
      .click()
    cy.get('@addDimenModel')
      .find('[data-cy="roleMapSelect"] #purposeType')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .should('be.visible')
      .contains(T_purposeType)
      .click()
    cy.get('@addDimenModel')
      .find('[data-cy="roleMapSelect"] #sourceName')
      .type(T_sourceName)
    cy.get('@addDimenModel')
      .find('[data-cy="roleMapSelect"] #purposeName')
      .type(T_purposeName)

    cy.get('@addDimenModel')
      .find('.modal-footer-v2 > .ant-btn-primary')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })
  it('create a  relation', function() {
    selectDimension()

    cy.get('[data-cy="DimensionTable@BatchAdd"]').click()

    if (T_sourceType === '员工') {
      checkStaff(1)
    }

    if (T_sourceType === '部门' || T_sourceType === '自定义档案') {
      checkDepartment(1)
    }
    if (T_purposeType === '部门' || T_purposeType === '自定义档案') {
      checkDepartment(2)
    }

    if (T_purposeType === '员工') {
      checkStaff(2)
    }

    cy.get('[data-cy="AddScopeModal@Confirm"').click()
    //保存成功
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('searchDimensionContent', function() {
    cy.get('[data-cy="searchFilterBar"] .ant-input').type(T_searchDimen)
    cy.get('[data-cy="dimensionList"] .ant-menu-sub .ant-menu-item')
      .first()
      .as('selectDimension')
      .click()
    cy.get('@selectDimension')
      .find('.item-name')
      .then(item => {
        expect(item[0].innerText.indexOf(T_searchDimen) !== -1).to.be.true
      })
    cy.get('[data-cy=dimensionContentHeader] .ant-input-search > .ant-input')
      .as('searchContentInput')
      .should('be.visible')
    cy.get('[data-cy="DimensionTable"]')
      .as('dimensionTable')
      .should('be.visible')

    cy.get('@searchContentInput')
      .type(T_searchContent)
      .type('{enter}')
    cy.wait(3000)
    cy.get('@dimensionTable')
      .find('.dx-datagrid-content tr.dx-data-row td:nth-child(2) span')
      .then(el => {
        expect(el[0].innerText.indexOf(T_searchContent) !== -1).to.be.true
        // expect(el.length === 2).to.be.true
      })
  })

  it('edit a  relation', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@Edit"]:first').click()
    cy.get('[data-cy="TagSelector@Input"]:first .ant-tag').should('have.length', 1)
    cy.get('[data-cy="TagSelector@Input"]:last .ant-tag').should('have.length', 1)
    cy.get('[data-cy="TagSelector@Input"]:last').click()
    if (T_purposeType === '部门' || T_purposeType === '自定义档案') {
      selectDepartment(2)
    }

    if (T_purposeType === '员工') {
      selectedStaff(3)
    }

    cy.get('[data-cy="TagSelector@Input"]:last .ant-tag').should('have.length', 2)
    cy.get('[data-cy="AddScopeModal@Confirm"]').click()
    //保存成功
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('delete a  relation', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@Delete"]')
      .first()
      .click()
    cy.get('.ant-popover-buttons > .ant-btn-primary').click({ force: true })
    //删除
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('switch dimension', function() {
    selectDimension()
    cy.get('[data-cy="DimensionTable@SwitchDimension"]').click()
    cy.get('.ant-dropdown ul li:last').click()

    cy.get('[data-cy="DimensionTable"]')
      .find('tbody:first tr td:nth-child(2)')
      .should('have.text', T_purposeName)

    cy.get('[data-cy="DimensionTable"]')
      .find('tbody:first tr td:nth-child(3)')
      .should('have.text', T_sourceName)
  })

  it('searchDimension', function() {
    cy.get('[data-cy="searchFilterBar"] .ant-input').type(T_search)
    cy.get('[data-cy="dimensionList"] .ant-menu-sub .ant-menu-item .item-name')
      .first()
      .then(item => {
        expect(item[0].innerText.indexOf(T_search) !== -1).to.be.true
      })
    cy.get('[data-cy="searchFilterBar"] .ant-input').clear()
  })

  it('editDimension', function() {
    cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title')
      .contains(T_groupName_edit)
      .parents('.ant-menu-submenu-title')
      .as('groupItem')
      .click()
    cy.get('@groupItem')
      .parent()
      .should('have.class', 'ant-menu-submenu-open')
    cy.get('@groupItem')
      .parent()
      .contains(T_sourceName + '与' + T_purposeName)
      .parents('[data][role="menuitem"]')
      .as('selectedDimension')
      .click()

    cy.get('[data-cy=dimensionContentHeader]').should('be.visible')
    cy.get('@selectedDimension')
      .trigger('mouseover')
      .find('.icon-menu')
      .trigger('mouseover')

    cy.get('[data-cy="dimensionListItemMenu"]')
      .last()
      .as('dropdownMenu')
    cy.get('@dropdownMenu').should('be.visible')
    cy.get('@dropdownMenu')
      .contains(window.i18n.get('编辑'))
      .click()

    cy.get('[data-cy="dimensionMapEdit"]')
      .as('editDimenModel')
      .should('be.visible')
    cy.get('@editDimenModel')
      .find('[data-cy="roleMapSelect"] #sourceType')
      .should('have.class', 'ant-select-disabled')
    cy.get('@editDimenModel')
      .find('[data-cy="roleMapSelect"] #sourceName')
      .as('sourceName')
      .type('_edit_edit_edit_edit_edit_edit_edit')
    cy.get('@sourceName')
      .parent()
      .parent()
      .should('have.class', 'has-error')
    cy.get('@sourceName')
      .clear()
      .type(T_sourceName_edit)

    cy.get('@editDimenModel')
      .find('.modal-footer-v2 > .ant-btn-primary')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
    cy.get('[data-cy=dimensionContentHeader] .content-title-text').should(
      'have.text',
      T_sourceName_edit + '与' + T_purposeName
    )
  })

  it('moveDimension', function() {
    cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title')
      .contains(T_groupName_edit)
      .parents('.ant-menu-submenu-title')
      .as('groupItem')
      .click()
    cy.get('@groupItem')
      .parent()
      .should('have.class', 'ant-menu-submenu-open')
    cy.get('@groupItem')
      .parent()
      .contains(T_sourceName_edit + '与' + T_purposeName)
      .parents('[data][role="menuitem"]')
      .as('selectedDimension')
      .click()

    cy.get('[data-cy=dimensionContentHeader]')
      .as('dimensionContentHeader')
      .should('be.visible')
    cy.get('@dimensionContentHeader')
      .find('.content-title-right > .content-title-btn')
      .first()
      .click()

    cy.get('[data-cy="dimensionMaoItemMove"]')
      .as('moveDimenModel')
      .should('be.visible')
    cy.get('@moveDimenModel')
      .find('#id')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .as('groupSelect')
      .should('be.visible')
    cy.get('@groupSelect')
      .contains(T_moveGroup)
      .click()
    cy.get('@moveDimenModel')
      .find('.modal-footer-v2 > .ant-btn-primary')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('deleteDimension', function() {
    cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title')
      .contains(T_moveGroup)
      .parents('.ant-menu-submenu-title')
      .as('groupItem')
      .click()
    cy.get('@groupItem')
      .parent()
      .should('have.class', 'ant-menu-submenu-open')
    cy.get('@groupItem')
      .parent()
      .contains(T_sourceName_edit + '与' + T_purposeName)
      .parents('[data][role="menuitem"]')
      .as('selectedDimension')
      .click()

    cy.get('[data-cy=dimensionContentHeader]')
      .as('dimensionContentHeader')
      .should('be.visible')
    cy.get('@dimensionContentHeader')
      .find('.content-title-right > .content-title-btn')
      .last()
      .click()

    cy.get('.del-role-group-modal')
      .as('deleteModel')
      .should('be.visible')
    cy.get('@deleteModel')
      .find('#name')
      .as('deleteName')
      .type('tipError')

    cy.get('@deleteName')
      .parent()
      .parent()
      .should('have.class', 'has-error')

    cy.get('@deleteModel')
      .find('.content .desc')
      .then(el => {
        const deleteNames = el[0].innerText.match(/[^\「\」]+(?=\」)/g)

        cy.get('@deleteModel')
          .find('#name')
          .clear()
          .type(deleteNames[0])
        cy.get('@deleteModel')
          .find('.modal-footer-v2 > .ant-btn-primary')
          .click()
        cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
      })
  })

  it('deleteDimensionGroup', function() {
    cy.get('[data-cy="dimensionList"] li > .ant-menu-submenu-title')
      .contains(T_groupName_edit)
      .parents('.ant-menu-submenu-title')
      .as('hoverGroupItem')
    cy.get('@hoverGroupItem').trigger('mouseover')
    cy.get('@hoverGroupItem')
      .find('.ant-dropdown-trigger.icon-menu')
      .should('be.visible')
      .trigger('mouseover')

    cy.get('[data-cy="dimensionListGroupMenu"]')
      .last()
      .as('dropdownMenu')
    cy.get('@dropdownMenu').should('be.visible')
    cy.get('@dropdownMenu')
      .find('li:nth-child(3)')
      .click()

    cy.get('.del-role-group-modal').as('deleteModel')
    cy.get('@deleteModel')
      .find('#name')
      .as('deleteName')
      .type('tipError')

    cy.get('@deleteName')
      .parent()
      .parent()
      .should('have.class', 'has-error')

    cy.get('@deleteModel')
      .find('.content .desc')
      .then(el => {
        const deleteNames = el[0].innerText.match(/[^\「\」]+(?=\」)/g)

        cy.get('@deleteModel')
          .find('#name')
          .clear()
          .type(deleteNames[0])
        cy.get('@deleteModel')
          .find('.modal-footer-v2 > .ant-btn-primary')
          .click()
        cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
      })
  })
})
