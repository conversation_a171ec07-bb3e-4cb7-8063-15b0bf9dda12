/**************************************************
 * 单据模板-报销单
 * 1.新建模板分组，名称为T_specificGroup
 * 2.编辑分组,名称为T_specificGroup_edit
 * 3.删除分组T_specificGroup
 *
 输入
 **************************************************/

describe('新建单据模版分组', function() {
  const T_specificGroup = 'Cy_group_测试_1'
  const T_specificGroup_edit = 'Cy_group_测试_编辑'
  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"customSpecification"}'
        )
        cy.visit('/web/app.html#/custom-specification')
        cy.wait(1000)
      })
  })

  it('新建分组', function() {
    cy.get('[data-cy="specificLeft-titlePart"] .ant-btn').click()
    cy.get('.add-card')
      .contains('新建分组')
      .click()
    cy.get('#custom-addGroupModal')
      .as('addGroupModal')
      .should('be.visible')
    cy.get('@addGroupModal')
      .find('#name')
      .type('12345678990432432423432')
    cy.get('@addGroupModal').should('contain', '分组名称不超过14个字')
    cy.get('@addGroupModal')
      .find('#name')
      .clear()
      .type(T_specificGroup)
    cy.get('@addGroupModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('编辑分组', function() {
    cy.get('[data-cy="specificLeft-listPart"] .group-title')
      .contains(T_specificGroup)
      .as('groupItem')
      .trigger('mouseover')
    cy.get('@groupItem')
      .find('.group-edit')
      .contains('编辑')
      .should('be.visible')
      .click()
    cy.get('#custom-editGroupModal')
      .as('editGroupModal')
      .should('be.visible')
    cy.get('@editGroupModal')
      .find('#name')
      .clear()
      .type(T_specificGroup_edit)
    cy.get('.modal-footer > .ant-btn-primary').click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('删除分组', function() {
    cy.get('[data-cy="specificLeft-listPart"] .group-title')
      .contains(T_specificGroup_edit)
      .as('groupItem')
      .trigger('mouseover')
    cy.get('@groupItem')
      .find('.group-edit')
      .contains('编辑')
      .should('be.visible')
      .click()
    cy.get('#custom-editGroupModal')
      .as('editGroupModal')
      .should('be.visible')
    cy.get('@editGroupModal')
      .find('.modal-footer')
      .contains('删 除')
      .click()
    cy.get('#custom-delGroupModal')
      .as('deleteGroupModal')
      .should('be.visible')
    cy.get('@deleteGroupModal')
      .find('#groupName')
      .type(T_specificGroup)
    cy.get('@deleteGroupModal').should('contain', '输入的分组名称不正确')
    cy.get('@deleteGroupModal')
      .find('#groupName')
      .clear()
      .type(T_specificGroup_edit)
    cy.get('@deleteGroupModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })
})
