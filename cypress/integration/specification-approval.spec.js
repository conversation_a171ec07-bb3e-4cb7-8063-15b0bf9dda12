/**************************************************
 * 单据模板-报销单
 * 2.新建通用审批单T_newExpense
 * 3.编辑通用审批单T_newExpense为T_editExpense，验证T_editExpense内配置的默认字段
 * 5.移动通用审批单T_editExpense到T_moveGroup分组
 * 6.删除通用审批单T_editExpense
 * 
输入 
**************************************************/

describe('通用审批单用例', function() {
  const T_specificGroup = 'Cy_group_1'
  const T_moveGroup = '报销单模板'
  const T_newExpense = {
    基本设置: {
      单据模板名称: 'Cy_通用审批单' + Math.floor(Math.random() * 1000),
      审批流程: '默认报销流程',
      打印模板: '默认基础打印模板'
    }
  }
  const T_editExpense = {
    基本设置: {
      单据模板名称: 'Cy_编辑通用审批单' + Math.floor(Math.random() * 1000),
      审批流程: '默认报销流程',
      打印模板: '默认基础打印模板'
    },
    字段设置: {
      默认字段: {
        标题: {
          字段名称: '标题',
          字段类型: '文本',
          文本类型: '多行文本',
          显示名称: '标题',
          提示文字: '请输入标题',
          // 系统计算: {
          //   计算规则: '从关联的申请单的标题取值',
          //   '业务对象的「联动赋值」 高于 「手动填写」': true
          // },
          手动填写: {
            限制字数: [0, 15],
            默认值: '无',
            '业务对象的「联动赋值」 高于 「手动填写」': true
          }
        },
        提交人: {
          字段名称: '提交人',
          字段类型: '员工',
          显示名称: '提交人',
          提示文字: '请选择提交人'
        },
        描述: {
          字段名称: '描述',
          字段类型: '文本',
          文本类型: '多行文本',
          显示名称: '描述',
          提示文字: '请输入描述',
          系统计算: {
            计算规则: '无',
            '业务对象的「联动赋值」 高于 「手动填写」': true
          },
          手动填写: {
            限制字数: [0, 150],
            '业务对象的「联动赋值」 高于 「手动填写」': true,
            默认值: '无'
          }
        }
      },
      自定义字段: {
        法人实体: {
          字段名称: '法人实体',
          字段类型: '自定义档案',
          展示方式: '搜索（适合候选值较多）',
          显示名称: '法人实体',
          提示文字: '请选择法人实体',
          这个是必填项: true,
          手动填写: {
            只可选择最末级: true,
            使用字段依赖性: ['费用承担部门', '法人'],
            默认值: '无',
            '业务对象的「联动赋值」 高于 「手动填写」': false
          }
        },
        税额: {
          字段名称: '税额',
          字段类型: '金额',
          显示名称: '税额',
          提示文字: '请输入税额',
          这个是必填项: true,
          系统计算: {
            计算规则: '启用个人费用标准',
            启用个人费用标准: '按月控制',
            '数据互联的「联动赋值」 高于 「自动计算」': false
          }
        },
        税率: {
          字段名称: '税率',
          字段类型: '数字',
          显示名称: '税率',
          提示文字: '请输入税率',
          系统计算: {
            计算规则: '固定值',
            固定值: '0.2',
            '数据互联的「联动赋值」 高于 「自动计算」': true
          }
        }
      }
    }
  }

  const testCheckBox = function(label, checked) {
    cy.get('@rightForm')
      .contains(label)
      .find('input')
      .each(input => {
        if (input[0].checked !== checked) {
          cy.get('@rightForm')
            .contains(label)
            .click()
        }
      })
  }

  const testManualVal = function(manualVal, label) {
    cy.get('@rightForm')
      .contains(label)
      .click()
    Object.keys(manualVal).forEach(function(key) {
      switch (key) {
        case '限制字数':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .each(function(input, i) {
              cy.get(input)
                .clear({ force: true })
                .type(manualVal[key][i])
            })
          break
        case '只可选择最末级':
          testCheckBox(key, manualVal[key])
          break
        case '使用字段依赖性':
          testCheckBox(key, !!manualVal[key])
          if (!!manualVal[key]) {
            const depVal = manualVal[key][0]
            const roleVal = manualVal[key][1]
            cy.get('@rightForm')
              .find('#dependence')
              .as('dependence')
              .find('.ant-select-selection')
              .should('be.visible')
              .click({ force: true })
            cy.get('@dependence')
              .find('.ant-select-search input')
              .should('be.visible')
              .type(depVal)
            cy.get('@dependence')
              .find('.ant-select-dropdown')
              .contains(depVal)
              .first()
              .click({ force: true })
            cy.get('@rightForm')
              .find('#roleDef')
              .as('roleDef')
              .find('.ant-select-selection')
              .should('be.visible')
              .click({ force: true })
            cy.get('@roleDef')
              .find('.ant-select-search input')
              .should('be.visible')
              .type(roleVal)
            cy.get('@roleDef')
              .find('.ant-select-dropdown')
              .contains(roleVal)
              .first()
              .click({ force: true })
          }
          break
        case '默认值':
          if (manualVal[key] === '固定值') {
            const defVal = manualVal['固定值']
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(manualVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other .ant-select')
              .should('be.visible')
              .click({ force: true })
            cy.get('.ant-select-tree-dropdown')
              .last()
              .as('selectTreeDropdown')
              .find('.ant-select-dropdown-search input')
              .type(defVal)
            cy.get('@selectTreeDropdown')
              .find('.ant-select-tree')
              .contains(defVal)
              .click('center', { force: true })
          } else {
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(manualVal[key])
              .click()
          }
          break
        case '业务对象的「联动赋值」 高于 「手动填写」':
          testCheckBox(key, manualVal[key])
          break
      }
    })
  }

  const testAutoVal = function(autoVal, label) {
    cy.get('@rightForm')
      .contains(label)
      .click()
    Object.keys(autoVal).forEach(function(key) {
      switch (key) {
        case '计算规则':
          if (autoVal[key] === '从明细中选取下述字段求和') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other')
              .as('radioValContainer')
              .should('be.visible')
              .find('.ant-select')
              .click({ force: true })
            cy.get('@radioValContainer')
              .find('.ant-select-dropdown')
              .contains(value)
              .click({ force: true })
          } else if (autoVal[key] === '启用个人费用标准') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other .ant-select')
              .should('be.visible')
              .click({ force: true })
            cy.get('.ant-select-dropdown')
              .last()
              .contains(value)
              .click({ force: true })
          } else if (autoVal[key] === '固定值') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other #ConditionalInput')
              .should('be.visible')
              .find('input')
              .clear({ force: true })
              .type(value, { force: true })
          } else if (autoVal[key] === '计算公式') {
            const value = autoVal[autoVal[key]]
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
            cy.get('@rightForm')
              .find('.radio-group-other')
              .as('radioOther')
              .should('be.visible')
            cy.get('@radioOther')
              .find('.content_obj .font')
              .then(el => {
                el.text(value)
              })
          } else {
            cy.get('@rightForm')
              .contains(key)
              .parents('.ant-form-item')
              .contains(autoVal[key])
              .click()
          }
          break
        case '数据互联的「联动赋值」 高于 「自动计算」':
          testCheckBox(key, autoVal[key])
          break
      }
    })
  }

  const testField = function(fieldVal, label) {
    cy.get('@simulator')
      .find('.simulator-item')
      .contains(label)
      .click()
    cy.wait(1000)
    Object.keys(fieldVal).forEach(function(key) {
      if (
        key === '这个是必填项' ||
        key === '需填写时间（小时、分钟）' ||
        key === '「报销总额」>申请单「申请金额」时有风险提示' ||
        key === '「报销金额」>申请单「申请余额」时有风险提示' ||
        key === '「报销金额」>申请单「申请余额」时禁止提交' ||
        key === '添加明细时，先选费用类型' ||
        key === '业务对象的「联动赋值」 高于 「手动填写」'
      ) {
        testCheckBox(key, fieldVal[key])
        return
      }

      switch (key) {
        case '字段名称':
          cy.get('@rightForm')
            .contains(key)
            .parents('.meta_name_item')
            .find('.meta_name_val')
            .should('have.text', fieldVal[key])
          break
        case '字段类型':
          cy.get('@rightForm')
            .contains(key)
            .parents('.meta_name_item')
            .find('.meta_name_val')
            .should('have.text', fieldVal[key])
          break
        case '文本类型':
          if (fieldVal[key] === '多行文本') {
            cy.get('@rightForm')
              .contains('多行文本')
              .click()
          }
          break
        case '显示名称':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(fieldVal[key])
          break
        case '提示文字':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(fieldVal[key])
          break
        case '展示方式':
          cy.get('@rightForm')
            .contains(key)
            .parents('.ant-form-item')
            .contains(fieldVal[key])
            .click()
          break
        case '手动填写':
          testManualVal(fieldVal[key], key)
          break
        case '系统计算':
          testAutoVal(fieldVal[key], key)
          break
        default:
          throw new Error('Did not find ' + key)
      }
    })
  }

  const testBasicSetting = function(basicInfo) {
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-content')
      .find('.ant-tabs-tabpane-active')
      .as('activePanelBasic')
    Object.keys(basicInfo).forEach(function(key, index) {
      switch (key) {
        case '单据模板名称':
          cy.get('@activePanelBasic')
            .contains('单据模板名称')
            .parents('.ant-form-item')
            .find('input')
            .clear()
            .type(basicInfo[key])
          break
        case '审批流程':
          cy.get('@activePanelBasic')
            .contains('审批流程')
            .parents('.ant-form-item')
            .find('.ant-select')
            .as('selectFlow')
            .click()
          cy.get('@selectFlow')
            .parent()
            .contains(basicInfo[key])
            .click()
          break
        case '打印模板':
          cy.get('@activePanelBasic')
            .contains('打印模板')
            .parents('.ant-form-item')
            .find('.ant-select')
            .as('selectPrint')
            .click()
          cy.get('@selectPrint')
            .parent()
            .contains(basicInfo[key])
            .click()
          break
      }
    })
  }

  const testDefaultField = function(defaultField) {
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting')
      .as('fieldSettingPanel')
      .should('be.visible')
    cy.get('@fieldSettingPanel')
      .find('#custom-simulator')
      .as('simulator')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    Object.keys(defaultField).forEach(function(key) {
      testField(defaultField[key], key)
      // switch (key) {
      //   case '标题':
      //     // testField(defaultField[key], key)
      //     break
      //   case '提交人':
      //     break
      //   case '报销日期':
      //     break
      //   case '报销部门':
      //     // testField(defaultField[key], key)
      //     break
      //   case '描述':
      //     // testField(defaultField[key], key)
      //     break
      //   case '报销总额':
      //     testField(defaultField[key], key)
      //     break
      //   case '关联申请':
      //       // testField(defaultField[key], key)
      //     break
      // }
    })
  }

  const testCustomField = function(customField) {
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting')
      .as('fieldSettingPanel')
      .should('be.visible')
    cy.get('@fieldSettingPanel')
      .find('#custom-simulator')
      .as('simulator')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')

    Object.keys(customField).forEach(function(key) {
      cy.get('@simulator')
        .find('.simulator-tool-bar .bar-item')
        .first()
        .click()
      cy.get('#custom-field-modal')
        .as('customFieldModal')
        .should('be.visible')
      cy.get('@customFieldModal')
        .find('.search input')
        .clear()
        .type(key)
      cy.get('@customFieldModal')
        .find('.content')
        .contains(key)
        .click()

      cy.get('@customFieldModal')
        .find('.modal-footer')
        .contains('确定')
        .click()
      testField(customField[key], key)
    })
  }

  const searchSelectExpense = function(expenseName) {
    cy.get('[data-cy="specificLeft-actionPart"] .ant-input')
      .clear()
      .type(expenseName)
    cy.get('[data-cy="specificLeft-listPart"] .highlight')
      .contains(expenseName)
      .click()

    cy.get('[data-cy="specificRight-titlePart"]')
      .contains(expenseName)
      .should('be.visible')
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .find('.ant-tabs-tab-active')
      .should('have.text', '基本设置')
  }

  beforeEach(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"customSpecification"}'
        )
        localStorage.setItem(`__userCache${session.user.corpId}`, '{"beforePage":"customSpecification"}')
        cy.visit('/web/app.html#/custom-specification')
      })
  })

  before(() => {
    cy.wait(1000)
  })

  afterEach(() => {
    cy.logout()
    cy.wait(1000)
  })

  it('创建通用审批单', function() {
    const basicInfo = T_newExpense['基本设置']
    cy.get('[data-cy="specificLeft-titlePart"] .ant-btn').click({ force: true })
    cy.get('.add-card')
      .contains('新建单据模板')
      .click()
    cy.get('#custom-addTemplateModal')
      .as('addTemplateModal')
      .should('be.visible')
    cy.get('@addTemplateModal')
      .find('#name')
      .click()
    cy.get('.ant-select-dropdown')
      .last()
      .contains(T_specificGroup)
      .click()
    cy.get('@addTemplateModal')
      .find('.template-list')
      .contains('通用审批单')
      .click()
    cy.get('@addTemplateModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()

    cy.get('[data-cy="specificRight-titlePart"]')
      .contains('添加基础单据模板')
      .should('be.visible')
    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .find('.ant-tabs-tab-active')
      .should('have.text', '基本设置')
    testBasicSetting(basicInfo)

    cy.get('[data-cy="specificRight-rightPart"] .ant-tabs-nav')
      .contains('字段设置')
      .click()
    cy.get('[data-cy="specificRight-rightPart"] #custom-fieldSetting')
      .as('fieldSettingPanel')
      .should('be.visible')
    cy.get('@fieldSettingPanel')
      .find('#custom-simulator')
      .as('simulator')
    cy.get('@fieldSettingPanel')
      .find('.right_form')
      .as('rightForm')
    cy.get('@simulator')
      .find('.simulator-item.simulator-default')
      .contains('提交人')
      .click()
    cy.get('@rightForm')
      .find('.meta_name_item')
      .first()
      .find('.meta_name_val')
      .should('have.text', '提交人')

    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('保 存')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('编辑通用审批单', function() {
    const basicInfo = T_editExpense['基本设置']
    const defaultField = T_editExpense['字段设置']['默认字段']

    const expenseName = T_newExpense['基本设置']['单据模板名称']

    searchSelectExpense(expenseName)

    testBasicSetting(basicInfo)

    testDefaultField(defaultField)
    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('保 存')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  // it('addCustomField', function() {
  //   const customField = T_editExpense['字段设置']['自定义字段']
  //   const basicInfo = T_editExpense['基本设置']
  //   const expenseName = basicInfo['单据模板名称']
  //   searchSelectExpense(expenseName)
  //   testCustomField(customField)
  //   cy.get('[data-cy="specificRight-newActionsPart"]')
  //     .contains('保 存')
  //     .click()
  //   cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  // })

  it('移动通用审批单', function() {
    const basicInfo = T_editExpense['基本设置']
    const expenseName = basicInfo['单据模板名称']
    searchSelectExpense(expenseName)
    cy.get('[data-cy="specificRight-titlePart"]')
      .contains('移动')
      .click()
    cy.get('.group-content')
      .as('groupContent')
      .should('be.visible')
    cy.get('@groupContent')
      .contains(T_moveGroup)
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })

  it('删除通用审批单', function() {
    const basicInfo = T_editExpense['基本设置']
    const expenseName = basicInfo['单据模板名称']
    searchSelectExpense(expenseName)
    cy.get('[data-cy="specificRight-newActionsPart"]')
      .contains('删 除')
      .click()
    cy.get('.del-confirm-modal')
      .as('delModal')
      .should('be.visible')
    cy.get('@delModal')
      .find('.content input')
      .clear()
      .type(expenseName)
    cy.get('@delModal')
      .find('.modal-footer')
      .contains('确 定')
      .click()
    cy.get('.ant-message-custom-content').should('have.class', 'ant-message-success')
  })
})
