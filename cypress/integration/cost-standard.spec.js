describe('cost-standard Test', function() {
  before(() => {
    cy.login()
      .initLanguage()
      .then(() => {
        const session = JSON.parse(localStorage.getItem('session'))
        localStorage.setItem(
          `__userCache${session.user.corpId}${session.user.userId}`,
          '{"beforePage":"customSpecification"}'
        )
        cy.visit('/app.html#/custom-specification')
      })
  })
  before(() => {
    cy.wait(2500)
  })
  it('createSpecification', function() {
    cy.get('[data-cy="specificLeft-titlePart"] >button').as('click-add-button')
    cy.get('@click-add-button').click()
    cy.contains('新建分组').click()
    cy.wait(1500)
    cy.get('[data-cy="add-specification-group"] input')
      .focus()
      .type('cy-group')
    cy.get('.group-footer button')
      .last()
      .click()

    cy.get('@click-add-button').click()
    cy.contains('新建单据模板').click()
    cy.wait(1000)
    cy.get('[data-cy="select-template-group"]').click()
    cy.wait(500)
    cy.get('.ant-select-dropdown li[label="cy-group"]').click()
    cy.get('.template-content>.template-list')
      .contains('报销单')
      .click()
    cy.get('.modal-footer .ant-btn-primary').click()
  })

  it('editTemplate', function() {
    cy.get('.ant-tabs-nav')
      .contains('基本设置')
      .click()
    cy.contains('基本设置').should('have.class', 'ant-tabs-tab-active')
  })
})
