const request = require('request')
const UUID = require('./uuid')
const fs = require('fs')

fs.readFile('./cypress.json', (err, data) => {
  if (!err) {
    if (data) {
      data = JSON.parse(data)
      const { server_url, phone, password, cypresskey } = data.env
      const requestData = {
        fullPhone: `86-${phone}`,
        password,
        isShortTermToken: false,
        deviceId: UUID(),
        deviceType: 'DESKTOP'
      }
      request(
        {
          method: 'POST',
          url: `${server_url}/api/account/v2/session/loginByOne`,
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify(requestData)
        },
        (err, res, body) => {
          if (!err) {
            const { token, userId } = JSON.parse(body)
            fs.writeFileSync('./cypress/ci-script/testFile', `${token}:${userId}`)
            fs.writeFileSync('./cypress/ci-script/testUrl', server_url)
            fs.writeFileSync('./cypress/ci-script/testCypressKey', cypresskey)
          }
        }
      )
    }
  }
})
