import { get } from 'lodash'
import { initLangData } from '../utils/utils'
import I18n from '../utils/i18n'
// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- Login --
Cypress.Commands.add('login', () => {
  cy.log('check token')
  const TOKEN = Cypress.env('TOKEN')
  console.log(TOKEN,'TOKEN')
  if (TOKEN) {
    const aStr = Cypress.env('TOKEN').split(':')
    cy.accessToken = aStr[0]
    cy.userId = aStr[1]
    cy.setCookie('ekb-access-token', aStr[0], {
      path: '/'
    })
    chooseCorporation()
  } else {
    cy.request('POST', '/api/account/v2/session/loginByOne', {
      fullPhone: `86-${Cypress.env('phone')}`,
      password: Cypress.env('password'),
      deviceId: Math.random()
        .toString()
        .substring(2),
      deviceType: 'DESKTOP',
      isShortTermToken: false
    }).then(response => {
      // expect(response.status).to.equal(200)
      expect(response.body.error).to.be.false
      cy.userId = response.body.userId
      cy.accessToken = response.body.token
      chooseCorporation()
    })
  }
  // cy.log('Login')
})

Cypress.Commands.add('logout', () => {
  const session = JSON.parse(localStorage.getItem('session'))
  if (session && session.user.corpId) {
    cy.request('DELETE', '/api/account/v2/session', {
      corpId: session.user.corpId
    }).then(response => {
      // expect(response.status).to.equal(204)
      localStorage.removeItem('session')
    })
  }
})

const chooseCorporation = () => {
  const makeStr = Cypress.env('corpId')
  if (makeStr) {
    cy.request('GET', '/api/v1/organization/corporations').then(response => {
      expect(response.status).to.equal(200)
      const aItems = response.body.items
      assert.isArray(aItems)
      if (aItems.length) {
        const oCor = aItems.filter(item => {
          if (item.name === makeStr || item.id === makeStr) {
            return true
          }
        })
        if (oCor && oCor.length) {
          const session = {
            user: {
              accessToken: cy.accessToken,
              corpId: oCor[0].id,
              userId: cy.userId
            }
          }
          localStorage.setItem('session', JSON.stringify(session))
        }
      }
    })
  }
}

// -- ChooseCorporation --
// Cypress.Commands.add('chooseCorporation', makeStr => {
//   cy.log('Choose Corporation')
//   cy.request('GET', '/api/v1/organization/corporations').then(response => {
//     expect(response.status).to.equal(200)
//     const aItems = response.body.items
//     assert.isArray(aItems)
//     if (aItems.length) {
//       const oCor = aItems.filter(item => {
//         if (item.name === makeStr || item.id === makeStr) {
//           return true
//         }
//       })
//       if (oCor && oCor.length) {
//         const session = {
//           user: {
//             accessToken: cy.accessToken,
//             corpId: oCor[0].id,
//             userId: cy.userId
//           }
//         }
//         localStorage.setItem('session', JSON.stringify(session))
//       }
//     }
//   })
// })

const getLanguage = (corpId, language) => {
  if (language) {
    return cy.request('GET', `./web/locales/${language}.json?corpId=${corpId}`)
  } else {
    return cy.request('GET', `./web/locales/zh-CN.json?corpId=${corpId}`)
  }
}

// -- InitLanguage --
Cypress.Commands.add('initLanguage', () => {
  const session = JSON.parse(localStorage.getItem('session'))
  const corpId = session.user.corpId
  cy.log('Get Language')
  cy.request('GET', `/api/v1/organization/staffs/staffSetting/$DESKTOP?corpId=${corpId}`).then(response => {
    expect(response.status).to.equal(200)
    const language = get(response, 'body.value.language')
    localStorage.setItem('lang', language)
    getLanguage(corpId, language).then(response => {
      expect(response.status).to.equal(200)
      const data = response.body
      window.i18n = new I18n(initLangData(data))
    })
  })
})
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })
