interface FMPMonitorParams {
  title: string,
  [key: string]: any
}

export interface FMPMonitorOptions {
  isFMPNode: (n: Element) => boolean,
  // 自定义埋点参数
  params: FMPMonitorParams | (() => FMPMonitorParams),
  // 路由匹配规则 - 只做路由层面的判断
  rule: (hash?: string) => boolean,
  // 页面状态校验 - 在监控过程中持续校验页面状态
  validate?: () => boolean
}

const existBillCard = () => {
  return !!(
    document.querySelector('#card-my-bills div[data-testid="home-card-empty"]') ||
    document.querySelector('#card-my-bills div[data-testid="home-bill-list-item"]')
  )
}

const existMenu = () => {
  return !!(
    document.querySelector('#layout5-menu-wrap-new .layout5-menu-item') ||
    document.querySelector('#layout5-menu-wrap-new .layout5-sub-menu-item')
  )
}

const isMenu = (node: Element) => node.classList.contains('layout5-menu-item') || node.classList.contains('layout5-sub-menu-item')
const isBillCard = (node: Element) => ['home-bill-list-item', 'home-card-empty'].includes((node as HTMLElement)?.dataset.testid)

export const FMP_MONITOR_CONFIG_MAP: Record<string, FMPMonitorOptions> = {
  home: {
    isFMPNode: node => {
      return (isMenu(node) && existBillCard()) || (isBillCard(node) && existMenu())
    },
    params: () => ({
      title: '首页FMP'
    }),
    // 只做路由匹配，不做页面元素校验
    rule: (hash: string) => ['/new-homepage', '/'].includes(hash),

    // 页面状态校验 - 在监控过程中校验
    validate: () => {
      const homeElements = {
        menu: document.querySelector('#layout5-menu-wrap-new'),
        billCard: document.querySelector('#card-my-bills')
      }

      const isValid = !!(homeElements.menu && homeElements.billCard)

      if (!isValid) {
        console.warn('首页关键元素不存在，停止FMP监控')
      }

      return isValid
    }
  }
}