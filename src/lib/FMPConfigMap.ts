interface FMPMonitorParams {
  title: string,
  [key: string]: any
}

export interface FMPMonitorOptions {
  isFMPNode: (n: Element) => boolean,
  // 自定义埋点参数
  params: FMPMonitorParams | (() => FMPMonitorParams),
  rule: (hash?: string) => boolean
}

const existBillCard = () => {
  return !!(
    document.querySelector('#card-my-bills div[data-testid="home-card-empty"]') ||
    document.querySelector('#card-my-bills div[data-testid="home-bill-list-item"]')
  )
}

const existMenu = () => {
  return !!(
    document.querySelector('#layout5-menu-wrap-new .layout5-menu-item') ||
    document.querySelector('#layout5-menu-wrap-new .layout5-sub-menu-item')
  )
}

const isMenu = node => node.classList.contains('layout5-menu-item') || node.classList.contains('layout5-sub-menu-item')
const isBillCard = node => ['home-bill-list-item', 'home-card-empty'].includes(node?.dataset.testid)

export const FMP_MONITOR_CONFIG_MAP: Record<string, FMPMonitorOptions> = {
  home: {
    isFMPNode: node => {
      return (isMenu(node) && existBillCard()) || (isBillCard(node) && existMenu())
    },
    params: () => ({
      title: '首页FMP'
    }),
    rule: (hash: string) => ['/new-homepage', '/'].includes(hash)
  }
}