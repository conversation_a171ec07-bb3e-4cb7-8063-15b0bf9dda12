import { FMPMonitorOptions, FMP_MONITOR_CONFIG_MAP } from './FMPConfigMap'
import { addRumAction } from '../hosting/dataflux-rum'


export const getTrueKey = (key: string = '') => {
  const arr = key.split('_')
  if (arr.length === 3) {
    return arr[2]
  } else if (arr.length > 3) {
    return arr.splice(2).join('_')
  } else {
    return key
  }
}

export const fixEntityObj = (it: any) => {
  if (!it) return it
  const obj: any = {}
  Object.keys(it).forEach(key => {
    const tKey = getTrueKey(key)
    obj[tKey] = it[key]
  })
  return obj
}

// 首页FMP监控状态
let homeFMPState = {
  isActive: false,
  hasCollected: false, // 是否已经采集过首页FMP数据
  observer: null as MutationObserver | null,
  timeoutId: null as NodeJS.Timeout | null
}

// 清理首页FMP监控
function cleanupHomeFMP() {
  if (homeFMPState.observer) {
    homeFMPState.observer.disconnect()
    homeFMPState.observer = null
  }

  if (homeFMPState.timeoutId) {
    clearTimeout(homeFMPState.timeoutId)
    homeFMPState.timeoutId = null
  }

  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('pagehide', handleVisibilityChange)
  window.removeEventListener('hashchange', handleRouteChange)

  homeFMPState.isActive = false
  console.log('首页FMP监控已清理')
}

// 页面可见性变化处理
function handleVisibilityChange() {
  if (document.visibilityState === 'hidden') {
    cleanupHomeFMP()
  }
}

// 路由变化处理 - 如果用户快速跳转离开首页，立即停止监控
function handleRouteChange() {
  const currentHash = location.hash.slice(1)
  const isHomePage = ['/new-homepage', '/'].includes(currentHash)

  if (!isHomePage && homeFMPState.isActive) {
    console.log('用户离开首页，停止FMP监控')
    cleanupHomeFMP()
  }
}

// 验证当前是否真的在首页
function validateIsHomePage(): boolean {
  const currentHash = location.hash.slice(1)

  // 1. 路由检查
  if (!['/new-homepage', '/'].includes(currentHash)) {
    return false
  }

  // 2. 页面元素检查 - 确保首页关键元素存在
  const homeElements = {
    menu: document.querySelector('#layout5-menu-wrap-new'),
    billCard: document.querySelector('#card-my-bills')
  }

  if (!homeElements.menu || !homeElements.billCard) {
    console.warn('路由显示为首页，但缺少首页关键元素')
    return false
  }

  return true
}

const monitorFMP = (options: Omit<FMPMonitorOptions, 'rule'>) => {
  if (!('MutationObserver' in window)) {
    return
  }

  // 如果已经采集过首页FMP数据，不再重复监控
  if (homeFMPState.hasCollected) {
    console.log('首页FMP数据已采集，跳过重复监控')
    return
  }

  // 如果已有监控在运行，先清理
  if (homeFMPState.isActive) {
    cleanupHomeFMP()
  }

  const { isFMPNode, params } = options

  try {
    if(document.visibilityState === 'hidden') return

    // 添加路由变化监听
    window.addEventListener('hashchange', handleRouteChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handleVisibilityChange)

    homeFMPState.observer = new MutationObserver((mutations) => {
      // 在每次检测时再次验证是否在首页
      if (!validateIsHomePage()) {
        console.log('检测到不在首页，停止FMP监控')
        cleanupHomeFMP()
        return
      }

      for (const mutation of mutations) {
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue

          if (isFMPNode(node as Element)) {
            const args = typeof params === 'function' ? params() : params
            addRumAction('first_meaning_paint', {
              ...args,
              loadedDuration: Math.floor(performance.now()),
              isFirstTimeLoad: !homeFMPState.hasCollected
            })
            console.log(args.title, 'FMP - 首次加载完成')

            // 标记已采集，防止重复采集
            homeFMPState.hasCollected = true
            cleanupHomeFMP()
            return
          }
        }
      }
    })

    homeFMPState.observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // 设置超时断开连接
    homeFMPState.timeoutId = setTimeout(() => {
      console.log('首页FMP监控超时')
      cleanupHomeFMP()
    }, 60 * 1000)

    homeFMPState.isActive = true
    console.log('首页FMP监控已启动')

  } catch (error) {
    console.error('首页FMP监控启动失败:', error)
  }
}

export const trackFMP = async (hash: string) => {
  if(!hash) return

  if(typeof hash === 'string') {
    hash = hash.replace('#', '')
  }

  const config = Object.values(FMP_MONITOR_CONFIG_MAP).find(config => config.rule(hash))

  if (config) {
    // 只处理首页FMP监控
    if (['/new-homepage', '/'].includes(hash)) {
      // 延迟验证，确保页面开始渲染
      setTimeout(async () => {
        const isValidHomePage = validateIsHomePage()

        if (isValidHomePage) {
          console.log('验证通过，开始首页FMP监控')
          monitorFMP({ isFMPNode: config.isFMPNode, params: config.params })
        } else {
          console.log('首页验证失败，跳过FMP监控')
        }
      }, 500) // 500ms延迟，确保页面基本稳定
    }
  }
}

// 导出工具函数供调试使用
export const HomeFMPUtils = {
  // 获取当前状态
  getState: () => ({
    isActive: homeFMPState.isActive,
    hasCollected: homeFMPState.hasCollected
  }),

  // 重置状态（用于测试或特殊情况）
  reset: () => {
    cleanupHomeFMP()
    homeFMPState.hasCollected = false
    console.log('首页FMP状态已重置')
  },

  // 手动清理
  cleanup: cleanupHomeFMP
}