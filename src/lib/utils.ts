import { FMPMonitorOptions, FMP_MONITOR_CONFIG_MAP } from './FMPConfigMap'
import { addRumAction } from '../hosting/dataflux-rum'


export const getTrueKey = (key: string = '') => {
  const arr = key.split('_')
  if (arr.length === 3) {
    return arr[2]
  } else if (arr.length > 3) {
    return arr.splice(2).join('_')
  } else {
    return key
  }
}

export const fixEntityObj = (it: any) => {
  if (!it) return it
  const obj: any = {}
  Object.keys(it).forEach(key => {
    const tKey = getTrueKey(key)
    obj[tKey] = it[key]
  })
  return obj
}

// FMP监控状态管理 - 通用的，不限于首页
let fmpMonitorState = {
  isActive: false,
  hasCollected: false, // 是否已经采集过FMP数据
  observer: null as MutationObserver | null,
  timeoutId: null as NodeJS.Timeout | null,
  currentPage: '' // 当前监控的页面
}

// 清理FMP监控
function cleanupFMP() {
  if (fmpMonitorState.observer) {
    fmpMonitorState.observer.disconnect()
    fmpMonitorState.observer = null
  }

  if (fmpMonitorState.timeoutId) {
    clearTimeout(fmpMonitorState.timeoutId)
    fmpMonitorState.timeoutId = null
  }

  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('pagehide', handleVisibilityChange)
  window.removeEventListener('hashchange', handleRouteChange)

  fmpMonitorState.isActive = false
  console.log('FMP监控已清理:', fmpMonitorState.currentPage)
}

// 页面可见性变化处理
function handleVisibilityChange() {
  if (document.visibilityState === 'hidden') {
    cleanupFMP()
  }
}

// 路由变化处理 - 如果用户快速跳转，立即停止监控
function handleRouteChange() {
  if (fmpMonitorState.isActive) {
    console.log('路由变化，停止FMP监控')
    cleanupFMP()
  }
}

const monitorFMP = (options: Omit<FMPMonitorOptions, 'rule'>, pageHash: string) => {
  if (!('MutationObserver' in window)) {
    return
  }

  // 如果已经采集过FMP数据，不再重复监控
  if (fmpMonitorState.hasCollected) {
    console.log('FMP数据已采集，跳过重复监控')
    return
  }

  // 如果已有监控在运行，先清理
  if (fmpMonitorState.isActive) {
    cleanupFMP()
  }

  const { isFMPNode, params } = options

  try {
    if(document.visibilityState === 'hidden') return

    // 记录当前监控的页面
    fmpMonitorState.currentPage = pageHash

    // 添加路由变化监听
    window.addEventListener('hashchange', handleRouteChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handleVisibilityChange)

    fmpMonitorState.observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue

          if (isFMPNode(node as Element)) {
            const args = typeof params === 'function' ? params() : params
            addRumAction('first_meaning_paint', {
              ...args,
              loadedDuration: Math.floor(performance.now()),
              isFirstTimeLoad: !fmpMonitorState.hasCollected,
              pageHash
            })
            console.log(args.title, 'FMP - 首次加载完成')

            // 标记已采集，防止重复采集
            fmpMonitorState.hasCollected = true
            cleanupFMP()
            return
          }
        }
      }
    })

    fmpMonitorState.observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // 设置超时断开连接
    fmpMonitorState.timeoutId = setTimeout(() => {
      console.log('FMP监控超时:', pageHash)
      cleanupFMP()
    }, 60 * 1000)

    fmpMonitorState.isActive = true
    console.log('FMP监控已启动:', pageHash)

  } catch (error) {
    console.error('FMP监控启动失败:', error)
  }
}

export const trackFMP = async (hash: string) => {
  if(!hash) return

  if(typeof hash === 'string') {
    hash = hash.replace('#', '')
  }

  const config = Object.values(FMP_MONITOR_CONFIG_MAP).find(config => config.rule(hash))

  if (config) {
    // 延迟启动，确保页面开始渲染
    setTimeout(() => {
      monitorFMP({ isFMPNode: config.isFMPNode, params: config.params })
    }, 500) // 500ms延迟，确保页面基本稳定
  }
}

// 导出工具函数供调试使用
export const HomeFMPUtils = {
  // 获取当前状态
  getState: () => ({
    isActive: homeFMPState.isActive,
    hasCollected: homeFMPState.hasCollected
  }),

  // 重置状态（用于测试或特殊情况）
  reset: () => {
    cleanupHomeFMP()
    homeFMPState.hasCollected = false
    console.log('首页FMP状态已重置')
  },

  // 手动清理
  cleanup: cleanupHomeFMP
}