import React, { PureComponent } from 'react'
import { TreeSelect as AntdTreeSelect } from 'antd'
import { TreeSelect as EUITreeSelect } from '@hose/eui'

export default class EKBTreeSelect extends PureComponent {
  handleOnChange = (value, label, extra) => {
    const { onChange, multiple, value: propsValue, treeCheckStrictly } = this.props
    if (multiple) {
      let newValue = value || []
      if (extra && extra.checked === true) {
        if (treeCheckStrictly) {
          //解决搜索选中的时候清空之前已选择项的问题
          newValue = propsValue.concat({ value: extra.triggerValue })
        } else {
          newValue = propsValue.concat(extra.triggerValue)
        }
      }
      onChange && onChange(newValue)
      return
    }
    onChange && onChange(value)
  }

  renderTreeNode(data, onlyLeafCanBeSelected = false, canSelectInactive = false) {
    if (!data.length) {
      return []
    }
    const { canNotBeApportioned, canNotBeApportionedFields, useEUI } = this.props
    const TreeNode = useEUI? EUITreeSelect.TreeNode : AntdTreeSelect.TreeNode
    const loop = arr =>
      arr.length > 0 &&
      arr.map(child => {
        const { id, name, enName, active, children = [], label, code, deleted } = child
        const labelName = i18n.currentLocale === 'en-US' && enName ? enName : name
        let nameStr = label || (code ? i18n.get(`{__k0}({__k1})`, { __k0: labelName, __k1: code }) : labelName)
        const text = deleted
          ? i18n.get(`{__k0}（已删除）`, { __k0: nameStr })
          : i18n.get(`{__k0}（已停用）`, { __k0: nameStr })
        nameStr = active ? nameStr : text
        let __active = active
        if (onlyLeafCanBeSelected && children.length) {
          __active = false
        }
        if(canNotBeApportioned && canNotBeApportionedFields?.includes(id)){
          __active = false
        }
        return (
          <TreeNode key={id} name={nameStr} value={id} title={nameStr} disabled={canSelectInactive ? false : !__active}>
            {children && loop(children)}
          </TreeNode>
        )
      })
    return loop(data)
  }
  render() {
    const {
      value,
      treeData,
      placeholder,
      isShowParent,
      refKey,
      className,
      treeCheckStrictly,
      getNode,
      onlyLeafCanBeSelected,
      canSelectInactive,
      useEUI,
      showCheckedStrategy,
      ...others
    } = this.props
    let config = { value }
    const TreeSelect = useEUI? EUITreeSelect : AntdTreeSelect
    const SHOW_PARENT = TreeSelect.SHOW_PARENT
    const SHOW_CHILD = TreeSelect.SHOW_CHILD
    const showCheckedStrategyType = showCheckedStrategy || (isShowParent ? SHOW_PARENT : SHOW_CHILD)
    return (
      <div id={refKey}>
        <TreeSelect
          ref={node => getNode && getNode(node)}
          className={className}
          searchPlaceholder={placeholder}
          placeholder={placeholder}
          showCheckedStrategy={showCheckedStrategyType}
          treeCheckStrictly={treeCheckStrictly}
          {...others}
          {...config}
          onChange={this.handleOnChange}
          getPopupContainer={!refKey ? () => document.body : () => document.getElementById(refKey)}
        >
          {this.renderTreeNode(treeData, onlyLeafCanBeSelected, canSelectInactive)}
        </TreeSelect>
      </div>
    )
  }
}
