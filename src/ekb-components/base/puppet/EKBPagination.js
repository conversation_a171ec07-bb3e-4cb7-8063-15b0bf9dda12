import React, { PureComponent } from 'react'
import { Pagination } from 'antd'

export default class EKBPagination extends PureComponent {
  onChange = page => {
    let { onChange } = this.props
    onChange && onChange(page)
  }

  render() {
    const { current, pageSize = 15, total } = this.props
    return (
      <div className="pagination-wrapper">
        <Pagination simple current={current} pageSize={pageSize} total={total} onChange={this.onChange} />
      </div>
    )
  }
}
