.steps-header {
  flex-shrink: 0;
  :global {
    .ant-steps {
      font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
        Helvetica Neue, Helvetica, Arial, sans-serif;
      font-size: 12px;
      line-height: 1.5;
      color: #333;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      list-style: none;
      width: 100%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    .ant-steps-item {
      position: relative;
      display: flex;
      align-items: center;
      vertical-align: top;
      flex: 1;
      overflow: hidden;
    }

    .ant-steps-item:last-child {
      -webkit-box-flex: 0;
      -webkit-flex: none;
      -ms-flex: none;
      flex: none;
    }

    .ant-steps-item:last-child .ant-steps-item-tail,
    .ant-steps-item:last-child .ant-steps-item-title:after {
      display: none;
    }

    .ant-steps-item-content,
    .ant-steps-item-icon {
      display: inline-block;
      vertical-align: top;
    }

    .ant-steps-item-icon {
      border: 1px solid #9c9c9c;
      width: 22px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      border-radius: 22px;
      font-size: 13px;
      margin-right: 8px;
      -webkit-transition: background-color 0.3s, border-color 0.3s;
      transition: background-color 0.3s, border-color 0.3s;
      font-family: Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB,
        Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
    }

    .ant-steps-item-icon > .ant-steps-icon {
      line-height: 1;
      top: -1px;
      color: var(--brand-base);
      position: relative;
    }

    .ant-steps-item-tail {
      position: absolute;
      left: 0;
      width: 100%;
      top: 12px;
      padding: 0 10px;
    }

    .ant-steps-item-tail:after {
      content: '';
      display: inline-block;
      background: #e6e6e6;
      height: 1px;
      border-radius: 1px;
      width: 100%;
      -webkit-transition: background 0.3s;
      transition: background 0.3s;
    }

    .ant-steps-item-title {
      font-size: 13px;
      color: #262626;
      display: inline-block;
      padding-right: 16px;
      position: relative;
      line-height: 32px;
    }

    .ant-steps-item-title:after {
      content: '';
      height: 1px;
      width: 9999px;
      background: #e6e6e6;
      display: block;
      position: absolute;
      top: 16px;
      left: 100%;
    }

    .ant-steps-item-description {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.43);
    }

    .ant-steps-item-wait .ant-steps-item-icon {
      border-color: #9c9c9c;
      background-color: #fff;
    }

    .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
      color: #9c9c9c;
    }

    .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
      background: #9c9c9c;
    }

    .ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-title {
      color: rgba(0, 0, 0, 0.43);
    }

    .ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-title:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-description {
      color: rgba(0, 0, 0, 0.43);
    }

    .ant-steps-item-wait > .ant-steps-item-tail:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item-process .ant-steps-item-icon {
      border-color: var(--brand-base);
      background-color: #fff;
    }

    .ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
      color: var(--brand-base);
    }

    .ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
      background: var(--brand-base);
    }

    .ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-title {
      color: #545454;
    }

    .ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-title:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-description {
      color: #333;
    }

    .ant-steps-item-process > .ant-steps-item-tail:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item-process .ant-steps-item-icon {
      background: var(--brand-base);
    }

    .ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
      color: #fff;
    }

    .ant-steps-item-process .ant-steps-item-title {
      font-weight: 500;
    }

    .ant-steps-item-finish .ant-steps-item-icon {
      border-color: var(--brand-base);
      background-color: #fff;
    }

    .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
      color: var(--brand-base);
    }

    .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
      background: var(--brand-base);
    }

    .ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-title {
      color: #333;
    }

    .ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-title:after {
      background-color: var(--brand-base);
    }

    .ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-description {
      color: rgba(0, 0, 0, 0.43);
    }

    .ant-steps-item-finish > .ant-steps-item-tail:after {
      background-color: var(--brand-base);
    }

    .ant-steps-item-error .ant-steps-item-icon {
      border-color: #ff7c7c;
      background-color: #fff;
    }

    .ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon {
      color: #ff7c7c;
    }

    .ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
      background: #ff7c7c;
    }

    .ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-title {
      color: #ff7c7c;
    }

    .ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-title:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-description {
      color: #ff7c7c;
    }

    .ant-steps-item-error > .ant-steps-item-tail:after {
      background-color: #e6e6e6;
    }

    .ant-steps-item.ant-steps-next-error .ant-steps-item-title:after {
      background: #ff7c7c;
    }

    .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item {
      margin-right: 16px;
      white-space: nowrap;
    }

    .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item:last-child {
      margin-right: 0;
    }

    .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item:last-child .ant-steps-item-title {
      padding-right: 0;
    }

    .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item-tail {
      display: none;
    }

    .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item-description {
      max-width: 140px;
    }

    .ant-steps-item-custom .ant-steps-item-icon {
      background: none;
      border: 0;
      width: auto;
      height: auto;
    }

    .ant-steps-item-custom .ant-steps-item-icon > .ant-steps-icon {
      font-size: 24px;
      line-height: 32px;
      top: 0;
      left: 0.5px;
      width: 32px;
      height: 32px;
    }

    .ant-steps-item-custom.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
      color: var(--brand-base);
    }

    .ant-steps-small.ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item {
      margin-right: 12px;
    }

    .ant-steps-small.ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item:last-child {
      margin-right: 0;
    }

    .ant-steps-small .ant-steps-item-icon {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 24px;
      font-size: 12px;
    }

    .ant-steps-small .ant-steps-item-title {
      font-size: 12px;
      line-height: 24px;
      padding-right: 12px;
    }

    .ant-steps-small .ant-steps-item-title:after {
      top: 12px;
    }

    .ant-steps-small .ant-steps-item-description {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.43);
    }

    .ant-steps-small .ant-steps-item-tail {
      top: 8px;
      padding: 0 8px;
    }

    .ant-steps-small .ant-steps-item-custom .ant-steps-item-icon {
      width: inherit;
      height: inherit;
      line-height: inherit;
      border-radius: 0;
      border: 0;
      background: none;
    }

    .ant-steps-small .ant-steps-item-custom .ant-steps-item-icon > .ant-steps-icon {
      font-size: 24px;
      line-height: 24px;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }

    .ant-steps-vertical {
      display: block;
    }

    .ant-steps-vertical .ant-steps-item {
      display: block;
      overflow: visible;
    }

    .ant-steps-vertical .ant-steps-item-icon {
      float: left;
      margin-right: 16px;
    }

    .ant-steps-vertical .ant-steps-item-content {
      min-height: 48px;
      overflow: hidden;
      display: block;
    }

    .ant-steps-vertical .ant-steps-item-title {
      line-height: 32px;
    }

    .ant-steps-vertical .ant-steps-item-description {
      padding-bottom: 12px;
    }

    .ant-steps-vertical > .ant-steps-item > .ant-steps-item-tail {
      position: absolute;
      left: 16px;
      top: 0;
      height: 100%;
      width: 1px;
      padding: 38px 0 6px;
    }

    .ant-steps-vertical > .ant-steps-item > .ant-steps-item-tail:after {
      height: 100%;
      width: 1px;
    }

    .ant-steps-vertical > .ant-steps-item:not(:last-child) > .ant-steps-item-tail {
      display: block;
    }

    .ant-steps-vertical > .ant-steps-item > .ant-steps-item-content > .ant-steps-item-title:after {
      display: none;
    }

    .ant-steps-vertical.ant-steps-small .ant-steps-item-tail {
      position: absolute;
      left: 12px;
      top: 0;
      padding: 30px 0 6px;
    }

    .ant-steps-vertical.ant-steps-small .ant-steps-item-title {
      line-height: 24px;
    }

    @media (max-width: 480px) {
      .ant-steps-horizontal.ant-steps-label-horizontal {
        display: block;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal .ant-steps-item {
        display: block;
        overflow: visible;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal .ant-steps-item-icon {
        float: left;
        margin-right: 16px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal .ant-steps-item-content {
        min-height: 48px;
        overflow: hidden;
        display: block;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal .ant-steps-item-title {
        line-height: 32px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal .ant-steps-item-description {
        padding-bottom: 12px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal > .ant-steps-item > .ant-steps-item-tail {
        position: absolute;
        left: 16px;
        top: 0;
        height: 100%;
        width: 1px;
        padding: 38px 0 6px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal > .ant-steps-item > .ant-steps-item-tail:after {
        height: 100%;
        width: 1px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal > .ant-steps-item:not(:last-child) > .ant-steps-item-tail {
        display: block;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal
        > .ant-steps-item
        > .ant-steps-item-content
        > .ant-steps-item-title:after {
        display: none;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal.ant-steps-small .ant-steps-item-tail {
        position: absolute;
        left: 12px;
        top: 0;
        padding: 30px 0 6px;
      }

      .ant-steps-horizontal.ant-steps-label-horizontal.ant-steps-small .ant-steps-item-title {
        line-height: 24px;
      }
    }

    .ant-steps-label-vertical .ant-steps-item {
      overflow: visible;
    }

    .ant-steps-label-vertical .ant-steps-item-tail {
      padding: 0 24px;
      margin-left: 48px;
    }

    .ant-steps-label-vertical .ant-steps-item-content {
      display: block;
      text-align: center;
      margin-top: 8px;
      width: 140px;
    }

    .ant-steps-label-vertical .ant-steps-item-icon {
      display: inline-block;
      margin-left: 36px;
    }

    .ant-steps-label-vertical .ant-steps-item-title {
      padding-right: 0;
    }

    .ant-steps-label-vertical .ant-steps-item-title:after {
      display: none;
    }

    .ant-steps-label-vertical .ant-steps-item-description {
      text-align: left;
    }

    .ant-steps-dot .ant-steps-item-title {
      line-height: 1.5;
    }

    .ant-steps-dot .ant-steps-item-tail {
      width: 100%;
      top: 2px;
      margin: 0 0 0 70px;
      padding: 0;
    }

    .ant-steps-dot .ant-steps-item-tail:after {
      height: 3px;
      width: calc(100% - 20px);
      margin-left: 12px;
    }

    .ant-steps-dot .ant-steps-item:first-child .ant-steps-icon-dot {
      left: 2px;
    }

    .ant-steps-dot .ant-steps-item-icon {
      padding-right: 0;
      width: 8px;
      height: 8px;
      line-height: 8px;
      border: 0;
      margin-left: 67px;
      background: transparent;
    }

    .ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot {
      float: left;
      width: 100%;
      height: 100%;
      border-radius: 100px;
      position: relative;
      -webkit-transition: all 0.3s;
      transition: all 0.3s;
    }

    .ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot:after {
      content: '';
      background: rgba(0, 0, 0, 0.001);
      width: 60px;
      height: 32px;
      position: absolute;
      top: -12px;
      left: -26px;
    }

    .ant-steps-dot .ant-steps-item-process .ant-steps-item-icon {
      width: 10px;
      height: 10px;
      line-height: 10px;
    }

    .ant-steps-dot .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon-dot {
      top: -1px;
    }

    .ant-steps-vertical.ant-steps-dot .ant-steps-item-icon {
      margin-left: 0;
      margin-top: 8px;
    }

    .ant-steps-vertical.ant-steps-dot .ant-steps-item-tail {
      margin: 0;
      left: -9px;
      top: 2px;
      padding: 22px 0 4px;
    }

    .ant-steps-vertical.ant-steps-dot .ant-steps-item:first-child .ant-steps-icon-dot {
      left: 0;
    }

    .ant-steps-vertical.ant-steps-dot .ant-steps-item-process .ant-steps-icon-dot {
      left: -2px;
    }
  }
}
