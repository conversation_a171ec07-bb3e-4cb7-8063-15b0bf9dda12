@import '~@ekuaibao/web-theme-variables/styles/default';

.upload_wrapper {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 4px;
  width: 88px;
  height: 88px;
}

.imageitem-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }
  :global {
    .mark {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      background-color: #000;
      opacity: 0.3;
      border-radius: 5px;
    }
    .progress {
      color: #fff;
      position: absolute;
      top: 10px;
      bottom: 10px;
      right: 10px;
      left: 10px;
    }
  }
}

.done:hover {
  border: 1px solid @primary-6;
}

.success:hover {
  border: solid 1px @primary-6;
  box-shadow: 0 1px 16px 0 var(--brand-fadeout-10);
}

.fail {
  border: solid 1px #f5222d;
}

.fail:hover {
  box-shadow: 0 1px 16px 0 rgba(245, 34, 45, 0.12);
}
