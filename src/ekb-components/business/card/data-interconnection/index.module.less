@import '~@ekuaibao/web-theme-variables/styles/default';

.cardWrapper {
  width: 100%;
  border-radius: 5px;
  :global {
    .card-content {
      padding: 3px;
      display: flex;
      justify-content: space-between;
      .left {
        display: flex;
        .image {
          width: 44px;
          height: 44px;
          margin-right: 16px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 6px;
          }
        }
        .name {
          font-size: 14px;
          color: #262626;
        }
        .disableName {
          color: #8c8c8c;
        }
        .id {
          margin-top: 4px;
          font-size: 14px;
          color: #8c8c8c;
        }
      }
      .right {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        .edit-btn {
          margin-right: 16px;
          font-size: 14px;
          color: @primary-6;
          cursor: pointer;
        }
        span {
          color: @gray-1;
        }
      }
    }
  }
}

.disable {
  background-color: #fafafa;
}
