@import '~@ekuaibao/web-theme-variables/styles/default';

.data-tree-card {
  width: 100%;
  position: relative;
  padding-left: 22px;
  :global {
    .t-icon {
      position: absolute;
      left: 4px;
      top: 0px;
      bottom:0px;
      img {
        cursor: pointer;
      }
    }
    .t-icon:before {
      content: '';
      position: absolute;
      left: 5px;
      top: 27px;
      bottom: -8px;
      width: 1px;
      background: rgba(29, 43, 61, 0.15);
    }
    .t-icon-no:before{
        content: '';
        display: none;
    }
    .card-content {
      padding: 6px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      .left {
        .title {
          font-size: 14px;
          color: #1d2b3d;
          font-weight: 400;
          line-height: 22px;
        }
        .disableName {
          color: #8c8c8c;
        }
        .id {
          margin-top: 2px;
          font-size: 14px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.5);
          line-height: 22px;
        }
      }
      .right {
        .edit-btn {
          margin-right: 16px;
          font-size: 14px;
          color: @primary-6;
          cursor: pointer;
          display: none;
          line-height: 26px;
        }
        span {
          color: @gray-1;
        }
      }
    }
    .card-content:hover {
      background: rgba(29, 43, 61, 0.06);
      .edit-btn {
        display: inline-block;
      }
    }
  }
}

.disable {
  background-color: #fafafa;
}
.data-tree-box {
  padding-left: 26px;
  position: relative;
  > img {
    left: 8px;
    position: absolute;
    top: 10px;
  }
}
