/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/05/16
 * */
.ekb-components-tags {

  div,
  a,
  img,
  i,
  span {
    margin: 0;
    padding: 0;
  }

  position: relative;
  width: 100%;
  border-radius: 4px;
  min-height: 92px;
  max-height: 480px;
  overflow-y: auto;
  background-color: #ffffff;
  border: solid 1px #e8e8e8;
  padding: 5px;
  display: flex;
  flex-wrap: wrap;

  .ant-tag {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 88px;
    max-width: 130px;
    height: 32px;
    margin: 0 8px 8px 0;
    border: none;

    img {
      width: 20px;
      height: 20px;
      border-radius: 2px;
      margin: 0 6px;
    }

    .name {
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      max-width: 70px;
      min-width: 28px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .anticon-cross {
      font-size: 14px;
      margin-right: 6px;
      font-weight: 400;
    }
  }

  .btn.ant-btn {
    width: 64px;
    height: 32px;
    display: inline-block;
  }

  .btn:hover {
    color: var(--brand-base);
    border-color: var(--brand-base);
  }

  .btn:focus {
    color: #333;
    border-color: #e6e6e6;
  }

  .mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: #f5f5f5;
    opacity: 0.3;
    z-index: 300;
  }
}

.disabled {
  background: #f5f5f5;
}

.error {
  border-color: #ff7c7c;
}

.error-text {
  font-size: 14px;
  color: #ff7c7c;
  padding: 5px 0;
}

.ekb-components-tags-4-plan-multiple {
  min-height: 92px;
}

.ekb-components-tags-4-plan-single {
  min-height: 92px;
}