/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/05/16
 * */
import React, { PureComponent } from 'react'
import { Tooltip, Icon } from 'antd'
import Tags from './tags'
import './index.less'

export default class RoleSelect extends PureComponent {
  constructor(props) {
    super(props)
  }
  render() {
    let { label, title, required, error } = this.props
    return (
      <div className="role-select">
        <div className="title">
          {required && <span className="required">*</span>}
          <span>{label}</span>
          <Tooltip title={title}>
            <Icon className="icon" type="question-circle-o" />
          </Tooltip>
          :
        </div>
        <Tags {...this.props} />
        {error && <div className="error-message">{error}</div>}
      </div>
    )
  }
}
