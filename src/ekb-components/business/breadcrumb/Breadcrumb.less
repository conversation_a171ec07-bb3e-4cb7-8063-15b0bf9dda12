@import "~@ekuaibao/eui-styles/less/token";
.navigation-bar-breadcrumb-wrapper {
  display: flex;
  flex: 1 1 auto;
  width: 100%;
  background: #fff;
  margin-bottom: @space-4;
  .navigation-bar-breadcrumb-content {
    position: relative;
    display: flex;
    flex: 1;
    -ms-flex-preferred-size: auto;
    align-items: center;
    color: @color-black-3;
    font-size: 14px;
    .line {
      margin: 0 8px;
      height: 16px;
      width: 1px;
      background: #e8e8e8;
    }
    .fix {
      display: flex;
      align-items: center;
      color: #cfd5d7;
      width: 16px;
      height: 16px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .ant-breadcrumb {
      font-weight: 400;
      font-size: 14px;
      height: 22px;
      line-height: 22px;
      color: @color-black-3;
    }

    .ant-breadcrumb > span {
      .cannotClick {
        cursor: text;
        color: @color-black-3;
        &:hover {
          color: @color-black-3;
        }
      }
      a {
        color: @color-black-3;
      }
      a:hover {
        color: @color-brand-2;
        text-decoration: none;
      }
    }
    .ant-breadcrumb > span:last-child {
      a,
      a:hover {
        color: @color-black-2;
        text-decoration: none;
      }
    }
  }
}
