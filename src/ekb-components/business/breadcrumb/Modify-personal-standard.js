/**************************************************
 * Created by zhanghuinan on 2018/9/19 下午4:54.
 **************************************************/
import React from 'react'
import { app as api } from '@ekuaibao/whispered'

function openStandardModal() {
  api.open('@expense-standard:ModifyStandardModal').then(res => {})
}
export default function ModifyPersonalStandard(props) {
  return (
    <span className="modify-standard" onClick={openStandardModal}>
      {props.name}
    </span>
  )
}
