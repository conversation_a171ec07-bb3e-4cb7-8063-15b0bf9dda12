.navigation-bar-title-wrapper {
  width: 100%;
  display: flex;
  align-items: center;

  .navigation-bar-title-content {
    height: 28px;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .change-color {
    color: #8c8c8c;
  }

  .status-tag {
    height: 22px;
    line-height: 22px;
    padding: 0 8px;
    border-radius: 11px;
    margin-left: 8px;
    background-color: #f5f5f5;
  }

  .tips {
    display: flex;
    align-items: center;

    .icon-tip {
      margin-left: 20px;
      overflow: hidden;
      height: 20px;
    }
  }
}

.order-management-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;  
}

.guide_popover_content {
  .content-item-layer {
    height: 532px;
    overflow-y: auto;
  }
  .item-title {
    position: relative;
    margin-bottom: 2px;
    color: rgba(29, 33, 41, 0.7);
  }
  .dot {
    font-size: 6px;
    position: absolute;
    top: 11px;
    transform: translateY(-50%);
  }
  .item-title-text {
    display: inline-block;
    margin-left: 10px;
  }
  .order-management-guide-pic {
    width: 100%;
    margin-top: 16px;
  }
}
