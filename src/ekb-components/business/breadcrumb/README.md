## NavigationBar 组件

### 使用说明

- NavigationBar 组件包括三个部分，从上到下分别是 面包屑，title 标题，说明文案

#### 面包屑

- 默认间隔符是'/'，如果想使用'>'，传递 separator = '>'
- 默认显示面包屑，传入参数 breadcrumbVisible=false 可隐藏

#### title 栏

- 默认显示 title 栏，传入 titleVisible=false 可隐藏
- title 栏字体提供两种颜色，默认黑色，传入 changeColor=true,则变为和面包屑与说明文案一样的灰色
- 表示状态的 statusText 需要使用者传入，不传则不显示，形式为字符串

#### 引入方式

- '....ekb-components/business/breadcrumb／Breadcrumb'的方式仅仅引入面包屑组件
- 通过'....ekb-components/business/breadcrumb／Title'仅引入 title 组件，此时需要直接传入 title 字符串
- 通过'..../ekb-components/business/breadcrumb'方式引入的是三个组件的组合，通过参数控制显示状态
