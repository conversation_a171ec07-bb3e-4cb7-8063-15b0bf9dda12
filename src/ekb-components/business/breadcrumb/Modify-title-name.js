/**************************************************
 * Created by zhaohuabing on 2018/9/19 下午4:54.
 **************************************************/
import { Button, Icon, Input, Menu, Dropdown } from 'antd'
import React, { PureComponent, Fragment } from 'react'

export default class ModifyTitleName extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      visible: false,
      errorStyle: false,
      value: ''
    }
  }
  handleSave = () => {
    const value = this.value
    if (!value) {
      return this.setState({ errorStyle: true })
    }
    this.setState({ visible: false, errorStyle: false, value: '' })
    //TODO 保存时调接口
  }
  handleCancel = () => {
    this.setState({ visible: false, errorStyle: false, value: '' })
  }

  onChange = e => {
    this.setState({ value: e.target.value, errorStyle: false })
  }

  renderMenu = () => {
    const { errorStyle, value } = this.state
    return (
      <Menu>
        <Menu.Item key="0">
          <div className="title">{i18n.get('修改费用标准名称')}</div>
          <Input
            onChange={this.onChange}
            value={value}
            placeholder={i18n.get('请输入新费用标准名称')}
            className="name-input"
          />
          {errorStyle && <div>{i18n.get('费用标准名称不能为空')}</div>}
          <div className="footer">
            <Button onClick={this.handleSave} type="primary">
              {i18n.get('保存')}
            </Button>
            <Button onClick={this.handleCancel}>{i18n.get('取消')}</Button>
          </div>
        </Menu.Item>
      </Menu>
    )
  }
  handleTitleVisibleChange = visible => {
    this.setState({ visible })
  }

  render() {
    return (
      <Dropdown
        placement="bottomRight"
        overlay={this.renderMenu()}
        trigger={['click']}
        visible={this.state.visible || false}
        onVisibleChange={this.handleTitleVisibleChange}
        getPopupContainer={() => document.getElementById('expense-standard-title-modify')}
      >
        <div id="expense-standard-title-modify">{this.props.title}</div>
      </Dropdown>
    )
  }
}
