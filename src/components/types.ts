/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-05-08 19:17:05
 */

export interface GlobalFieldModel {
  type?: string
  children?: any
  name: string
  label: string
  active: boolean
  canAsDimension: boolean
  ability: string
  dataType: {
    type: string
    entity: string
  }
  entity?: string
  source?: string
  id: string
}

export interface CustomDimensionModel {
  id: string
  code: string
  label: string
  name: string
  active: boolean
}

export interface CustomDimensionNodeModel {
  id: string
  code: string
  name: string
  dimensionId: string
  children: CustomDimensionNodeModel[]
  active: boolean
}

export interface FilterInterface {
  middleTags?: MiddleTags[]
  isShowInput?: boolean
  type?: string
  left: string
  operator: string
  right: string[] | {
    type: string
    value: string[] | string
  }
  includeChildren: boolean
  fromWhere: string
  isSearchInMaster?: boolean
  rightType?: string
}
export interface MiddleTags {
  id: string
  label: string
}

export interface StandardListInterface {
  active: boolean
  controlType: string
  createTime: number
  id: string
  name: string
}

export interface FieldInterface {
  inputVisible?: boolean
  name: string
  type: string
  text: string
  label: string
  hiddenLabel: boolean
  value: any
  size: string
  tips: string
  Component?: keyof JSX.IntrinsicElements | any

  disabled?: boolean
}
type Value = { value: string }
export interface RadioFieldInterface {
  defaultValue: Value
  name: string
  disabled: boolean
  tags: Array<{ value: Value; label: string }>
  showForm: string
  size: string
  label: string
  labelSize: string
}

export interface SelectorProps {
  billState: string[]
  value: string[]
  onChange: Function
  hidden: boolean
  field: any
}

export interface SelectorState {
  value: string[]
}

export interface LeftTags {
  dataType: object
  value: string
  label: string
}

export interface DepartmentTree {
  id: string
  name: string
  nameSpell: string
  code: string
  parentId: string
  children: DepartmentTree[]
  active: boolean
}
