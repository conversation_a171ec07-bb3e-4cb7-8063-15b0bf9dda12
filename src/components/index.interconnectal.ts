import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

const InterconInput = loadableWithDescriptor(() => import('./interconnectal/InterconInput'), {
  type: 'interconnectal-text'
})
const InterconSelect = loadableWithDescriptor(() => import('./interconnectal/InterconSelect'), {
  type: 'interconnectal-select'
})
const LedgerStatisticFieldSelect = loadableWithDescriptor(
  () => import('./interconnectal/LedgerStatisticsFieldSelect'),
  {
    type: 'ledger-statistics-field-select'
  }
)
const InterconSelectCascader = loadableWithDescriptor(() => import('./interconnectal/InterconSelectCascader'), {
  type: 'interconnectal-select-cascader'
})
const InterconRadioGroup = loadableWithDescriptor(() => import('./interconnectal/InterconRadioGroup'), {
  type: 'interconnectal-radio-group'
})
const InterconRadioGroupSelect = loadableWithDescriptor(() => import('./interconnectal/InterconRadioGroupSelect'), {
  type: 'interconnectal-radio-group-select'
})
const InterconSelectRelation = loadableWithDescriptor(() => import('./interconnectal/InterconSelectRelation'), {
  type: 'interconnectal-select-relation'
})
const Icon = loadableWithDescriptor(() => import('./interconnectal/Icon'), {
  type: 'icon'
})
const ImportMethod = loadableWithDescriptor(() => import('./interconnectal/ImportMethod'), {
  type: 'interconnectal-import-way'
})
const PlatFormAdmin = loadableWithDescriptor(() => import('./interconnectal/PlatFormAdmin'), {
  type: 'select:admin'
})

const PlatFormAdminV2 = loadableWithDescriptor(() => import('./interconnectal/PlatFormAdminV2'), {
  type: 'select:admin:v2'
})
const SelectStaff = loadableWithDescriptor(() => import('./interconnectal/SelectStaff'), {
  type: 'select:staff'
})
const Entity = loadableWithDescriptor(() => import('./interconnectal/Entity'), {
  type: 'entities'
})
const EntityTree = loadableWithDescriptor(() => import('./interconnectal/EntityTree'), {
  type: 'entitiestree'
})
const InterconSeparator = loadableWithDescriptor(() => import('./interconnectal/InterconSeparator'), {
  type: 'interconnectal-separator'
})
const RefFeetypeInputTags = loadableWithDescriptor(() => import('./dynamic/RefFeetypeInputTags'), {
  type: 'ref:feetype:input:tags'
})
const InterconTreeSelectInterface = loadableWithDescriptor(() => import('./interconnectal/InterconTreeSelect'), {
  type: 'interconnectal:tree:select'
})

const CustomDimensionFilterWrapper =  loadableWithDescriptor(() => import('./interconnectal/CustomDimensionFilterWrapper'), {
  type: 'custom-dimension-filter-wrapper'
})

const BillStateSelector = loadableWithDescriptor(() => import('./interconnectal/BillStateSelector'), {
  type: 'limit:bill:state'
})
const InterconTagSelect = loadableWithDescriptor(() => import('./interconnectal/InterconTagSelect'), {
  type: 'interconnectal-tag-select'
})
const InterconLabel = loadableWithDescriptor(() => import('./interconnectal/InterconLable'), {
  type: 'interconnectal-label'
})
const RecordLogCheckBox = loadableWithDescriptor(() => import('./interconnectal/RecordLogCheckBox'), {
  type: 'reacord-log-checkbox'
})
const CheckBox = loadableWithDescriptor(() => import('./internal/CheckBox'), {
  type: 'checkbox'
})
const MappingRelation = loadableWithDescriptor(() => import('./interconnectal/MappingRelation'), {
  type: 'mapping:relation'
})

const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })

const interconnectalDataClear = loadableWithDescriptor(() => import('./interconnectal/interconnectalDataClear'), {
  type: 'interconnectal-dataclear'
})

const interInitDataCurrency = loadableWithDescriptor(() => import('./interconnectal/interInitDataCurrency'), {
  type: 'interconnectal-data-currency'
})

const TaxRuleField = loadableWithDescriptor(() => import('./interconnectal/TaxRuleField'), {
  type: 'interconnectal-tax-rule'
})

const interInitExcelDataSet = loadableWithDescriptor(() => import('./interconnectal/interconnectal-excel-dataset'), {
  type: 'interconnectal-excel-dataset'
})

const ApportionConfigSelect = loadableWithDescriptor(() => import('./apportion_config_select'), {
  type: 'apportion-config-select'
})

export const interconnectal = [
  InterconInput,
  InterconSelect,
  InterconRadioGroup,
  InterconRadioGroupSelect,
  InterconSelectRelation,
  Icon,
  ImportMethod,
  PlatFormAdmin,
  PlatFormAdminV2,
  Entity,
  InterconSeparator,
  RefFeetypeInputTags,
  InterconTreeSelectInterface,
  InterconSelectCascader,
  CustomDimensionFilterWrapper,
  BillStateSelector,
  InterconTagSelect,
  LedgerStatisticFieldSelect,
  EntityTree,
  InterconLabel,
  RecordLogCheckBox,
  CheckBox,
  SelectStaff,
  MappingRelation,
  interInitDataCurrency,
  TaxRuleField,
  interconnectalDataClear,
  interInitExcelDataSet,
  ApportionConfigSelect,
  Unknown
]
