/**
 * <AUTHOR>
 * @Date :2020/08/20
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { required } from '../validator/validator'
import { wrapper } from '../layout/FormWrapper'
import { Select } from '@ekuaibao/eui-web'
import { T } from '@ekuaibao/i18n'
import styles from './BudgetConfig.module.less'
import { findIndex, slice } from 'lodash'
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'budgetConfig'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class BudgetConfig extends PureComponent<any> {
  handleOnChange = value => {
    const { onChange, field, onResult } = this.props
    onChange && onChange(value)
    onResult && onResult()
    field.name === 'budgetOccupiedNodeId' && this.filterDataSource()
  }

  filterDataSource = () => {
    const { form, dataSource, onUpdataDateSource } = this.props
    const name = 'budgetOccupiedNodeId'
    const id = form.getFieldValue(name)
    if (id) {
      const index = findIndex(dataSource[name], function(o: any) {
        return o.id === id
      })
      const data = index !== dataSource.length - 1 ? slice(dataSource[name], index + 1) : []
      form.setFieldsValue({ budgetConfirmedNodeId: null })
      onUpdataDateSource &&
        onUpdataDateSource({
          budgetOccupiedNodeId: dataSource[name],
          budgetConfirmedNodeId: data
        })
    }
  }
  render() {
    const {
      field: { info, optional, name, disabled },
      value,
      dataSource
    } = this.props
    return (
      <div className={styles['budget-config-wrapper']}>
        {optional === false && <span className="required">*</span>}
        <T name="当单据流转经过" />
        <Select
          value={value}
          allowClear={optional}
          data={dataSource[name]}
          disabled={disabled}
          style={{ width: 150, marginRight: 4, marginLeft: 4 }}
          onChange={this.handleOnChange}
        />
        <T name={info} />
      </div>
    )
  }
}
