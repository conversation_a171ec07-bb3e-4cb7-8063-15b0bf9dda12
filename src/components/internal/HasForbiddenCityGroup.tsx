
import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import styles from './HasForbiddenCityGroup.module.less'


// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'hasForbiddenCityGroup'
  }
})
export default class HasForbiddenCityGroup extends PureComponent<any> {
  onChange = (e: any) => {
    const { onChange, value, form } = this.props
    const { checked } = e.target
    if (checked) {
      onChange &&
        onChange(value || true )
    } else {
      form.setFieldsValue({ cityGroupId: null })
      onChange && onChange(null)
    }
  }

  render() {
    const { field, value } = this.props
    const { text, label, style = {}, disabled = false } = field
    return (
      <div className={styles['hasForbiddenCityGroup-wrap']} style={...style}>
        <div className={'new_label'}>{label}</div>
        <Checkbox disabled={disabled} onChange={this.onChange} checked={value}>
          <span className={'text-large'}>{text}</span>
        </Checkbox>
      </div>
    )
  }
}
