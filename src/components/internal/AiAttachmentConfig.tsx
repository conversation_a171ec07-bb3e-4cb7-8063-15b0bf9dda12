import { EnhanceField } from '@ekuaibao/template'
import React, { useMemo } from 'react'
import { wrapper } from '../layout/FormWrapper'
import styles from './AiAttachmentConfig.module.less'
import { TwoToneGeneralAiSummary, OutlinedTipsInfo } from '@hose/eui-icons'
import { Radio, Space, Switch, Tooltip } from '@hose/eui'
import classNames from 'classnames'


const AiAttachmentConfig = props => {
  const { value, onChange, form, filedValue } = props

  const handleAiEnabledChange = (val) => {
    setTimeout(() => {
      if (!form) return;
      ['type', 'autoFill'].forEach(key => {
        if (key === 'type') {
          const keyVal = val ? 'aiAttachments' : 'attachments'
          form && form.setFieldsValue({
            [key]: keyVal
          })
        } else {
          form && form.setFieldsValue({
            [key]: val
          })
        }
      })
    }, 50)
  }

  const aiEnabled = useMemo(() => {
    return filedValue?.type === 'aiAttachments'
  }, [filedValue])

  return <div className={styles.aiAttachmentConfig}>
    <div className={styles.aiAttachmentConfigHeader}>
      <Space size={4}>
        <TwoToneGeneralAiSummary fontSize={20} />
        <span>{i18n.get('AI 附件摘要')}</span>
        <Tooltip title={i18n.get('仅支持pdf、docx、png、jpg、jpeg格式')}>
          <OutlinedTipsInfo color='var(--eui-icon-n2)' fontSize={20} />
        </Tooltip>
      </Space>
      <Switch checked={aiEnabled} onChange={handleAiEnabledChange} />
    </div>
    <div className={styles.aiAttachmentConfigMark}>{i18n.get('开启后，AI可提取附件中当前单据模板的字段信息并填单。')}</div>
    <div className={classNames(styles.aiAttachmentIsAutoFill, {
      [styles.hidden]: !aiEnabled
    })}>
      <label htmlFor="aiAttachmentConfig">{i18n.get('是否自动填单：')}</label>
      <Radio.Group onChange={(e) => onChange(e.target.value)} value={value} id="aiAttachmentConfig">
        <Radio value={true}>{i18n.get('允许')}</Radio>
        <Radio value={false}>{i18n.get('不允许')}</Radio>
      </Radio.Group>
    </div>
  </div>
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'ai-attachment-config'
  },
  initialValue(props) {
    let { field = {}, filedValue } = props
    return filedValue?.autoFill ?? field?.defaultValue
  },
  wrapper: wrapper()
})(AiAttachmentConfig)
