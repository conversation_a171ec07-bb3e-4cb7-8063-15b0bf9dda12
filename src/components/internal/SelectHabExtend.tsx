import React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Select } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'select:hab:extend'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class SelectHabExtend extends React.PureComponent<any> {
  constructor(props) {
    super(props)
  }

  onValueChange = (value) => {
    const res = value.map(it => ({
      relationExtendId: it,
      applicationType: 'flowTab'
    }))
    this.props?.onChange(res)
  }

  render() {
    const { value, field, form, tag = [] } = this.props
    const { placeholder, relatedConfig } = field
    const disabled = isDisable(this.props)
    let visible = true

    const realValue = value.map(item => item?.relationExtendId)

    if (relatedConfig && form) {
      //该组件被其他组件关联，用related config配置
      const { ability, value: _value } = relatedConfig
      const abilityValue = form.getFieldValue(ability)
      visible = abilityValue === _value
    }

    if (!visible) {
      return null
    }

    return (
      <Select
        showArrow
        style={{ width: '100%' }}
        mode="multiple"
        optionFilterProp='label'
        disabled={disabled}
        placeholder={placeholder || '请选择'}
        onChange={this.onValueChange}
        value={realValue}
        options={tag}
      />
    )
  }
}
