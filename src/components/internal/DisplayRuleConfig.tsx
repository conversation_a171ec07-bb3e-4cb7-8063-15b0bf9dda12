import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { observer } from 'mobx-react'
import styles from './DisplayRuleConfig.module.less'
import { Select } from 'antd'

const { Option } = Select

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'display-rule-config'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { editStatePathRule, readStatePathRule } = value

    if (!editStatePathRule || !readStatePathRule) {
      return callback('显示规则不能为空')
    }
    return callback()
  }
})
@observer
export default class DisplayRuleConfig extends PureComponent<any, any> {
  constructor(props) {
    super(props)
  }

  handleChange = (key: string, value: string) => {
    this.props.onChange && this.props.onChange({ ...this.props.value, ...{ [key]: value } })
  }

  render() {
    const { field, value = { editStatePathRule: 'choosePath', readStatePathRule: 'fullPath' } } = this.props
    const { label, style = {}, disabled = false, filedList, optionList, placeholder } = field

    return (
      <div className={styles['displayRuleConfig-wrap']} style={...style}>
        <div className={'new_label'}>{label}</div>
        {filedList?.length &&
          filedList.map(item => {
            return (
              <div className="content-item" key={item.key}>
                <span className="left-text">{item.leftTxt}</span>
                <Select
                  value={value[item.key]}
                  style={{ width: 165 }}
                  onChange={this.handleChange.bind(this, item.key)}
                  placeholder={placeholder}
                >
                  {optionList?.length &&
                    optionList.map(option => {
                      return (
                        <Option value={option.value} disabled={disabled} key={option.value}>
                          {option.label}
                        </Option>
                      )
                    })}
                </Select>
              </div>
            )
          })}
      </div>
    )
  }
}
