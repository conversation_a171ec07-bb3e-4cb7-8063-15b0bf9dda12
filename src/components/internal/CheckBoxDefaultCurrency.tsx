import React, { FC, useEffect, useState } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Icon } from 'antd'
import { Tooltip } from "@hose/eui"
import CurrencyDropdown from '../../elements/currency/currency-dropdown'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
type IField = {
  hiddenLabel: boolean
  label: string
  name: string
  optional: boolean
  type: string
}
interface IDefaultCurrencyProps {
  value?: any
  field?: IField
  allCurrencyRates: any[]
  standardCurrency: any
  onChange: Function
}
type ICurrVal = {
  //是否配置默认币种 true表示勾选，false表示没勾选或者没这个config
  isConfigureDefaultCurrency: boolean
  //配置的币种信息id
  currencyInfoId: string
}
const styles = { cursor: 'pointer', padding: '4px 10px', borderRadius: '2px', border: '1px solid #e6e6e6' }
// 过滤币种列表
const filterCurrency = (currencyList = [], key: string, keyWord = 'numCode') =>
  currencyList.filter(i => i[keyWord] === key)
// 根据关键字查询币种列表
const filterStandardCurrencyList = (allCurrencyRates = [], standardCurrency) => {
  const { numCode } = standardCurrency
  return filterCurrency(allCurrencyRates, numCode, 'originalId')
}
// 查询币种列表
const findCurrency = (currencyList = [], key: string, keyWord = 'id') => currencyList.find(i => i[keyWord] === key)
// 选择后反显
const renderChildren = selectCurrency => {
  const { name = '', symbol = '', numCode = '', strCode = '' } = selectCurrency ?? {}
  return selectCurrency ? (
    <div style={styles}>
      <span>{name}</span>
      <span>{i18n.get('（字母代码：') + strCode}</span>&nbsp;&nbsp;&nbsp;
      <span>{i18n.get('数字代码：') + numCode}</span>&nbsp;&nbsp;&nbsp;
      <span>{i18n.get('符号：') + symbol + i18n.get('）')}</span>
    </div>
  ) : (
    <div style={{ ...styles, color: 'rgb(202, 202, 202)' }}>{i18n.get('请选择币种')}</div>
  )
}
const defaultValue: ICurrVal = {
  isConfigureDefaultCurrency: false,
  currencyInfoId: ''
}
const CheckBoxDefaultCurrency: FC<IDefaultCurrencyProps> = props => {
  const { value = defaultValue, field, allCurrencyRates, standardCurrency, onChange } = props
  const { isConfigureDefaultCurrency = false, currencyInfoId } = value
  const [currency, setCurrency] = useState([])
  const sourceCurrency = findCurrency(currency, currencyInfoId)
  const handlerIsDefault = () => {
    onChange({ ...value, isConfigureDefaultCurrency: !!!isConfigureDefaultCurrency, currencyInfoId: '' })
  }
  const handleCurrencyChange = currency => {
    onChange({ ...value, currencyInfoId: currency?.id })
  }
  useEffect(() => {
    setCurrency(filterStandardCurrencyList(allCurrencyRates, standardCurrency))
  }, [])
  return (
    <>
      <Checkbox onChange={handlerIsDefault} checked={isConfigureDefaultCurrency}>
        {i18n.get(field?.label ?? '默认原币币种')}
      </Checkbox>
      <Tooltip placement="bottom" title={i18n.get('配置后，单据上所有金额字段展示默认原币')}>
        <Icon
          className="enum-tooltip"
          type="question-circle-o"
          style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
        />
      </Tooltip>
      {!!isConfigureDefaultCurrency && (
        <CurrencyDropdown
          menuStyle={{ width: '100%' }}
          placement="bottomCenter"
          data={currency}
          checkedType={'image'}
          trigger={['click']}
          checkedData={sourceCurrency ? [sourceCurrency] : []}
          onChange={handleCurrencyChange}
        >
          {renderChildren(sourceCurrency)}
        </CurrencyDropdown>
      )}
    </>
  )
}
export default EnhanceField({
  descriptor: {
    type: 'checkbox:default:currency'
  },
  initialValue() {
    return defaultValue
  },
  // validator: (field, props) => (rule, value, callback) => {
  //   const { isConfigureDefaultCurrency, currencyInfoId } = value
  //   if (rule.level > 0) {
  //     return callback()
  //   }
  //   if (isConfigureDefaultCurrency && !!!currencyInfoId) {
  //     return callback(i18n.get('请选择默认币种'))
  //   }
  //   callback(required(field, value))
  // },
  wrapper: wrapper()
} as any)(
  EnhanceConnect(state => ({
    allCurrencyRates: state['@common']?.allCurrencyRates,
    standardCurrency: state['@common']?.standardCurrency
  }))(CheckBoxDefaultCurrency as any)
)
