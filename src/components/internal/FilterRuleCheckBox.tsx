import React, { PureComponent } from 'react'
import { Checkbox, Input, Select } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import styles from './FilterRuleCheckBox.module.less'
import { wrapper } from '../layout/FormWrapper'
import delete_icon from '../../images/delete-8c.svg'
import { EnhanceConnect } from '@ekuaibao/store'
import {FieldIF} from "@ekuaibao/ekuaibao_types";
import {app as api} from "@ekuaibao/whispered";
import { accountChannel } from '../../elements/payee-account/utils'
import _uniq from 'lodash/uniq'

const { Option } = Select
const private_account = i18n.get('个人账户')
const public_account = i18n.get('对公账户')
const account_types = i18n.get('账户类型')
const bank_of_account = i18n.get('账户所属银行')
const limitNumber = 10
interface IProps {
  field: any
  bus: any
  value: any
  onChange: Function
  billType?: string
  orgData?: any
  specificationId?: string
  bankList?: any[],
  payeeConfig?: any
  certificateTypeList:[]
}

interface IState {
  isChecked: boolean
  leftOption: any
  rightOption: any
  filterRules: any
  extraTextFields: FieldIF[]
}
let ERROR_FLAG = false

const staticLeftOption = [{
  name: account_types,
  value: 'type'
}, {
  name: bank_of_account,
  value: 'bank'
},{
  name: i18n.get('账户类别'),
  value: 'sort'
},{
  name: i18n.get('证件类型'),
  value: 'certificateType'
}]

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'filter-rule-checkbox'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: Function) => {
    if (props.field.name === 'filterRules' && ERROR_FLAG) {
      const findError = value?.some(item => !item.name || !item?.value?.length)
      const exceed_10 = value?.some(item => item?.value?.length > 10)
      if (findError) {
        return callback(i18n.get('请配置筛选条件'))
      }
      if (exceed_10) {
        return callback(i18n.get('配置条件不能超过10个'))
      }
      return callback()
    }
    ERROR_FLAG = true
    return callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  bankList: state['@custom-specification'].bankList,
  payeeConfig: state['@common'].payeeConfig,
  certificateTypeList: state['@common'].CertificateTypeList,
}))
export default class FilterRuleCheckBox extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = {
      isChecked: !!props?.value?.length,
      filterRules: props?.value ? props?.value : [],
      leftOption: staticLeftOption,
      extraTextFields: [],
      rightOption: {
        'type': [{
          name: private_account,
          value: 'PERSONAL'
        },
        {
          name: public_account,
          value: 'PUBLIC'
        }],
        'bank': props.bankList || [],
        'sort':  accountChannel().map((item) => ({
          name: item.label,
          value: item.value,
        })),
        'certificateType':[]
      }
    }
  }
  componentDidMount() {
    api.invokeService(`@common:get:CertificateTypeList`).then(res=>{
      if(res && res.items){
        res.items.concat({ code: '', name: '无' })
        this.setState({
          rightOption:Object.assign(this.state.rightOption,{certificateType:(res.items || []).map((item)=>({
            name: item.name,
            value: item.code,
          }))})
        })
      }
    })
    api.dataLoader('@common.payeeConfig').reload().then(() => {
      setTimeout(() => this.initPayeeExtraTextFields())
    })
  }

  initPayeeExtraTextFields() {
    const { payeeConfig = {} } = this.props
    const { personalAccountConfig = {}, publicAccountConfig = {}} = payeeConfig
    const {leftOption, } = this.state
    const globalFields = api.getState('@common.globalFields.data') || []
    const customFields = _uniq((personalAccountConfig.customFields || ['subsidyGeneration']).concat(publicAccountConfig.customFields || ['subsidyGeneration', 'alterFlag', 'invoiceBagCode'])) ?? ["title"]
    const customFieldsData = globalFields.filter(v => customFields.includes(v.name))
    const allOption = leftOption.concat(
      customFieldsData.map((field) => ({
        name: field.label,
        value: field.name
      }))
    )
    // 新增自定义字段赋值
    if (customFields) {
      // 额外字符串赋值
      this.setState({
        leftOption:allOption,
        extraTextFields: customFieldsData
      })
    }
  }

  handleChangeCheckbox = (e: any) => {
    const { onChange } = this.props
    const { checked } = e.target
    const _filterRule = checked ? [{
      name: undefined,
      value: undefined,
    }] : null
    ERROR_FLAG = false
    onChange && onChange(_filterRule)
    this.setState({ isChecked: checked, filterRules: _filterRule })
  }

  handelLeftChange = (value, index) => {
    const { onChange } = this.props
    const { filterRules } = this.state
    const isExtraField = this.isExtraTextField(value)
    let _filterRule = filterRules
    _filterRule = _filterRule.map((item, idx) => idx === index ? { ...item, name: value, value: undefined, isCustomField: isExtraField ? true : false } : item)
    ERROR_FLAG = false
    onChange && onChange(_filterRule)
    this.setState({ filterRules: _filterRule })
  }

  addRule = () => {
    const { onChange } = this.props
    const { filterRules } = this.state
    let _filterRule = filterRules.map(item => item)
    _filterRule.push({
      name: undefined,
      value: undefined,
    })
    ERROR_FLAG = false
    onChange && onChange(_filterRule)
    this.setState({ filterRules: _filterRule})
  }

  deleteRule = (index) => {
    const { onChange } = this.props
    const { filterRules } = this.state
    const _filterRule = filterRules.filter((_, idx) => idx !== index)
    ERROR_FLAG = false
    onChange && onChange(_filterRule)
    this.setState({ filterRules: _filterRule })
  }

  handelRightChange = (value, index) => {
    const { onChange } = this.props
    const { filterRules } = this.state
    let _filterRule = filterRules
    _filterRule = _filterRule.map((item, idx) => idx === index ? { ...item, value } : item)
    ERROR_FLAG = false
    onChange && onChange(_filterRule)
    this.setState({ filterRules: _filterRule })
  }

  isExtraTextField(name) {
    if (!name) {
      return false
    }
    switch (name) {
      case 'type':
      case 'bank':
      case 'certificateType':
      case 'sort':
      case undefined:
        return false
      default:
        return true
    }
  }

  render() {
    const { field } = this.props
    const { text, style } = field
    const { isChecked, leftOption = [], rightOption, filterRules = [] } = this.state

    return (
      <div className={styles['filter-rule-wrapper']} style={style}>
        <Checkbox className={styles['filter-rule-checkbox']} checked={isChecked} onChange={this.handleChangeCheckbox}>
          {text}
        </Checkbox>
        {
          isChecked && filterRules.length && (
            <div className={styles['all-wrapper']}>
              {
                filterRules.map((item, index) => {
                  return (
                    <>
                      <div key={index} className={styles['selectWrp']}>
                        <div className={styles['rule-line']}>
                          <Select
                            value={item.name}
                            placeholder={i18n.get('请选择字段名称')}
                            onChange={val => this.handelLeftChange(val, index)}
                            style={{ width: '120px' }}
                            showSearch={true}
                            optionFilterProp={'label'}>
                            {
                              leftOption.map((v: { active: boolean, value: string; name: string; }) => (
                                <Option disabled={!(filterRules.findIndex(rule => rule.name === v.value) === -1)} value={v.value} key={v.value}>
                                  {v.name}
                                </Option>
                              ))
                            }
                          </Select>
                          <div className={styles['relation']}>{i18n.get('是')}</div>
                          {!this.isExtraTextField(item?.name) && (
                            <Select
                              mode="multiple"
                              value={item.value}
                              placeholder={i18n.get('请选择字段的值')}
                              onChange={val => this.handelRightChange(val, index)}
                              style={{ width: '220px' }}
                              showSearch={true}
                              optionFilterProp={'label'}>
                              {
                                item?.name && rightOption[item?.name] && rightOption[item?.name].map((v: { value: string; name: string; }) => (
                                  <Option label={v.name} value={v.value} key={v.value}>
                                    {v.name}
                                  </Option>
                                ))
                              }
                            </Select>
                          )}
                          {this.isExtraTextField(item?.name) && (
                            <Select mode="tags" style={{ width: '220px' }} placeholder="请输入字段的值，回车可创建多个" value={item.value} onChange={val => this.handelRightChange(val, index)}></Select>
                          )}
                        </div>
                        {(filterRules.length > 1) && <img onClick={() => this.deleteRule(index)} className={styles['minus-svg']} src={delete_icon} />}
                      </div>
                      {filterRules.length > 1 && (index !== filterRules.length -1) && <span className={styles['or_connection']}>{i18n.get('或')}</span>}
                    </>
                  )
                })
              }
              {(filterRules.length < limitNumber) && <div onClick={this.addRule} className={styles['add-btn']}>{'+ ' + i18n.get('增加筛选条件')}</div>}
            </div>
          )
        }
      </div>
    )
  }
}
