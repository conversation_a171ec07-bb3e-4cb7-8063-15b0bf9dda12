import styles from './Color.module.less'
import React, { PureComponent } from 'react'
import { Row } from 'antd'
import classNames from 'classnames'
import { Enhance<PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
const colorStr =
  '#f9a825,#ff6f00,#ef9a9a,#ef5350,#e53935,#c62828,#f48fb1,#ec407a,#d81b60,#ad1457,#b39ddb,#7e57c2,#5e35b1,#4527a0,#90caf9,#42a5f5,#1e88e5,#1565c0,#80deea,#26c6da,#00acc1,#00838f,#a5d6a7,#66bb6a,#43a047,#2e7d32,#dce775,#cddc39,#afb42b,#827717,#bcaaa4,#8d6e63,#6d4c41,#4e342e,#b0bec5,#78909c,#546e7a,#37474f'
const ColorList = colorStr.split(',')

const colorMap = {
  '#f9a825': '#CB272D',
  '#ff6f00': '#F53F3F',
  '#ef9a9a': '#F76560',
  '#ef5350': '#D25F00',
  '#e53935': '#FF7D00',
  '#c62828': '#FF9A2E',
  '#f48fb1': '#CFAF0F',
  '#ec407a': '#FADC19',
  '#d81b60': '#FBE842',
  '#ad1457': '#008026',
  '#b39ddb': '#009A29',
  '#7e57c2': '#00B42A',
  '#5e35b1': '#23C343',
  '#4527a0': '#206CCF',
  '#90caf9': '#3491FA',
  '#42a5f5': '#57A9FB',
  '#1e88e5': '#1739D2',
  '#1565c0': '#2555FF',
  '#80deea': '#4C79FF',
  '#26c6da': '#739BFF',
  '#00acc1': '#07828B',
  '#00838f': '#0DA5AA',
  '#a5d6a7': '#14C9C9',
  '#66bb6a': '#37D4CF',
  '#43a047': '#551DB0',
  '#2e7d32': '#722ED1',
  '#dce775': '#8D4EDA',
  '#cddc39': '#A871E3',
  '#afb42b': '#CB1E83',
  '#827717': '#F5319D',
  '#bcaaa4': '#F754A8',
  '#8d6e63': '#202A56',
  '#6d4c41': '#2C3659',
  '#4e342e': '#47537A',
  '#b0bec5': '#12952D',
  '#78909c': '#22BA3C',
  '#546e7a': '#37DE4C',
  '#37474f': '#57E565'
}

const newColors = ColorList.map(color => colorMap[color])

@EnhanceField({
  descriptor: {
    type: 'color'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Color extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { curColor: '' }
    props.bus.emit('dynamic:changeColor', this.state.curColor)
  }

  onChange = curColor => {
    const { onChange, bus } = this.props
    onChange && onChange(curColor)
    bus.emit('dynamic:changeColor', curColor)
  }

  handleColorChange = curColor => {
    this.setState({ curColor })
    this.onChange(curColor)
  }

  render() {
    const { value, field } = this.props
    let color = value
    color = colorMap[value] || value
    const { disabled } = field ?? {}

    return (
      <div className={styles['color-layout']}>
        <Row className="color-wrapper pb-20">
          {newColors.map((el, idx) => (
            <div
              key={idx}
              className={classNames(
                `${color && color.toUpperCase() === el.toUpperCase() ? 'color-btn current' : 'color-btn'}`,
                { disabled }
              )}
              onClick={this.handleColorChange.bind(this, el)}
            >
              <div style={{ background: el, width: '100%', height: '100%' }} />
            </div>
          ))}
        </Row>
      </div>
    )
  }
}
