/**
 *  Created by gym on 2019-08-26 16:37.
 */
import React, { PureComponent } from 'react'
import { Radio, Checkbox, Select } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import styles from './ApportionRuleRadio.module.less'
import { wrapper } from '../layout/FormWrapper'
import { FieldInterface, StandardListInterface } from '../types'
const { Option } = Select
const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px',
  fontSize: '14px',
  color: '#1D2B3D'
}

enum RadioType {
  'ALL' = 'ALL',
  'PERCENTAGE' = 'PERCENTAGE',
  'MONEY' = 'MONEY'
}
interface IProps {
  field: FieldInterface
  standardList: StandardListInterface[]
  onChange: any
  currentComponent: {
    rule: RadioType
  }
}

interface IState {
  value: string | number
  isChecked: boolean
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'radio-select'
  },
  wrapper: wrapper()
})
export default class ApportionRuleRadio extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    const { currentComponent } = props
    const { rule } = currentComponent
    this.state = {
      value: rule || 'ALL',
      isChecked: false
    }
  }

  onChange = (e: any) => {
    const { onChange } = this.props
    this.setState({
      value: e.target.value
    })
    onChange && onChange(RadioType[e.target.value])
  }

  checkboxOnChange = (e: any) => {
    this.setState({
      isChecked: e.target.checked
    })
  }

  render() {
    const {
      field: { tips, disabled = false },
      standardList
    } = this.props
    const { value, isChecked } = this.state
    return (
      <div className={styles['apportion-radio-content']}>
        <div className="apportion-tips">{tips}</div>
        <div>
          <Radio.Group onChange={this.onChange} disabled={disabled} value={value}>
            <Radio style={radioStyle} value={RadioType.ALL}>
              {i18n.get('自由调整')}
            </Radio>
            <Radio style={radioStyle} value={RadioType.PERCENTAGE}>
              {i18n.get('根据分摊总额、分摊比例，自动调整分摊金额', { label: '分摊总额' })}
            </Radio>
            <Radio style={radioStyle} value={RadioType.MONEY}>
              {i18n.get('根据分摊金额，自动调整费用金额、分摊比例', { label: '分摊总额' })}
            </Radio>
            {value === RadioType.MONEY && (
              <div className="apportion-percentage">
                <div className="apportion-tips">
                  {i18n.get(
                    '此时指定的分摊金额字段是无需填写的。为了更容易理解，我们强烈建议您将指定的分摊金额字段设置为「自动计算-无」，并添加「说明文本」组件为员工提供适当的引导'
                  )}
                </div>
                {/* 本次迭代暂时先不做 */}
                {/* <Checkbox onChange={this.checkboxOnChange}>{i18n.get('通过费用标准自动计算分摊金额')}</Checkbox>
                {isChecked && (
                  <div className="apportion-select">
                    <div className="apportion-tips">{i18n.get('请选择作为计算依据的费用标准')}</div>
                    <Select style={{ width: 368 }}>
                      {standardList.map(item => {
                        return <Option key={item.id}>{item.name}</Option>
                      })}
                    </Select>
                  </div>
                )} */}
              </div>
            )}
          </Radio.Group>
        </div>
      </div>
    )
  }
}
