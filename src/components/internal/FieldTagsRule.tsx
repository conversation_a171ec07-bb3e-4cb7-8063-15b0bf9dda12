import React, { PureComponent, useCallback, useEffect, useState } from 'react'
import { Select } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { cloneDeep } from 'lodash'
const { Option } = Select
interface IProps {
  onChange: (value: Val) => void
  value: Val
  components: any
}
type Val = { fieldIds?: Array<any> }
const entity = ['organization.Staff']
function checkEntity(str, entities: Array<string>) {
  return entities.includes(str)
}
function isPerson(item) {
  return (
    (getV(item, 'dataType.type') === 'ref' || getV(item, 'dataType.type') === 'list') &&
    (checkEntity(getV(item, 'dataType.entity'), entity) || checkEntity(getV(item, 'dataType.elemType.entity'), entity))
  )
}
export const FieldTagsRule = (props: IProps) => {
  const { value = {}, onChange, components } = props
  const [staffFields, setStaffFields] = useState([])
  useEffect(() => {
    const baseStaff = getStaffList()
    setStaffFields(getComponentList(baseStaff))
  }, [])
  const getStaffList = useCallback(() => {
    const baseDataProperties = cloneDeep(api.getState('@common').globalFields.data)
    return baseDataProperties.filter(item => {
      item.value = item.name
      return isPerson(item) && item.name !== 'payerId' && item.ability !== 'print'
    })
  }, [])
  const getComponentList = useCallback(baseStaff => {
    return baseStaff.filter(staffItem => components.filter(item => item.field === staffItem.value).length !== 0)
  }, [])
  const handleType = useCallback(e => {
    let newvalue = { ...value }
    newvalue.fieldIds = e
    onChange && onChange(newvalue)
  }, [])
  return (
    <div style={{ marginLeft: '20px' }}>
      <Select
        value={value?.fieldIds}
        mode="multiple"
        showSearch
        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        onChange={handleType}
        placeholder={i18n.get('请选择人员或人员多选字段')}
        style={{ width: 300 }}
        size="large"
      >
        {staffFields.map(i => (
          <Option value={i.name} key={i.name} disabled={!i.active}>
            {i.label}
          </Option>
        ))}
      </Select>
    </div>
  )
}
