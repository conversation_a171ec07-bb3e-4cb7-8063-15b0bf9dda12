import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import styles from './DestrictedTravelTypeSelect.module.less'
import { Checkbox, Select } from 'antd'
import { app } from '@ekuaibao/whispered'
const { Option } = Select
import { wrapper } from '../layout/FormWrapper'
interface Props {
  field: FieldProps
  value: any
  onChange: (value: any) => void
}

interface State {
  isSelect: boolean,
  list: any[]
}

interface FieldProps {
  value: string
  popupText: string
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'destrictedTravelType'
  },
  validator: (field: any, props: Props) => (rule: any, value: string[] | undefined, callback: Function) => {
    if (rule.level !== -1 && value?.length === 0) {
      return callback(i18n.get('请选择行程类型'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class DestrictedTravelTypeSelect extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    const { value } = props
    this.state = {
      isSelect: value?.length > 0,
      list: []
    }
  }
  componentDidMount() {
    this.props.bus.setValidateLevel(-1)
    const { field, value, onChange } = this.props
    const v = value || []
    const { name } = field
    if (name === "limitTravelTypeIds") {
      app.invokeService('@tpp-v2:get:getTravelManagement').then(templateObj => {
        if (templateObj?.items) {
          let list = templateObj?.items.filter((i: any) => i.active) // @i18n-ignore
          this.setState({
            list
          })
          const newValue = v.filter(i => list.find(item => item.id === i))
          if (newValue.length) {
            onChange && onChange(newValue)
          }
        }
      })
    } else {
      app
        .invokeService('@tpp-v2:get:tripDataLinkEntityList', { type: 'TRAVEL_MANAGEMENT' })
        .then((res) => {
          if (res?.items) {
            let u = res?.items.find((i: any) => i.name === '行程') // @i18n-ignore
            let children = u?.children || []
            children = children.filter(i => {
              return i.active || v.includes(i.id)
            })
            this.setState({
              list: children
            })
          }
        })
    }
  }
  handleOnChange = e => {
    const { checked } = e.target
    const { onChange } = this.props
    this.setState({ isSelect: checked })
    if (checked) {
      onChange([])
    } else {
      onChange(undefined)
    }
  }
  handleChange = value => {
    const { onChange } = this.props
    onChange(value)
  }
  renderOptions = () => {
    const value = this.props.value || []
    const list = this.state.list || []
    const valueList = list.map(item => {
      return <Option key={item.id} disabled={!item.active && !value.includes(item.id)}>{item.name}</Option>
    })
    return valueList
  }
  render() {
    const { isSelect } = this.state
    const { value } = this.props
    return (
      <div className={styles.destrictedTravelTypeSelect}>
        <Checkbox checked={isSelect} onChange={this.handleOnChange}>
          {i18n.get('限制行程类型')}
        </Checkbox>
        {isSelect && (
          <Select
            maxTagCount={8}
            mode="tags"
            value={value}
            size={'large'}
            style={{ width: 400, margin: '10px 0 10px 26px' }}
            placeholder="请选择行程类型"
            onChange={this.handleChange}
          >
            {this.renderOptions()}
          </Select>
        )}
      </div>
    )
  }
}
