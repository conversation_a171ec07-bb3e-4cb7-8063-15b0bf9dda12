import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { IField } from '@ekuaibao/template/types/Cellar'
import { Select, Checkbox, Icon, Tooltip } from 'antd'
import styles from './InternalSelect.module.less'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { WrappedFormUtils } from 'antd/lib/form/Form'

interface Props {
  field: IField
  form: WrappedFormUtils
  value?: any
  tags?: any
  onChange?: (value: any) => void
}

interface IState {
  checked: boolean
  data: any
}

@((EnhanceField as any)({
  descriptor: {
    type: 'checkbox:limit:select'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    if (value === undefined || value === null) {
      // 如果是undefined没有选择，直接返回
      return callback()
    }
    if (!value?.length) {
      return callback(field.placeholder)
    }
    return callback()
  },
  wrapper: wrapper()
}))
export default class InternalSelect extends React.Component<Props, IState> {
  state = {
    checked: false,
    data: []
  }

  componentDidMount() {
    const { field, value } = this.props
    const dataState = { checked: !!value?.length, data: [] }
    if (field.tags && Array.isArray(field.tags)) {
      dataState.data = field.tags
    }
    this.setState({ ...dataState })
  }

  handleChange = value => {
    const { onChange } = this.props
    if (onChange) {
      onChange(value)
    }
  }

  handleCheckboxChanged = (e: CheckboxChangeEvent) => {
    if (!e.target.checked) {
      const { form, field } = this.props
      form.setFields({ [field.name]: { value: undefined, errors: null } })
      this.handleChange(null)
    } else {
      this.handleChange([])
    }
    this.setState({ checked: e.target.checked })
  }

  render() {
    const { field, value, form } = this.props
    const { checked, data } = this.state
    const { relatedConfig } = field

    let visible = true
    if (relatedConfig && form) {
      const { ability, value } = relatedConfig
      visible = form.getFieldValue(ability) === value
    }
    if (!visible) return null
    return (
      <div className={styles['internal-select-wrapper']}>
        <Checkbox checked={checked} onChange={this.handleCheckboxChanged}>
          {field.label}
        </Checkbox>
        {checked ? (
          <Select
            style={{ paddingBottom: 6 }}
            value={value}
            placeholder={field.placeholder}
            mode={field.multiple ? 'multiple' : 'default'}
            showSearch
            optionFilterProp={'title'}
            optionLabelProp={'title'}
            onChange={this.handleChange}
          >
            {data.map(item => {
              return (
                <Select.Option key={item.value} value={item.value} title={item.label}>
                  {item.label}
                  {!!item.tip ? (
                    <Tooltip placement="topLeft" title={item.tip}>
                      <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
                    </Tooltip>
                  ) : null}
                </Select.Option>
              )
            })}
          </Select>
        ) : null}
      </div>
    )
  }
}
