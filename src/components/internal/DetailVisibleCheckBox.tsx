import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import styles from './DetailVisibleCheckBox.module.less'
import { Checkbox } from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
import TagSelectorField from '../../elements/tag-selector'
import { app as api } from '@ekuaibao/whispered'
const FlowNodeRole = api.require('@custom-flow/elements/plan-detail/FlowNodeRole')
import { find, concat, remove } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'

interface Props {
  field: fieldProps
  planRoleConfig: any[]
  form: any
  onChange: (selection: any) => void
  staffs: any[]
  roles: any[]
  departmentTree: any
}

interface State {
  selection: any
  showRoleSelection: boolean
  showWhiteListSelection: boolean
  visibilityList: any[]
  visibility: any
}

interface fieldProps {
  value: string
  popupText: string
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'detailVisibleCheckBox'
  },
  validator: (field: any, props: Props) => (rule: any, value: string | string[], callback: Function) => {
    if (value === 'unset') {
      showMessage.error(i18n.get('请设置可查看费用明细的白名单'))
      return callback(i18n.get('请设置可查看费用明细的白名单'))
    }
    return callback()
  }
})
@EnhanceConnect(state => ({
  planRoleConfig: state['@custom-flow'].planRoleConfig || [],
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data
}))
export default class DetailVisibleCheckBox extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    const { currentComponent } = props
    const { visibility, visibleStaffSelection } = currentComponent
    this.state = {
      selection: visibleStaffSelection,
      visibility,
      visibilityList: this.getvisibilityList(visibility),
      showRoleSelection: !!visibleStaffSelection,
      showWhiteListSelection: !!visibility
    }
  }

  handleSelectRole = selection => {
    const { form } = this.props
    const selectionV = this.fnFilterSlection(selection)
    form.setFieldsValue({ visibleStaffSelection: selectionV })
    this.setState({ selection }, this.forceUpdate)
  }

  fnFilterSlection = selection => {
    return {
      type: selection.type,
      source: selection.source,
      fieldName: selection.fieldName,
      roleDefId: selection.roleDefId,
      isSkipped: selection.isSkipped
    }
  }

  getvisibilityList = visibility => {
    if (!visibility) return null
    const { staffs, roles, departmentTree } = this.props
    const staffsState = visibility && visibility.staffs
    const rolesState = visibility && visibility.roles
    const departments = visibility && visibility.departments
    const visibilityList = []
    let depChild = []
    departmentTree.forEach(item => {
      if (!!item.children) {
        depChild = concat(item.children, depChild)
      }
    })

    departments &&
      departments.forEach(item => {
        const dep = find(departmentTree, line => line.id === item)
        dep && visibilityList.push(dep)
        const depfind = find(depChild, line => line.id === item)
        depfind && visibilityList.push(depfind)
      })
    rolesState &&
      rolesState.forEach(item => {
        visibilityList.push(find(roles, line => line.id === item))
      })
    staffsState &&
      staffsState.forEach(item => {
        visibilityList.push(find(staffs, line => line.id === item))
      })
    return visibilityList
  }

  handleOnChange = e => {
    const { checked } = e.target
    const { planRoleConfig, form } = this.props
    if (checked) {
      const selection = this.fnFilterSlection(planRoleConfig[0])
      form.setFieldsValue({ visibleStaffSelection: selection })
      this.setState({ showRoleSelection: checked, selection: planRoleConfig[0] })
    } else {
      form.setFieldsValue({ visibility: null, visibleStaffSelection: null })
      this.setState({ showRoleSelection: checked, showWhiteListSelection: false, selection: null })
    }
  }

  handleWhiteListCheckBoxOnChange = e => {
    const { checked } = e.target
    const { form } = this.props
    const visibility = checked ? 'unset' : null
    form.setFieldsValue({ visibility })
    this.setState({ showWhiteListSelection: checked, visibilityList: null, visibility: null })
  }

  getCheckedList = visibility => {
    const visibilityValue = visibility || { staffs: [], departments: [], roles: [] }
    return [
      { type: 'department-member', multiple: true, checkedKeys: visibilityValue.staffs || [] },
      { type: 'department', multiple: true, checkedKeys: visibilityValue.departments || [] },
      { type: 'role', multiple: true, checkedKeys: visibilityValue.roles || [] }
    ]
  }

  handleAreaClick = () => {
    const { visibility } = this.state
    const checkedList = this.getCheckedList(visibility)
    const departmentsIncludeChildren = visibility && visibility.departmentsIncludeChildren ? true : false
    // @ts-ignore
    api.open('@layout:SelectStaffsModal', { checkedList, departmentsIncludeChildren, multiple: true }).then(data => {
      this.setVisibleList(data)
    })
  }

  setVisibleList(data: any) {
    const { checkedList, departmentsIncludeChildren } = data
    const depart = find(checkedList, line => line.type === 'department')
    const role = find(checkedList, line => line.type === 'role')
    const staff = find(checkedList, line => line.type === 'department-member')
    const staffList = staff.checkedKeys || []
    const departmentList = depart.checkedKeys || []
    const roleList = role.checkedKeys || []
    const cVisibility = {
      staffs: staffList.filter((v: any) => v),
      departments: departmentList.filter((v: any) => v),
      roles: roleList.filter((v: any) => v),
      departmentsIncludeChildren
    }
    let list = concat([], depart.checkedData || [])
    list = concat(list, role.checkedData || [])
    list = concat(list, staff.checkedData || [])

    const { form } = this.props
    const visibility = list.length ? cVisibility : 'unset'
    form.setFieldsValue({ visibility })
    this.setState({ visibility: cVisibility, visibilityList: list })
  }

  handleClose = (data: any, deleteItem: { id: string }) => {
    const { visibility } = this.state
    const { form } = this.props
    const cVisibility = this.handleDeleteItem(visibility, deleteItem)
    const visibilityValue = data && data.length ? cVisibility : 'unset'
    form.setFieldsValue({ visibility: visibilityValue })
    this.setState({ visibility: cVisibility, visibilityList: data })
  }

  handleDeleteItem = (visibility: any = {}, deleteItem: { id: string }) => {
    const staffs = visibility.staffs
    remove(staffs, id => id === deleteItem.id)
    const departments = visibility.departments
    remove(departments, id => id === deleteItem.id)
    const roles = visibility.roles
    remove(roles, id => id === deleteItem.id)
    return { staffs, departments, roles }
  }

  render() {
    const { planRoleConfig, canEditConfigImportExpenseModal } = this.props
    const { selection, showRoleSelection, showWhiteListSelection, visibilityList } = this.state

    return (
      <div className={styles.detailVisibleCheckBox}>
        <Checkbox checked={showRoleSelection} onChange={this.handleOnChange}>
          {i18n.get('设置可查看的角色')}
        </Checkbox>
        {showRoleSelection && (
          <>
            <FlowNodeRole
              edit={canEditConfigImportExpenseModal}
              selections={selection}
              planRoleConfig={planRoleConfig}
              fnHandleSelectSelection={this.handleSelectRole.bind(this)}
            />
            <Checkbox checked={showWhiteListSelection} className="mt-8" onChange={this.handleWhiteListCheckBoxOnChange}>
              {i18n.get('添加始终可查看的白名单')}
            </Checkbox>
            {showWhiteListSelection && (
              <TagSelectorField
                value={visibilityList || []}
                onClick={this.handleAreaClick.bind(this)}
                onChange={this.handleClose.bind(this)}
                className="select-tag-input detailVisibleCheckBox-item"
                placeholder={i18n.get('请选择人员')}
                style={{ color: '#1D2B3DBF' }}
              />
            )}
          </>
        )}
      </div>
    )
  }
}
