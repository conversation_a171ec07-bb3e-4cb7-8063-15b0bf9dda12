import React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import SelectCustomCondition from '../../elements/SelectCustomCondition'
import { cloneDeep, isNil } from 'lodash'
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

type IProps = {
    value: any
    field: any
    orgId: any
    conditionList: any[]
    onChange: (val: any) => void  
}
const limitOrderCondition = (props: IProps) => {
    let { value = {}, field, orgId, conditionList, onChange } = props

    const handleChecked = e => {
        let newvalue = { ...value }
        newvalue.isConditionChecked = e.target.checked
        onChange && onChange(newvalue)
    }

    const handleCustomSelectCondition = params => {
        let newValue = cloneDeep(value)
        if (!params) {
        newValue.orderCondition = null
        return onChange && onChange(newValue)
        }
        const condition = isNil(newValue.orderCondition) ? {} : newValue.orderCondition
        condition.id = params && params.key
        condition.name = params && params.label
        condition.type = 'CUSTOM'
        condition.level = null
        newValue.orderCondition = condition
        onChange && onChange(newValue)
    }
    return (
        <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
          <div style={{ marginTop: '-10px' }}>
            <Checkbox onChange={handleChecked} checked={value?.isConditionChecked}>
              {field?.label}
            </Checkbox>
            <div style={{ marginLeft: '20px' }}>
              {value?.isConditionChecked && (
                <SelectCustomCondition
                  onSelect={handleCustomSelectCondition}
                  conditionList={conditionList}
                  condition={value?.orderCondition ?? null}
                  orgId={orgId}
                />
              )}
            </div>
          </div>
        </UniversalComponent>
    )
}


// @ts-ignore
export default EnhanceField({
    descriptor: {
      type: 'limitOrderCondition'
    },
    wrapper: wrapper()
  })(
    // @ts-ignore
    EnhanceConnect(state => {
      return {
        conditionList: state['@custom-flow'].conditionList
      }
    })(limitOrderCondition)
  )
