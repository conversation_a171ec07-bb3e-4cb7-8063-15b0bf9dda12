/**
 *  Created by gym on 2020-09-09 17:24.
 */

import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './TextField.module.less'
import classNames from 'classnames'
import { FieldInterface } from './DataLinkComplexFilter.type'
import { app } from '@ekuaibao/whispered'
const LanguagePanel = app.require<any>('@field-setting/components/LanguagePanel')

interface IProps {
  field: FieldInterface
  onChange: (any) => void
  value: { label: string; cnLabel?: string; enLabel?: string }
}

interface IState {}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'text-field'
  },
  validator: field => (rule, value, callback) => {
    const { label, maxLength, optional } = field
    if (!optional && value && !value.cnLabel && !value.enLabel) {
      return callback(i18n.get('请至少填写一项中英文显示名称'))
    }
    if (value && (value.cnLabel?.length > maxLength || value.enLabel?.length > maxLength)) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength , length: value.length}))
    }

    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  initialValue(props) {
    return ''
  },
  wrapper: wrapper()
})
export default class TextField extends Component<IProps, IState> {
  render() {
    const { field, value, onChange } = this.props
    const { placeholder = '', optional, newStyle = false, textType, disabled } = field
    const className = classNames({ [styles['large-text']]: newStyle })
    return (
      <LanguagePanel
        className={className}
        isDynamic={true}
        value={value}
        disabled={disabled}
        placeholder={placeholder}
        onChange={onChange}
        type={textType}
      />
    )
  }
}
