import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { cloneDeep } from 'lodash'
const { Option } = Select
@EnhanceField({
  descriptor: {
    type: 'autoShareRule'
  },
  wrapper: wrapper()
})
export default class AutoShareRule extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      staffFields: []
    }
  }

  componentDidMount() {
    this.setState({
      staffFields: this.getStaffList()
    })
  }

  handleChecked = e => {
    let { onChange, value = {} } = this.props
    let newvalue = { ...value }
    newvalue.isOpen = e.target.checked
    if (!newvalue.isOpen) {
      newvalue.fieldIds = []
      newvalue.hasDeptChild = false
    }
    onChange && onChange(newvalue)
  }
  handleDeptChildChecked = e => {
    let { onChange, value = {} } = this.props
    let newvalue = { ...value }
    newvalue.hasDeptChild = e.target.checked
    onChange && onChange(newvalue)
  }
  handleType = e => {
    let { onChange, value = {} } = this.props
    let newvalue = { ...value }
    newvalue.fieldIds = e
    onChange && onChange(newvalue)
  }
  getStaffList = () => {
    const baseDataProperties = cloneDeep(api.getState('@common').globalFields.data)
    const entity = ['organization.Staff', 'organization.Department']
    return baseDataProperties.filter(
      (item) => {
        item.value = item.name
        return (item.dataType.type === 'ref' || item.dataType.type === 'list') &&
          (entity.includes(getV(item, 'dataType.entity')) || entity.includes(getV(item, 'dataType.elemType.entity'))) &&
          item.name !== 'payerId' &&
          item.ability !== 'print'
      }
    )
  }
  render() {
    let { value = {} } = this.props
    let { staffFields } = this.state
    return (
      <div>
        <Checkbox onChange={this.handleChecked} checked={value?.isOpen}>
          {i18n.get('自动分享申请事项')}
        </Checkbox>
        <div style={{ marginLeft: '20px' }}>
          {value?.isOpen && (
            <Select
              value={value.fieldIds}
              mode="multiple"
              showSearch
              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              onChange={this.handleType}
              placeholder={i18n.get('请选择字段')}
              style={{ width: 300 }}
              size="large"
            >
              {staffFields.map(i => (
                <Option value={i.name} key={i.name} disabled={!i.active}>
                  {i.label}
                </Option>
              ))}
            </Select>
          )}
        </div>
        {value?.isOpen && (<Checkbox style={{ marginLeft: '20px' }} onChange={this.handleDeptChildChecked} checked={value?.hasDeptChild}>
          {i18n.get('包含所选部门的子部门')}
        </Checkbox>)}
      </div>
    )
  }
}
