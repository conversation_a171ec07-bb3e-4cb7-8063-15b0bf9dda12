/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/25 下午3:38.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import styles from './DataLinkComplexSelect.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import { Resource } from '@ekuaibao/fetch'
import { showMessage } from '@ekuaibao/show-util'

const customSplitRule = new Resource('/api/form/v2/customSplitRule')

interface State {
  splitRuleList: any[]
  valueState: any[]
}
interface Props {
  value: string[]
  field: any
  onChange: Function
}

@EnhanceConnect((state: any) => ({
  list: state['@custom-specification'].defaultRuleList
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'subsidy-complex-select'
  },
  // // @ts-ignore
  validator: (field: any, props: Props) => (rule: any, value: string[], callback: Function) => {
    if (!value || !value.length ) {
      return callback(i18n.get('补贴拆分方式'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class SubsidyComplexSelect extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      valueState: props.value || [],
      splitRuleList: []
    }
  }

  componentWillReceiveProps(nextProps: Props) {
    if (this.props.value !== nextProps.value) {
      this.setState({ valueState: nextProps.value })
    }
  }
  componentDidMount() {
    this.getSplitRule()
  }
  getSplitRule = async () => {
    const res = await customSplitRule.GET('')
    this.setState({
      splitRuleList: res.items
    })
  }
  handleEdit = async (param) => {
    const { id } = param
    const res = await customSplitRule.GET('/$id', { id })
    api.open('@custom-specification:SplitRuleModal', { rule: res?.value })
      .then(async (obj: any) => {
        this.getSplitRule()
      }).catch(e => {
        showMessage.error(e.msg)
        console.log(e.msg)
      })
  }

  handleAdd = value => {
    if (value !== 'add') {
      return
    }
    api.open('@custom-specification:SplitRuleModal', {})
      .then(async (obj: any) => {
        this.getSplitRule()
      }).catch(e => {
        showMessage.error(e.msg)
        console.log(e.msg)
      })
  }
  handleSetDefault = (line: any) => {

  }
  handleOnChange = (data: any) => {
    const { onChange } = this.props
    onChange && onChange([data])
    this.setState({
      valueState: data
    })
  }
  
  render() {
    const { splitRuleList, valueState } = this.state
    return (
      <div className={styles['intercon-select-wrapper']}>
        <ComSelect
          classNames="select-wrapper"
          value={valueState}
          list={splitRuleList}
          emptyTitle={i18n.get('目前没有任何补贴拆分方式')}
          title={i18n.get('新的补贴拆分方式')}
          handleAdd={this.handleAdd}
          handleEdit={this.handleEdit}
          labelInValue={false}
          disabledEdit={false}
          handleSetDefault={this.handleSetDefault}
          enableDelete={false}
          onChange={this.handleOnChange}
        />
      </div>
    )
  }
}
