/**
 *  Created by pw on 2021/7/27 下午4:59.
 */
import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
const { wrapper } = app.require('@components/layout/FormWrapper')
const { required } = app.require('@components/validator/validator')
import { EnhanceField } from '@ekuaibao/template'
import { IField } from '@ekuaibao/template/types/Cellar'
import MessageCenter from '@ekuaibao/messagecenter'
import { SpecificationGroup, SpecificationIF } from '@ekuaibao/ekuaibao_types'
import styles from './SpecificationDynamicItem.module.less'
import { Select } from '@hose/eui'
import { SelectValue } from 'antd/es/select'
import { isArray, reduce, isEqual } from 'lodash';
import { showMessage } from '@ekuaibao/show-util'
const { Option } = Select
interface Props {
  bus: MessageCenter
  value: string
  field: ExpendField
  tag: any
  billTypes?: string[]
  onChange: (key: string) => void
}

interface ExpendField extends IField {
  placeholder?: string
  filterStatement?: boolean
  tempTypes?: string[]
  maxLength?: number
}

interface State {
  specifications: SpecificationIF[]
}

@((EnhanceField as any)({
  descriptor: {
    type: 'specification-dynamic-item'
  },
  validator: (field: IField) => (rule: any, value: string, callback: () => void) => {
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('单据模板') }, value))
  },
  wrapper: wrapper()
}))
export default class SpecificationDynamicItem extends Component<Props, State> {
  state = {
    specifications: []
  }

  async componentDidMount() {
    const { tag = {}, value, field, onChange } = this.props
    const { filterStatement = false, tempTypes = [] } = field
    const { billTypes = [] } = tag
    const billTypeMap = billTypes.reduce((result, cur) => {
      result[cur] = cur
      return result
    }, {})
    const result = await app.invokeService('@custom-specification:get:specificationGroups')
    const specifications = result.items
      .filter((group: SpecificationGroup) => group.active)
      .reduce((result, line: any) => {
        const { specifications } = line
        const list = specifications.filter((item: SpecificationIF) => {
          if (billTypes.length) {
            return item.active && !!billTypeMap[item.type] && item?.state === 'PUBLISHED'
          }
          if (tempTypes?.length) {
            const isActive=item.active&&item?.state === 'PUBLISHED'
            return isActive && !!tempTypes.includes(item?.type)
          }
          return item.active && item?.state === 'PUBLISHED'
        })
        result = result.concat(filterStatement ? list?.filter(i => !i.id?.endsWith('system:对账单')) : list)
        return result
      }, [])
    if (value) {
      if (isArray(value)) {
        // 数组 多选
        const temp = []
        const specificationsMap = specifications.reduce((result, line) => {
          result[line?.id] = line
          return result
        }, {})
        value?.forEach((id) => {
          if (specificationsMap[id]) {
            temp.push(id)
          }
        })
        if (!isEqual(temp, value)) {
          onChange(temp)
        }
      } else {
        // 字符串 单选
        const it = specifications.find((item) => { return item?.id === value })
        if (!it) {
          onChange(it)
        }
      } 
    }
    this.setState({ specifications })
  }

  handleSelect = (value: SelectValue) => {
    const { field = {} } = this.props
    const { maxLength = null } = field
    if (!!maxLength && value?.length > maxLength) {
      return showMessage.info(i18n.get(`最多可选择{__k0}个选项`, {
        __k0: maxLength
      }))
    }
    const { onChange } = this.props
    onChange(value as string)
  }

  render() {
    const { field, value } = this.props
    const { specifications } = this.state
    const mode = field.mode || ''
    return (
      <div className={styles['specification-dynamic-item-wrapper']}>
        <Select
          placeholder={field?.placeholder}
          showSearch
          mode={mode}
          style={{ width: '100%' }}
          optionFilterProp={'title'}
          value={value}
          onChange={this.handleSelect}
        >
          {specifications.map(item => {
            const { name, id } = item
            return (
              <Option key={id} value={id} title={name}>
                {name}
              </Option>
            )
          })}
        </Select>
      </div>
    )
  }
}
