/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/6.
 */
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio, InputNumber } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RadioGroupMore.module.less'
import CheckSurpassTags from '../../elements/puppet/CheckSurpassTags'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep } from 'lodash'
import IgnoreSpecification from './IgnoreSpecification'
const CustomizeCloseRule = api.require('@custom-specification/CustomizeCloseRule')
const RadioGroup = Radio.Group
const powerCode = '170003'
@EnhanceField({
  descriptor: {
    type: 'radio-group-more'
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  powerCodeMap: state['@common'].powers.powerCodeMap || []
}))
export default class RadioGroupMore extends PureComponent {
  state = {
    value: this.filterValue() || '',
    count: 1,
    tags: this.filterTags() || [],
    ignoreUsageSpecificationIds: [],
    overLimitCloseOption: {
      overLimitDay: false,
      overLimitPerson: false,
      overLimitDayOptionField: '',
      overLimitDayOptionValue: '',
      overLimitDayCount: 0,
      overLimitDayCountUnit: 1,
      dateRange: '',
      overLimitExpense: false,
      overLimitExpenseCount: 1,
      ignoreUsageSpecificationIds: []
    }
  }
  filterTags() {
    let { field } = this.props
    let { tags } = field
    let isCheckSurpass = this.props.powerCodeMap?.indexOf(powerCode) > -1
    if (!isCheckSurpass) {
      let newTags = cloneDeep(tags).filter(v => v.value !== 'exceed')
      return newTags
    } else {
      return tags
    }
  }

  filterValue() {
    let { value = {} } = this.props
    let vv = value ? value.rule : val
    let isCheckSurpass = this.props.powerCodeMap?.indexOf(powerCode) > -1
    if (!isCheckSurpass) {
      if (vv === 'exceed') {
        return 'manual'
      }
    }
  }

  componentWillMount() {
    let { value = {}, bus } = this.props
    bus.on('only:trip:selected', this.handleChangeTags)
    this.formatValue(value)
  }

  componentWillReceiveProps(np) {
    if (np.value !== this.props.value) {
      this.formatValue(np.value)
    }
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('only:trip:selected', this.handleChangeTags)
  }

  formatValue(val) {
    let vv = val ? val.rule : val
    if (vv && !!~vv.indexOf('_')) {
      let arr = vv.split('_')
      let value = arr[0]
      let count = arr[1] || 1
      this.setState({ value, count, ignoreUsageSpecificationIds: val.ignoreUsageSpecificationIds ?? [] })
    } else {
      if (vv === 'limit') {
        this.submitNullValues('limit_1', val.ignoreUsageSpecificationIds)
      }
      this.setState({
        value: vv,
        count: 1,
        requisitionCustomCloseRuleId:val.requisitionCustomCloseRuleId,
        overLimitCloseOption:
          val.overLimitCloseOption === null || val.overLimitCloseOption === undefined || val.overLimitCloseOption === ''
            ? {
                overLimitDay: false,
                overLimitPerson: false,
                overLimitDayOptionField: '',
                overLimitDayOptionValue: '',
                overLimitDayCount: 0,
                overLimitDayCountUnit: 1,
                dateRange: '',
                overLimitExpense: false,
                overLimitExpenseCount: 1,
                ignoreUsageSpecificationIds: []
              }
            : val.overLimitCloseOption
      })
    }
  }

  submitValues = val => {
    const { overLimitCloseOption } = this.state
    const { onChange } = this.props
    let newObj = {
      rule: val,
      overLimitCloseOption: overLimitCloseOption
    }
    onChange && onChange(newObj)
  }

  submitNullValues = (val, ignoreUsageSpecificationIds) => {
    const { onChange } = this.props
    let newObj = {
      rule: val,
      ignoreUsageSpecificationIds: ignoreUsageSpecificationIds ?? [],
      overLimitCloseOption: {
        overLimitDay: false,
        overLimitPerson: false,
        overLimitDayOptionField: '',
        overLimitDayOptionValue: '',
        overLimitDayCount: 0,
        overLimitDayCountUnit: 1,
        dateRange: '',
        overLimitExpense: false,
        overLimitExpenseCount: 1,
        ignoreUsageSpecificationIds: []
      }
    }
    onChange && onChange(newObj)
  }

  handleChangeTags = key => {
    let { field } = this.props
    let { tags } = field
    let { value } = this.state
    let isCheckSurpass = this.props.powerCodeMap?.indexOf(powerCode) > -1
    let surpassTags = []
    if (!isCheckSurpass) {
      surpassTags = this.filterTags()
    } else {
      surpassTags = tags
    }

    if (key === 'trip') {
      let newTags = surpassTags.filter(v => v.value !== 'auto')
      this.setState({ tags: newTags })
      if (value === 'auto') {
        this.setState({ value: 'manual' })
        this.submitNullValues('manual')
      }
    } else {
      this.setState({ tags: surpassTags })
    }
  }

  handleChange = e => {
    let val = e.target.value
    this.setState({
      value: val
    })
    if (val !== 'exceed') {
      this.submitNullValues(val)
    } else {
      this.submitValues(val)
    }
  }

  handleInputChange = count => {
    let { value = {} } = this.props
    let vv = value ? value.rule : value
    let arr = vv.split('_')
    let val = arr[0]
    let newVal = `${val}_${count ?? 1}`
    this.submitValues(newVal)
  }

  handleCheckSurpass = values => {
    const { value } = this.state
    const { onChange } = this.props
    this.setState({
      overLimitCloseOption: values
    })
    let newObj = { rule: value, overLimitCloseOption: values }
    onChange && onChange(newObj)
  }

  extensionChange = values => {
    console.log(values)
    const { value, count } = this.state
    const { onChange } = this.props
    const newObj = { rule: `${value}_${count}`, ignoreUsageSpecificationIds: values }
    onChange?.(newObj)
  }
  handleCloseRule = values => {
    const { value } = this.state
    const { onChange } = this.props
    this.setState({
      requisitionCustomCloseRuleId: values
    })
    let newObj = { rule: value, requisitionCustomCloseRuleId: values }
    onChange && onChange(newObj)
  }
  render() {
    let { value, count, tags, overLimitCloseOption, ignoreUsageSpecificationIds, requisitionCustomCloseRuleId } = this.state
    let { field } = this.props
    const { disabled } = field ?? {}

    return (
      <RadioGroup onChange={this.handleChange} style={{ width: 50, marginTop: 7 }} value={value} disabled={disabled}>
        {tags.map(v => {
          return (
            <Radio key={v.value} style={{ display: 'block', marginBottom: '10px' }} value={v.value}>
              {v.label}
              {v.more && value === v.value && (
                <div className={styles['radio-group-more']}>
                  <InputNumber
                    className="radio-input"
                    value={count}
                    min={1}
                    max={999}
                    precision={0}
                    onChange={this.handleInputChange}
                    disabled={disabled}
                  />
                  {i18n.get('次')}
                </div>
              )}
              {v.surpass && value === v.value && (
                <div className={styles['radio-group-surpass']}>
                  <CheckSurpassTags onChange={this.handleCheckSurpass} overLimitCloseOption={overLimitCloseOption} options={v.tags}/>
                </div>
              )}
              {v.ignoreSpecification && value === v.value && (
                <IgnoreSpecification
                  options={v.tags}
                  value={ignoreUsageSpecificationIds}
                  disabled={disabled}
                  onChange={this.extensionChange}
                />
              )}
              {v.customRule && value === v.value && (
                <CustomizeCloseRule
                  value={requisitionCustomCloseRuleId}
                  onChange={this.handleCloseRule}
                  disabled={disabled}
                  expenseLink={v.tags}
                  IgnoreSpecification={IgnoreSpecification}
                />
              )}
              {/*{v.extension?.({ onChange: this.extensionChange, value, ignoreUsageSpecificationIds })}*/}
            </Radio>
          )
        })}
      </RadioGroup>
    )
  }
}
