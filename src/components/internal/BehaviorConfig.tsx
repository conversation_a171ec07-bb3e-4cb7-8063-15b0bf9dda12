import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { Checkbox, Radio } from 'antd'
import { Space } from '@hose/eui'
const CheckboxGroup = Checkbox.Group
const RadioGroup = Radio.Group
const style = {
  color: 'rgb(255, 124, 124)',
  marginRight: 4,
  fontSize: '15px'
}

interface IState {
  behaviorValue: string[]
  importMode: string
  showType: string
  logic: string
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'behavior-config'
  },
  initialValue(props) {
    const { field = {} } = props
    const { originValue = {} } = field
    const { behaviour, importMode, showType } = originValue
    return { behaviour, importMode, showType }
  },
  validator: field => (rule, value, callback) => {
    const { behaviour, importMode, showType } = value
    if (!behaviour) {
      return callback(i18n.get('请选择增改业务对象数据方式'))
    }
    if (!importMode) {
      return callback(i18n.get('请选择可选择数量'))
    }
    if (!showType) {
      return callback(i18n.get('请选择展示布局'))
    }
    callback()
  },
  wrapper: wrapper()
})
class BehaviorConfig extends Component<any, IState> {
  constructor(props: any) {
    super(props)
  }

  getBehaviorValue = behaviour => {
    if (!behaviour) return []
    if (behaviour === 'MORE') {
      return ['INSERT', 'UPDATE']
    }
    return [behaviour]
  }

  formatBehavior = behaviorValue => {
    let behaviour = ''
    if (behaviorValue.length === 2) {
      behaviour = 'MORE'
    } else {
      behaviour = behaviorValue[0]
    }
    return behaviour
  }

  handleChangeLogic = (value) => {
    const { value: oldValue, onChange, bus } = this.props
    let behaviour = ''
    let showType = oldValue.showType
    if (value === 'REF') {
      behaviour = 'REF'
      if (oldValue.showType === 'FIELD') {
        showType = 'TABLE'
      }
    }
    bus?.emit('behaviour:changed', { ...oldValue, showType, behaviour })
    // onChange({ ...oldValue, showType, behaviour })
  }

  handleChangeBehaviorValue = (behaviorValue: string[]) => {
    const { onChange, bus } = this.props
    const behaviour = this.formatBehavior(behaviorValue)
    const params = { behaviour } as Record<string, any>
    if (behaviour === 'MORE') {
      params.importMode = 'MULTIPLE'
      params.showType = 'TABLE'
    } else if (behaviour) {
      params.importMode = 'SINGLE'
      params.showType = 'FIELD'
    } else {
      params.importMode = ''
      params.showType = ''
    }
    bus?.emit('behaviour:changed', params)
    // onChange(params)
  }

  handleImportModeChange = (importMode: string) => {
    const { onChange, value } = this.props
    const behaviour = value.behaviour
    const params = {
      importMode,
      showType: behaviour !== 'REF' && importMode === 'SINGLE' ? 'FIELD' : 'TABLE'
    }
    onChange?.({ ...value, ...params })
  }

  handleShowTypeChange = (showType: string) => {
    const { onChange, value } = this.props
    onChange?.({ ...value, showType })
  }


  render() {
    const { field, value } = this.props
    const vv = value ?? {}
    const { showType, importMode, behaviour } = vv
    const behaviorValue = this.getBehaviorValue(behaviour)
    const logic = behaviour === 'REF' ? 'REF' : 'EDIT'
    return (
      <div data-cy={`ekb-radio-group-${field.name}`} className='fs-14'>
        <div><span style={style}>*</span>数据可选择数量</div>
        <RadioGroup className='fs-14' value={importMode} onChange={e => this.handleImportModeChange(e?.target?.value)}>
          <Radio value={'SINGLE'}>
            <span className='fs-14'>单选</span>
          </Radio>
          <Radio value={'MULTIPLE'}>
            <span className='fs-14'>多选</span>
          </Radio>
        </RadioGroup>
        <div><span style={style}>*</span>执行逻辑</div>
        <RadioGroup value={logic} onChange={e => this.handleChangeLogic(e?.target?.value)}>
          <Space direction='vertical'>
            <Radio value={'REF'}>
              <span className='fs-14'>关联业务对象</span>
            </Radio>
            <Radio value={'EDIT'} disabled={field.originValue?.isPrivateCar}>
              <span className='fs-14'>增改业务对象数据</span>
            </Radio>
          </Space>
        </RadioGroup>
        {logic === 'EDIT' && <div style={{ marginTop: -12, marginLeft: 20 }}>
          <CheckboxGroup value={behaviorValue} onChange={this.handleChangeBehaviorValue}>
            <Space direction='vertical'>
              <Checkbox value='INSERT'>
                <span className='fs-14'>{i18n.get('新增业务对象数据')}</span>
              </Checkbox>
              <Checkbox value='UPDATE'>
                <span className='fs-14'>{i18n.get('修改业务对象数据')}</span>
              </Checkbox>
            </Space>
          </CheckboxGroup>
        </div>}
        <div style={{ marginTop: 12 }}><span style={style}>*</span>展示方式</div>
        <RadioGroup value={showType} onChange={e => this.handleShowTypeChange(e?.target?.value)}>
          <Space direction='vertical'>
            {((logic === 'EDIT' && importMode === 'MULTIPLE') || logic === 'REF') && <Radio value={'TABLE'}>
              <span className='fs-14'>表格布局</span>
            </Radio>}
            {logic === 'EDIT' && importMode === 'SINGLE' && <Radio value={'FIELD'}>
              <span className='fs-14'>表单布局</span>
            </Radio>}
            {((logic === 'EDIT' && importMode === 'MULTIPLE') || logic === 'REF') && <Radio value={'LIST'}>
              <span className='fs-14'>列表布局</span>
            </Radio>}
          </Space>
        </RadioGroup>
      </div>
    )
  }
}

export default BehaviorConfig

// 配置关系
// INSERT -> SINGLE -> FIELD
// INSERT -> MULTIPLE -> TABLE or LIST
// UPDATE -> SINGLE -> FIELD or LIST
// UPDATE -> MULTIPLE -> TABLE or LIST
// INSERT and UPDATE -> MULTIPLE -> TABLE or LIST
