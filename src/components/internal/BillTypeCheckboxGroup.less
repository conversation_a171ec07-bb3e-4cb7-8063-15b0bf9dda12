@import '~@ekuaibao/eui-styles/less/token.less';

.bill-type-checkbox-wrapper {
  display: flex;
  flex-direction: column;
  margin-top: -35px;
  margin-left: 22px;

  .default {
    .font-weight-2;
    .font-size-2;
    color: #1d2b3d;
  }

  .exist-properties {
    display: block;
    margin-left: 22px;
    span {
      font-size: 14px;
      color: #1d2b3d;
    }
  }
  .checkbox.ant-checkbox-wrapper {
    margin-left: 0;
    span {
      font-size: 14px;
      color: #1d2b3d;
    }
  }
  .limit-scope {
    margin-left: 22px;
    .tips {
      margin: -8px 0 0 24px;
      color: #C3C3C3;
    }
    .title {
      color: #666666;
    }
    .flow-tags {
      border: 1px solid rgba(20, 34, 52, 0.15);
      padding: 0 6px;
  
      & .tag-item {
        margin: 0 2px;
        border-width: 1px;
        border-color: #EEEEEE;
        border-style: solid;
  
        &.tag-edit {
          border-style: dashed;
  
          &:hover {
            border-color: @color-brand;
            color: @color-brand;
          }
        }
        &.is-delete {
          color: @color-error;
        }
      }
    }
  }
}

.limit-bill-party {
  margin-left: 22px;
}

.limit-disabled {
  color: #8c8c8c;
}

.bill-type-error {
  .ant-form-explain {
    margin-left: 22px;
  }
}

.bill-limit-error {
  .ant-form-explain {
    margin-left: 44px;
  }
}

.bill-limit-error-with-defaultvalue {
  .ant-form-explain {
    margin-left: @space-7;
  }
}

.wrapper-background {
  background: #F8FAFC;
  padding: @space-7;
  margin-top: -@space-7;
  margin-left: 0;
  border-radius: @radius-2;

  .default-wrapper {
    margin-bottom: 10px;
  }

  .ant-checkbox-wrapper {
    margin-top: @space-3;
  }
  .exist-properties {
    margin-top: -@space-2;
  }
  .limit-bill-party {
    margin-top: -@space-1;
    .ant-checkbox-wrapper {
      margin-top: -@space-1;
      margin-bottom: @space-3;
    }
  }
}
