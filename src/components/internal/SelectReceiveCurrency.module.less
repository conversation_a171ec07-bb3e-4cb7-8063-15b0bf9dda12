@import '~@ekuaibao/eui-styles/less/token.less';
.SelectReceiveCurrency {
  width: 444px;
  background-color: var(--eui-bg-float-base);
  border-radius: 6px;
  padding: 8px;

  :global {
    .eui-select-selection-overflow {
      max-height: 200px;
      overflow-y: scroll;
    }
  }
}

.currency-option {
  margin: 1px 8px;
  align-items: center;
  
  :global {
    .eui-select-item-option-content {
      display: flex;
      align-items: center;

      .currency-label {
        margin-left: 12px;
        font: var(--eui-font-body-r1);
        .currency-code {
          color: var(--eui-text-placeholder);
        }
      }
    }
  }
}

.eui-select-item-option-selected {
  :global {
    .currency-code {
      color: var(--eui-primary-pri-500);
    }
  }
}