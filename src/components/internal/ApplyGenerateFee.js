/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2021-09-22 14:17:46
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2021-10-16 00:30:04
 */

import styles from './ApplyGenerateFee.module.less'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Radio, Icon } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import { EnhanceConnect } from '@ekuaibao/store'
import { Tooltip } from "@hose/eui";
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'checkbox:generate:fee'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    if (value?.autoAssociate && !value?.mappingRuleIds?.length) {
      return callback('请选择生成规则')
    }
    return callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  powers: state['@common'].powers
}))
export default class ApplyGenerateFee extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      dataSourceList: []
    }
  }
  componentDidMount() {
    const { field } = this.props
    api.invokeService('@custom-specification:getGenerateFeeConfig').then(res => {
      this.setState({ dataSourceList: res.items })
    })
  }

  formatValue = (value, checked) => {
    let newValue = { ...value }
    newValue.autoAssociate = checked
    newValue.mappingRuleIds = []
    newValue.allowDeleteFeeDetail = false
    return newValue
  }

  setIds = (value, ids) => {
    let newValue = { ...value }
    newValue.ids = ids
    return newValue
  }

  handleChange = (type, vv) => {
    if (type === 'mappingRuleIds' && vv.includes('add')) return
    let { onChange, value } = this.props
    let newValue = { ...value, [type]: vv }
    onChange && onChange(newValue)
  }

  filterSelectSpecification = ids => {
    const selectIds = this.props.value.ids || []
    const disableIds = this.state.dataSourceList
      .map(line => {
        if (line.active === false) {
          return line.id
        }
      })
      .filter(id => selectIds.indexOf(id) < 0)
    return ids.filter(id => disableIds.indexOf(id) < 0)
  }
  handleAdd = async value => {
    if (value.includes('add')) {
      await api.open('@custom-specification:GenerateFeeRuleModal')
      const res = await api.invokeService('@custom-specification:getGenerateFeeConfig')
      this.setState({ dataSourceList: res.items })
    }
  }
  handleEdit = async value => {
    await api.open('@custom-specification:GenerateFeeRuleModal', { value })
    const res = await api.invokeService('@custom-specification:getGenerateFeeConfig')
    this.setState({ dataSourceList: res.items })
  }
  handleDelete = async value => {
    await api.invokeService('@custom-specification:deleteGenerateFeeConfig', { id: value.id })
    const res = await api.invokeService('@custom-specification:getGenerateFeeConfig')
    this.setState({ dataSourceList: res.items })
  }
  render() {
    let { field, value, form, element, powers } = this.props
    const { KA_CONSUMING_RECORDS } = powers
    const { relatedConfig } = field
    value = value || {}
    let { autoAssociate, allowDeleteFeeDetail, mappingRuleIds, allowSubmitDetail } = value
    const { dataSourceList } = this.state
    const { disabled } = field ?? {}

    let visible = true
    if (relatedConfig && form) {
      let { ability, value } = relatedConfig
      visible = form.getFieldValue(ability) === value
    }
    return (
      visible && (
        <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
          <div className={styles.check_box_tags} style={{ marginTop: -6, marginLeft: 20, marginBottom: 0 }}>
            <Checkbox disabled={disabled} checked={autoAssociate} onChange={e => this.handleChange('autoAssociate', e.target.checked)}>
              {field.label}
              <Tooltip
                placement="topLeft"
                title={i18n.get('可以自动将申请关联的消费数据关联至报销单中。注意:仅按整体事项关联才生效')}
              >
                <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
              </Tooltip>
            </Checkbox>
            {autoAssociate && (
              <ComSelect
                value={mappingRuleIds}
                list={dataSourceList}
                emptyTitle={i18n.get('目前没有任何赋值规则')}
                title={i18n.get('新的生成规则')}
                handleAdd={this.handleAdd}
                handleEdit={this.handleEdit}
                classNames={'fee-type-select'}
                mode="multiple"
                onChange={ids => this.handleChange('mappingRuleIds', ids)}
                labelInValue={false}
                enableDelete={true}
                handleDelete={this.handleDelete}
                placeholder={i18n.get('请选择消费信息与费用的赋值规则')}
              />
            )}
            {autoAssociate && (
              <RadioGroup
                className={styles['radio-group-wrapper']}
                onChange={e => this.handleChange('allowDeleteFeeDetail', e.target.value)}
                value={allowDeleteFeeDetail}
              >
                <Radio value={true}>{i18n.get('允许删除并展示风险')}</Radio>
                <Radio value={false}>{i18n.get('不允许删除')}</Radio>
              </RadioGroup>
            )}
            {autoAssociate && KA_CONSUMING_RECORDS && (
              <Checkbox
                disabled={disabled}
                style={{ marginLeft: 20 }}
                checked={allowSubmitDetail}
                onChange={e => this.handleChange('allowSubmitDetail', e.target.checked)}
              >
                {i18n.get('只展示提交人的差旅费用')}
                <Tooltip
                  placement="topLeft"
                  title={'关联申请事项后，生成费用明细时，只展示出行人中包含提交人的订单生成的费用明细（需要在【自动生成差旅消费费用】中配置相关的规则才会生效）'}
                >
                  <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
                </Tooltip>
              </Checkbox>
            )}
          </div>
        </UniversalComponent>
      )
    )
  }
}
