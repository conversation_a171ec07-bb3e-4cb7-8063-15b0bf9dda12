.filter-rule-wrapper {
    line-height: normal;
    margin-bottom: 20px;


    :global {
      .ant-select-selection--multiple {
        max-height: 50px !important;
        overflow: auto !important;
      }
    }

    .all-wrapper {
      padding-left: 24px;
      .or_connection {
        margin-top: 10px;
        display: inline-block;
      }
    }
    .filter-rule-checkbox {
      font-size: 14px;
      color: #1d2b3d;
    }
    .add-btn {
      color: var(--brand-base);
      margin-top: 8px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    .add-btn:hover {
      text-decoration: underline;
    }
    .selectWrp {
      margin-top:10px;
      display: flex;
      flex-direction: row;
      .rule-line {
        display: flex;
        align-items: center;
      }

      .all-wrapper .ant-select-search__field__placeholder, .ant-select-selection__placeholder {
        display: block !important;
      }

      .minus-svg {
        color: #e6e6e6 !important;
        margin-left: 10px;
        cursor: pointer;
      }

      .relation {
        width: 30px;
        height: 28px;
        padding: 5px;
        margin: 0 5px;
        border: 1px solid #e6e6e6;
        color:#333;
        text-align: center;
      }
    }
   
}
  