import React, { Component } from 'react'
import { InputNumber } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import style from './NumberRange.module.less'

interface IProps {
  tags?: any[]
  value: any[]
  suffix: any
  onChange: Function
  field: any
  bus: any
}
interface IState {
  values: any[]
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'numberRange'
  },
  validator: (field: any) => (rule: any, value: any[], callback: Function) => {
    if (rule.level > 0 || !Array.isArray(value)) {
      return callback()
    }
    const validateNum = (tag: any, value: any, isEmpty: boolean) => {
      if (isEmpty && (value === undefined || value === '')) {
        return ''
      }

      if (value && typeof value === 'string') {
        value = value.trim()
      }
      if (value === undefined || value === '') {
        return i18n.get('not-empty', { label: i18n.get(field.label) })
      }

      const { label, min, max, dataType = {} } = tag
      let re = /^-?[0-9]+$/
      const { scale = 0 } = dataType
      if (scale) {
        re = new RegExp(`^(-?([1-9]\\d*)|-?0)(\\.\\d{1,${scale}})?$`)
      }
      const msg = scale ? i18n.get('please-number-format', { scale }) : i18n.get('请输入整数')
      if (!re.test(value)) {
        return msg
      }
      if (value * 1 > max * 1) {
        return i18n.get('cannot-be-greater', { label, max })
      }
      if (value * 1 < min * 1) {
        return i18n.get('cannot-be-less', { label, min })
      }
      return ''
    }
    const { tags } = field
    let errMsg = ''
    for (let i = 0; i < tags.length; i++) {
      const isEmpty = rule.field === 'lengthRange' && !i
      errMsg = validateNum(tags[i], value[i], isEmpty)
      if (errMsg) {
        return callback(errMsg)
      }
    }
    if (value[0] * 1 > value[1] * 1) {
      return callback(i18n.get('最小值不能大于最大值'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class NumberRange extends Component<IProps, IState> {
  constructor(props: IProps) {
    super(props)
  }

  onChange = (value: any, index: number) => {
    const preVal = this.props.value[index]
    if (preVal !== value) {
      const { onChange } = this.props
      const newVal = [...this.props.value]
      newVal[index] = value
      onChange && onChange(newVal)
    }
  }

  blur = () => {
    const { value, bus, field: { name } } = this.props
    const newV = { [name]: value}
    bus && bus.emit('fields:blur', { ...newV })
  }

  render() {
    const { field: { suffix, tags, disabled }, value = []  } = this.props
    return (
      <div className={style['numberrange-wrapper']} data-cy="number-range">
        {tags.map((obj: any, index: number, arr: any[]) => {
          const val = value[index]
          const { min, max, defaultValue, ...rest } = obj
          return (
            <React.Fragment key={obj.label}>
              <div className={style['numberrange-item']}>
                <InputNumber
                  disabled={disabled}
                  onBlur={() => this.blur()}
                  {...rest}
                  value={val}
                  onChange={value => this.onChange(value, index)}
                />
                {suffix && <span className={style['numberrange-item-suffix']}>{suffix}</span>}
              </div>
              {index !== arr.length - 1 && <span className={style['numberrange-item-mid']}>~</span>}
            </React.Fragment>
          )
        })}
      </div>
    )
  }
}
