/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/5.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './CheckBoxDefaultValue.module.less'
@EnhanceField({
  descriptor: {
    type: 'checkbox:default:value'
  }
})
export default class CheckBoxDefaultValue extends PureComponent {
  onChange = e => {
    let event = e.target.checked
    let { onChange } = this.props
    let value = { type: 'constant', value: event }
    onChange && onChange(value)
  }
  render() {
    let { field, value = {} } = this.props
    let { text } = field
    return (
      <div style={{ marginBottom: '8px' }}>
        <Checkbox onChange={this.onChange} checked={!!value.value}>
          <span className={styles['new_label']}>{text}</span>
        </Checkbox>
      </div>
    )
  }
}
