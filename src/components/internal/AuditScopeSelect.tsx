import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Select} from 'antd'
import styles from './SelectStaffCheckBox.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
const { Option } = Select;
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'audit_scope_select'
  },
  wrapper: wrapper(),
  validator: (field, props) => (rule, value, callback) => {
    if (!value || value.length ===0) {
      return callback(i18n.get('请选择字段'))
    }
    return callback()
  },
})
@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  baseDataProperties: state['@common'].globalFields.data
}))
export default class AuditScopeSelect extends PureComponent<any> {
  constructor(props) {
    super(props)
    this.state = {
      list: []
    }
  }
  componentDidMount() {
    api.invokeService('@common:get:staffs:roleList:department')
    this.getStaffField()
  }

  getStaffField = (): void => {
    const { baseDataProperties } = this.props
    const list = baseDataProperties.filter((item: any) => item?.dataType?.elemType?.entity === "organization.Staff")
    this.setState({ list })
  }

  handleChange = (value:any)=>{
    const { onChange } = this.props
    onChange && onChange(value)
  }
  render() {
    const { field, value } = this.props
    const { list } = this.state
    return (
      <div className={styles['SelectStaffCheckBox']}>
        <div className="mt-8">
          <Select
            mode="multiple"
            allowClear
            size = {"large"}
            placeholder="请选择字段"
            style={{width:'100%'}}
            defaultValue={value || []}
            onChange={this.handleChange}
          >
           {list.map(item=>{
             const {label,name} = item
             return <Option key={name}>{label}</Option>
           })}
          </Select>
        </div>
      </div>
    )
  }
}
