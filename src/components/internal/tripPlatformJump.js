/**zhang <PERSON>**/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
const { Option } = Select
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')
const fetchList = new Resource('/api/engine/intent/availabletmc')

@EnhanceField({
  descriptor: {
    type: 'tripPlatform'
  },
  wrapper: wrapper()
})
export default class tripPlatform extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      trips: []
    }
  }

  async componentDidMount() {
    const { value = {}, onChange } = this.props
    let powers = api.getState()['@common'].powers
    const res = await fetchList.GET('')
    const optionalList = res?.items || []
    let arr = []
    if (powers && powers.powersList) {
      arr = powers.powersList.filter(i => optionalList.includes(i.powerCode) && i.state === 'using');
    }
    const toggle = api.getState()['@common'].toggleManage?.['tg_allow_trun_off_hose_mall']
    if (arr.some(item => item.powerCode === '120209') && !value?.platform?.includes('120209') && !toggle && value?.checked) {
      const newValue = this.resetPlatform(arr, value, true)
      onChange && onChange(newValue)
    }
    this.setState({
      trips: arr
    })
  }

  resetPlatform = (trips, value, isINIT) => {
    const toggle = api.getState()['@common'].toggleManage?.['tg_allow_trun_off_hose_mall']
    let newValue = { ...value }
    let platform = newValue?.platform ?? []
    if (isINIT || (trips.some(item => item.powerCode === '120209') && !value?.platform?.includes('120209') && !toggle && value?.checked)) {
      platform.push('120209')
    }
    if (!value.checked) {
      platform = null
    }
    newValue.platform = platform
    return newValue
  }

  handleChecked = e => {
    let { onChange, value = {} } = this.props
    const newvalue = this.resetPlatform(this.state.trips, { ...value, checked: e.target.checked })
    onChange && onChange(newvalue)
  }
  handleType = e => {
    let { onChange, value = {} } = this.props
    let newvalue = { ...value }
    newvalue.platform = e
    onChange && onChange(newvalue)
  }
  getKey = value => {
    return value
  }
  render() {
    let { value = {}, field } = this.props
    let { trips } = this.state
    const { disabled } = field ?? {}
    const toggle = api.getState()['@common'].toggleManage?.['tg_allow_trun_off_hose_mall']
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div>
          <Checkbox onChange={this.handleChecked} checked={value?.checked} disabled={disabled}>
            {i18n.get('仅允许使用勾选的订购平台')}
          </Checkbox>
          <div style={{ marginLeft: '20px' }}>
            {value?.checked && (
              <Select
                value={value?.platform?.filter(item => trips.some(obj => obj.powerCode === item))}
                mode="multiple"
                showSearch
                filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                onChange={this.handleType}
                placeholder={i18n.get('请选择平台')}
                style={{ width: 300 }}
                size="large"
                disabled={disabled}
              >
                {trips.map(i => (
                  <Option
                    value={this.getKey(i.powerCode)}
                    key={this.getKey(i.powerCode)}
                    disabled={i.powerCode === '120209' && !toggle}
                  >
                    {i18n.get(i.powerName)}
                  </Option>
                ))}
              </Select>
            )}
          </div>
        </div>
      </UniversalComponent>
    )
  }
}
