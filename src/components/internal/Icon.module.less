@import '~@ekuaibao/web-theme-variables/styles/default';

.icon-layout {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  :global {
    .icon-wrapper {
      .icon-btn {
        width: 32px;
        height: 32px;
        margin-right: 20px;
        margin-bottom: 5px;
        cursor: pointer;
        padding: 6px;

        &.current {
          border: 1px solid var(--brand-base);
          border-radius: 4px;
          box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
          background-color: #ffffff;
        }
        &.disabled {
          cursor: not-allowed;
        }
      }
      .content-label {
        padding-bottom: 25px;
      }
      .content-wrapper {
        padding-left: 5px;
      }
    }
  }
}
