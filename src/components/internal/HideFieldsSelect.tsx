import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import styles from './CheckBox.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import SelectStaffFake from '../../elements/puppet/staff-select-heavy'
import { Radio } from '@hose/eui'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'hide-fields-checkbox'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { name } = field
    const {
      form: { getFieldValue }
    } = props
    
    const hideConditionSetting = getFieldValue('hideConditionSetting')
    const isHide = getFieldValue('hide')
    const isHideBlacklistVisibility = getFieldValue('hideBlacklistVisibility')?.fullVisible

    if (value && isHide && hideConditionSetting && name === 'hideBlacklistVisibility' && isHideBlacklistVisibility === false) {
      const { departments, roles, staffs } = value
      if (departments?.length === 0 && roles?.length === 0 && staffs?.length === 0)
        return callback(i18n.get('请选择人员'))
    }
    return callback()
  }
})
@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data
}))
export default class CheckBox extends PureComponent<any> {
  componentDidMount() {
    api.invokeService('@common:get:staffs:roleList:department')
    const { form } = this.props
    const { getFieldValue, setFieldsValue } = form
    const isHide = getFieldValue('hide')
    const isHideBlacklistVisibility = getFieldValue('hideBlacklistVisibility')?.fullVisible
    
    if (isHide && isHideBlacklistVisibility===false) {
      setFieldsValue({ hideConditionSetting: true })
    }
  }
  onChange = e => {
    const { onChange, value, form, field } = this.props
    const { mutualExclusion } = field
    const { checked } = e.target

    if (checked) {
      onChange &&
        onChange({ departments: [], departmentsIncludeChildren: true, roles: [], staffs: [], fullVisible: false })
      if (mutualExclusion?.length > 0 && form) {
        const currentFormValue = form.getFieldsValue()
        const formValue = {}
        mutualExclusion.forEach(v => {
          if (currentFormValue[v.name] !== undefined) {
            formValue[v.name] = v.value
          }
        })
        if (Object.keys(formValue).length > 0) {
          form.setFieldsValue(formValue)
        }
      }
    } else {
      onChange &&
        onChange({ departments: [], departmentsIncludeChildren: true, roles: [], staffs: [], fullVisible: true })
    }
  }
  handleValueChange = val => {
    this.props.onChange?.(val)
  }

  renderError = () => {
    const { form, field } = this.props
    const { name } = field
    const { getFieldError, getFieldValue } = form
    const value = getFieldValue(name)
    const error = getFieldError(name)
    if (name === 'hideBlacklistVisibility') {
      if (value?.fullVisible || !error) {
        return null
      }
      return <div style={{ color: '#ff7c7c', marginLeft: '22px' }}>{error}</div>
    }
    return null
  }
  render() {
    const { field, value = {}, form } = this.props
    const { text, size, style = {}, disabled = false, isRadio = false, relatedConfig } = field
    const { fullVisible = true } = value

    if (relatedConfig?.ability && !form.getFieldValue(relatedConfig.ability)) {
      return null
    }
    return (
      <div className={styles['ekb-check']} data-cy={`ekb-check-${field.name}`} style={...style}>
        <div>
          {isRadio && (
            <Radio onChange={this.onChange} disabled={disabled} checked={!fullVisible}>
              <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
            </Radio>
          )}
          {!isRadio && (
            <Checkbox onChange={this.onChange} disabled={disabled} checked={!fullVisible}>
              <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
            </Checkbox>
          )}
        </div>
        {!fullVisible && (
          // <div className="mt-8 ml-24" >
          <SelectStaffFake className="mt-8 ml-24" value={value} tagShowClassName="w-100b" bus={this.props.bus} disabled={disabled} onValueChange={this.handleValueChange} showIncludeChildren={true} />
          // </div>
        )}
        {this.renderError()}
      </div>
    )
  }
}
