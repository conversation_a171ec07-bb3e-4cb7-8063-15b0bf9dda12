/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 06/07/2017 13:35.
 **************************************************/
import styles from './CheckBoxScopeTags.module.less'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { cloneDeep } from 'lodash'
import { wrapper } from '../layout/FormWrapper'
import SelectStaffFake from '../../elements/puppet/staff-select-heavy'
import { tagLabelLimit } from '../../elements/puppet/staff-select-heavy/utile'
import { Space, Radio, Select } from '@hose/eui'

@EnhanceField({
  descriptor: {
    type: 'checkbox:scope:tags'
  },
  wrapper: wrapper()
})
export default class CheckBoxScopeTags extends PureComponent {
  updateChecked = (value, checked) => {
    let newValue = cloneDeep(value)
    newValue.fullVisible = !checked
    newValue.staffs = []
    newValue.departments = []
    newValue.roles = []
    newValue.departmentsIncludeChildren = false
    newValue.forbidSubmit = false
    return newValue
  }

  handleChecked = e => {
    let { onChange, value } = this.props
    let val = this.updateChecked(value, e.target.checked)
    onChange && onChange(val)
  }

  handleValueChange = val => {
    this.props.onChange?.(val)
  }

  handleFullVisibleChange = e => {
    let { onChange, value = {} } = this.props
    onChange && onChange(this.updateChecked(value, !e.target.value))
  }

  handleForbidSubmitChange = e => {
    let { onChange, value = {} } = this.props
    onChange && onChange({ ...value, forbidSubmit: e })
  }

  render() {
    let { field, value, form } = this.props
    const { disabled, relatedConfig, domain } = field ?? {}

    let visible = true

    if (relatedConfig && form) {
      //该组件被其他组件关联，用related config配置
      const { ability, value: _value } = relatedConfig
      const abilityValue = form.getFieldValue(ability)
      visible = abilityValue === _value
    }

    if (!visible) {
      return null
    }

    const options = [
      {
        label: i18n.get('允许提交单据'),
        value: false
      },
      {
        label: i18n.get('禁止提交单据'),
        value: true
      }
    ]

    if (domain === 'bills') {
      return (
        <div className={styles.check_box_scope_tags}>
          <header>{field.label}</header>
          <main>
            <Radio.Group onChange={this.handleFullVisibleChange} value={value?.fullVisible}>
              <Space direction="vertical">
                <Radio value={true}>{i18n.get('全部人员')}</Radio>
                <Radio value={false}>{i18n.get('仅部分人员')}</Radio>
              </Space>
            </Radio.Group>
            {!value?.fullVisible && (
              <>
                <Select
                  className="select-wrapper"
                  defaultValue={value?.forbidSubmit || false}
                  onChange={this.handleForbidSubmitChange}
                  options={options}
                />
                <SelectStaffFake
                  className="staff-select-wrapper"
                  value={value}
                  onValueChange={this.handleValueChange}
                  showIncludeChildren={true}
                  tagLabelLimit={tag => tagLabelLimit(tag, 26)}
                  allowClear
                  includeSubWrapperStyle={{ lineHeight: '20px', marginTop: '4px' }}
                />
              </>
            )}
          </main>
          <header className="mt-20">{i18n.get('功能设置')}</header>
        </div>
      )
    }

    return (
      <div className={styles.check_box_tags}>
        <Checkbox onChange={this.handleChecked} checked={!value?.fullVisible} disabled={disabled}>
          {field.label}
        </Checkbox>
        {!value?.fullVisible && (
          <SelectStaffFake
            className="staff-select-wrapper"
            value={value}
            onValueChange={this.handleValueChange}
            showIncludeChildren={true}
            tagLabelLimit={tag => tagLabelLimit(tag, 40)}
          />
        )}
      </div>
    )
  }
}
