import { app } from '@ekuaibao/whispered'
import React, { useState, useEffect, useRef, useCallback } from 'react'
import { default as ComplexSelect } from '../../elements/select/ComplexSelect'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { showModal } from '@ekuaibao/show-util'
import styles from './DataLinkComplexSelect.module.less'
import { Checkbox } from 'antd'
import { cloneDeep } from 'lodash'
import { errorFn, getSpecId } from '../dynamic/FlowLinks/utils'

const FlowLinksStateSettle = props => {
  const { value = {}, field = {}, form, templateFields = [], specificationId = '', onChange, feeTypes = [] } = props
  const { opts = {} } = field
  const { isFeeTypeSetting = false, templateType = '' } = opts
  const { editable = false } = opts?.component
  const _val = cloneDeep(value)
  const [showRule, setRule] = useState(!editable)
  const [flowRuleList, setList] = useState([])
  const [checked, setChecked] = useState(_val?.type === 'flowLink')

  const _specId = getSpecId(specificationId, isFeeTypeSetting, feeTypes, templateType)
  const handlerOpenModal = (value?: any) => {
    return app.open('@custom-specification:FlowLinksOptionModal', {
      editable,
      isFeeTypeSetting,
      templateFields,
      specificationId: _specId,
      value,
      isNew: !!!value
    })
  }

  const handleAdd = () => {
    handlerOpenModal().then(res => {
      getFlowLinksList()
    })
  }

  const handleEdit = (value: any = {}) => {
    handlerOpenModal(value).then(res => {
      getFlowLinksList()
    })
  }
  const changeVal = value => {
    const params = value
      ? {
          type: 'flowLink', //单据查询类型=flowLink；无=none
          value
        }
      : { type: 'none' }
    onChange && onChange(params)
  }
  const handleDelete = ({ id = '' }) => {
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get('是否删除？'),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        if (_val?.value === id) changeVal(null)
        app
          .invokeService('@custom-specification:del:flow:link:config', { id })
          .then(res => {
            getFlowLinksList()
          })
          .catch(errorFn)
      }
    })
  }
  const handlerChange = (value: any = {}) => {
    if (value === 'add') {
      return handleAdd()
    }
    return changeVal(value)
  }
  const onCheck = val => {
    const check = val?.target?.checked
    setRule(check)
    setChecked(check)
    if (check) {
      return changeVal(_val?.value)
    }
    return changeVal(null)
  }
  const getFlowLinksList = () => {
    app
      .invokeService('@custom-specification:get:flow:link:config:list', { specificationId: _specId })
      .then(({ items = [] }) => {
        setList(items)
      })
      .catch(errorFn)
  }
  useEffect(() => {
    isFeeTypeSetting && app.dataLoader('@common.feetypes')?.load()
  }, [])
  useEffect(() => {
    getFlowLinksList()
  }, [_specId])
  return (
    <div className={styles['intercon-select-wrapper']}>
      {editable && (
        <Checkbox onChange={onCheck} checked={checked}>
          {i18n.get('筛选条件')}
        </Checkbox>
      )}
      {(showRule || checked) && (
        <ComplexSelect
          classNames="select-wrapper"
          value={_val?.value}
          list={flowRuleList?.filter(it => it?.editable === editable)}
          emptyTitle={i18n.get('目前没有任何赋值规则')}
          title={i18n.get('新的赋值规则')}
          labelInValue={false}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          enableDelete={true}
          handleAdd={handlerChange}
        />
      )}
    </div>
  )
}
// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'flow-links-state'
  },
  validator: (_, props) => (rule, value, callback) => {
    const { field = {} } = props
    const { opts = {} } = field
    const { editable = false } = opts?.component
    if (!editable && !value?.value) {
      return callback(i18n.get('请选择联查规则'))
    }
    return callback()
  },
  wrapper: wrapper()
})(
  // @ts-ignore
  EnhanceConnect(state => {
    return {
      feeTypes: state['@common']?.feetypes?.list ?? []
    }
  })(FlowLinksStateSettle)
)
