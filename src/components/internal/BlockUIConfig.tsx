import React, { PureComponent } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>ield } from '@ekuaibao/template'
import <PERSON> from '@ekuaibao/painter'
import { Fetch } from '@ekuaibao/fetch'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'blockUI'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value) {
      return callback(i18n.get('信息不完整'))
    }
    return callback()
  }
})
export default class BlockUIConfig extends PureComponent<any, any> {

  constructor(props) {
    super(props)
    this.state = { blockUIdsl: undefined }
  }

  componentDidMount() {
    const { currentComponent } = this.props
    const referenceData = currentComponent?.dataType?.entity?.split('.').pop()
    const id =
      currentComponent?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0
        ? 'loanStatisticsWidgetCard'
        : referenceData
    Fetch.POST(`/api/engine/blockUI/componentDSL/$${id}`, {}, { body: { ...currentComponent.blockUIConfig } }).then(
      res => {
        if (res?.value) {
          this.setState({ blockUIdsl: res?.value })
        }
      }
    )
  }
  actions = {
    'on:radio:change': (v: unknown) => {
      let { onChange } = this.props
      onChange && onChange(v)
    },
  }
  render() {
    const { blockUIdsl } = this.state
    if (!blockUIdsl) {
      return null
    }
    return (
      <div style={{marginBottom: 24}}>
        <Painter.Painter {...blockUIdsl} actions={this.actions}></Painter.Painter>
      </div>
    )
  }
}
