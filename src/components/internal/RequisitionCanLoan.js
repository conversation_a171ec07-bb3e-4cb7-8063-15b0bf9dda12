/**zhang <PERSON>**/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RequisitionCanLoan.module.less'
const CheckboxGroup = Checkbox.Group
import ReactDOM from 'react-dom'
import { SelectDate, ModifyDate, OptionalPayeeByZeroData } from './repaymentdate/RepaymentDateView'
import { app as api } from '@ekuaibao/whispered'
@EnhanceField({
  descriptor: {
    type: 'requisitionCanLoan'
  },
  wrapper: wrapper()
})
export default class RequisitionCanLoan extends PureComponent {
  constructor(props) {
    super(props)
  }

  componentDidMount() {
    let { onChange, value = {} } = this.props
    onChange && onChange(value)
    this.setStyle()
  }

  setStyle = () => {
    const dom = ReactDOM.findDOMNode(this.checkboxGroup)
    const children = dom && dom.childNodes
    children &&
      children.forEach(d => {
        const selectDate = d.getElementsByClassName('requisitionCanLoanDate')
        if (selectDate && selectDate.length > 0) {
          d.style.marginLeft = '24px'
          d.style.marginBottom = '-12px'
        }
      })
  }

  onChange = e => {
    let { onChange, value = {} } = this.props
    let { checked } = e.target
    let newValue = { ...value }
    newValue.checked = checked
    onChange && onChange(newValue)
  }

  fnAllowModify = (newValue, checkedList) => {
    const islimitModifyDate = !!checkedList.find(v => v === 'loan:allowModifyRepaymentDateConfig')
    if (islimitModifyDate) {
      const defaultallowModifyRepaymentDateConfig = {
        selected: 'auto',
        autoDelayDays: 15,
        autoDelayTimes: 3
      }
      newValue.allowModifyRepaymentDateConfig = {
        ...defaultallowModifyRepaymentDateConfig,
        ...newValue.allowModifyRepaymentDateConfig,
        islimitModifyDate
      }
    } else {
      delete newValue.allowModifyRepaymentDateConfig
    }
    return newValue
  }

  fnSelectDate = (newValue, checkedList) => {
    const islimitSelectDate = !!checkedList.find(v => v === 'loan:limitRepaymentDateRange')
    if (islimitSelectDate) {
      const defaultlimitRepaymentDateRange = { selected: 'year' }
      newValue.limitRepaymentDateRange = {
        ...defaultlimitRepaymentDateRange,
        ...newValue.limitRepaymentDateRange,
        islimitSelectDate
      }
    } else {
      delete newValue.limitRepaymentDateRange
    }
    return newValue
  }

  fnFilterPayZero = (newValue, checkedList) => {
    const hasPayInfo = !!checkedList.filter(v => v === 'pay')?.length
    if (!hasPayInfo && newValue.vv?.length > 0) {
      newValue.vv = newValue.vv.filter(v => v !== 'pay:optionalPayeeByZero')
    }
    return newValue
  }

  onGroupChange = checkedList => {
    let { onChange, value = {} } = this.props
    let newValue = { ...value }
    newValue.vv = checkedList
    newValue = this.fnAllowModify(newValue, checkedList)
    newValue = this.fnSelectDate(newValue, checkedList)
    newValue = this.fnFilterPayZero(newValue, checkedList)
    onChange && onChange(newValue)
    setTimeout(() => {
      this.setStyle()
    }, 0)
  }
  handleOnchangeModify = obj => {
    let { onChange, value = {} } = this.props
    let newValue = { ...value }
    newValue.allowModifyRepaymentDateConfig = { ...obj.allowModifyRepaymentDateConfig }
    onChange && onChange(newValue)
  }

  handleOnchangeSelect = obj => {
    let { onChange, value = {} } = this.props
    let newValue = { ...value }
    newValue.limitRepaymentDateRange = { ...obj.limitRepaymentDateRange }
    onChange && onChange(newValue)
  }

  fnRenderOptions = (isLimitRepaymentDate, isLimitBeneficiaryInfo, defaultOptions) => {
    let { value = {}, field } = this.props
    let { disabled } = field ?? {}

    if (isLimitRepaymentDate) {
      const { islimitModifyDate, ...o } = value.allowModifyRepaymentDateConfig || {}
      const { islimitSelectDate, ...others } = value.limitRepaymentDateRange || {}
      const modifyOptions = {
        label: (
          <ModifyDate
            className={'selectDate requisitionCanLoanDate'}
            isShow={false}
            disabled={disabled}
            islimitModifyDate={!!value.vv.find(v => v === 'loan:allowModifyRepaymentDateConfig')}
            allowModifyRepaymentDateConfig={o}
            onChange={this.handleOnchangeModify}
          />
        ),
        styles: { marginLeft: 24 },
        value: 'loan:allowModifyRepaymentDateConfig'
      }
      const selectDate = {
        label: (
          <SelectDate
            className={'selectDate requisitionCanLoanDate'}
            isShow={false}
            disabled={disabled}
            islimitSelectDate={!!value.vv.find(v => v === 'loan:limitRepaymentDateRange')}
            limitRepaymentDateRange={others}
            onChange={this.handleOnchangeSelect}
          />
        ),
        value: 'loan:limitRepaymentDateRange'
      }

      defaultOptions.splice(1, 0, modifyOptions, selectDate)
    }
    if (isLimitBeneficiaryInfo) {
      const OptionalPayeeByZero = {
        label: (
          <OptionalPayeeByZeroData
            className={'requisitionCanLoanDate option-payee-by-zero'}
            isShow={false}
            optionalPayeeByZero={!!value.vv.find(v => v === 'pay:optionalPayeeByZero')}
          />
        ),
        value: 'pay:optionalPayeeByZero'
      }
      defaultOptions.push(OptionalPayeeByZero)
    }
    return defaultOptions
  }
  render() {
    let { field, value = {} } = this.props
    let { text, disabled } = field
    const isLimitRepaymentDate = !!value.vv.find(v => v === 'loan')
    const isLimitBeneficiaryInfo = !!value.vv.find(v => v === 'pay')
    let defaultOptions = [
      {
        label: i18n.get('填写还款日期'),
        value: 'loan'
      },
      {
        label: i18n.get('填写收款信息'),
        value: 'pay'
      }
    ]
    defaultOptions = this.fnRenderOptions(isLimitRepaymentDate, isLimitBeneficiaryInfo, defaultOptions)
    return (
      <div className={styles.requisitionCanLoan}>
        <Checkbox onChange={this.onChange} checked={value.checked} disabled={disabled}>
          {text}
        </Checkbox>
        {value.checked && (
          <CheckboxGroup
            ref={node => (this.checkboxGroup = node)}
            options={defaultOptions}
            value={value.vv}
            disabled={disabled}
            onChange={this.onGroupChange}
          />
        )}
      </div>
    )
  }
}
