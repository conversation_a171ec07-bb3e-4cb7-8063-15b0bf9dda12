@import '~@ekuaibao/web-theme-variables/styles/default';

.color-layout {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  :global {
    .color-wrapper {
      .color-btn {
        margin: 3px;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #ffffff;
        cursor: pointer;
        display: inline-flex;
        padding: 7px;
        &.current {
          border: 1px solid var(--brand-base);
          border-radius: 4px;
          box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
          background-color: #ffffff;
        }
        &.disabled{
          cursor: not-allowed;
        }
      }
      & > div {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
    }
    .content-label {
      padding-bottom: 25px;
    }
    .content-wrapper {
      padding-left: 5px;
    }
  }
}
