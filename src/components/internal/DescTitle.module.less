@import '~@ekuaibao/web-theme-variables/styles/default';

.meta_name {
  display: flex;
  flex: 1;
  flex-direction: column;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  :global {
    .meta_name_item {
      line-height: 28px;
      display: flex;
      .meta_name_label {
        color: rgba(29, 43, 61, 0.5);
      }
      .meta_name_tip { 
        span {
          background: rgba(29, 43, 61, 0.09);
          border-radius: 4px;
          padding: 2px 4px;
          font-size: 12px;
          color: rgba(29, 43, 61, 0.5);
        }
        margin-left: 8px;
        
      }
      .meta_name_val {
        overflow: hidden;
        white-space: pre-wrap;
        text-overflow: ellipsis;
        display: inline-block;
        vertical-align: top;
        margin-left: 8px;
      }
    }
  }
}
