/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/8.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Tag } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'

@EnhanceField({
  descriptor: {
    type: 'tag-list'
  },
  wrapper: wrapper()
})
export default class TagsList extends PureComponent {
  render() {
    let { field } = this.props
    let { tags, label } = field
    return (
      <div>
        <div>{label}</div>
        <div>
          {tags.map(v => {
            return (
              <Tag style={{ cursor: 'default', marginRight: 8 }} size="small" fill="outline" key={v.id}>
                {v.name}
              </Tag>
            )
          })}
        </div>
      </div>
    )
  }
}
