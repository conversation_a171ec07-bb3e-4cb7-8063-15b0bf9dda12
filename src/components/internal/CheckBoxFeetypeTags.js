/**
 * Created by lika<PERSON> on 7/10/17.
 */
import styles from './CheckBoxTags.module.less'
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import FeeTypeSelect from '../../elements/feeType-tree-select'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'

@EnhanceField({
  descriptor: {
    type: 'checkbox:feetype:tags'
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  feetypesAllMap: state['@common'].feetypesAll.map,
  feeTypes: state['@common'].feetypes.data
}))
export default class CheckBoxFeetypeTags extends PureComponent {
  componentWillMount() {
    if(this.props.feeTypes?.length){
      return
    }
    api.dataLoader('@common.feetypesAll').reload()
  }
  setChecked = (value, checked) => {
    let newValue = { ...value }
    newValue.isAll = !checked
    newValue.ids = []
    return newValue
  }

  setIds = (value, ids) => {
    let newValue = { ...value }
    newValue.ids = ids
    return newValue
  }

  valueSerialize = value => {
    if (!value) {
      return { checked: false, ids: [] }
    }
    return { checked: !value.isAll, ids: value.ids?.filter(id => !!id?.length) }
  }

  handleChecked = e => {
    let { onChange, value } = this.props
    let newValue = this.setChecked(value, e.target.checked)
    onChange && onChange(newValue)
  }

  handleFeeTypeChange = checkedKeys => {
    let { onChange, value } = this.props
    let newValue = this.setIds(value, checkedKeys)
    onChange && onChange(newValue)
  }

  render() {
    let { field, value, feeTypes, feetypesAllMap } = this.props
    let { checked, ids } = this.valueSerialize(value, field)
    let visible = true
    const checkedKeys = ids.map(id => {
      const feeType = feetypesAllMap[id]
      if (feeType && !feeType.active) {
        return feeType.name + i18n.get('(已停用)')
      }
      return id
    })
    const { disabled } = field ?? {}

    return (
      visible && (
        <div className={styles.check_box_tags}>
          <Checkbox onChange={this.handleChecked} checked={checked} disabled={disabled}>
            {field.label}
          </Checkbox>
          {checked && (
            <FeeTypeSelect
              className="fee-type-select"
              size="large"
              multiple={true}
              treeCheckable={true}
              feeTypes={feeTypes}
              checkedKeys={checkedKeys}
              onChange={this.handleFeeTypeChange}
              disabled={disabled}
            />
          )}
        </div>
      )
    )
  }
}
