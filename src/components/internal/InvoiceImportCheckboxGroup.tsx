/**
 *  Created by gym on 2019-04-16 17:29.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import './InvoiceImportCheckboxGroup.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { T } from '@ekuaibao/i18n'
import { find, concat, remove, get } from 'lodash'
import { app, app as api } from '@ekuaibao/whispered'
import TagSelectorField from '../../elements/tag-selector'
const { UniversalComponent } = app.require('@elements/StandardVersionComponent')
const { ConditionWrapper } = app.require('@elements/conditionComponents')

interface Props {
  value: any
  OCRPower: boolean
  OCRMetering: boolean
  AliPayCardPower: boolean
  staffs: any[]
  roles: any[]
  onChange: Function
}

interface State {
  query: boolean
  upload: boolean
  scan: boolean
  photo: boolean
  ocr: boolean
  medical: boolean | undefined
  weixin: boolean | undefined
  alipaycard: boolean
  aifapiao: boolean
  formPoolSelect: boolean
  visibilityMap: any
  visibilityMapList: any,
  overseasInvoice:boolean
}

interface Map {
  [key: string]: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'import-invoice-checkbox-group'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: Function) => {
    if (!(value.ocr || value.photo || value.query || value.scan || value.upload || value.weixin || value.alipaycard || value.aifapiao || value.formPoolSelect || value.overseasInvoice)) {
      return callback(i18n.get('导入方式不能为空'))
    }
    if (value.upload || value.scan) {
      const importModeList = Object.keys(value).filter(item => value[item])
      if (importModeList.length === 1) {
        return callback(i18n.get('该导入方式不能单独使用'))
      }
    }
    return callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  OCRPower: state['@common'].powers.OCR,
  OCRMedical: state['@common'].powers.OCRMedical,
  OCRMetering: state['@common'].powers.OCRMetering,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  AliPayCardPower: state['@common'].powers.AliPayCard,
  ByTime: state['@common'].powers.BYTIME,
  OverseasInvoice:state['@common'].powers.OverseasInvoice,
}))
export default class InvoiceImportCheckboxGroup extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const { value = {} } = this.props
    const { query, upload, scan, photo, ocr, weixin, alipaycard, aifapiao, medical, formPoolSelect, visibilityMap = {}, overseasInvoice } = value
    const visibilityMapList = this.getVisibilityMapList(visibilityMap)
    this.state = { query, upload, scan, photo, ocr, weixin, alipaycard, aifapiao, medical, formPoolSelect, visibilityMap, visibilityMapList, overseasInvoice }
  }

  getVisibilityMapList = (visibilityMap = {}) => {
    const visibilityMapList = {}
    const { staffs, roles } = this.props
    Object.keys(visibilityMap).forEach(key => {
      const visibility = visibilityMap[key]
      if (visibility) {
        const visibilityList = []
        if (visibility.staffs?.length) {
          visibility.staffs.forEach(item => {
            visibilityList.push(find(staffs, line => line.id === item))
          })
        }
        if (visibility.roles?.length) {
          visibility.roles.forEach(item => {
            visibilityList.push(find(roles, line => line.id === item))
          })
        }
        visibilityMapList[key] = visibilityList
      }
    })
    return visibilityMapList
  }

  componentWillReceiveProps(nextProps: any) {
    if (nextProps.value && this.props.value !== nextProps.value) {
      this.setState({ ...nextProps.value })
    }
  }

  handleonChange = (e: any) => {
    const { value, checked } = e.target
    const obj: Map = {}
    obj[value] = checked
    // @ts-ignore
    this.setState({ ...obj }, () => {
      this.onChange(this.state)
    })
  }

  onChange = (v: any) => {
    const { onChange, OCRPower, OCRMetering } = this.props
    const { query, upload, scan, photo, ocr, weixin, alipaycard, aifapiao, medical, formPoolSelect, visibilityMap, overseasInvoice } = v
    const isOcr = ( OCRPower || OCRMetering) ? ocr : false
    onChange && onChange({ query, upload, scan, photo, ocr: isOcr, weixin, alipaycard, aifapiao, medical, formPoolSelect, visibilityMap, overseasInvoice })
  }

  getCheckedList = visibility => {
    const visibilityValue = visibility || { staffs: [], roles: [] }
    return [
      { type: 'department-member', multiple: true, checkedKeys: visibilityValue.staffs || [] },
      // { type: 'department', multiple: true, checkedKeys: visibilityValue.departments || [] },
      { type: 'role', multiple: true, checkedKeys: visibilityValue.roles || [] }
    ]
  }

  handleAreaClick = (type: string) => {
    const { visibilityMap } = this.state
    const checkedList = this.getCheckedList(visibilityMap[type])
    // @ts-ignore
    api.open('@layout:SelectStaffsModal', { checkedList, multiple: true }).then((data: any) => {
      this.setVisibleList(type, data)
    })
  }

  setVisibleList = (type: string, data: any) => {
    const { visibilityMap, visibilityMapList } = this.state
    if (data?.checkedList?.length) {
      const role = find(data.checkedList, line => line.type === 'role')
      const staff = find(data.checkedList, line => line.type === 'department-member')
      const staffList = staff.checkedKeys || []
      const roleList = role.checkedKeys || []
      const staffs = staffList.filter((v: any) => v)
      const roles = roleList.filter((v: any) => v)
      const fullVisible = !roles.length && !staffs.length
      const typeVisibility = {
        fullVisible,
        staffs,
        roles
      }
      let list = concat([], role.checkedData || [])
      list = concat(list, staff.checkedData || [])
      this.setState({
        visibilityMap: {...visibilityMap, [type]: typeVisibility },
        visibilityMapList: { ...visibilityMapList, [type]: list }
      }, () => {
        this.onChange(this.state)
      })
    }
  }

  handleVisibilityChange = (type: string, data: any, deleteItem: { id: string }) => {
    const { visibilityMap, visibilityMapList } = this.state
    const typeVisibility = this.handleDeleteItem(visibilityMap[type], deleteItem)
    this.setState({
      visibilityMap: {...visibilityMap, [type]: typeVisibility },
      visibilityMapList: { ...visibilityMapList, [type]: data }
    }, () => {
      this.onChange(this.state)
    })
  }

  handleDeleteItem = (visibility: any = {}, deleteItem: { id: string }) => {
    const staffs = visibility.staffs
    remove(staffs, id => id === deleteItem.id)
    const roles = visibility.roles
    remove(roles, id => id === deleteItem.id)
    const fullVisible = !roles.length && !staffs.length
    return { staffs, roles, fullVisible }
  }

  renderVisibilitySelect = (check: boolean, type: string) => {
    const { visibilityMapList } = this.state
    const list = get(visibilityMapList, type, [])
    return check ? (
      <TagSelectorField
        value={list || []}
        onClick={() => this.handleAreaClick(type)}
        onChange={(...args) => this.handleVisibilityChange(type, ...args)}
        className="select-tag-input"
        placeholder={i18n.get('请选择人员')}
        style={{ color: '#1D2B3DBF' }}
      />
    ) : null
  }

  // MVP版本目前只支持付款管理下的模版只支持OCR和票池选择
  poolSelectCondition = () => {
    return api.invokeService('@custom-specification:is:320-corporateReceipt:template', this.props.specification)
  }

  render() {
    const { OCRPower, AliPayCardPower, OCRMetering, OverseasInvoice, field, settingType } = this.props
    const { query, upload, scan, photo, ocr, medical, weixin, alipaycard, aifapiao, formPoolSelect, overseasInvoice } = this.state
    const isBillInvoice = field.name === 'importMode' && settingType.startsWith('billSpecification:')
    const IS_SMG = window.PLATFORMINFO?.platform === 'SMG'
    const IS_OPG = window.PLATFORMINFO?.platform === 'OPG'
    const { disabled } = field ?? {}
    const whiteList = ['APP','THIRDPARTY', 'HYBRID']

    // 工行e政务屏蔽微信发票  SMG、OPG屏蔽微信发票
    const hasWeixinInvoice = (whiteList.includes(window.__PLANTFORM__) && !window.IS_ICBC && !IS_SMG && !IS_OPG ) || window.__PLANTFORM__ === 'WEIXIN'
    return (
      <div className="invoice-import-checkbox-wrapper">
        {(OCRPower || OCRMetering) && (
          <>
            <Checkbox disabled={disabled} className="checkbox" value="ocr" checked={ocr} onChange={this.handleonChange}>
              <T name="智能识票" />
            </Checkbox>
            {this.renderVisibilitySelect(ocr, 'ocr')}
          </>
        )}
        <ConditionWrapper condition={() => !this.poolSelectCondition()}>
          <Checkbox disabled={disabled} className="checkbox" value="scan" checked={scan} onChange={this.handleonChange}>
            <T name="扫描发票(移动端)" />
          </Checkbox>
          {this.renderVisibilitySelect(scan, 'scan')}
          <Checkbox disabled={disabled} className="checkbox" value="upload" checked={upload} onChange={this.handleonChange}>
            <T name="电子发票文件（仅PC端）" />
          </Checkbox>
          {this.renderVisibilitySelect(upload, 'upload')}
          {!isBillInvoice && (
            <>
              <Checkbox disabled={disabled} className="checkbox" value="photo" checked={photo} onChange={this.handleonChange}>
                <T name="发票照片" />
              </Checkbox>
              {this.renderVisibilitySelect(photo, 'photo')}
            </>
          )}
          <Checkbox disabled={disabled} className="checkbox" value="query" checked={query} onChange={this.handleonChange}>
            <T name="手工录入" />
          </Checkbox>
          {this.renderVisibilitySelect(query, 'query')}
          {hasWeixinInvoice && (
            <>
              <Checkbox disabled={disabled} className="checkbox" value="weixin" checked={weixin} onChange={this.handleonChange}>
                <T name="微信发票" />
              </Checkbox>
              {this.renderVisibilitySelect(weixin, 'weixin')}
            </>
          )}
          {AliPayCardPower && (
            <>
              <Checkbox disabled={disabled} className="checkbox" value="alipaycard" checked={alipaycard} onChange={this.handleonChange}>
                <T name="支付宝卡包" />
              </Checkbox>
              {this.renderVisibilitySelect(alipaycard, 'alipaycard')}
            </>
          )}
          {/* {OCRMedical && (
          <>
            <Checkbox className="checkbox" value="medical" checked={medical} onChange={this.handleonChange}>
              <T name="医疗发票" />
            </Checkbox>
            {this.renderVisibilitySelect(medical, 'medical')}
          </>
        )} */}
          <UniversalComponent uniqueKey={'customFeeType.pc.aifapiaoConfig'}>
            <Checkbox disabled={disabled} className="checkbox" value="aifapiao" checked={aifapiao} onChange={this.handleonChange}>
              <T name="爱发票" />
            </Checkbox>
            {this.renderVisibilitySelect(aifapiao, 'aifapiao')}
          </UniversalComponent>
        </ConditionWrapper>
        <ConditionWrapper condition={this.poolSelectCondition}>
          <Checkbox disabled={disabled} className="checkbox" value="formPoolSelect" checked={formPoolSelect} onChange={this.handleonChange}>
            <T name="票池选择" />
          </Checkbox>
          {this.renderVisibilitySelect(formPoolSelect, 'formPoolSelect')}
        </ConditionWrapper>
        {
          OverseasInvoice && <>
            <Checkbox disabled={disabled} className="checkbox" value="overseasInvoice" checked={overseasInvoice} onChange={this.handleonChange}>
                <T name="海外票据识别" />
              </Checkbox>
            {this.renderVisibilitySelect(overseasInvoice, 'overseasInvoice')}
          </>
        }

      </div>
    )
  }
}
