import React, { FC, useEffect, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
import { Avatar, Radio, Select, Space } from '@hose/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import styles from './SelectReceiveCurrency.module.less'
const { Option } = Select

type IField = {
  name: string
  relatedConfig: Record<string, any>
  style: Record<string, any>
}
interface ISelectReceiveCurrencyProps {
  value?: any
  field?: IField
  onChange: Function
  receiveCurrencyRange?: Record<string, any>[]
  form: any
}

const SelectReceiveCurrency: FC<ISelectReceiveCurrencyProps> = props => {
  const { onChange, receiveCurrencyRange = [], value, form, field } = props
  const { relatedConfig, style } = field
  const [currencyRange, setCurrencyRange] = useState([])
  const [isAll, setIsAll] = useState(true)

  const getCurrencyList = async () => {
    if (receiveCurrencyRange.length > 0) {
      setCurrencyRange(receiveCurrencyRange)
    } else {
      const result = await api.dataLoader('@common.getCurrencyAll').load()
      setCurrencyRange(result || [])
    }
  }
  const initValue = () => {
    const isAll = !value?.length
    setIsAll(isAll)
    onChange({ ids: isAll ? [] : value, isAll })
  }

  useEffect(() => {
    getCurrencyList()
    initValue()
  }, [])

  const handleChangeRadioGroup = (e: any) => {
    setIsAll(e.target.value)
    onChange({ ids: value.ids, isAll: e.target.value })
  }

  const handleChangeCurrency = (value: any) => {
    onChange({ ids: value, isAll })
  }

  let abilityValue = form.getFieldValue(relatedConfig.ability)
  if (!abilityValue) return null

  return (
    <div style={style}>
      <div className={styles['SelectReceiveCurrency']}>
        <Radio.Group value={isAll} onChange={handleChangeRadioGroup}>
          <Space direction="vertical">
            <Radio value={true}>{i18n.get('全部币种')}</Radio>
            <Radio value={false}>{i18n.get('部分币种')}</Radio>
          </Space>
        </Radio.Group>
        {!isAll && (
          <Select
            mode="multiple"
            defaultValue={value?.ids || []}
            style={{ width: '100%', marginTop: 8 }}
            placeholder={i18n.get('请选择币种')}
            onChange={handleChangeCurrency}
            optionLabelProp="label"
            autoClearSearchValue
            showArrow
            allowClear
            filterOption={(input, option) => option.label.includes(input) || option.value.includes(input)}
          >
            {currencyRange.map(item => {
              return (
                <Option value={item.id} label={item.name} className={styles['currency-option']}>
                  <Avatar src={item.icon} size="small" />
                  <div className={'currency-label'}>
                    <div className={'currency-name'}>{item.name}</div>
                    <div className={'currency-code'}>
                      代码：{item.strCode}（{item.numCode}）
                    </div>
                  </div>
                </Option>
              )
            })}
          </Select>
        )}
      </div>
    </div>
  )
}
export default EnhanceField({
  descriptor: {
    name: 'pay:currencyRange'
  },
  wrapper: wrapper()
} as any)(EnhanceConnect(() => {})(SelectReceiveCurrency as any))
