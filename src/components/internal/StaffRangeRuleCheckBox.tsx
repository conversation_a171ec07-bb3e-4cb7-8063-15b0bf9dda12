import React, { PureComponent } from 'react'
import { Checkbox, Icon } from 'antd'
import { Tooltip } from '@hose/eui'
import { <PERSON>hanceField } from '@ekuaibao/template'
import styles from './StaffRangeRuleCheckBox.module.less'
import { wrapper } from '../layout/FormWrapper'

let ERROR_FLAG = false
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'staff-Range-Rule-checkbox'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: Function) => {
    if (props.field.name === 'valueRangeFilter' && ERROR_FLAG) {
      const findError = value && typeof value !== 'string'
      if (findError) {
        return callback(i18n.get('请配置规则'))
      }
      return callback()
    }
    ERROR_FLAG = true

    return callback()
  },
  wrapper: wrapper()
})
export default class DepRuleCheckBox extends PureComponent {
  constructor(props) {
    super(props)
  }
  handleChangeCheckbox = (e: any) => {
    const { onChange } = this.props
    const { checked } = e.target
    onChange && onChange(checked)
  }
  render() {
    const { field, value, onChange } = this.props
    const { text, Component, disabled } = field
    const checked = value && value !== 'false'
    return (
      <div className={styles['staffRange-wrapper']}>
        <div>
          <Checkbox
            disabled={disabled}
            className={styles['staffRange-checkbox']}
            checked={checked}
            onChange={this.handleChangeCheckbox}
          >
            {text}
          </Checkbox>
          <Tooltip
            placement="top"
            title={i18n.get('取本单据员工、员工（多选）类字段的值，或者其他类型字段值对应的角色值为取值范围。')}
          >
            <Icon
              className="enum-tooltip"
              type="question-circle-o"
              style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
            />
          </Tooltip>
        </div>
        <div className={styles['staffRange-rule']}>
          {checked && Component && <Component disabled={disabled} value={value} onChange={onChange} />}
        </div>
      </div>
    )
  }
}
