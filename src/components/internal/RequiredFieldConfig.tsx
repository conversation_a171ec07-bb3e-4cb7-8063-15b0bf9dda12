import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import styles from './RequiredFieldConfig.module.less'
import classnames from 'classnames'
import { app } from '@ekuaibao/whispered'
const { UniversalComponent } = app.require('@elements/UniversalComponent')

export const RequiredFieldConfigValidator: any = (field, props) => (rule, value, callback) => {
  const { form } = props
  const { isCheckAttr } = field
  if (isCheckAttr && !form.getFieldValue('required')) {
    return callback()
  }
  if (value && value.active && !value.formula) {
    return callback(isCheckAttr ? i18n.get('请配置必填条件') : i18n.get('请配置校验规则'))
  }
  return callback()
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'required-field-config'
  },
  validator: RequiredFieldConfigValidator
})
class RequiredFieldConfig extends PureComponent<any, any> {
  render(){
    return(
      <RequiredFieldConfigComponent {...this.props}/>
    )
  }
}

export default  RequiredFieldConfig

// tslint:disable-next-line: max-classes-per-file
export class RequiredFieldConfigComponent extends PureComponent<any, any> {
  constructor(props) {
    super(props)
  }
  
  handleCheck = e => {
    const isChecked = e.target.checked
    const { onChange, value } = this.props
    onChange({ ...value, active: isChecked })
    this.props?.handleCheckboxCallback && this.props.handleCheckboxCallback(isChecked)
  }

  handleCmpChange = param => {
    const { type, value } = param
    const { onChange, field } = this.props
    const { isCheckAttr, isCheckValid } = field
    if (onChange && type === 'formula') {
      onChange(
        isCheckAttr
          ? {
              ability: 'caculate',
              valueType: 'BOOLEAN',
              formula: value,
              property: 'optional',
              active: true
            }
          : {
              ability: 'check',
              valueType: 'BOOLEAN',
              formula: value,
              active: true
            }
      )
    }
  }

  renderError = () => {
    const { form, field } = this.props
    const { name, isCheckValid } = field
    const { getFieldError } = form
    const error = getFieldError(name)
    if (!error) {
      return null
    }
    return (
      <div className={classnames({ 'check-valid': isCheckValid })} style={{ color: '#ff7c7c' }}>
        {error}
      </div>
    )
  }

  render() {
    const { field, value, form, bus, onChange } = this.props
    const { text, size, style = {}, disabled = false, tags, isCheckAttr, isCheckValid} = field
    if (isCheckAttr && !form.getFieldValue('required')) {
      return null
    }

    const { Component, others } = tags[0]
    const formulaVal = value ? { type: 'formula', value: value.formula } : undefined
    const isChecked = value?.active
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.requiredConfig`}>
        <div className={styles['ekb-check']} data-cy={`ekb-check-${field.name}`} style={{...style}}>
          <div className="mb-8">
            <Checkbox onChange={this.handleCheck} disabled={disabled} checked={isChecked}>
              <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
            </Checkbox>
          </div>
          {isChecked && (
            <div className={classnames({ 'check-valid': isCheckValid })}>
              <Component
                bus={bus}
                value={formulaVal}
                isCheckAttr={isCheckAttr}
                isCheckValid={isCheckValid}
                onChangeValue={this.handleCmpChange}
                others={others}
              />
            </div>
          )}
          {this.renderError()}
        </div>
      </UniversalComponent>
    )
  }
}
