import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import styles from './Describe.module.less'
import { Tooltip, Icon } from 'antd'

interface Props {
  field: fieldProps
}

interface fieldProps {
  value: string
  popupText: string
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'describeWithPopup'
  }
})
export default class DescribeWithPopup extends PureComponent<Props> {
  render() {
    const { field } = this.props
    return <div className={styles.describe}>
      {field.value}
      {!!field.popupText && <Tooltip
        placement="top"
        title={field.popupText}>
        <Icon
          className="enum-tooltip"
          type="question-circle-o"
          style={{ fontSize: 12, paddingTop: 4, marginLeft: 8, color: '#1d2b3d' }}
        />
      </Tooltip>}
    </div>
  }
}
