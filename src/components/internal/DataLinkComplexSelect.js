/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/25 下午3:38.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import styles from './DataLinkComplexSelect.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { getV } from '@ekuaibao/lib/lib/help'
import { get } from 'lodash'

@EnhanceConnect(state => ({
  list: state['@custom-specification'].defaultRuleList,
  entityValue: state['@custom-specification'].entityValue
}))
@EnhanceField({
  descriptor: {
    type: 'complex-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value) return callback(i18n.get('请选择赋值规则'))
    return callback()
  },
  wrapper: wrapper()
})
export default class ComplexSelect extends PureComponent {
  entityFields = []
  constructor(props) {
    super(props)
    this.state = {
      value: {
        key: props.value,
        label: ''
      }
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      this.setState({
        value: {
          key: nextProps.value,
          label: ''
        }
      })
    }
  }

  async componentDidMount() {
    const { entityValue } = this.props
    const result = await api.invokeService('@third-party-manage:get:entity:fields', { id: entityValue.id })
    this.entityFields = result
    // api.invokeService('@third-party-manage:get:entity:field:info:byId', entityValue.id)
  }

  handleEdit = line => {
    let { field, entityValue, settingType, currentComponent } = this.props
    let id = line.id
    const fieldName = get(currentComponent, 'field')
    console.log(this.entityFields)
    api
      .open('@third-party-manage:ConfigRulesModal', {
        value: entityValue,
        id,
        settingType,
        fields: this.entityFields,
        fieldName,
        isMultiSelectDatalinkField: field?.isMultiSelectDatalinkField
      })
      .then(result => {
        let entityId = field.entityId
        api.invokeService('@custom-specification:get:default:rule:list', entityId)
      })
  }

  handleAdd = value => {
    if (value.key === 'add') {
      let { entityValue, field, settingType, currentComponent } = this.props
      const fieldName = get(currentComponent, 'field')
      api
        .open('@third-party-manage:ConfigRulesModal', {
          value: entityValue,
          settingType,
          fields: this.entityFields,
          fieldName,
          isMultiSelectDatalinkField: field?.isMultiSelectDatalinkField
        })
        .then(result => {
          let entityId = field.entityId
          api.invokeService('@custom-specification:get:default:rule:list', entityId).then(items => {
            let selectValue = { key: result.id, label: '' }
            this.setState({ value: selectValue })
            let { onChange } = this.props
            onChange && onChange(selectValue.key)
          })
        })
    } else {
      let { onChange } = this.props
      onChange && onChange(value.key)
      this.setState({ value })
    }
  }

  render() {
    let { list } = this.props
    let { value } = this.state
    return (
      <div className={styles['intercon-select-wrapper']}>
        <ComSelect
          classNames="select-wrapper"
          value={value}
          list={list}
          emptyTitle={i18n.get('目前没有任何赋值规则')}
          title={i18n.get('新的赋值规则')}
          handleAdd={this.handleAdd}
          handleEdit={this.handleEdit}
          handleDelete={this.handleDelete}
        />
      </div>
    )
  }
}
