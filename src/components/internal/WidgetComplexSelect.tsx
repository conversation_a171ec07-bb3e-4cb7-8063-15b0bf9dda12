import { app } from '@ekuaibao/whispered'
import React, { useState, useEffect, useRef } from 'react'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { get } from 'lodash';
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import styles from './DataLinkComplexSelect.module.less'
interface GlobalWidgetFieldIF extends GlobalFieldIF {
  type: string
  entity: string
}
type IProps = {
  list: any
  value: any
  baseDataProperties: GlobalWidgetFieldIF[]
  onChange: (val: any) => void
  settingType: string
  currentComponent: any
  id: string
}
const WidgetComplexSelect: React.FC<IProps> = (props: IProps) => {
  const [list, setList] = useState(props.list)
  const [value, setValue] = useState({
    key: props.value,
    label: ''
  })
  const entityFields = useRef({
    fields: props.currentComponent?.fields,
    appletFields: props.currentComponent?.appletFields,
    name: props.currentComponent?.name,
    id: props.currentComponent?.referenceData
  })

  const fnGetList = async () => {
    if (list == void 0) {
      await fnUpdateList()
    }
  }
  useEffect(() => {
    fnGetList()
  }, [])
  useEffect(() => {
    setValue({
      key: props.value,
      label: ''
    })
  }, [props.value])

  const fnUpdateList = async () => {
    let { items } = await app.invokeService('@custom-specification:get:default:rule:list', props?.currentComponent?.referenceData)
    const environmentType = props?.id === 'appletAssignmentRule' ? 'applet' : 'web'
    items = items.filter(item => {
      const itemType = item.environmentType ?? 'web'
      return itemType === environmentType
    })
    setList(items)
  }
  //临时mock 类型 
  const FieldType = (item) => {
    const fieldId = item.fieldId && item.fieldId.slice(item.fieldId.lastIndexOf(':') + 1)
    const result = {
      TEXT: { type: 'text' },
      NUMBER: { type: 'number' },
      MONEY: { type: 'money' },
      DATE: { type: 'date' },
      DATE_RANGE: { type: 'dateRange' },
      STAFF: { type: 'ref', entity: 'organization.Staff' },
      PAYEE_INFO: { type: 'ref', entity: 'pay.PayeeInfo' },
      ENUM: { type: 'ref', entity: 'basedata.Enum.' + fieldId },
      SWITCH: { type: 'switcher' },
      ATTACHMENT: { type: 'list', elemType: { type: "attachment" } },
      DATA_LINK: { type: 'ref', entity: 'datalink.DataLinkEntity.' + fieldId },
      DATA_LINK_LIST: {
        type: 'list:datalink.DataLinkEntity', elemType: {
          type: "complex",
          entity: "datalink.DataLinkEntity." + fieldId
        }
      },
      DEPARTMENT: { type: 'ref', entity: 'organization.Department' },
      DIMENSION: { type: 'ref', entity: 'basedata.Dimension.' + fieldId },
      STAFF_LIST: { type: 'list', elemType: { type: 'ref', entity: 'organization.Staff' } },
      CITY: { type: 'ref', entity: 'basedata.city' },
      BOOLEAN: { type: 'boolean' },
      INVOICE: { type: 'invoice' }
    }
    return result[item.fieldType]
  }
  const fnMappingFieldType = (fields) => {
    return fields.map((item: any) => {
      const type = FieldType(item) || {}
      item = { ...item, name: item.key, ...type }
      return item
    }).filter(field => {
      return !!field.type
    })
  }
  const handleAdd = async (value) => {
    if (value.key === 'add') {
      const { settingType, currentComponent, onChange, id } = props
      const fieldName = get(currentComponent, 'field')
      const fields = id === 'appletAssignmentRule' ? fnMappingFieldType(entityFields.current?.appletFields) : fnMappingFieldType(entityFields.current?.fields)
      const res: { id: string } = await app.open('@third-party-manage:ConfigRulesModal', {
        value: { ...entityFields.current, fields },
        settingType,
        fields,
        fieldName,
        isWidget: true,
        environmentType: id === 'appletAssignmentRule' ? 'applet' : 'web' // 移动端组件启用联动赋值
      })
      const selectValue = { key: res.id, label: '' }
      setValue(selectValue)
      await fnUpdateList()
      onChange && onChange(selectValue.key)
    } else {
      const { onChange } = props
      onChange && onChange(value.key)
      setValue(value)
    }
  }

  const handleEdit = async (line) => {
    let { settingType, currentComponent, id: fieldId } = props
    let id = line.id
    const fieldName = get(currentComponent, 'label')
    const fields = fieldId === 'appletAssignmentRule' ? fnMappingFieldType(entityFields.current?.appletFields) : fnMappingFieldType(entityFields.current?.fields)
    await app.open('@third-party-manage:ConfigRulesModal', {
      value: { ...entityFields.current, fields },
      id,
      settingType,
      fields,
      fieldName,
      isWidget: true
    })
    await fnUpdateList()
  }

  return (
    <div className={styles['intercon-select-wrapper']}>
      <ComSelect
        classNames="select-wrapper"
        value={value}
        list={list}
        emptyTitle={i18n.get('目前没有任何赋值规则')}
        title={i18n.get('新的赋值规则')}
        handleAdd={handleAdd}
        handleEdit={handleEdit}
      />
    </div>
  )
}
// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'widget-complex-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value) return callback(i18n.get('请选择赋值规则'))
    return callback()
  },
  wrapper: wrapper()
  // @ts-ignore
})(EnhanceConnect((state: any) => ({
}))(WidgetComplexSelect))