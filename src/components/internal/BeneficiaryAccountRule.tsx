import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './BeneficiaryAccountRule.module.less'
import { app as api } from '@ekuaibao/whispered'
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

interface Props {
  onChange: Function
  value: any
  field: any
  form: any
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'beneficiaryAccountRule'
  },
  wrapper: wrapper()
})
export default class BeneficiaryAccountRule extends PureComponent<Props, State> {
  handleChange = e => {
    const { onChange } = this.props
    const beneficiaryType = e.target.value
    onChange && onChange(beneficiaryType)
  }

  render() {
    const { field, value = {}, form } = this.props
    const { tags, text, repaymentComponentType, disabled } = field
    const isCanLoan = form.getFieldValue('canLoan:value') || { checked: false }
    const isShow = repaymentComponentType === 'manualRepayment' ? true : isCanLoan.checked
    const cls = repaymentComponentType === 'manualRepayment' ? 'beneficiaryType-wrapper' : 'canLoan-wrapper'
    return (
      isShow && (
        <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
          <div className={styles[cls]}>
            <span className="beneficiaryType-title">{text}</span>
            <RadioGroup disabled={disabled} onChange={this.handleChange} value={value || 'ANY_ACCOUNT'} style={{ width: '100%' }}>
              {tags.map(v => {
                return (
                  <UniversalComponent
                    key={v.value}
                    uniqueKey={`customSpecification.pc.beneficiaryAccountRule.${v.value}`}
                  >
                    <Radio key={v.value} value={v.value} style={{ width: '100%' }}>
                      {v.label}
                    </Radio>
                  </UniversalComponent>
                )
              })}
            </RadioGroup>
          </div>
        </UniversalComponent>
      )
    )
  }
}
