import React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox, Radio, Icon } from 'antd'
import { Tooltip } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

type IProps = {
  value: any
  field: any
  form: any
  onChange: (val: any) => void
}
const CheckTravelCloseLoop = (props: IProps) => {
  let { value = {}, field, form, onChange } = props
  const { relatedConfig } = field
  let visible = true
  if (relatedConfig && form) {
    let { ability, value } = relatedConfig
    visible = form.getFieldValue(ability) === value
  }
  const handleChecked = e => {
    let newvalue = { ...value }
    newvalue.openCheck = e.target.checked
    if (!!!e.target.checked) {
      newvalue.isForbidSubmit = true
    }
    onChange && onChange(newvalue)
  }

  const handleChange = e => {
    let newvalue = { ...value }
    newvalue.isForbidSubmit = e.target.value
    onChange && onChange(newvalue)
  }

  return (
    visible && (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div style={{ marginTop: '-6px', marginLeft: '20px' }}>
          <Checkbox onChange={handleChecked} checked={value?.openCheck}>
            {field?.label}
            <Tooltip placement="topLeft" title={i18n.get('关联多个申请事项不生效')}>
              <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
            </Tooltip>
          </Checkbox>
          <div style={{ marginLeft: '20px' }}>
            {value?.openCheck && (
              <RadioGroup
                style={{ display: 'flex', flexDirection: 'column' }}
                onChange={handleChange}
                value={value?.isForbidSubmit === undefined ? true : value?.isForbidSubmit}
              >
                <Radio value={true} style={{ marginBottom: '10px' }}>
                  {i18n.get('申请单上行程未闭环，禁止提交单据')}
                </Radio>
                <Radio value={false}>{i18n.get('申请单上行程未闭环，允许提交单据')}</Radio>
              </RadioGroup>
            )}
          </div>
        </div>
      </UniversalComponent>
    )
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'checkbox:travel:closed'
  },
  wrapper: wrapper()
})(
  // @ts-ignore
  EnhanceConnect(state => {})(CheckTravelCloseLoop)
)
