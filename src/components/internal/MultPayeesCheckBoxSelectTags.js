import styles from './CheckBoxTags.module.less'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { findDOMNode } from 'react-dom'
import { defaultMode } from '../dynamic/SetMultiplePayee'
import { app as api } from '@ekuaibao/whispered'
import { showMessage } from '@ekuaibao/show-util'
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'multipayees:checkbox:select:tags'
  },
  wrapper: wrapper()
})
export default class CheckBoxSelectTags extends PureComponent {
  state = {
    items: [
      { active: true, id: defaultMode.detail, name: i18n.get('按明细') },
      { active: true, id: defaultMode.money, name: i18n.get('按金额') },
      { active: true, id: defaultMode.payee, name: i18n.get('按收款信息汇总明细金额') }
    ]
  }

  setChecked = (value, checked) => {
    return {
      ...value,
      isAll: checked,
    }
  }

  setIds = (value, ids) => {
    value = { ...value, ids }
    return value
  }

  valueSerialize = value => {
    if (!value) return { checked: false, ids: [], paymentPlanByApportion: false }
    return {
      checked: value.isAll,
      ids: value.ids,
      paymentPlanByApportion: value.paymentPlanByApportion,
    }
  }

  handleChecked = e => {
    const { onChange, value, form, field } = this.props
    const { mutualExclusion } = field
    const newValue = this.setChecked(value, e.target.checked)
    onChange?.(newValue)
    if (e.target.checked && mutualExclusion?.length > 0 && form) {
      const currentFormValue = form.getFieldsValue()
      const formValue = {}
      mutualExclusion.forEach(v => {
        if (currentFormValue[v.name] !== undefined && currentFormValue[v.name]!== v.value) {
          formValue[v.name] = v.value
          v.message && showMessage.info(v.message)
        }
      })
      if (Object.keys(formValue).length > 0) {
        form.setFieldsValue(formValue)
      }
    }
  }

  handleCheckApportionDetail = e => {
    const { onChange, value } = this.props
    onChange?.({ ...value, paymentPlanByApportion: e.target.checked })
  }

  handleSpecificationChange = ids => {
    const { onChange, value } = this.props
    const newIds = this.filterIds(ids)
    const newValue = this.setIds(value, newIds)
    if (!newIds.includes(defaultMode.detail)) {
      newValue.paymentPlanByApportion = false
    }
    onChange?.(newValue)
  }

  filterIds = ids => {
    const { value } = this.props
    const { items } = this.state
    const selectIds = value?.ids ?? []
    const disableIds = items.map(line => !line.active && line.id).filter(id => selectIds.indexOf(id) < 0)
    return ids.filter(id => disableIds.indexOf(id) < 0)
  }

  render() {
    const { field, value, form } = this.props
    const { checked, ids, paymentPlanByApportion } = this.valueSerialize(value, field)
    const { relatedConfig, disabled} = field
    const { items } = this.state
    let visible = true
    if (relatedConfig && form) {
      const { ability, value } = relatedConfig
      visible = form.getFieldValue(ability) === value
    }
    const showApportionCheckbox = ids?.includes(defaultMode.detail)
    if (!visible) return null
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div ref="multiple-select" className={styles.check_box_tags} style={relatedConfig ? { marginLeft: 20 } : {}}>
          <Checkbox checked={checked} onChange={this.handleChecked} disabled={disabled}>
            {field.label}
          </Checkbox>
          {checked && (
            <>
              <Select
                mode="multiple"
                value={ids}
                disabled={disabled}
                onChange={this.handleSpecificationChange}
                getPopupContainer={() => findDOMNode(this.refs['multiple-select'])}
              >
                {items.map(v => (
                  <Select.Option value={v.id} key={v.id}>
                    <div
                      style={{
                        cursor: !v.active ? 'not-allowed' : 'pointer',
                        color: !v.active ? "#b6b6b6" : "#333333"
                      }}
                    >
                      {v.name}
                    </div>
                  </Select.Option>
                ))}
              </Select>
              {showApportionCheckbox && (<Checkbox
                className="ml-20"
                disabled={disabled}
                checked={paymentPlanByApportion}
                onChange={this.handleCheckApportionDetail}>
                {i18n.get("是否包含分摊")}
              </Checkbox>)}
            </>
          )}
        </div>
      </UniversalComponent>
    )
  }
}
