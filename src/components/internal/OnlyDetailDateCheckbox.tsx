import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { ENUM_DATE_TYPE } from '../consts'
import styles from './RequiredFieldConfig.module.less'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'only-detail-date-checkbox'
  },
  // validator: (field, props) => (rule, value, callback) => {
  //   if (value && value.active && !value.formula) {
  //     return callback(i18n.get('请配置隐藏条件') ) 
  //   }
  //   return callback()
  // }
})
export default class OnlyDetailDateCheckbox extends PureComponent<any, any> {

  constructor(props) {
    super(props)
  }

  handleCheck = e => {
    const isChecked = e.target.checked
    const { onChange } = this.props
    onChange(isChecked)
  }

  renderError = () => {
    const { form, field } = this.props
    const { name} = field
    const { getFieldError } = form
    const error = getFieldError(name)
    if (!error) {
        return null
    }
    return (
        <div style={{ color: '#ff7c7c' }}>
            {error}
        </div>
    )
  }

  render() {
    const { field, value, form } = this.props
    const { text, size, style = {}, disabled = false } = field

    if (form.getFieldValue('dateTimeType') !== ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME) {
      return null
    }

    const isChecked = value
    return (
      <div className={styles['ekb-check']} data-cy={`ekb-check-${field.name}`} style={...style}>
        <div className='mb-8'>
          <Checkbox onChange={this.handleCheck} disabled={disabled} checked={isChecked}>
            <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
          </Checkbox>
        </div>
        {this.renderError()}
      </div>
    )
  }
}
