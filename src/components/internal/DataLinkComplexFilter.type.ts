/**
 *  Created by gym on 2019-07-30 14:51.
 */

export interface FieldInterface {
  hiddenLabel: boolean
  label: string
  name: string
  size: string
  type: string
  entityId?: string
  entityInfo: EntityInfoInterface
  entityName?: string
  templateFields?: TempFieldsInterface[]
  placeholder?: string
  optional?: boolean
  newStyle?: boolean

  disabled?: boolean
}

export interface TempFieldsInterface {
  dataType: DataTypeInterface
  defaultValue: DataTypeInterface
  dependence: null
  editable: boolean
  field: string
  id: string
  label: string
  multiple: boolean
  optional: boolean
  placeholder: string
  priority: []
  selectRange: string
  showInDetails: boolean
  type: string
  children?: any
  copyType?: string
  name?: string
}

interface DataTypeInterface {
  type?: string
  entity?: string
}

interface EntityInfoInterface {
  fields?: FieldsInterface[]
  planned?: PlannedInterface[]
  id?: string
}

export interface FieldsInterface {
  name: string
  label: string
  active: boolean
  ability?: string
  canAsDimension?: boolean
  dataType: DataTypeInterface
  id?: string
}

interface PlannedInterface {
  id: string
  version: number
  active: boolean
  createTime: number
  updateTime: number
  corporationId: string
  staffId: string
  dataLinkEntityId: string
  name: string
  showProgressBar: boolean
  plannedMoneyField: string
  occupyMoneyField: string
  controlType: string
}

interface OperatorValue {
  label: string
  value: string
}

interface FormatFieldInterface {
  version?: number
  createTime?: number
  updateTime?: number
  corporationId?: string
  staffId?: string
  dataLinkEntityId?: string
  showProgressBar?: boolean
  plannedMoneyField?: string
  occupyMoneyField?: string
  controlType?: string
  value: string
  children?: OperatorValue[]
  id?: string
  name: string
  label: string
  active?: boolean
  ability?: string
  canAsDimension?: boolean
  dataType: DataTypeInterface
  type?: string
}

export interface InterfaceMate {
  active: boolean
  components: FormatFieldInterface
  configs: any[]
  createTime: number
  id: string
  name: string
  type: string
  updateTime: number
  version: number
  visibility: any
}
