@import '~@ekuaibao/eui-styles/less/token.less';

.detailVisibleCheckBox {
  .font-size-2;
  color: @color-black-1;
  margin-bottom: @space-4;
  :global {
    .ant-checkbox-wrapper {
      .font-size-2;

    }
    .flowNodeRole-wrap {
      .detailVisibleCheckBox-item;
    }
    .detailVisibleCheckBox-item {
      margin-left: 20px;
      margin-top: @space-4;
    }
    .select-tag-input {
      padding: 4px 0 4px 5px;
      border: solid 1px #d6d6d6;
      border-radius: 4px;
      width: 100%;
      overflow-y: auto;
      max-height: 150px;
      cursor: pointer;
      .placeholder {
        color: @color-black-1;
      }
    }
  }
}
