/**************************************************
 * Created by nany<PERSON>ingfeng on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, InputNumber} from 'antd'
import styles from './BillExceedCheckbox.module.less'
import { get } from 'lodash'

@EnhanceField({
  descriptor: {
    type: 'bill-exceed-checkbox'
  }
})
export default class CheckBox extends PureComponent {
  constructor(props) {
    super(props)
    const amountChecked = get(props?.value, 'amountChecked', false)
    const invoiceAmount = get(props?.value, 'invoiceAmount', 0)
    this.state={
      amountChecked: amountChecked,
      invoiceAmount: invoiceAmount
    }
  }

  componentWillReceiveProps(np) {
    if (np.value !== this.props.value) {
      const amountChecked = get(np?.value, 'amountChecked', false)
      const invoiceAmount = get(np?.value, 'invoiceAmount', 0)
      this.setState({
        amountChecked: amountChecked,
        invoiceAmount: invoiceAmount
      })
    }
  }
  
  handleChange = e => {
    const { onChange } = this.props
    const { checked } = e.target
    const {invoiceAmount} = this.state
    const exceedConfigs ={
      amountChecked: checked,
      invoiceAmount: invoiceAmount
    }
    this.setState({
      amountChecked: checked
    })
    onChange && onChange(exceedConfigs)
  }

  handleInputCount = count => {
    const { onChange } = this.props
    const {amountChecked } = this.state
    const exceedConfigs ={
      amountChecked: amountChecked,
      invoiceAmount: count
    }
    this.setState({
      invoiceAmount: count
    })
    onChange && onChange(exceedConfigs) 
}

  render() {
    const { field, form, value } = this.props
    const {
      size,
      disabled = false
    } = field

    const {amountChecked, invoiceAmount} = this.state
    return (
      <div className={styles['ekb-check']}>
         <Checkbox onChange={this.handleChange} disabled={disabled} checked={amountChecked} />
          <span className={size === 'large' ? 'text-large' : 'text-normal'}>{i18n.get('当有发票金额超')}</span>
          <InputNumber 
            className="check-input" 
            value={invoiceAmount}
            min={0}
            precision={0}
            disabled={!amountChecked} 
            onChange={this.handleInputCount}
          />
          <span >{i18n.get('时，需必填')}</span>
      </div>
    )
  }
}
