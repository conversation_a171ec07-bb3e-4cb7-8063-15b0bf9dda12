/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { checkPriority, getPriorityValue } from '@ekuaibao/lib/lib/checkFieldPriority'
import styles from './Priority.module.less'
import { app } from '@ekuaibao/whispered'
const { UniversalComponent } = app.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    name: 'priority'
  }
})
export default class Priority extends PureComponent {
  constructor(props) {
    super(props)
  }

  onChange = e => {
    let {
      form,
      currentComponent: { type: compType },
      onChange,
      field
    } = this.props
    let editable = field.editable
    let { checked } = e.target
    if (!checked) {
      // 如果是取消选择，并且是配置收款信息字段，则将可编辑设置成默认的可编辑状态。
      if (compType === 'payeeInfo') {
        editable = true
        setTimeout(() => {
          form.setFieldsValue({
            editable
          })
        }, 0)
      }
    }
    let newValue = getPriorityValue(checked, editable)
    onChange && onChange(newValue)
  }

  render() {
    let { field, value } = this.props
    let { text, editable, label, disabled } = field
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div className={styles.priority} data-cy="ekb-priority">
          <div className={'new_label'}>{label}</div>
          <Checkbox disabled={disabled} onChange={this.onChange} checked={checkPriority(value, editable)}>
            <span className={'text-large'}>{text}</span>
          </Checkbox>
        </div>
      </UniversalComponent>
    )
  }
}
