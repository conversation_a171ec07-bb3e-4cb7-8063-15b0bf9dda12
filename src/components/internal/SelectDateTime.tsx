import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { Select } from 'antd'
import { DATE_TYPE, ENUM_DATE_TYPE, DATE_TYPE_RANGE } from '../consts'
import { wrapper } from '../layout/FormWrapper'

interface IProps {
  readonly?: boolean
  value: string
  currentComponent: {
    withTime: string
    type: string
  }
  onChange: (key: string) => void
}

interface IState {
  value: string
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'select-date-time'
  },
  wrapper: wrapper()
})
export default class SelectDateTime extends PureComponent<IProps, IState>{

  constructor(props) {
    super(props)

    this.state = {
      value: this.getValue()
    }
  }

  onChange = (value: string) => {
    const { onChange } = this.props
    onChange && onChange(value)
    this.setState({
      value
    })
  }

  getValue = (): string => {
    const { value, currentComponent, onChange } = this.props
    const withTime = currentComponent?.withTime

    if(value) {
      return value
    }

    if(withTime) {
      onChange(ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME)
      return ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME
    }

    return ENUM_DATE_TYPE.YEAR_MONTH_DAY
  }

  render() {
    const {
      field
    } = this.props
    const { disabled = false } = field ?? {}
    const type = this.props.currentComponent?.type
    return (
      <Select
        disabled={disabled}
        onChange={this.onChange}
        defaultValue={this.state.value}
        style={{ width: '100%' }}
        size="large"
      >
        {
          type === 'dateRange' ?
            DATE_TYPE_RANGE.map(item => <Select.Option key={item.k} value={item.k}>{i18n.get(item.v)}</Select.Option>) :
            DATE_TYPE.map(item => <Select.Option key={item.k} value={item.k}>{i18n.get(item.v)}</Select.Option>)
        }
      </Select>
    )
  }
}