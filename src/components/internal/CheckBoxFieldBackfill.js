/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 06/07/2017 13:35.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { app } from '@ekuaibao/whispered'
import { Checkbox, TreeSelect } from '@hose/eui'
import { cloneDeep } from 'lodash'
import { wrapper } from '../layout/FormWrapper'
import styles from './CheckBoxFieldBackfill.module.less'
import { Fetch } from '@ekuaibao/fetch'
import { get, uniq } from 'lodash'

@EnhanceField({
  descriptor: {
    type: 'checkbox:field:backfill'
  },
  validator: (_field, _props) => (_rule, value, callback) => {
    const { isWriteBackContractId, writeDataLinkFields } = value
    if (isWriteBackContractId && !writeDataLinkFields?.length) {
      return callback(i18n.get('字段不能为空'))
    }
    callback()
  },
  wrapper: wrapper()
})
export default class CheckBoxFieldBackfill extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { treeData: [], allFieldsMap: {} }
  }
  componentDidMount() {
    app.on('update:field:backfill:tree:data', this.updateTreeData)
    this.getTreeData(this.props)
  }

  componentWillUnmount() {
    app.un('update:field:backfill:tree:data', this.updateTreeData)
  }

  updateTreeData = () => {
    const { specification, value } = this.props
    const { allFieldsMap } = this.state
    const list = specification.components.filter(item => Object.keys(allFieldsMap).includes(item.field))
    const treeData = list.map(item => {
      const { field } = item
      const contractFields = allFieldsMap[field]?.contractFields || []
      return {
        title: this.getLabel(item),
        value: item.field,
        key: item.field,
        children: contractFields.map(o => ({
          title: o.label,
          value: `${field}/${o.field}`,
          key: `${field}/${o.field}`
        }))
      }
    })
    const valuelist = uniq(value.writeDataLinkFields?.map(item => item.dataLinkComponentField) || []) //所有选中的值
    const treeDataValueList = treeData.map(item => item.value)
    valuelist.forEach(item => {
      if (!treeDataValueList.includes(item)) {
        const data = allFieldsMap[item]
        const selectData = value.writeDataLinkFields
          ?.filter(o => o.dataLinkComponentField === item)
          .map(o => o.contractField)
        if (data) {
          treeData.push({
            title: data.dataLinkComponentField.label,
            value: data.dataLinkComponentField.field,
            key: data.dataLinkComponentField.field,
            children: data.contractFields
              .filter(item => selectData.includes(item.field))
              .map(o => ({
                title: `${o.label}(已删除)`,
                value: `${data.dataLinkComponentField.field}/${o.field}`,
                key: `${data.dataLinkComponentField.field}/${o.field}`
              }))
          })
        }
      }
    })
    this.setState({ treeData })
  }

  getLabel = field => {
    if (window?.i18n?.currentLocale === 'en-US') {
      return (field.enLabel ? field.enLabel : field.cnLabel) || field.label
    }
    return field.cnLabel || field.label
  }
  getTreeData = async props => {
    const { specification } = props
    if (specification?.id) {
      const dispatch = await Fetch.GET('/api/v1/contractsettle/getWriteBackFieldMapping', {
        appId: specification.appId
      })
      const list = dispatch?.items || []
      const allFieldsMap = list.reduce((result, item) => {
        result[get(item, 'dataLinkComponentField.field')] = item
        return result
      }, {})

      this.setState({ allFieldsMap }, () => {
        this.updateTreeData()
      })
    }
  }

  updateChecked = (value, checked) => {
    const newValue = cloneDeep(value)
    newValue.isWriteBackContractId = checked
    newValue.writeDataLinkFields = []
    return newValue
  }

  handleChecked = e => {
    const { onChange, value = {} } = this.props
    const val = this.updateChecked(value, e.target.checked)
    onChange && onChange(val)
  }
  handleOnChange = value => {
    const { onChange } = this.props
    onChange &&
      onChange({
        isWriteBackContractId: true,
        writeDataLinkFields: value.map(item => ({
          dataLinkComponentField: item.split('/')[0],
          contractField: item.split('/')[1]
        }))
      })
  }
  getDataLinkComponentField = field => {
    const { treeData } = this.state
    return treeData.find(item => !!item.children.find(o => o.value === field))?.value || ''
  }
  valueParse = value => {
    if (!value) {
      return { isWriteBackContractId: false, writeDataLinkFields: [] }
    }
    return {
      isWriteBackContractId: value.isWriteBackContractId,
      writeDataLinkFields:
        value?.writeDataLinkFields?.map(item => `${item.dataLinkComponentField}/${item.contractField}`) || []
    }
  }
  render() {
    let { field, value } = this.props
    const { disabled } = field ?? {}
    const { treeData } = this.state
    const newValue = this.valueParse(value)
    return (
      <div className={styles['check_box_field_backfill']}>
        <Checkbox onChange={this.handleChecked} checked={value?.isWriteBackContractId} disabled={disabled}>
          {field.label}
        </Checkbox>
        {value?.isWriteBackContractId && (
          <TreeSelect
            value={newValue?.writeDataLinkFields}
            style={{ width: '100%' }}
            treeData={treeData}
            treeCheckable={true}
            onChange={this.handleOnChange}
            showCheckedStrategy={TreeSelect.SHOW_CHILD}
          />
        )}
      </div>
    )
  }
}
