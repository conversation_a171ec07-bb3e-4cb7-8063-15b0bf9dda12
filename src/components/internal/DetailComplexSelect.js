import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import styles from './DataLinkComplexSelect.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import { Spin, Checkbox } from 'antd'
import { Modal } from '@hose/eui'

@EnhanceConnect(state => ({
  list: state['@custom-specification'].feeTypeImportRuleList,
  entityValue: state['@custom-specification'].entityValue,
  userInfo: state['@common'].userinfo.data
}))
@EnhanceField({
  descriptor: {
    type: 'detail-complex-select'
  },
  wrapper: wrapper()
})
export default class ComplexSelect extends PureComponent {
  constructor(props) {
    super(props)
    let {
      billType,
      orgData: { orgId },
      canEditConfigImportExpenseModal = true,
      specification
    } = props
    api.invokeService('@custom-specification:get:feetypeImportRule', { formType: billType, orgId })
    api.invokeService('@custom-specification:get:feetypeImportSource', {
      formType: billType,
      appId: specification.appId
    })
    this.state = {
      value: {
        key: props.value,
        label: ''
      },
      spinning: false,
      orgVisible: false,
      canEditConfigImportExpenseModal
    }
  }

  componentDidMount = async () => {
    const hasOrgCharge =
      (await api.invokeService('@custom-specification:check:power:code', { powerCode: '110224' })) || {}
    const orgVisible = hasOrgCharge.value && window.isNewHome
    this.setState({ orgVisible })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value || this.props.list !== nextProps.list) {
      this.setState({
        value: {
          key: nextProps.value,
          label: ''
        }
      })
    }
  }

  handleEdit = line => {
    let {
      templateFields,
      entityValue,
      settingType,
      billType,
      orgData: { orgId, orgType },
      list,
      specification
    } = this.props
    const { canEditConfigImportExpenseModal } = this.state
    let id = line.id
    this.setState({ spinning: true }, _ => {
      api
        .invokeService('@custom-specification:get:feetypeImportList:byId', {
          id,
          formType: billType,
          appId: specification?.appId
        })
        .then(res => {
          this.setState({ spinning: false })
          // 编辑时，新首页且开了多组织的charge，就把join回来的数据传下去
          const organizationData = this.state.orgVisible ? res.value.organizationData : undefined
          api
            .open('@custom-specification:ConfigImportExpenseModal', {
              id,
              list,
              orgId,
              orgType,
              canEdit: canEditConfigImportExpenseModal,
              billType,
              settingType,
              templateFields,
              organizationData,
              type: 'EDIT',
              value: entityValue,
              info: res.value
            })
            .then(result => {
              result.formType = billType
              api.invokeService('@custom-specification:set:feetypeImportList', { orgId, result }).then(_ => {
                api.invokeService('@custom-specification:get:feetypeImportRule', { orgId, formType: billType })
              })
            })
        })
    })
  }

  handleAdd = value => {
    if (value.key === 'add') {
      let {
        templateFields,
        settingType,
        entityValue,
        billType,
        specification,
        orgData: { orgId }
      } = this.props
      // id为''时代表新建
      api
        .open('@custom-specification:ConfigImportExpenseModal', {
          billType,
          settingType,
          templateFields,
          value: entityValue,
          id: '',
          type: 'ADD',
          canEdit: true,
          specification
        })
        .then(result => {
          result.formType = billType
          api.invokeService('@custom-specification:set:feetypeImportList', { orgId, result }).then(res => {
            let { id } = res
            api.invokeService('@custom-specification:get:feetypeImportRule', { orgId, formType: billType }).then(_ => {
              let { list } = this.props
              let value = list.find(item => item.id === id)
              if (value) {
                let selectValue = { key: value.id, label: value.name }
                let { onChange } = this.props
                onChange && onChange(selectValue.key)
                this.setState({ selectValue })
              }
            })
          })
        })
    } else {
      let { onChange } = this.props
      onChange && onChange(value.key)
      this.setState({ value })
    }
  }

  renderCheckBox(feeTypeCanImport) {
    //这里没有做交互，取feeTypeCanImport，且不可更改；
    //取feeTypeCanImport: 报销单为true，申请单为false
    return (
      <Checkbox disabled={true} checked={feeTypeCanImport}>
        <span className="fs-14">{i18n.get('费用明细允许导入')}</span>
      </Checkbox>
    )
  }

  fnGetDefalutID = () => {
    const { list } = this.props
    let line
    if (list) {
      line = list.find(line => line.id.indexOf('DEFAULT_ID') >= 0)
    }
    return line
  }

  handleGoToInvoiceMatchRule = () => {
    const { userInfo, parentBus } = this.props
    if (userInfo?.permissions?.includes('INVOICE_MATCH_RULE')) {
      if (parentBus && parentBus.has('check:specification:modify')) {
        parentBus.invoke('check:specification:modify', true).then(isEqual => {
          if (isEqual) {
            api.go('/invoice-match-rule')
          } else {
            Modal.confirm({
              title: i18n.get('提示'),
              content: i18n.get('请先对当前模版内容进行保存'),
              okText: i18n.get('保存并跳转'),
              cancelText: i18n.get('取消'),
              onOk: () => {
                parentBus.invoke('specification:manage:save:specification', true).then(_ => {
                  api.go('/invoice-match-rule')
                })
              }
            })
          }
        })
      } else {
        api.go('/invoice-match-rule')
      }
    } else {
      Modal.warning({
        title: i18n.get('您没有在发票规范下的「发票匹配规则」菜单权限，如需获取请联系管理员')
      })
    }
  }

  render() {
    let { list, field, billType } = this.props
    let { value, spinning, canEditConfigImportExpenseModal } = this.state
    const { feeTypeCanImport } = field
    if (value && (value.key === 'DEFAULT_ID' || !value.key)) {
      value.key = billType === 'expense' ? `DEFAULT_ID_${Fetch.ekbCorpId}` : `DEFAULT_ID_${Fetch.ekbCorpId}_${billType}`
      const defaultLine = this.fnGetDefalutID()
      if (defaultLine) {
        value.key = defaultLine.id
      }
    }
    return (
      <div className={styles['intercon-select-wrapper']}>
        {/*{this.renderCheckBox(feeTypeCanImport)}*/}
        {feeTypeCanImport && (
          <Spin spinning={spinning}>
            <ComSelect
              classNames="select-wrapper"
              value={value}
              list={list}
              canEdit={canEditConfigImportExpenseModal}
              emptyTitle={i18n.get('目前没有任何导入消费列表')}
              title={i18n.get('新的导入消费列表')}
              handleAdd={this.handleAdd}
              handleEdit={this.handleEdit}
            />
          </Spin>
        )}
        {feeTypeCanImport && (
          <div className="intercon-select-wrapper-invoice-match">
            <div className="intercon-select-wrapper-invoice-match-title">{i18n.get('发票内容与费用类型匹配')}</div>
            <div
              className="intercon-select-wrapper-invoice-match-link"
              onClick={this.handleGoToInvoiceMatchRule}
            >{`>>${i18n.get('配置匹配规则')}`}</div>
          </div>
        )}
      </div>
    )
  }
}
