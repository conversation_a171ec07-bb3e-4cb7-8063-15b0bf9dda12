/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/11.
 */
import styles from './CheckBoxTags.module.less'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'

@EnhanceField({
  descriptor: {
    type: 'checkbox:select:tags'
  },
  wrapper: wrapper()
})
export default class CheckBoxSelectTags extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      dataSourceList: []
    }
    this.isRequisition = props.field.name === 'apply:requisitionList'
  }
  componentDidMount() {
    const typeList = this.isRequisition ? ['requisition'] : ['requisition', 'loan']
    api.invokeService('@custom-specification:get:all:specifications:by:types', typeList).then(res => {
      this.setState({ dataSourceList: res.items })
    })
  }

  setChecked = (value, checked) => {
    let newValue = { ...value }
    newValue.isAll = !checked
    newValue.ids = []
    return newValue
  }

  setIds = (value, ids) => {
    let newValue = { ...value }
    newValue.ids = ids
    return newValue
  }

  valueSerialize = value => {
    if (!value) return { checked: false, ids: [] }
    return { checked: !value.isAll, ids: value.ids.filter(item => item) }
  }

  handleChecked = e => {
    let { onChange, value } = this.props
    let newValue = this.setChecked(value, e.target.checked)
    onChange && onChange(newValue)
  }

  handleSpecificationChange = ids => {
    const newIds = this.filterSelectSpecification(ids)
    let { onChange, value } = this.props
    let newValue = this.setIds(value, newIds)
    onChange && onChange(newValue)
  }

  filterSelectSpecification = ids => {
    const selectIds = this.props.value.ids || []
    const disableIds = this.state.dataSourceList
      .map(line => {
        if (!line.active) {
          return line.id
        }
      })
      .filter(id => selectIds.indexOf(id) < 0)
    return ids.filter(id => disableIds.indexOf(id) < 0)
  }

  render() {
    let { field, value, form } = this.props
    let { checked, ids } = this.valueSerialize(value, field)
    let { relatedConfig } = field
    const { dataSourceList } = this.state
    const { disabled } = field ?? {}

    let visible = true
    if (relatedConfig && form) {
      let { ability, value } = relatedConfig
      visible = form.getFieldValue(ability) === value
    }
    return (
      visible && (
        <div
          className={styles.check_box_tags}
          style={visible && relatedConfig ? { marginTop: -25, marginLeft: 20 } : {}}
        >
          <Checkbox checked={checked} onChange={this.handleChecked} disabled={disabled}>
            {field.label}
          </Checkbox>
          {checked && (
            <Select mode="multiple" value={ids} optionFilterProp="name" onChange={this.handleSpecificationChange} disabled={disabled}>
              {dataSourceList.map(v => {
                return (
                  <Select.Option value={v.id} key={v.id} name={v.name}>
                    <div
                      style={{
                        cursor: !v.active ? 'not-allowed' : 'pointer',
                        color: !v.active ? '#b6b6b6' : '#333333'
                      }}
                    >
                      {v.active ? v.name : i18n.get(`{__k0}(已删除)`, { __k0: v.name })}
                    </div>
                  </Select.Option>
                )
              })}
            </Select>
          )}
        </div>
      )
    )
  }
}
