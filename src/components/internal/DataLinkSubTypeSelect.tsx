import React, { Component } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import styles from './DataLinkSubTypeSelect.module.less'
import { Checkbox, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
const Option = Select.Option
interface IProps {
  field: any
  value: string
  onChange(params: any): void
}
interface IState {
  selectedValue: string
  checked: boolean
  dataLinkData: any[]
}

const refDataMap: Record<string, any> = {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'dataLinkSubTypeIdSelect'
  },
  initialValue(props) {
    const { field = {} } = props
    return field?.defaultValue?.value || ''
  },
  validator: field => (rule, value, callback) => {
    if (value === null) {
      return callback(i18n.get('请选择要显示的类型'))
    }
    callback()
  },
  wrapper: wrapper(false, false, false, true)
})
class DataLinkSubTypeSelect extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    const { value } = props
    this.state = {
      selectedValue: value,
      checked: !!value,
      dataLinkData: []
    }
  }

  componentDidMount(): void {
    this.fetchDataLinkData()
  }

  fetchDataLinkData = async () => {
    const id = this.props?.field?.referenceData || ''
    let dataLinkData = refDataMap[id]
    if (!dataLinkData) {
      const { items = [] } = await api.invokeService('@custom-specification:search:child:entity', { id })
      dataLinkData = items
      refDataMap[id] = items
    }
    this.setState({ dataLinkData })
  }

  handleChange = e => {
    const { checked } = e.target
    this.setState({ checked })
    if (checked) {
      this.fnOnChange(null)
    } else {
      this.handleSelect('')
    }
  }
  handleSelect = selectedValue => {
    this.setState({ selectedValue })
    this.fnOnChange(selectedValue)
  }

  fnOnChange = val => {
    const { onChange } = this.props
    onChange?.(val)
  }

  render() {
    const { field } = this.props
    const { name, label, size } = field
    const { checked, selectedValue, dataLinkData } = this.state
    const className = field?.onlySelectReferenceData ? styles['select-wrapper-data-link-row'] : ''
    return (
      <div className={`${styles.selectWrapper} ${className}`} data-cy={`ekb-check-${name}`}>
        {field?.onlySelectReferenceData ? (
          <span className={size === 'large' ? 'text-large' : 'text-normal'}>{label}</span>
        ) : (
          <Checkbox onChange={this.handleChange} checked={checked}>
            <span className={size === 'large' ? 'text-large' : 'text-normal'}>{label}</span>
          </Checkbox>
        )}
        {(selectedValue || checked) && (
          <div className="select-wrapper">
            <Select style={{ width: '100%' }} value={selectedValue} onChange={this.handleSelect}>
              {dataLinkData.map(item => (
                <Option key={item.id} value={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
            {field?.showReferenceDataNote && <span className={'show-note'}>{field.showReferenceDataNote}</span>}
          </div>
        )}
      </div>
    )
  }
}

export default DataLinkSubTypeSelect
