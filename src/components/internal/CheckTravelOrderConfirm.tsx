import React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox, Radio, Icon } from 'antd'
import { Tooltip } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require<any>('@elements/StandardVersionComponent')

type IProps = {
  value: any
  field: any
  form: any
  onChange: (val: any) => void
}
const CheckTravelOrderConfirm = (props: IProps) => {
  let { value = {}, field, form, onChange } = props
  const { relatedConfig } = field
  let visible = true
  if (relatedConfig && form) {
    let { ability, value } = relatedConfig
    visible = form.getFieldValue(ability) === value
  }
  const handleChecked = e => {
    let newvalue = { ...value }
    newvalue.openCheck = e.target.checked
    if (!!!e.target.checked) {
      newvalue.isForbidSubmit = true
    }
    onChange && onChange(newvalue)
  }

  const handleChange = e => {
    let newvalue = { ...value }
    newvalue.isForbidSubmit = e.target.value
    onChange && onChange(newvalue)
  }

  return (
    visible && (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div style={{ marginTop: '-6px', marginLeft: '20px' }}>
          <Checkbox onChange={handleChecked} checked={value?.openCheck}>
            {field?.label}
            <Tooltip placement="topLeft" title={i18n.get('关联多个申请事项不生效')}>
              <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
            </Tooltip>
          </Checkbox>
          <div style={{ marginLeft: '20px' }}>
            {value?.openCheck && (
              <RadioGroup
                style={{ display: 'flex', flexDirection: 'column' }}
                onChange={handleChange}
                value={value?.isForbidSubmit === undefined ? true : value?.isForbidSubmit}
              >
                <Radio value={true} style={{ marginBottom: '10px' }}>
                  {i18n.get('申请单关联订单未全部确认，禁止提交')}
                </Radio>
                <Radio value={false}>{i18n.get('申请单关联订单未全部确认，允许提交')}</Radio>
              </RadioGroup>
            )}
          </div>
        </div>
      </UniversalComponent>
    )
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'checkbox:order:confirmed'
  },
  wrapper: wrapper()
})(
  // @ts-ignore
  EnhanceConnect(state => {})(CheckTravelOrderConfirm)
)
