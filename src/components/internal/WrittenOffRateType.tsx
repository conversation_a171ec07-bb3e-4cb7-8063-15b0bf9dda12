import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './WrittenOffRateType.module.less'
const RadioGroup = Radio.Group

interface Props {
  onChange: Function
  value: any
  field: any
  form: any
}

interface State { }

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'writtenOffRateType'
  },
  wrapper: wrapper()
})
export default class WrittenOffRateType extends PureComponent<Props, State> {
  handleChange = e => {
    const { onChange } = this.props
    const value = e.target.value
    onChange && onChange(value)
  }

  render() {
    const { field, value = 'REAL_TIME', form } = this.props
    const { tags, text, disabled, name } = field
    const foreignCurrency = name === 'writtenOff:crossCurrencyWrittenOffRateType'
      ? form.getFieldValue('writtenOff:crossCurrencyWrittenOff')
      : form.getFieldValue('writtenOff:foreignCurrency')
    return (
      foreignCurrency ? (
        <div className={styles['writtenOffRateType-wrapper']}>
          <span className="writtenOffRateType-title">{text}</span>
          <RadioGroup onChange={this.handleChange} value={value} disabled={disabled} style={{ width: '100%' }}>
            {tags.map(v => {
              return (
                <Radio key={v.value} value={v.value} style={{ width: '100%' }}>
                  {v.label}
                </Radio>
              )
            })}
          </RadioGroup>
        </div>
      ) : null
    )
  }
}
