import React, { PureComponent } from 'react'
import { Checkbox, Tooltip, Icon } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import styles from './DepRuleCheckBox.module.less'
import { wrapper } from '../layout/FormWrapper'

interface IProps {
  field: any
  bus: any
  value: any
  onChange: Function
  billType?: string
  orgData?: any
  specificationId?: string
}

interface IState {
  isChecked: boolean
}
let ERROR_FLAG = false
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'depRule-checkbox'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: Function) => {
    const name = props.field.name
    if ((name === 'dependence' || name === 'autoDependence') && ERROR_FLAG) {
      const findError = value?.dependencesItems?.find(v => !v.dependenceId || !v.roleDefId || !v.direction)
      if (findError) {
        return callback(i18n.get('请填写依赖详情'))
      }
      return callback()
    }
    ERROR_FLAG = true

    return callback()
  },
  wrapper: wrapper()
})
export default class DepRuleCheckBox extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
  }
  // componentDidMount() {
  //   const { value, field, onChange } = this.props
  //   const { name } = field

  //   if (name === 'selectRange' && !value) {
  //     onChange && onChange('all')
  //   }
  // }

  handleChangeCheckbox = (e: any) => {
    const {
      field: { Component, name },
      onChange,
      bus
    } = this.props
    const { checked } = e.target
    if (name === 'selectRange') {
      onChange && onChange(checked ? 'leaf' : 'all')
    }
    if (name === 'dependence' || name === 'autoDependence') {
      if (checked) {
        ERROR_FLAG = false
      }
      bus.emit('field:update:optional', checked, this.props.field)
      onChange && onChange([])
    }
    if (name === 'allowExternalStaff') {
      onChange && onChange(checked)
    }
  }
  dependenceChange = () => {
    ERROR_FLAG = false
  }
  render() {
    const { field, bus, value = [], onChange, billType, orgData, specificationId } = this.props
    const { name, text, Component, style, others, disabled } = field
    const isChecked =
      (name === 'selectRange' && value === 'leaf') ||
      (name === 'dependence' && value) ||
      (name === 'autoDependence' && value) ||
      (name === 'allowExternalStaff' && value)
    return (
      <div className={styles['depRange-wrapper']} style={style}>
        <div>
          {name !== 'dependence' && name !== 'autoDependence' && (
            <Checkbox
              disabled={disabled}
              className={styles['depRange-checkbox']}
              checked={isChecked}
              onChange={this.handleChangeCheckbox}
            >
              {text}
            </Checkbox>)}
          {/* {name === 'dependence' && (
            <Tooltip
              placement="bottom"
              title={i18n.get(
                '取「所依赖的字段」对应的「档案关系」用于配置字段间的联动取值范围，如档案关系中配置员工A和项目B的档案关系，则项目B的值可通过依赖员工A的值取到其所负责的项目（即档案关系）获取对应的值或其范围。'
              )}
            >
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )} */}
        </div>

        {isChecked && Component && (
          <Component
            disabled={disabled}
            specificationId={specificationId}
            bus={bus}
            value={value}
            {...others}
            handleDependenceCheck={this.dependenceChange}
            billType={billType}
            onChange={onChange}
            orgData={orgData}
            name={name}
          />
        )}
      </div>
    )
  }
}
