/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/25 下午3:38.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { default as ComSelect } from '../../elements/select/ComplexSelectMultiple'
import styles from './DataLinkComplexSelect.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { observer } from 'mobx-react'
import { inject } from '@ekuaibao/react-ioc'

interface SelectConnect {
  '@custom-specification': any
}
interface SelectState {
  specificationIds: any[]
  [key: string]: any
  valueState: any[]
}
interface SelectProps {
  value: string[]
  field: any
  entityValue: any
  settingType: any
  [key: string]: any
}

@EnhanceConnect((state: SelectConnect) => ({
  list: state['@custom-specification'].defaultRuleList,
  entityValue: state['@custom-specification'].entityValue,
  apportionSpecList: state['@custom-specification'].apportionSpecifications
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'expense-complex-select-amortize'
  },
  // // @ts-ignore
  validator: (field: any, props: SelectProps) => (rule: any, value: string[], callback: Function) => {
    if (!value || !value.length || !props.apportionSpecList.filter((item: any) => !!~value.indexOf(item.id)).length) {
      return callback(i18n.get('请选择摊销规则'))
    }
    return callback()
  },
  wrapper: wrapper()
})
@observer
export default class ExpenseComplexSelectAmortize extends PureComponent<SelectProps, SelectState> {
  @inject('permission') permission: any

  constructor(props: SelectProps) {
    super(props)
    const { specificationIds = [] } = props.currentComponent
    this.state = {
      valueState: specificationIds,
      specificationList: [],
      specificationIds: []
    }
  }

  componentWillReceiveProps(nextProps: SelectProps) {
    if (this.props.value !== nextProps.value) {
      this.setState({
        value: {
          key: nextProps.value,
          label: ''
        }
      })
    }
  }
  componentDidMount() {
    this.getSpecificationList()
  }
  getSpecificationList = (needOnchange = false) => {
    const { onChange } = this.props
    api.invokeService('@custom-specification:get:all:specification', { type: 'amortize' }).then((res: any) => {
      this.setState({
        specificationList: res.items
      })
      // if (needOnchange) {
      //   const ids = res.items.map(item => item.id)
      //   onChange && onChange(ids)
      // }
    })
  }
  handleEdit = (line: { id: any; name: string; configs: [] }) => {
    const { id, name, configs } = line
    const handleType = 'EDIT'
    const config: any = (configs && configs.find((i: any) => i.ability === 'amortize')) || {}
    api
      .open('@third-party-manage:ExpenseConfigAmortizeModal', {
        line,
        name,
        id,
        handleType,
        amortizeMoneyField: config?.amortizeMoneyField,
        getSpecificationList: this.getSpecificationList,
        otherApportionMoneyFields: config.otherApportionMoneyFields ?? []
      })
      .then((res: any) => {})
  }

  handleAdd = (value: string) => {
    const { onChange } = this.props
    const { specificationList } = this.state
    const handleType = 'ADD'
    const line: any = {}
    line.components = []
    if (value === 'add') {
      const { valueState } = this.state
      api
        .open('@third-party-manage:ExpenseConfigAmortizeModal', {
          line,
          name: '',
          id: '',
          handleType,
          getSpecificationList: this.getSpecificationList
        })
        .then((res: any) => {
          const { value } = res
          const componentsCopy = specificationList.concat(value)
          // @ts-ignore
          const v = valueState.length > 0 ? valueState : [...valueState, value.id]
          this.setState({
            // @ts-ignore
            specificationList: [...componentsCopy],
            // valueState: [...valueState, value.id]
            valueState: v
          })
          onChange && onChange(v)
        })
    } else {
    }
  }
  handleSetDefault = (line: any) => {
    const { onChange } = this.props
    const { id } = line
    const { valueState } = this.state
    const list = valueState.filter(item => item !== id)
    list.unshift(id)
    this.setState({ valueState: list })
    onChange && onChange(list)
  }
  handleOnChange = (data: any) => {
    if (data === 'add') return
    const { onChange } = this.props
    const newValue = this.filterValue(data)
    onChange && onChange(newValue)
    this.setState({
      specificationIds: newValue,
      valueState: newValue
    })
  }
  filterValue = (value: string[]) => {
    value = Array.isArray(value) ? value : [value]
    return (value = value.filter(item => item !== 'add'))
  }
  render() {
    const { currentComponent } = this.props
    const { specificationList, valueState } = this.state
    const { specificationIds = [] } = currentComponent
    return (
      <div className={styles['intercon-select-wrapper']}>
        <ComSelect
          classNames="select-wrapper"
          value={valueState}
          defaultValue={specificationIds}
          list={specificationList}
          emptyTitle={i18n.get('目前没有任何摊销规则')}
          title={i18n.get('新建摊销规则')}
          handleAdd={this.handleAdd}
          handleEdit={this.handleEdit}
          labelInValue={false}
          disabled={!this.permission?.canEdit}
          disabledEdit={false}
          handleSetDefault={this.handleSetDefault}
          enableDelete={false}
          handleOnChange={this.handleOnChange}
          single
        />
      </div>
    )
  }
}
