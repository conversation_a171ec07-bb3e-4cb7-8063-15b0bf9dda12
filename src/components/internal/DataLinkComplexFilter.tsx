/**
 *  Created by gym on 2019-07-30 12:17.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './DataLinkComplexFilter.module.less'
import { app as api } from '@ekuaibao/whispered'
import { FieldInterface, FieldsInterface, InterfaceMate, TempFieldsInterface } from './DataLinkComplexFilter.type'
import { get, unionBy } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox } from 'antd'
import { getBoolVariation } from '../../lib/featbit'
import { useNewAutomaticAssignment } from '../utils/fnAutoDependence'

interface FilterProps {
  value?: string | boolean
  onChange: (key: string) => void
  field: FieldInterface
  globalfields?: FieldsInterface[]
  settingType?: string
  templateFields?: TempFieldsInterface[]
  billType?: string
  expenseMeta: InterfaceMate
  loanMeta: InterfaceMate
  requisitionMeta: InterfaceMate
  customMeta: InterfaceMate
  paymentMeta: InterfaceMate
}
interface FilterState {}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'complex-filter'
  },
  validator: (field: FieldInterface, props: FilterProps) => (rule: any, value: any, callback: any) => {
    if (useNewAutomaticAssignment()) {
      if (!value) return callback(i18n.get('请配置筛选条件'))
      return callback()
    } else {
      if (field.name !== 'autoFillByFilterDataOnlyOne') {
        if (!value) return callback(i18n.get('请配置筛选条件'))
        return callback()
      } else {
        return callback()
      }
    }
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  globalfields: state['@common'].globalFields.data,
  expenseMeta: state['@custom-specification'].expenseMeta,
  loanMeta: state['@custom-specification'].loanMeta,
  requisitionMeta: state['@custom-specification'].requisitionMeta,
  customMeta: state['@custom-specification'].customMeta,
  paymentMeta: state['@custom-specification'].paymentMeta
}))
export default class DataLinkComplexFilter extends PureComponent<FilterProps, FilterState> {
  hasAutoFillConfig: any
  constructor(props) {
    super(props)
    // 筛选结果为唯一值时自动赋值控制开关
    this.hasAutoFillConfig = props?.field?.name === 'autoFillByFilterDataOnlyOne'
  }

  getTemplateFieldInGlobalfields = (templateFields: TempFieldsInterface[]) => {
    const { globalfields } = this.props
    let templateField: any[] = []
    templateFields.forEach(oo => {
      let field = globalfields.find(vv => vv.name === oo.field)
      if (field) {
        templateField.push(field)
      }
    })
    return templateField
      .filter(oo => get(oo, 'dataType.entity') !== 'basedata.city')
      .filter(vv => get(vv, 'dataType.entity') !== 'requisition.RequisitionInfo')
  }

  getBillType = (billType: string) => {
    const { expenseMeta, loanMeta, requisitionMeta, customMeta, paymentMeta } = this.props
    switch (billType) {
      case 'expense':
        return expenseMeta
      case 'requisition':
        return requisitionMeta
      case 'loan':
        return loanMeta
      case 'custom':
        return customMeta
      default:
        return paymentMeta
    }
  }

  getTempFieldByAbility = (settingType: string) => {
    const { globalfields, billType } = this.props
    if (settingType.startsWith('billSpecification')) {
      const billTypeMeta = this.getBillType(billType)
      const configs: any[] | [] = (billTypeMeta && billTypeMeta.configs) || []
      const abilityList = configs.map(o => o.ability)
      let field = globalfields
        .filter(v => v.ability && !!~abilityList.indexOf(v.ability))
        .filter(oo => get(oo, 'dataType.entity') !== 'requisition.RequisitionInfo')
      return { field, sourceType: i18n.get('单据字段') }
    } else if (settingType.startsWith('feetypeSpecification')) {
      let field = globalfields
        .filter(v => v.ability && v.ability === 'feeDetail')
        .filter(oo => oo.name !== 'detailId' && oo.name !== 'multiplePayeesMode')
      return { field, sourceType: i18n.get('费用明细字段') }
    } else {
      let fields = globalfields.filter(v => v.ability && v.ability === 'trip').filter(oo => oo.name !== 'tripId')
      let field = fields.filter(vv => get(vv, 'dataType.entity') !== 'basedata.city')
      return { field, sourceType: i18n.get('行程明细字段') }
    }
  }

  handleClick = async () => {
    const {
      field: { entityInfo, entityId, entityName },
      onChange,
      value,
      settingType,
      templateFields,
      currentComponent
    } = this.props

    let abilityTempFields: any[] = []
    let sourceTypeFieldName: string = i18n.get('单据字段')
    if (settingType) {
      let { field, sourceType } = this.getTempFieldByAbility(settingType)
      abilityTempFields = field
      sourceTypeFieldName = sourceType
    }
    // 获取所有的单据上的字段(包含单据上还没保存的字段)
    const templateField = this.getTemplateFieldInGlobalfields(templateFields)
    const templateFieldList = unionBy(templateField, abilityTempFields, 'name')

    let valueData = ''
    let resultBlankStatus = false
    if (value) {
      const permissionValue = await api.invokeService('@custom-specification:get:datalink:permission', value)
      valueData = permissionValue?.value?.conditions
      resultBlankStatus = permissionValue?.value?.isCanViewAllDataWithResultBlank
    }
    const isNewOrder = currentComponent.field === 'tmcOrder'
    const result = await api.open('@custom-specification:DataLinkFilterModal', {
      entityInfo,
      entityName,
      sourceTypeFieldName,
      templateFields: templateFieldList,
      value: valueData,
      isNewOrder,
      isCanViewAllDataWithResultBlank: resultBlankStatus,
      hasEmptyConfig: true
    })

    const params: any = {
      entityId: isNewOrder ? 'tmcOrder' : entityId,
      conditions: result?.conditional || [],
      isCanViewAllDataWithResultBlank: result?.isCanViewAllDataWithResultBlank
    }
    if (value) {
      params.id = value
    }
    const filterId = await api.invokeService('@custom-specification:save:datalink:permission', params)
    onChange && onChange(filterId && filterId.id)
  }

  handleChange = e => {
    let { onChange } = this.props
    onChange && onChange(e.target.checked)
  }

  render() {
    const { field, value } = this.props

    return (
      <div className={styles['auto-position']}>
        {!this.hasAutoFillConfig || useNewAutomaticAssignment() ? (
          <div className={styles['dataLink-complex-filter']} onClick={this.handleClick}>
            {i18n.get('配置筛选条件')}
          </div>
        ) : (
          <div className={styles['auto-assign-oneResult']}>
            <Checkbox checked={value || false} onChange={this.handleChange}>
              <span className="fs-14">{i18n.get(field.label)}</span>
            </Checkbox>
          </div>
        )}
      </div>
    )
  }
}
