/**************************************************
 * Created by zhaohuabing on 2018/7/16 下午5:21.
 **************************************************/
import React, { PureComponent } from 'react'
import { Checkbox, Select } from 'antd'
import styles from './UnifiedBilling.module.less'
export default class UnifiedBilling extends PureComponent {
  state = { choose: false, limit: false, invoiceCorporation: [] }
  onChange = v => {
    let { onChange } = this.props
    onChange && onChange(v)
  }

  billPartChange = e => {
    this.setState({ invoiceCorporation: e }, _ => {
      this.onChange(this.state)
    })
  }

  unifiedChange = e => {
    this.setState({ choose: e.target.checked }, _ => {
      this.onChange(this.state)
    })
  }

  limitChange = e => {
    this.setState({ limit: e.target.checked }, _ => {
      this.onChange(this.state)
    })
  }

  BillPartyChoosed = () => {
    let invoiceCorporation = (this.props.value && this.props.value.invoiceCorporation) || []
    let { children } = this.props
    return (
      <Select
        mode="multiple"
        style={{ width: '100%' }}
        placeholder={i18n.get("请选择开票方")}
        showSearch
        value={invoiceCorporation}
        optionFilterProp={'label'}
        onChange={this.billPartChange}
      >
        {children}
      </Select>
    )
  }

  limitBillParty = (value, className) => {
    let limit = value && value.limit
    let key = 'limit-bill-party ' + className
    return (
      <div className={key}>
        <Checkbox checked={limit} onChange={this.limitChange}>
          <span className={styles['text-check']}>{i18n.get('限制开票方')}</span>
        </Checkbox>
        {limit && this.BillPartyChoosed()}
      </div>
    )
  }

  componentDidMount() {
    let { value } = this.props
    this.setState({ ...value })
  }

  render() {
    let { value, disabled: formDisabled, children } = this.props
    let checked
    value && (checked = value.choose)
    let [text, disabled, className] = children.length
      ? [i18n.get('统一开票'), false, '']
      : [i18n.get('统一开票（未添加开票方，请联系管理员）'), true, 'limit-disabled']
    return (
      <div>
        <Checkbox checked={checked} disabled={formDisabled || disabled} onChange={this.unifiedChange}>
          <span className={styles['text-check']}>{text}</span>
        </Checkbox>
        {checked && this.limitBillParty(value, className)}
      </div>
    )
  }
}
