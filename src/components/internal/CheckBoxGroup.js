/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { isArray } from 'lodash'
const Group = Checkbox.Group
@EnhanceField({
  descriptor: {
    type: 'checkbox-group'
  },
  initialValue(props) {
    let { field = {} } = props
    return field?.defaultValue?.value
  },
  validator: field => (rule, value = [], callback) => {
    const { name } = field
    if (name === 'reconciliationTypes' && value.length === 0) {
      return callback(i18n.get('请选择商旅对账类别'))
    }
    if (name === 'consumeTypes' && value.length === 0) {
      return callback(i18n.get('请选择参与补助计算的商城消费'))
    }
    callback()
  },
  wrapper: wrapper()
})
export default class CheckBoxGroup extends PureComponent {
  onChange = e => {
    let { onChange } = this.props
    onChange && onChange(e.target.checked)
  }

  fnBuildItems(tags = []) {
    return tags.map((line, index) => {
      let { value, label } = line
      return (
        <Checkbox key={`${index}/${value}`} value={value} onChange={this.onChange.bind(this, index)}>
          {label}
        </Checkbox>
      )
    })
  }
  handleChange = value => {
    let { onChange } = this.props
    onChange && onChange(value)
  }
  render() {
    let { field, value } = this.props
    let { tags } = field
    const { valueType } = field
    if (!Array.isArray(tags)) {
      throw new Error('checkbox-group need tags:Array')
    }
    if ((valueType === 'valueArr' && field.name === 'reconciliationTypes') || field.name === 'consumeTypes') {
      return (
        <Group value={value} onChange={this.handleChange}>
          {this.fnBuildItems(tags)}
        </Group>
      )
    }
    return <div>{this.fnBuildItems(tags)}</div>
  }
}
