/**
 *  Created by pw on 2021/7/27 下午4:50.
 */
import React, { Component } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { app } from '@ekuaibao/whispered'
const { wrapper } = app.require('@components/layout/FormWrapper')
const { required } = app.require('@components/validator/validator')
import { EnhanceConnect } from '@ekuaibao/store'
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select')
import { FeeTypeIF, FeeTypeMapIF } from '@ekuaibao/ekuaibao_types'
import MessageCenter from '@ekuaibao/messagecenter'
import { IField } from '@ekuaibao/template/types/Cellar'
import { isObject } from '@ekuaibao/helpers'
// @ts-ignore
import styles from './FeetypeDynamicItem.module.less'

interface Props {
  bus: MessageCenter
  value: string | FeeTypeIF

  field: ExpendField
  feeTypes: FeeTypeMapIF[]
  feeTypeMap: FeeTypeMapIF
  tag?: any
  onChange: (key: string | FeeTypeIF) => void
}

interface ExpendField extends IField {
  multiple?: boolean
}

@((Enhance<PERSON>ield as any)({
  descriptor: {
    type: 'feetype-dynamic-item'
  },
  validator: (field: IField) => (rule: any, value: string, callback: () => void) => {
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('费用类型') }, value))
  },
  wrapper: wrapper()
}))
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data,
  feeTypeMap: state['@common'].feetypes.map
}))
export default class FeetypeDynamicItem extends Component<Props> {
  componentDidMount() {
    app.dataLoader('@common.feetypes').load()
  }

  private handleFeeTypeChange = async (id: string) => {
    const { onChange, bus, feeTypeMap, tag = { needSpecification: true, needObject: false } } = this.props
    const feeType = feeTypeMap[id]
    if (tag?.needObject) {
      onChange && onChange(feeType)
    } else {
      onChange && onChange(id)
    }
    if (tag?.needSpecification) {
      const { items } = await app.invokeService('@custom-feetype:getFeetypeTemplateById', feeType?.id)
      const [feeTypeSP] = items
      bus.emit('feetype:dynamic:item:change', feeTypeSP)
    }
  }

  render() {
    const { feeTypes, value, field } = this.props
    const { allowClear = true } = field
    let changeValue = value
    if (isObject(changeValue)) {
      changeValue = (value as FeeTypeIF).id
    }
    return (
      <div className={styles['feetype-dynammic-item-wrapper']}>
        <FeeTypeSelect
          size="large"
          style={{ width: '100%' }}
          disabledCheckedFather
          multiple={field?.multiple || false}
          treeCheckable={false}
          feeTypes={feeTypes}
          checkedKeys={value ? [changeValue] : undefined}
          className="cg_feeType_select"
          allowClear={allowClear}
          onChange={this.handleFeeTypeChange}
        />
      </div>
    )
  }
}
