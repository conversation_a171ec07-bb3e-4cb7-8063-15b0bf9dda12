import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './WrittenOffRateType.module.less'
const RadioGroup = Radio.Group

interface Props {
  onChange: Function
  value: any
  field: any
  form: any
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'allowedRepaymentSelect'
  },
  wrapper: wrapper()
})
export default class AllowedRepaymentSelect extends PureComponent<Props, State> {
  handleChange = e => {
    const { onChange } = this.props
    const value = e.target.value
    onChange && onChange(value)
  }

  render() {
    const { field, value = 'Y', form } = this.props
    const { tags, text } = field
    const isAllowedRepayment = form.getFieldValue('allowRepayment:isAllowedRepayment') || false
    return (
      isAllowedRepayment && (
        <div className={styles['writtenOffRateType-wrapper']}>
          <span className="writtenOffRateType-title">{text}</span>
          <RadioGroup onChange={this.handleChange} value={value} style={{ width: '100%' }}>
            {tags.map(v => {
              return (
                <Radio key={v.value} value={v.value} style={{ width: '100%' }}>
                  {v.label}
                </Radio>
              )
            })}
          </RadioGroup>
        </div>
      )
    )
  }
}
