import {
  FilledGeneralAirplane,
  FilledGeneralCommunity,
  FilledGeneralShopping,
  FilledGeneralHot,
  FilledGeneralGasFull,
  FilledGeneralPin,
  FilledGeneralCollect,
  FilledGeneralTrain,
  FilledGeneralActivity,
  FilledGeneralPrinter,
  OutlinedDirectionRefresh,
  FilledGeneralCar,
  OutlinedEditSearch,
  FilledGeneralFlag,
  FilledGeneralRoom,
  FilledGeneralJobs,
  FilledGeneralBuzz,
  FilledGeneralBus,
  FilledGeneralTool,
  FilledGeneralBalance,
  FilledGeneralSailboat,
  FilledGeneralGoblet,
  FilledGeneralBuilding,
  FilledGeneralPaGraduation,
  FilledGeneralAnnounce,
  FilledGeneralMail,
  FilledGeneralKnifeFork,
  FilledGeneralCallNet,
  OutlinedGeneralMore,
  FilledGeneralLater,
  FilledGeneralInbox,
  FilledGeneralBook,
  FilledGeneralFinancing,
  FilledGeneralBonusPayroll
} from '@hose/eui-icons'

export const ALL_ICON = [
  {
    name: '#EDico-airplane',
    component: FilledGeneralAirplane
  },
  {
    name: '#EDico-team',
    component: FilledGeneralCommunity
  },
  {
    name: '#EDico-shopping',
    component: FilledGeneralShopping
  },
  {
    name: '#EDico-fire',
    component: FilledGeneralHot
  },
  {
    name: '#EDico-gas',
    component: FilledGeneralGasFull
  },
  {
    name: '#EDico-pin',
    component: FilledGeneralPin
  },
  {
    name: '#EDico-star',
    component: FilledGeneralCollect
  },
  {
    name: '#EDico-train',
    component: FilledGeneralTrain
  },
  {
    name: '#EDico-gift',
    component: FilledGeneralActivity
  },
  {
    name: '#EDico-fax',
    component: FilledGeneralPrinter
  },
  {
    name: '#EDico-refresh',
    component: OutlinedDirectionRefresh
  },
  {
    name: '#EDico-taxi',
    component: FilledGeneralCar
  },
  {
    name: '#EDico-search',
    component: OutlinedEditSearch
  },
  {
    name: '#EDico-flag',
    component: FilledGeneralFlag
  },
  {
    name: '#EDico-hotel',
    component: FilledGeneralRoom
  },
  {
    name: '#EDico-briefcase',
    component: FilledGeneralJobs
  },
  {
    name: '#EDico-lightning',
    component: FilledGeneralBuzz
  },
  {
    name: '#EDico-bus',
    component: FilledGeneralBus
  },
  {
    name: '#EDico-wrench',
    component: FilledGeneralTool
  },
  {
    name: '#EDico-balance',
    component: FilledGeneralBalance
  },
  {
    name: '#EDico-ship',
    component: FilledGeneralSailboat
  },
  {
    name: '#EDico-wineglass',
    component: FilledGeneralGoblet
  },
  {
    name: '#EDico-wineshop',
    component: FilledGeneralBuilding
  },
  {
    name: '#EDico-bachelorcap',
    component: FilledGeneralPaGraduation
  },
  {
    name: '#EDico-horn',
    component: FilledGeneralAnnounce
  },
  {
    name: '#EDico-mail',
    component: FilledGeneralMail
  },
  {
    name: '#EDico-tableware',
    component: FilledGeneralKnifeFork
  },
  {
    name: '#EDico-communication',
    component: FilledGeneralCallNet
  },
  {
    name: '#EDico-omit',
    component: OutlinedGeneralMore
  },
  {
    name: '#EDico-mark',
    component: FilledGeneralLater
  },
  {
    name: '#EDico-pack',
    component: FilledGeneralInbox
  },
  {
    name: '#EDico-books',
    component: FilledGeneralBook
  },
  {
    name: '#EDico-purse',
    component: FilledGeneralFinancing
  },
  {
    name: '#EDico-goldcoin',
    component: FilledGeneralBonusPayroll
  }
]

/** 新版本的icon与旧版icon的映射关系列表 */
export default function(name) {
  if (!name) {
    return null
  }
  const filter = ALL_ICON.filter(icon => icon.name === name)
  if (filter.length === 0) {
    return null
  } else {
    return filter[0].component
  }
}
