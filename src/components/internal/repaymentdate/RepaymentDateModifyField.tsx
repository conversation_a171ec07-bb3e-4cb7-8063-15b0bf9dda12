import React, { PureComponent } from 'react'
import { ModifyDate } from './RepaymentDateView'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import styles from './RepaymentDateView.module.less'
import classnames from 'classnames'
import { get } from 'lodash'

interface Props {
  value: any
  onChange: any
  field: any
  form: any
}
interface State {
  value: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'repaymentdatemodify'
  },
  wrapper: wrapper()
})
export default class RepaymentDateModifyField extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    const allowModifyRepaymentDateConfig = {
      selected: 'auto',
      autoDelayDays: 15,
      autoDelayTimes: 3
    }
    const selectedType = get(props, 'value.selected')
    this.state = {
      value: selectedType
        ? { allowModifyRepaymentDateConfig: props.value, islimitModifyDate: true }
        : {
            allowModifyRepaymentDateConfig,
            islimitModifyDate: false
          }
    }
  }
  handleOnChange = value => {
    const { onChange } = this.props
    const val = this.state.value
    const newVal = { ...val, ...value }
    this.setState({ value: newVal })
    const data = newVal.islimitModifyDate ? newVal.allowModifyRepaymentDateConfig : {}
    onChange && onChange(data)
  }

  render() {
    const {
      field: {
        relatedConfig: { ability },
        disabled
      },
      form
    } = this.props
    const isShow = form.getFieldValue(ability)
    const cls = classnames(`${styles['repaymentdate-wrapper']} ${styles['repaymentdate-wrapper2']}`)
    if (!isShow) {
      return null
    }
    return (
      <div className={cls}>
        <ModifyDate className="date-wrapper" {...this.state.value} disabled={disabled} onChange={this.handleOnChange} />
      </div>
    )
  }
}
