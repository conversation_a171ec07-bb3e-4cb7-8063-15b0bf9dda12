import React, { PureComponent } from 'react'
import { SelectDate } from './RepaymentDateView'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import styles from './RepaymentDateView.module.less'
import classnames from 'classnames'
interface Props {
  value: any
  onChange: any
  field: any
  form: any
}
interface State {
  value: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'repaymentdatelimit'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (value && value.selected === 'date' && !value.overDate) {
      callback(i18n.get('选择限制日期'))
    }
    callback()
  },
  wrapper: wrapper()
})
export default class RepaymentDateField extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    const limitRepaymentDateRange = { selected: 'year' }
    this.state = {
      value: props.value
        ? { limitRepaymentDateRange: props.value, islimitSelectDate: true }
        : {
            limitRepaymentDateRange,
            islimitSelectDate: false
          }
    }
  }

  handleOnChange = value => {
    const { onChange } = this.props
    const val = this.state.value
    const newVal = { ...val, ...value }
    this.setState({ value: newVal })
    if (newVal.islimitSelectDate) {
      onChange && onChange(newVal.limitRepaymentDateRange)
    } else {
      onChange && onChange()
    }
  }

  render() {
    const {
      field: {
        relatedConfig: { ability },
        disabled
      },
      form
    } = this.props
    const isShow = form.getFieldValue(ability)
    const cls = classnames(`${styles['repaymentdate-wrapper']}`)
    if (!isShow) {
      return null
    }
    return (
      <div className={cls}>
        <SelectDate className="date-wrapper" {...this.state.value} disabled={disabled} onChange={this.handleOnChange} />
      </div>
    )
  }
}
