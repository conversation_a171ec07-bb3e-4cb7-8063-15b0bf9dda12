import React from 'react'
import { Checkbox, Radio, InputNumber, DatePicker } from 'antd'
const RadioGroup = Radio.Group
import get from 'lodash/get'
import moment from 'moment'
interface ModifyDate {
  onChange: (value) => void
  islimitModifyDate: boolean
  isShow?: boolean
  className?: string
  id?: string
  allowModifyRepaymentDateConfig: {
    selected: string
    autoDelayDays?: number
    autoDelayTimes?: number
  }
  disabled?: boolean
}

export function ModifyDate(props: ModifyDate) {
  const { onChange, islimitModifyDate, allowModifyRepaymentDateConfig, isShow = true, className, id, disabled } = props
  const selected = get(allowModifyRepaymentDateConfig, 'selected')
  const autoDelayDays = get(allowModifyRepaymentDateConfig, 'autoDelayDays', 15) || 15
  const autoDelayTimes = get(allowModifyRepaymentDateConfig, 'autoDelayTimes', 3) || 3
  return (
    <div id={id} className={`${className} rule`}>
      {isShow ? (
        <Checkbox
          className="modify-date"
          checked={islimitModifyDate}
          disabled={disabled}
          onChange={value => onChange({ islimitModifyDate: get(value, 'target.checked') })}
        >
          {i18n.get('获得借款后，允许修改还款日期')}
        </Checkbox>
      ) : (
        i18n.get('获得借款后，允许修改还款日期')
      )}
      {islimitModifyDate && (
        <RadioGroup
          className="ml-30 radiogroup"
          value={selected}
          disabled={disabled}
          onChange={e => {
            const selected = get(e, 'target.value')
            let obj: any = { selected }
            if (selected === 'auto') {
              obj = { ...obj, autoDelayDays, autoDelayTimes }
            }
            onChange({
              islimitModifyDate,
              allowModifyRepaymentDateConfig: { ...obj }
            })
          }}
        >
          <Radio className="radio-item" value={'auto'}>
            {i18n.get('默认顺延')}
            <InputNumber
              value={autoDelayDays}
              disabled={disabled}
              min={1}
              max={365}
              className="delay-inputnumber"
              onChange={value =>
                onChange({
                  islimitModifyDate,
                  allowModifyRepaymentDateConfig: { selected: 'auto', autoDelayDays: value, autoDelayTimes }
                })
              }
            />
            {i18n.get('天')},{i18n.get('可顺延')}{' '}
            <InputNumber
              value={autoDelayTimes}
              disabled={disabled}
              className="delay-inputnumber"
              min={1}
              max={100}
              onChange={value =>
                onChange({
                  islimitModifyDate,
                  allowModifyRepaymentDateConfig: { selected: 'auto', autoDelayTimes: value, autoDelayDays }
                })
              }
            />
            {i18n.get('次')}
          </Radio>
          <Radio className="radio-item mt-4" value={'manual'}>
            {i18n.get('手动输入变更日期')}
          </Radio>
        </RadioGroup>
      )}
    </div>
  )
}
interface SelectDate {
  onChange: (value) => void
  islimitSelectDate: boolean
  isShow?: boolean
  className?: string
  id?: string
  limitRepaymentDateRange: { selected: string; overDate?: number }
  disabled?: boolean
}
export function SelectDate(props: SelectDate) {
  const { onChange, limitRepaymentDateRange, islimitSelectDate, isShow = true, className, id, disabled } = props
  const selected = get(limitRepaymentDateRange, 'selected')
  const overDate =
    get(limitRepaymentDateRange, 'overDate') ||
    moment()
      .add(30, 'd')
      .valueOf()
  return (
    <div id={id} className={`${className} rule rule1`}>
      {isShow ? (
        <Checkbox
          className="modify-date"
          checked={islimitSelectDate}
          disabled={disabled}
          onChange={value => onChange({ islimitSelectDate: get(value, 'target.checked') })}
        >
          {i18n.get('限制还款日期选择范围')}
        </Checkbox>
      ) : (
        i18n.get('限制还款日期选择范围')
      )}
      {islimitSelectDate && (
        <RadioGroup
          className="ml-30 radiogroup"
          value={selected}
          disabled={disabled}
          onChange={e => {
            const selected = get(e, 'target.value')
            let obj: any = { selected }
            if (selected === 'date') {
              obj = { ...obj, overDate }
            }
            onChange({
              islimitSelectDate,
              limitRepaymentDateRange: { ...obj }
            })
          }}
        >
          <Radio className="radio-item" value={'date'}>
            {i18n.get('不得超过指定日期')}
            <div className="select-datepicker">
              <DatePicker
                allowClear={false}
                value={moment(overDate)}
                disabledDate={disabledDate}
                onChange={value =>
                  onChange({
                    islimitSelectDate,
                    limitRepaymentDateRange: { selected: 'date', overDate: moment(value).valueOf() }
                  })
                }
              />
            </div>
          </Radio>
          <Radio className="radio-item radio-year" value={'year'}>
            {i18n.get('不得超过借款日期所在年份')}
          </Radio>
        </RadioGroup>
      )}
    </div>
  )
}

interface OptionalPayeeByZeroData {
  onChange: (value) => void
  optionalPayeeByZero: boolean
  isShow?: boolean
  className?: string
  id?: string
}
export function OptionalPayeeByZeroData(props: OptionalPayeeByZeroData) {
  const { onChange, optionalPayeeByZero, isShow = true, className, id } = props
  return (
    <div id={id} className={`${className} rule rule1`}>
      {isShow ? (
        <Checkbox
          className="modify-date"
          checked={optionalPayeeByZero}
          onChange={value => onChange({ optionalPayeeByZero: get(value, 'target.checked') })}
        >
          {i18n.get('当支付金额为0时，收款信息非必填')}
        </Checkbox>
      ) : (
        i18n.get('当支付金额为0时，收款信息非必填')
      )}
    </div>
  )
}

function disabledDate(current) {
  return current && current < moment().endOf('day')
}
