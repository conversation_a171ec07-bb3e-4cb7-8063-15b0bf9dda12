/**************************************************
 * Created by nanyuanting<PERSON> on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent, Fragment } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RadioGroup.module.less'
import { app as api } from '@ekuaibao/whispered'
import { isEqual, get } from 'lodash'
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'radio-group'
  },
  initialValue(props) {
    let { field = {} } = props
    return field?.defaultValue?.value
  },
  validator: (field, props) => (rule, value, callback) => {
    if (value instanceof Object && value.type === 'constant' && !value.value && props.bus._$CHECKED_CONSTANT_ERROR) {
      return callback(i18n.get('固定值不能为空'))
    }
    let defaultValue = value && value.defaultValue ? value.defaultValue : value
    if (defaultValue && defaultValue.type === 'formula' && props.bus._$CHECKED_FORMULA_ERROR) {
      const { checkedCustomCurrency, customRuleId, value } = defaultValue
      if (!value) {
        return callback(i18n.get('编辑的公式不符合计算法则，无法计算'))
      }
      if (value && checkedCustomCurrency && !customRuleId) {
        return callback(i18n.get('请配置正确的币种自定义规则'))
      }
    }
    if (get(value, 'value.type') === 'costStandard' && !get(value, 'value.id') && props.bus._$CHECKED_CONSTANT_ERROR) {
      return callback(i18n.get('请选择费用标准'))
    }
    if (get(value, 'value.type') === 'costStandard' && !api.getState('@common').powers.CostStandard) {
      return callback(i18n.get(`「{__k0}」配置已失效，请修改配置`, { __k0: props.currentComponent.label }))
    }
    if (defaultValue && defaultValue.type === 'customizeQuery' && !value.value) {
      return callback(i18n.get('请选择业务对象数据联查规则'))
    }
    if (defaultValue && defaultValue.type === 'predefine' && !value.value) {
      return callback(i18n.get('请选择行程规划城市'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class RadioGroupWrapper extends PureComponent {
  constructor(props) {
    super(props)
    let flag = this.isShowCustomComp(props)
    let { field } = this.props
    this.state = {
      isShowCustomComp: flag || false,
      tags: field.tags || []
    }
    this.checkedValue = props.value
    this.handleChecked(this.checkedValue)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      this.checkedValue = nextProps.value
      let flag = this.isShowCustomComp(nextProps)
      let { field } = nextProps
      this.setState({
        isShowCustomComp: flag || false,
        tags: field.tags || []
      })
      this.handleChecked(this.checkedValue)
    }
  }

  handleChange = e => {
    let val = e.target.value
    let { onChange, field, form, bus } = this.props
    const name = this.getFieldName()
    let { decode, config } = field

    if (decode) {
      val = decode(val)
    }
    if (config && form) {
      //如果该组件关联了其他组件 用config描述关联关系
      let { relateData, relateValue } = config
      if (relateValue === val) {
        let obj = {}
        relateData.forEach(v => {
          let { ability, defaultValue } = v
          obj[ability] = defaultValue
        })
        form.setFieldsValue(obj)
      }
    }
    this.checkedValue = val
    let isShowCustomComp = this.isShowCustomComp(this.props) && e.target.checked
    this.setState({ isShowCustomComp })
    onChange && onChange(val)
    //默认值选项修改时的一些联动
    this.handleChecked(val, true)
    if (name === 'defaultValue') {
      this.updateValue(form)
    }
  }

  getFieldName() {
    return get(this.props, 'field.name', '')
  }

  handleChecked(value, isChange) {
    const name = this.getFieldName()
    let { bus } = this.props
    if (name === 'defaultValue') {
      bus.emit('field:update:optional', value, this.props.field)
    }
  }

  updateValue = form => {
    //默认值是固定值的时候选择范围锁死是'all'
    if (this.isDefConstant(form)) {
      let orgValue = form.getFieldsValue()
      orgValue = { ...orgValue, selectRange: 'all' }
      form.setFieldsValue(orgValue)
    }
  }

  isDefConstant = form => {
    if (!!form) return false
    let defValue = form.getFieldValue('defaultValue')
    return defValue && defValue.type === 'constant'
  }

  isShowCustomComp(props) {
    let { value, field } = props
    if (!value) return false
    let tag = field.tags.find(v => isEqual(v.value, value))
    return Boolean(tag && tag.Component)
  }

  handleCustomValue = value => {
    let { tags } = this.state
    tags = tags.slice()
    tags.find(v => {
      if (isEqual(v.value, this.checkedValue)) {
        v.value = value
      }
    })
    this.setState({ tags })
    let { onChange } = this.props
    onChange(value)
  }

  render() {
    let { isShowCustomComp, tags } = this.state
    let { field, value, bus, form } = this.props
    let { defaultValue, name, disabled, encode, style = {}, showForm, size, others, component } = field
    let val = encode ? encode(value) : value
    let defVal = defaultValue && defaultValue?.value
    val = val ?? defVal
    let temDis = this.isDefConstant(form) && name === 'selectRange'
    let attributes = {
      disabled: temDis || disabled,
      value: val,
      defaultValue: defVal || 0
    }
    return (
      <div data-cy={`ekb-radio-group-${field.name}`}>
        <RadioGroup
          className={styles['radio-group-wrapper']}
          onChange={this.handleChange}
          {...attributes}
          style={style}
        >
          {tags.map((v, key, arr) => {
            let Component = v.Component
            let flg = isEqual(val, v.value)
            let value = flg ? val : v.value
            let isShow =
              isShowCustomComp &&
              Component &&
              (value.type || get(value, 'defaultValue.type')) === (val.type || get(val, 'defaultValue.type'))
            const radioStyle =
              showForm === 'horizontal'
                ? {}
                : {
                    display: 'block',
                    marginBottom: key === arr.length - 1 && !isShow ? '0' : '10px'
                  }

            return (
              <Fragment key={`${key}:root`}>
                <UniversalComponent uniqueKey={`customSpecification.pc.${v?.value?.type}`}>
                  <Radio key={key} style={radioStyle} value={value} className={size}>
                    {v.label}
                  </Radio>
                  {isShow && (
                    <div key={`${key}:${key}`} className={'radio-group-other'}>
                      <Component
                        bus={bus}
                        value={v.value}
                        disabled={disabled}
                        meta={v.meta}
                        valueRange={v.valueRange}
                        onChangeValue={this.handleCustomValue}
                        baseDataProperties={v.baseDataProperties}
                        field={v.dataType}
                        component={component}
                        others={others}
                      />
                    </div>
                  )}
                </UniversalComponent>
              </Fragment>
            )
          })}
        </RadioGroup>
      </div>
    )
  }
}
