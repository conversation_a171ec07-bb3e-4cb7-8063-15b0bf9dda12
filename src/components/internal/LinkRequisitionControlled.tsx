import React, { PureComponent } from 'react'
import { Checkbox, InputNumber } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import styles from './LinkRequisitionControlled.module.less'
import { CheckboxChangeEvent } from 'antd/lib/checkbox'

interface IProps {
  form: any
  field: any
  value: any
  onChange: (value: any) => void
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'linkRequisitionControlled'
  }
})
export default class LinkRequisitionControlled extends PureComponent<IProps> {
  constructor(props: IProps) {
    super(props)
  }
  render() {
    let { value, form, field } = this.props
    const { label, writtenOffComponentType, name, style = {}, disabled } = field
    const isWrittenOffMaxTimes = name.endsWith(':writtenOffMaxTimes')
    if (isWrittenOffMaxTimes) {
      const isCanLoan = form.getFieldValue('canLoan:value') || { checked: false }
      const isShow = writtenOffComponentType === 'canLoan:writtenOff' ? isCanLoan.checked : true
      if (!isShow) {
        return null
      }
    }
    value = value || {
      value: false,
      count: 0
    }
    
    return (
      <div className={styles['controlled-wrapper']} style={style}>
        <Checkbox onChange={this.onChange} checked={value.value} disabled={disabled}>
          <span>{label}</span>
        </Checkbox>
        {value.value && <InputNumber precision={0} min={0} value={value.count} disabled={disabled} onChange={this.countOnChange}  />}
      </div>
    )
  }
  private onChange = (e: CheckboxChangeEvent): void => {
    let { onChange, value } = this.props
    value = {
      value: false,
      count: 0
    }
    value.value = e.target.checked
    onChange && onChange({ ...value })
  }
  private countOnChange = (val: number | string | undefined) => {
    let { onChange, value } = this.props
    value = value || {
      value: false,
      count: 0
    }
    if (typeof val === 'string') {
      return
    }
    value.count = val || 0
    onChange && onChange({ ...value })
  }
}
