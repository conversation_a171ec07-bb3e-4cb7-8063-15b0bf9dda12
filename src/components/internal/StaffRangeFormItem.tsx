/**
 *  Created by pw on 2022/5/13 下午4:38.
 */
import React, { Component } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './StaffRangeFormItem.module.less'
import TagSelector from '../../elements/tag-selector'
import { ComponentIF, DepartmentIF, RoleIF, StaffIF } from '@ekuaibao/ekuaibao_types'
import { app } from '@ekuaibao/whispered'
import { remove, cloneDeep } from 'lodash'
import { toJS } from 'mobx'

interface Props {
  value?: any
  field: ComponentIF
  staffMap: Record<string, StaffIF>
  roles: RoleIF[]
  tag?: any
  departmentMap: Record<string, DepartmentIF>
  onChange: (values: any) => void
}

interface State {
  tags: any[]
}

@((EnhanceField as any)({
  descriptor: {
    type: 'staff-range-form-item'
  },
  validator: (field: any) => (rule: any, value: any, callback: any) => {
    const { optional } = field
    if (!optional && !value?.staffIds?.length && !value?.departmentIds?.length && !value?.roleIds?.length) {
      return callback(`${field?.label}不能为空`)
    }
    return callback()
  },
  wrapper: wrapper()
}))
@EnhanceConnect((state: any) => ({
  staffMap: state['@common'].authStaffDepartmentStaffMap?.staffMap || {},
  roles: state['@common'].roleList,
  departmentMap: state['@common'].department.mapData
}))
export default class StaffRangeFormItem extends Component<Props, State> {
  state = {
    tags: []
  }
  componentWillReceiveProps(nextProps: Props) {
    if (nextProps.value !== this.props.value) {
      this.updateTags(nextProps)
    }
  }
  componentDidMount() {
   this.updateTags(this.props)
  }

  updateTags =(props:Props)=>{
    const { value, staffMap, roles, departmentMap, tag = { needConversionObject: true } } = props
    let tags = []
    if (tag?.needConversionObject) {
      if (value?.staffIds?.length) {
        tags = tags.concat(value?.staffIds.map(staffId => staffMap[staffId]))
      }
      if (value?.departmentIds?.length) {
        tags = tags.concat(value?.departmentIds.map(departmentId => departmentMap[departmentId]))
      }
      if (value?.roleIds?.length) {
        const roleMap = roles.reduce((result, role) => {
          result[role.id] = role
          return result
        }, {})
        tags = tags.concat(value?.roleIds.map(roleId => roleMap[roleId]))
      }
    } else {
      tags = this.fnGetDataTags(value)
    }
    this.setState({ tags })
  }

  fnGetDataTags = value => {
    let tags = []
    if (value?.staffIds?.length) {
      tags = tags.concat(value?.staffIds.map(staff => staff))
    }
    if (value?.departmentIds?.length) {
      tags = tags.concat(value?.departmentIds.map(department => department))
    }
    if (value?.roleIds?.length) {
      tags = tags.concat(value?.roleIds.map(role => role))
    }
    return tags
  }

  fnGetCheckedList = (value, tag) => {
    let { staffIds = [], departmentIds = [], roleIds = [] } = value
    const checkedList = []
    if (!tag.needConversionObject) {
      staffIds = staffIds?.map(staff => staff?.id)
      departmentIds = departmentIds?.map(department => department?.id)
      roleIds = roleIds?.map(role => role?.id)
    }
    if (tag?.showStaff) {
      checkedList.push({
        type: 'department-member',
        multiple: tag.multiple,
        checkedKeys: staffIds
      })
    }
    if (tag?.showDepartment) {
      checkedList.push({ type: 'department', multiple: tag.multiple, checkedKeys: departmentIds })
    }
    if (tag?.showRole) {
      checkedList.push({ type: 'role', multiple: tag.multiple, checkedKeys: roleIds })
    }
    if (!checkedList?.length) {
      // 没有可配置人员的话，默认给员工
      checkedList.push({
        type: 'department-member',
        multiple: tag?.multiple,
        checkedKeys: staffIds
      })
    }
    return checkedList
  }

  handleClick = () => {
    const {
      onChange,
      value = {},
      tag = { showStaff: true, showDepartment: true, showRole: true, multiple: true, needObject: false }
    } = this.props
    const { departmentsIncludeChildren = true } = value
    const checkedList = this.fnGetCheckedList(value, tag)
    app
      .open('@layout:SelectStaffsModal', {
        checkedList,
        departmentsIncludeChildren
      })
      .then((data: any) => {
        const { checkedList, departmentsIncludeChildren } = data
        const staffs = checkedList.find(o => o.type === 'department-member')
        const departments = checkedList.find(o => o.type === 'department')
        const roles = checkedList.find(o => o.type === 'role')
        const valueChange: any = { departmentsIncludeChildren }
        if (staffs) {
          valueChange.staffIds = tag.needObject ? staffs.checkedData : staffs.checkedKeys
        }
        if (departments) {
          valueChange.departmentIds = tag.needObject ? departments.checkedData : departments.checkedKeys
        }
        if (roles) {
          valueChange.roleIds = tag.needObject ? roles.checkedData : roles.checkedKeys
        }
        onChange && onChange(valueChange)
        const tags = this.fnGetDataTags({
          staffIds: staffs?.checkedData,
          departmentIds: departments?.checkedData,
          roleIds: roles?.checkedData
        })
        this.setState({ tags })
      })
  }

  handleTagsChanged = (tags, deleteTag) => {
    const { onChange, value, tag = { needObject: false } } = this.props
    const { staffIds = [], departmentIds = [], roleIds = [], departmentsIncludeChildren } = toJS(value)
    const fnRemoveItem = (item): boolean => {
      if (tag.needObject) {
        return item.id === deleteTag.id
      }
      return item.id === deleteTag.id
    }
    let delItems = []
    if (staffIds?.length) {
      delItems = remove(staffIds, fnRemoveItem)
    }
    if (!delItems?.length && departmentIds?.length) {
      delItems = remove(departmentIds, fnRemoveItem)
    }
    if (!delItems?.length && roleIds?.length) {
      remove(roleIds, fnRemoveItem)
    }
    let { tags: stateTags = [] } = this.state
    stateTags = tags.filter(tag => tag.id !== deleteTag.id)
    this.setState({ tags: stateTags })
    onChange && onChange({ staffIds, departmentIds, roleIds, departmentsIncludeChildren })
  }

  render() {
    const { field } = this.props
    const { placeholder } = field
    const { tags } = this.state
    return (
      <div className={styles.staff_tags}>
        <TagSelector
          placeholder={placeholder || i18n.get('请选择')}
          className={styles.staff_select_tags}
          value={tags}
          onClick={this.handleClick}
          onChange={this.handleTagsChanged}
        />
      </div>
    )
  }
}
