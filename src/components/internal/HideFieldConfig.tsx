import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import { Radio } from '@hose/eui'
import styles from './RequiredFieldConfig.module.less'
import { app } from '@ekuaibao/whispered'
const { UniversalComponent } = app.require('@elements/StandardVersionComponent')

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'hide-field-config'
  },
  validator: (field, props) => (rule, value, callback) => {
    // const { form} = props
    // const { isCheckAttr, isCheckValid } = field
    // if (isCheckAttr && !form.getFieldValue('required')) {
    //   return callback()
    // }
    if (value && value.active && !value.formula) {
      return callback(i18n.get('请配置计算公式'))
    }
    return callback()
  }
})
export default class HideFieldConfig extends PureComponent<any, any> {
  constructor(props) {
    super(props)
  }

  componentDidMount() {
    const { form } = this.props
    const { getFieldValue } = form
    const isHide = getFieldValue('hide')
    const isHideCalculationFormula = getFieldValue('hideCalculationFormula')
    
    if (isHide && isHideCalculationFormula) {
      form.setFieldsValue({ hideConditionSetting: true })
    }
  }
  handleCheck = e => {
    const isChecked = e.target.checked
    const { onChange, value, form, field } = this.props
    const { mutualExclusion } = field
    onChange({ ...value, active: isChecked })
    if (isChecked && mutualExclusion?.length > 0 && form) {
      const currentFormValue = form.getFieldsValue()
      const formValue = {}
      mutualExclusion.forEach(v => {
        if (currentFormValue[v.name] !== undefined) {
          formValue[v.name] = v.value
        }
      })
      if (Object.keys(formValue).length > 0) {
        form.setFieldsValue(formValue)
      }
    }
  }

  handleCmpChange = param => {
    const { type, value } = param
    const { onChange, field } = this.props
    // const { isCheckAttr, isCheckValid } = field
    if (onChange && type === 'formula') {
      onChange({
        ability: 'caculate',
        valueType: 'BOOLEAN',
        formula: value,
        property: 'hide',
        active: true
      })
    }
  }

  renderError = () => {
    const { form, field } = this.props
    const { name } = field
    const { getFieldError } = form
    const error = getFieldError(name)
    if (!error) {
      return null
    }
    return <div style={{ color: '#ff7c7c', marginLeft: '22px' }}>{error}</div>
  }

  render() {
    const { field, value, form, bus, onChange } = this.props
    const { text, size, style = {}, disabled = false, tags, isCheckSet, relatedConfig, isRadio } = field
    // if (isCheckAttr && !form.getFieldValue('required')) {
    //   return null
    // }
    if (relatedConfig?.ability && !form.getFieldValue(relatedConfig.ability)) {
      return null
    }

    const { Component, others } = tags[0]
    const formulaVal = value ? { type: 'formula', value: value.formula } : undefined
    const isChecked = value?.active
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.hideConditionSetting`}>
        <div className={styles['ekb-check']} data-cy={`ekb-check-${field.name}`} style={...style}>
          <div className="mb-8">
            {isRadio && <Radio onChange={this.handleCheck} disabled={disabled} checked={isChecked}>
              <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
            </Radio>}
            {!isRadio && <Checkbox onChange={this.handleCheck} disabled={disabled} checked={isChecked}>
              <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
            </Checkbox>}
          </div>
          {isChecked && (
            <div style={{marginLeft: 24}}>
              <Component
                bus={bus}
                value={formulaVal}
                isCheckSet={isCheckSet}
                onChangeValue={this.handleCmpChange}
                others={others}
              />
            </div>
          )}
          {this.renderError()}
        </div>
      </UniversalComponent>
    )
  }
}
