/**************************************************
 * Created by zhaohuabing on 2018/7/16 下午5:21.
 **************************************************/
import React, { PureComponent } from 'react'
import { app, app as api } from "@ekuaibao/whispered";
import { Fetch } from '@ekuaibao/fetch'
import { isTianYangTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Select, Tag } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import UnifiedBilling from './UnifiedBilling'
import './BillTypeCheckboxGroup.less'
import { EnhanceConnect } from '@ekuaibao/store'
import classNames from 'classnames'
import _cloneDeep from 'lodash/cloneDeep'
import { RequiredFieldConfigComponent, RequiredFieldConfigValidator } from '../internal/RequiredFieldConfig'
import { get } from 'lodash'

const { ConditionWrapper } = app.require('@elements/conditionComponents')
const Option = Select.Option

@EnhanceField({
  descriptor: {
    type: 'bill-checkbox-group'
  },
  validator: (field, props) => (rule, value, callback) => {
    const editable = props.form.getFieldValue('editable')
    if (editable && value.unify.choose && value.unify.limit && !value.unify.invoiceCorporation.length) {
      return callback(i18n.get('限制开票方状态选中时，开票方不能为空'))
    }
    if (editable && !(value.exist || value.noExist || value.wait || value.unify.choose)) {
      return callback(i18n.get('请选择发票形式'))
    }
    if (editable && field?.isFeeTypeInvoiceConfig && !value?.defaultInvoiceType) {
      return callback(i18n.get('请配置发票方式默认值'))
    }
    return callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  limitBillPartyList: state['@custom-feetype'].limitBillPartyList,
  allFlows: state['@custom-flow'].list
}))
export default class BillTypeCheckboxGroup extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      visible: props.form.getFieldValue('editable'),
      exist: false,
      noExist: false,
      noExistConfig: {},
      wait: false,
      unify: {},
      allFlowsMap: {}
    }
  }

  initDefaultInvoiceTypeList = props => {
    let { exist, noExist, wait, unify, defaultInvoiceType } = props
    let defaultInvoiceTypeList = [
      { key: 'exist', label: '已有发票', active: exist },
      { key: 'wait', label: '待开发票', active: wait },
      { key: 'noExist', label: '无发票', active: noExist },
      { key: 'unify', label: '统一开票', active: unify?.choose }
    ]
    defaultInvoiceTypeList = defaultInvoiceTypeList.filter(v => v.active)
    // 没有选择发票方式时清空默认值
    const noList = !defaultInvoiceTypeList?.length
    // 有默认值但默认值没在选中的发票方式中
    const noFind = defaultInvoiceType && !defaultInvoiceTypeList.find(el => el.key === defaultInvoiceType)
    if (defaultInvoiceType && (noList || noFind)) {
      defaultInvoiceType = get(defaultInvoiceTypeList, '[0].key', '')
    }
    return { defaultInvoiceType, defaultInvoiceTypeList }
  }

  changeDefaultInvoiceType = type => {
    this.setState({ defaultInvoiceType: type }, _ => {
      this.onChange(this.state)
    })
  }

  handleAllFlowsData = () => {
    const { allFlows = [] } = this.props
    const _setAllFlowsMap = {}
    for (const item of allFlows) {
      _setAllFlowsMap[item.id] = item
    }
    this.setState({ allFlowsMap: _setAllFlowsMap })
  }

  onChange = v => {
    let { onChange, field } = this.props
    const { isFeeTypeInvoiceConfig } = field
    const { exist, noExist, noExistConfig = {}, wait, unify, isRequired, defaultInvoiceType } = v
    let param = { wait, exist, unify, noExist, noExistConfig, isRequired }
    if (isFeeTypeInvoiceConfig) {
      param.defaultInvoiceType = defaultInvoiceType
    }
    onChange && onChange({ ...param })
  }

  checkChange = e => {
    let { field } = this.props
    const { isFeeTypeInvoiceConfig } = field
    let { name, checked } = e.target
    let obj = {}
    obj[name] = checked
    if (name === 'wait' && checked) {
      obj.exist = checked
    }
    if (name === 'noExist' && checked) {
      obj.noExistConfig = {
        visibility: false,
        freeflowScope: []
      }
    }
    let param = { ...obj }
    if (isFeeTypeInvoiceConfig) {
      const newDefault = this.initDefaultInvoiceTypeList({ ...this.state, ...obj })
      param = { ...obj, ...newDefault }
    }
    this.setState({ ...param }, _ => {
      this.onChange(this.state)
    })
  }

  childChange = v => {
    let { field } = this.props
    const { isFeeTypeInvoiceConfig } = field
    if (!v.choose) {
      v.limit = false
      v.invoiceCorporation = []
    } else {
      if (!v.limit) {
        v.invoiceCorporation = []
      }
    }
    let param = { unify: v }
    if (isFeeTypeInvoiceConfig) {
      const newDefault = this.initDefaultInvoiceTypeList({ ...this.state, unify: v })
      param = { unify: v, ...newDefault }
    }
    this.setState({ ...param }, _ => {
      this.onChange(this.state)
    })
  }

  componentWillMount() {
    let { bus, value, field } = this.props
    const { isFeeTypeInvoiceConfig } = field
    let param = { ...value }
    if (isFeeTypeInvoiceConfig) {
      const newDefault = this.initDefaultInvoiceTypeList({ ...this.state, ...value })
      param = { ...value, ...newDefault }
    }
    value && this.setState({ ...param }, _ => this.onChange(this.state))
    api.invokeService('@custom-flow:get:plan:configs', { orgId: '' }).then(() => {
      this.handleAllFlowsData()
    })
  }

  componentWillReceiveProps(nextProps) {
    const { value, field } = nextProps
    const { isFeeTypeInvoiceConfig } = field
    if (nextProps.value && this.props.value !== nextProps.value) {
      let param = { ...nextProps.value }
      if (isFeeTypeInvoiceConfig) {
        const newDefault = this.initDefaultInvoiceTypeList({ ...this.state, ...value })
        param = { ...nextProps.value, ...newDefault }
      }
      this.setState({ ...param })
    }
  }

  fnIsCheckboxGroupVisible = visible => {
    visible
      ? this.setState({ visible })
      : this.setState({ visible, exist: true }, _ => {
          this.onChange(this.state)
        })
  }

  setErrorNodeStyle = (node, className) => {
    let { field } = this.props
    const { isFeeTypeInvoiceConfig } = field
    let parentNode = node && node.parentNode
    if (parentNode) {
      if (!!~parentNode.className.indexOf('ant-form-item-control')) {
        return className === 'billType'
          ? parentNode.classList.add('bill-type-error')
          : parentNode.classList.add(isFeeTypeInvoiceConfig ? 'bill-limit-error-with-defaultvalue' : 'bill-limit-error')
      } else {
        this.setErrorNodeStyle(parentNode, className)
      }
    }
  }

  componentDidUpdate() {
    let { value, form } = this.props
    const editable = form.getFieldValue('editable')
    if (editable && value) {
      let className = !(value.exist || value.noExist || value.wait || value.unify.choose) ? 'billType' : 'limit'
      let node = document.getElementsByClassName('bill-type-checkbox-wrapper')[0]
      this.setErrorNodeStyle(node, className)
    }
  }

  handleEditFlows = () => {
    const { allFlows, value: { noExistConfig: { freeflowScope = [] } = {} } = {} } = this.props
    api
      .open('@modals:LimitFlowScopeModal', {
        title: i18n.get('选择审批流'),
        flows: allFlows,
        freeflowScope: _cloneDeep(freeflowScope) || []
      })
      .then(({ freeflowScope = [] }) => {
        const { noExistConfig = {} } = this.state
        this.onChange({ ...this.state, noExistConfig: { ...(noExistConfig || {}), freeflowScope } })
      })
  }

  handleVisibilityChange = e => {
    const checked = e.target.checked
    const { wait = false, exist = false, noExistConfig = {} } = this.state
    this.onChange({
      ...this.state,
      // 如果选中【限制适用范围】，已有发票和待开发票都没选，则自动选中已有发票，以避免客户无选项可选
      exist: wait || exist || !checked ? exist : true,
      noExistConfig: {
        ...noExistConfig,
        visibility: checked,
        freeflowScope: checked ? noExistConfig?.freeflowScope : []
      }
    })
  }

  handleDeleteFlow = index => {
    const { noExistConfig = {} } = this.state
    const { freeflowScope = [] } = noExistConfig
    freeflowScope.splice(index, 1)
    this.onChange({ ...this.state, noExistConfig: { ...(noExistConfig || {}), freeflowScope } })
  }
  // fix 解决初始化数据required 不被赋值兼容问题
  handleCheckboxCallback = checked => {
    const { form, value } = this.props
    if (checked) {
      form && form.setFieldsValue({ required: value.isRequired })
    }
  }

 hasWaitOption = (show) => {
    return show || api.invokeService('@custom-specification:is:320-corporateReceipt:template', this.props.specification) // 付款管理模版需要待开发票发票
 }

  render() {
    let { value, limitBillPartyList, form, field, bus, filedValue, settingType} = this.props
    const isBillInvoice = field.name === 'invoiceType' && settingType.startsWith('billSpecification:')
    const { isFeeTypeInvoiceConfig, disabled } = field
    const visible = form.getFieldValue('editable')
    if (!value) return null
    let { allFlowsMap, defaultInvoiceTypeList, defaultInvoiceType = '' } = this.state
    let { exist, noExist, noExistConfig = {}, wait, unify, isRequired } = value
    let children = []
    limitBillPartyList.forEach(v => {
      children.push(
        <Option key={v.id} value={v.id} label={v.name}>
          {v.name}
        </Option>
      )
    })
    // 构造requiredConfig组件所需要的参数
    const requiredConfig = {
      field:{
        name:'requiredConfig',
        isCheckValid: true,
        isCheckAttr:true,
        tags:field.tags || [],
        size:'large',
        style:{ marginLeft: 48,marginBottom:0 },
        text:i18n.get('必填条件配置'),
      }
    }

    const isTianYangCorp = isTianYangTestingEnterprise(Fetch.ekbCorpId)

    return (
      <div>
        {visible && (
          <div className={`bill-type-checkbox-wrapper ${isFeeTypeInvoiceConfig ? 'wrapper-background' : ''}`}>
            {isFeeTypeInvoiceConfig && (
              <div className="default-wrapper">
                <span className="default">{i18n.get('显示默认值')}</span>
                <span>
                  <Select
                    onChange={this.changeDefaultInvoiceType}
                    value={defaultInvoiceType}
                    style={{ width: 290, marginLeft: 16 }}
                    size="large"
                  >
                    {defaultInvoiceTypeList.map(el => <Select.Option key={el.key} value={el.key}>{i18n.get(el.label)}</Select.Option>)}
                  </Select>
                </span>
              </div>
            )}
            <Checkbox
              className="checkbox exit-checkbox-wrapper"
              name="exist"
              disabled={wait || disabled || (noExist && noExistConfig?.visibility) || isBillInvoice}
              checked={wait || exist}
              onChange={this.checkChange}
            >
              {i18n.get('已有发票')}
            </Checkbox>
            {exist && (
              <>
                {
                  form.getFieldDecorator('required',{ initialValue:isRequired, valuePropName:'checked'})
                  ( <Checkbox
                      className="exist-properties"
                      name="isRequired"
                      disabled={disabled}
                      onChange={this.checkChange}
                    >
                      {i18n.get('必填')}
                    </Checkbox>)
                  }
                  {
                    form.getFieldDecorator('requiredConfig',{
                      initialValue:filedValue?.requiredConfig || null,
                      rules:[{validator: RequiredFieldConfigValidator(requiredConfig.field,this.props)}]
                    })(<RequiredFieldConfigComponent disabled={disabled} form = {form} bus = {bus} {...requiredConfig} handleCheckboxCallback = {this.handleCheckboxCallback} />)
                  }
              </>
            )}
            <ConditionWrapper condition={() => this.hasWaitOption(!isBillInvoice) }>
              <Checkbox className="checkbox" name="wait" checked={wait} onChange={this.checkChange}>
                {i18n.get('待开发票')}
              </Checkbox>
            </ConditionWrapper>
            {!isBillInvoice && (
              <>
                <Checkbox className="checkbox" name="noExist" checked={noExist} onChange={this.checkChange}>
                  {i18n.get('无发票')}
                </Checkbox>
                {isTianYangCorp && noExist && (
                  <LimitScope
                    allFlowsMap={allFlowsMap}
                    noExistConfig={noExistConfig || {}}
                    handleVisibilityChange={this.handleVisibilityChange}
                    handleDeleteFlow={this.handleDeleteFlow}
                    handleEditFlows={this.handleEditFlows}
                  />
                )}
                <UnifiedBilling
                  disabled={disabled}
                  onChange={this.childChange}
                  value={unify}
                  limitBillPartyList={limitBillPartyList}
                  children={children}
                />
              </>
            )}
          </div>
        )}
      </div>
    )
  }
}

function LimitScope(props) {
  const { allFlowsMap = {}, noExistConfig = {}, handleVisibilityChange, handleDeleteFlow, handleEditFlows } = props

  return (
    <div className="limit-scope">
      <Checkbox checked={noExistConfig.visibility} onChange={handleVisibilityChange}>
        {i18n.get('限制适用范围')}
      </Checkbox>
      {noExistConfig.visibility ? (
        <>
          <div className="title">{i18n.get('适用审批流范围')}</div>
          <div className="flow-tags">
            {(noExistConfig.freeflowScope || []).map((item, index) => (
              <Tag
                key={item.freeflowId}
                closable
                className={classNames('tag-item', {
                  ['is-delete']: !(allFlowsMap || {})[item.freeflowId]?.name
                })}
                onClose={() => handleDeleteFlow(index)}
              >
                {allFlowsMap[item.freeflowId]?.name || i18n.get('{__k0} (已删除)', { __k0: item.freeflowName })}
              </Tag>
            ))}
            <Tag className={classNames('tag-edit', 'tag-item')} onClick={handleEditFlows}>
              {i18n.get('编辑')}
            </Tag>
          </div>
        </>
      ) : null}
    </div>
  )
}
