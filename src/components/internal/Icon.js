import { PureComponent } from 'react'
import { Row } from 'antd'
import { ALL_ICON, getSpecificationIconByName } from './SpecificationIcon'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './Icon.module.less'
// OutlinedGeneralAirplane
import classNames from 'classnames'

const ICON_FONTSIZE = 18

const ICON_STYLE = {
  fontSize: ICON_FONTSIZE,
  position: 'relative',
  top: '-4px',
  left: '1px'
}

@EnhanceField({
  descriptor: {
    type: 'icon'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Icon extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { curIcon: '' }
  }

  onChange = curIcon => {
    const { onChange } = this.props
    onChange && onChange(curIcon)
  }

  componentWillMount() {
    let { bus } = this.props
    bus.watch('dynamic:changeColor', this.changeColor)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('dynamic:changeColor', this.changeColor)
  }

  changeColor = color => {
    this.setState({ curColor: color })
  }

  handleIconChange = icon => {
    this.setState({ curIcon: icon })
    this.onChange(icon)
  }

  render() {
    const { value, field} = this.props
    const { curColor } = this.state
    const { disabled } = field ?? {}

    return (
      <div className={`dis-f jc-sb ${styles['icon-layout']}`}>
        <Row>
          <div className="icon-wrapper">
            {ALL_ICON.map((el, idx) => {
              if (el.component) {
                let style =
                  value === el.name
                    ? { style: { color: curColor, display: 'inline-block' } }
                    : { style: { display: 'inline-block' } }
                const IconCompo = el.component
                return (
                  <span
                    key={el.name}
                    {...style}
                    className={
                      value && value.toUpperCase() === el.name.toUpperCase() ? 'icon icon-btn current' : 'icon icon-btn'
                    }
                    onClick={this.handleIconChange.bind(this, el.name || el)}
                  >
                    <IconCompo style={ICON_STYLE} />
                  </span>
                )
              } else {
                let style =
                  value === el
                    ? { style: { color: curColor, display: 'inline-block' } }
                    : { style: { display: 'inline-block' } }
                return (
                  <svg
                    key={idx}
                    className={ classNames(
                        `${value && value.toUpperCase() === el.toUpperCase() ? 'icon icon-btn current' : 'icon icon-btn'}`,
                        {disabled})
                    }
                    {...style}
                    aria-hidden="true"
                    onClick={this.handleIconChange.bind(this, el)}
                  >
                    <use xlinkHref={el} />
                  </svg>
                )
              }
            })}
          </div>
        </Row>
      </div>
    )
  }
}
