import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio, Checkbox} from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './ReceiptSummarySelect.module.less'
import { summarySelectType } from '../consts'
const RadioGroup = Radio.Group

interface Props {
  onChange: Function
  value: any
  field: any
  form: any
}

interface State {
  checkValue: boolean
}
const feeDetailAndLoan =  'receipt:feeDetailAndLoan' // 选择汇总字段
const feeDetailNotRequired = 'receipt:feeDetailNotRequired' // 费用明细非必填
const abilityType = 'receipt:summarySelect' // 组件type


// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'receiptSummarySelect'
  },
  wrapper: wrapper()
})
export default class ReceiptSummarySelect extends PureComponent<Props, State> {
  state = {
    checkValue: false 
}
  componentDidMount() {
    const { field:{name}, form, value } = this.props
    if((name === feeDetailNotRequired || name === feeDetailAndLoan) && value){
      this.setState({
        checkValue: true
      })
    }
  }

  handleChange = e => {
    const { onChange, field:{ name } } = this.props
    const summarySelect = e.target.value
    if(name === abilityType){
      if(summarySelect === summarySelectType.repay){
        this.setState({checkValue: true})
      }
    }
    onChange && onChange(summarySelect)
  }

  onChange = e => {
    const { onChange,field } = this.props
    const { checked } = e.target

    this.setState({checkValue: checked})
    if(field.name === feeDetailNotRequired){
      onChange && onChange(checked)
      return
    }
    onChange && onChange(Number(checked))
  }
  render() {
    const { field, form, value } = this.props
    const { checkValue } = this.state
    const {
      text,
      tags,
      name,
      relatedConfig
    } = field
    let visible = true
    if (relatedConfig && form) {
      // 该组件被其他组件关联，用related config配置
      const { ability } = relatedConfig
      const abilityValue = form.getFieldValue(ability) 
      if (ability === abilityType && name === feeDetailAndLoan && value && abilityValue === 1) {
        const tempValue = {}
        tempValue[name] = null
        form.setFieldsValue(tempValue)
      }
      visible = abilityValue === relatedConfig?.value 
    }

    return (
      visible && <div className={styles['receiptMoney-wrapper']}>
          {
            name === feeDetailAndLoan || name === feeDetailNotRequired ?
            <Checkbox checked={checkValue} onChange={this.onChange}>
              {text}
            </Checkbox>
            :
            <span className="receiptMoney-title">{text}</span>
          }
          {
            name === abilityType || (checkValue && name !== feeDetailNotRequired) ?
            <RadioGroup onChange={this.handleChange} value={value || 1} style={{ width: '100%' }}>
              {tags.map(v => {
                return (
                  <Radio key={v.value} value={v.value} style={{ width: '100%' }}>
                    {v.label}
                  </Radio>
                )
              })}
            </RadioGroup>
            :
            null
          }
        </div>
    )
  }
}
