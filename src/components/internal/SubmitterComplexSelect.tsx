/**
 * @description 提交人联动赋值下拉
 * <AUTHOR>
 */
import { app } from '@ekuaibao/whispered'
import React, { useState, useEffect, useRef } from 'react'
import { default as ComSelect } from '../../elements/select/ComplexSelect'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { get, concat } from 'lodash';
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import styles from './DataLinkComplexSelect.module.less'
interface GlobalStaffFieldIF extends GlobalFieldIF {
  type: string
  entity: string
}
type IProps = {
  list: any
  value: any
  baseDataProperties: GlobalStaffFieldIF[]
  onChange: (val: any) => void
  settingType: string
  currentComponent: any
}
const SubmitterComplexSelect: React.FC<IProps> = (props: IProps) => {
  const [list, setList] = useState(props.list)
  const [value, setValue] = useState({
    key: props.value,
    label: ''
  })
  const entityFields = useRef({})
  const fnGetStaffFields = async () => {
    const name = props?.currentComponent?.label ?? ''
    const res = await app.invokeService('@contacts:get:staff:dynamic:property', {})
    const data = res?.items.map(item => {
      if (!item.active) return null
      item.type = item?.dataType?.type
      item.entity = item?.dataType?.entity
      return item
    }).filter(Boolean).sort((a, b) => {
      return a?.type?.localeCompare(b?.type)
    })
    const department = {
      name: "defaultDepartment",
      label: "默认部门",
      active: true,
      canAsDimension: true,
      dataType: {
        type: "ref",
        entity: "organization.Department"
      },
      type: "ref",
      entity: "organization.Department",
      cnLabel: "默认部门",
      enLabel: ""
    }
    entityFields.current = { fields: data.concat(department), name }
  }
  const fnGetList = async () => {
    if (list == void 0) {
      await fnUpdateList()
    }
  }
  useEffect(() => {
    fnGetStaffFields()
    fnGetList()
  }, [])
  useEffect(() => {
    setValue({
      key: props.value,
      label: ''
    })
  }, [props.value])

  const fnUpdateList = async () => {
    const { items } = await app.invokeService('@custom-specification:get:submitter:rule:list')
    setList(items)
  }

  const handleAdd = async (value) => {
    if (value.key === 'add') {
      const { settingType, currentComponent, onChange } = props
      const fieldName = get(currentComponent, 'field')
      const res: {id: string} = await app.open('@third-party-manage:ConfigRulesModal', {
        value: entityFields.current,
        settingType,
        // @ts-ignore
        fields: entityFields.current?.fields,
        fieldName,
        isSubmitter: true
      })
      const selectValue = { key: res.id, label: '' }
      setValue(selectValue)
      await fnUpdateList()
      onChange && onChange(selectValue.key)
    } else {
      const { onChange } = props
      onChange && onChange(value.key)
      setValue(value)
    }
  }

  const handleEdit = async (line) => {
    let { settingType, currentComponent } = props
    let id = line.id
    const fieldName = get(currentComponent, 'field')
    await app.open('@third-party-manage:ConfigRulesModal', {
        value: entityFields.current,
        id,
        settingType,
        // @ts-ignore
        fields: entityFields.current?.fields,
        fieldName,
        isSubmitter: true
      })
    await fnUpdateList()
  }

  return (
      <div className={styles['intercon-select-wrapper']}>
        <ComSelect
          classNames="select-wrapper"
          value={value}
          list={list}
          emptyTitle={i18n.get('目前没有任何赋值规则')}
          title={i18n.get('新的赋值规则')}
          handleAdd={handleAdd}
          handleEdit={handleEdit}
        />
      </div>
    )
}
// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'submitter-select' // 为什么叫submitter呢？ 刚开始这是给提交人做的，后来扩展到所有的人员字段。所以命名还是不要太具体
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value) return callback(i18n.get('请选择赋值规则'))
    return callback()
  },
  wrapper: wrapper()
  // @ts-ignore
})(EnhanceConnect((state: any) => ({
  list: state['@custom-specification'].submitterRuleList,
  baseDataProperties: state['@common'].globalFields.data
}))(SubmitterComplexSelect))