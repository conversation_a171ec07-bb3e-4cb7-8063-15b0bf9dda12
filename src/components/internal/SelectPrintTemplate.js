import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import {Checkbox, Radio, Select} from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import Multiple1in1 from '../../images/multiple-1in1.png'
import Multiple2in1 from '../../images/multiple-2in1.png'
import Multiple4in1 from '../../images/multiple-4in1.png'
import './SelectPrintTemplate.less'

@EnhanceField({
  descriptor: {
    type: 'select:printTemplate'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    const { optional } = field
    if (!optional) {
      callback(required(field, value.printTemplateId))
    }
  },
  wrapper: wrapper()
})
export default class SelectBox extends React.PureComponent {
  fnBuildItems() {
    let { tag } = this.props
    let children = []
    tag = tag || []
    tag.forEach(line => {
      let { value, label } = line
      children.push(<Select.Option data-testid={`field-selectPrintTemplate-${label}`} key={value}>{label}</Select.Option>)
    })
    return children
  }

  onChange = value => {
    let { onChange } = this.props
    let result = this.props.value
    onChange &&
      onChange({
        ...result,
        printTemplateId: value,
        isPrintInvoice: false
      })
    this.setState({ withPrintInvoice: this.isCanWithPrintInvoice(value) })
  }
  printPreview = () => {
    let { bus, value } = this.props
    if (!value) return
    bus.emit('previewPrintTemplate', value.printTemplateId)
  }
  isCanWithPrintInvoice(value) {
    let { tag } = this.props
    tag = tag || []
    let flag = false
    tag.forEach(line => {
      if (line.value === value) {
        flag = line.withInvoice
        return
      }
    })
    return flag
  }
  onCheckChange = e => {
    let checked = e.target.checked
    let { onChange, value } = this.props
    onChange &&
      onChange({
        ...value,
        isPrintInvoice: checked,
        multipleMode: '1in1'
      })
  }


  handleMultipleModeChange(e) {
    let { onChange, value } = this.props
    onChange &&
      onChange({
        ...value,
        multipleMode: e.target?.value ?? '1in1'
      })
  }

  componentWillMount() {
    let { value } = this.props
    let withPrintInvoice = this.isCanWithPrintInvoice(value.printTemplateId)
    this.setState({ withPrintInvoice })
  }
  render() {
    let { value, field } = this.props
    let { withPrintInvoice } = this.state
    let children = this.fnBuildItems()
    const { disabled } = field ?? {}

    return (
      <div>
        <div className="dis-f jc-sb">
          <Select
            style={{ width: '100%' }}
            onChange={this.onChange}
            value={value.printTemplateId}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            disabled={disabled}
            data-testid={`field-PrintTemplate-select`}
          >
            {children}
          </Select>
          {!disabled &&<div className="ml-10 w-60 color-blue cur-p" onClick={this.printPreview}>
            {i18n.get('预览模板')}
          </div>}
        </div>
        {withPrintInvoice && (
          <div className="special-depart" style={{ paddingTop: 10 }}>
            <Checkbox checked={value.isPrintInvoice} onChange={this.onCheckChange.bind(this)}>
              {i18n.get('打印单据时，一并打印单据中的电子发票文件')}（{i18n.get('暂仅支持PDF格式')}）
            </Checkbox>
          </div>
        )}
        {value.isPrintInvoice && (
          <Radio.Group value={value.multipleMode ?? '1in1'} onChange={this.handleMultipleModeChange.bind(this)} className="multiple-mode-select" defaultValue='1in1'>
            <Radio className='multiple-mode-select-item' value='1in1' >
              <span>{i18n.get('一页一张')}</span>
              <br/>
              <img src={Multiple1in1} alt=""/>
            </Radio>
            <Radio className='multiple-mode-select-item' value='2in1' >
              <span>{i18n.get('一页两张')}</span>
              <br/>
              <img src={Multiple2in1} alt=""/>
            </Radio>
            <Radio className='multiple-mode-select-item' value='4in1' >
              <span>{i18n.get('一页四张')}</span>
              <br/>
              <img src={Multiple4in1} alt=""/>
            </Radio>
          </Radio.Group>
        )}
      </div>
    )
  }
}
