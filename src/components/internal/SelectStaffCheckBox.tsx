import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox } from 'antd'
import styles from './SelectStaffCheckBox.module.less'
import FakeInput from '../../elements/puppet/FakeInput'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import TagSelector from '../../elements/tag-selector'
import { remove } from 'lodash'
import { wrapper } from '../layout/FormWrapper'
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'select-staff-checkbox'
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data
}))
export default class SelectStaffCheckBox extends PureComponent<any> {
  componentDidMount() {
    api.invokeService('@common:get:staffs:roleList:department')
  }
  onChange = e => {
    const { onChange, value } = this.props
    const { checked } = e.target
    if (checked) {
      onChange &&
        onChange(
          value || { departments: [], departmentsIncludeChildren: true, roles: [], staffs: [], fullVisible: true }
        )
    } else {
      onChange && onChange(null)
    }
  }
  handleSelectStaff = () => {
    const { value, onChange } = this.props
    const { departments = [], departmentsIncludeChildren = true, roles = [], staffs = [] } = value || {}
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          {
            type: 'department-member',
            multiple: true,
            checkedKeys: staffs
          },
          {
            type: 'department',
            multiple: true,
            checkedKeys: departments
          },
          { type: 'role', multiple: true, checkedKeys: roles }
        ],
        departmentsIncludeChildren
      })
      .then((data: any) => {
        const { checkedList } = data
        const staffs = checkedList.find(o => o.type === 'department-member').checkedKeys || []
        const departments = checkedList.find(o => o.type === 'department').checkedKeys || []
        const roles = checkedList.find(o => o.type === 'role').checkedKeys || []
        const val = { ...value, departments, staffs, roles }
        onChange && onChange(val)
      })
  }
  handleTagsChanged = (tags, deleteTag) => {
    const { onChange, value } = this.props
    const { staffs = [], departments = [], roles = [] } = value
    remove(staffs, id => id === deleteTag.id)
    remove(departments, id => id === deleteTag.id)
    remove(roles, id => id === deleteTag.id)
    const val = { ...value, departments, staffs, roles }
    onChange && onChange(val)
  }
  getDeptItemsByIds = (list = [], ids = []) => {
    const items = []
    const fn = item => {
      if (ids && ids.indexOf(item.id) > -1) {
        items.push(item)
      }
      item.children = item.children || []
      if (item.children.length) {
        item.children.forEach(c => {
          fn(c)
        })
      }
    }
    list.forEach(item => {
      fn(item)
    })
    return items
  }
  getItemByIds = (data = [], ids = []) => {
    return data.filter(line => {
      return ids.indexOf(line.id) > -1
    })
  }
  valueParse = value => {
    if (!value) {
      return { tags: [] }
    }
    return {
      tags: [
        ...this.getDeptItemsByIds(this.props.departmentTree, value.departments || []),
        ...this.getItemByIds(this.props.staffs, value.staffs || []),
        ...this.getItemByIds(this.props.roles, value.roles || [])
      ]
    }
  }
  render() {
    const { field, value } = this.props
    const { text, size, style = {}, disabled = false,label ,hiddenLabel} = field
    const { tags } = this.valueParse(value)
    return (
      <div className={styles['SelectStaffCheckBox']} style={...style}>
        <div>
          <Checkbox onChange={this.onChange} disabled={disabled} checked={value}>
            <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
          </Checkbox>
        </div>
        {value && (
          <div className="mt-8">
            <FakeInput onClick={this.handleSelectStaff}>
              <TagSelector
                placeholder={i18n.get('请选择人员')}
                className={styles.staff_select_tags}
                value={tags}
                onChange={this.handleTagsChanged}
              />
            </FakeInput>
          </div>
        )}
      </div>
    )
  }
}
