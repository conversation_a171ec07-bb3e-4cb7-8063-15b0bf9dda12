import React, { useEffect, useMemo, useState } from 'react'
import { Select } from 'antd'
import { T } from '@ekuaibao/i18n'
import { cloneDeep } from 'lodash'
import styles from './IgnoreSpecification.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { SpecificationGroup } from '@ekuaibao/ekuaibao_types'
const Option = Select.Option

interface IgnoreSpecificationProps {
  specificationGroups: SpecificationGroup[]
  options: any[]
  value: any
  onChange: (value: any) => void
  inConditionModal: boolean
  disabled?: boolean
}

export const IgnoreSpecification: React.FC<IgnoreSpecificationProps> = ({
  value,
  disabled,
  onChange,
  options,
  specificationGroups,
  inConditionModal
}) => {
  const [o, setO] = useState(options)
  const selectOptions = useMemo(() => {
    return o?.map(item => {
      return (
        <Option key={item.id} value={item.id}>
          {item.name}
        </Option>
      )
    })
  }, [o])
  const handleChange = (newValue: any) => {
    onChange?.(newValue)
  }
  useEffect(() => {
    const optionsIds = options?.map(item => item.id)
    const nullValues = value.filter(id => !optionsIds?.includes(id))
    if (!!nullValues.length) {
      const spes = specificationGroups
        .reduce((result, next) => result.concat(next.specifications), [])
        .filter(item => nullValues.includes(item.id))
      if (!!spes.length) {
        setO(
          options.concat(
            cloneDeep(spes).map(item => {
              item.name = `${item?.name}「已经不能再关联此申请单」`
              return item
            })
          )
        )
      }
    }
  }, [value, options])
  return (
    <div className={inConditionModal? styles['ignore-specification-wrapper-pop'] : styles['ignore-specification-wrapper']}>
      <div className="title">
        <T name="请选择不计算次数的单据范围" />
      </div>
      <Select
        mode="multiple"
        className="select-specification"
        placeholder={i18n.get('选择不需要计算次数的单据模版')}
        value={value}
        disabled={disabled}
        onChange={handleChange}
      >
        {selectOptions}
      </Select>
    </div>
  )
}

export default EnhanceConnect(state => ({
  specificationGroups: state['@custom-specification'].specificationGroups || []
}))(IgnoreSpecification)
