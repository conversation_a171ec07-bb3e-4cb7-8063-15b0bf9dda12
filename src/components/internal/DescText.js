/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/25 下午3:34.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
@EnhanceField({
  descriptor: {
    type: 'descText'
  },
  wrapper: wrapper()
})
export default class Text extends PureComponent {
  render() {
    let { field } = this.props
    return <div>{field.showValue}</div>
  }
}
