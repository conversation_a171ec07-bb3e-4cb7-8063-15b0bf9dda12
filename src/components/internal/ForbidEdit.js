/**************************************************
 * Created by nanyuantingfeng on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { checkPriority, getPriorityValue } from '@ekuaibao/lib/lib/checkFieldPriority'
import { Checkbox } from 'antd'
import styles from './ForbidEdit.module.less'
@EnhanceField({
  descriptor: {
    type: 'forbidEdit'
  }
})
export default class ForbidEdit extends PureComponent {
  constructor(props) {
    super(props)
  }

  onChange = e => {
    let { onChange, form } = this.props
    let { checked } = e.target
    const priorityOldVal = form.getFieldValue('priority')
    const editableOldVal = form.getFieldValue('editable')
    const priorityOldChecked = checkPriority(priorityOldVal, editableOldVal)
    let newValue = getPriorityValue(priorityOldChecked, !checked)
    form.setFieldsValue({
      priority: newValue
    })
    onChange && onChange(!checked)
  }

  render() {
    let { field, value, form } = this.props
    if (value === undefined) {
      value = field.editable === undefined ? true : field.editable
    }
    const priority = form.getFieldValue('priority')
    if (!priority || !checkPriority(priority, value)) {
      return null
    }
    let { text, label, style } = field
    return (
      <div className={styles.priority} style={style} data-cy="ekb-priorityEditable">
        <div className={'new_label'}>{label}</div>
        <Checkbox onChange={this.onChange} checked={!value}>
          <span className={'text-large'}>{text}</span>
        </Checkbox>
      </div>
    )
  }
}
