/**************************************************
 * Created by zhaohuabing on 2018/7/26 上午11:59.
 **************************************************/

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import style from './BillTypeRadioGroup.module.less'

const RadioGroup = Radio.Group

@EnhanceField({
  descriptor: {
    type: 'bill-radio-group'
  },
  wrapper: wrapper()
})
export default class BillTypeRadioGroup extends PureComponent {
  constructor(props) {
    super(props)
  }

  onChange = e => {
    let { onChange } = this.props
    let { value } = e.target
    onChange && onChange(value)
  }

  render() {
    let { value, field } = this.props
    const { disabled } = field ?? {}
    value = value ? value : false
    return (
      <div className={style['bill-type-radio-group-wrapper']}>
        <RadioGroup disabled={disabled} onChange={this.onChange} value={value}>
          <Radio value={false}>{i18n.get('否')}</Radio>
          <Radio value={true}>{i18n.get('是')}</Radio>
        </RadioGroup>
      </div>
    )
  }
}
