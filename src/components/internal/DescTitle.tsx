import React, { PureComponent } from 'react'
import { Divider } from 'antd'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './DescTitle.module.less'

interface IProps {
  field: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'descTitle'
  }
})
export default class DescTitle extends PureComponent<IProps> {
  render() {
    const {
      field: { tags }
    } = this.props

    return (
      <div className={styles.meta_name} data-cy="ekb-description">
        {tags.map((v: any) => (
          <div key={v['label']} className="meta_name_item">
            <span className="meta_name_label">
              {v['label']}
              {i18n.get('：')}
            </span>
            <span className="meta_name_val">{v['value']}</span>
            {v['tip'] && (
              <span className="meta_name_tip">
                <span>{v['tip']}</span>
              </span>
            )}
            {v['mc_tip'] && (
              <span className="meta_name_tip">
                <span>{v['mc_tip']}</span>
              </span>
            )}
          </div>
        ))}
        <Divider dashed />
      </div>
    )
  }
}
