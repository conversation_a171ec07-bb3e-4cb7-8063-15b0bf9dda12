import React from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'select:code:rule'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    const { optional } = field
    if (!optional) {
      callback(required(field, value.codeRuleId))
    }
  },
  wrapper: wrapper()
})
export default class SelectCodeRule extends React.PureComponent {
  onChange = data => {
    let { onChange, value } = this.props
    value.codeRuleId = data
    onChange && onChange(value)
  }

  fnBuildItems() {
    let { tag } = this.props
    let children = []
    tag = tag || []
    tag.forEach(line => {
      let { id, name } = line
      children.push(<Select.Option data-testid={`field-selectCodeRule-${name}`} key={id}>{name}</Select.Option>)
    })
    return children
  }

  render() {
    const { value, field } = this.props
    const children = this.fnBuildItems()
    const { placeholder } = field
    const disabled = isDisable(this.props)
    return (
      <div className="dis-f jc-sb">
        <Select
          style={{ flex: 1 }}
          disabled={disabled}
          placeholder={placeholder}
          onChange={this.onChange}
          getPopupContainer={triggerNode => triggerNode.parentNode}
          value={value?.codeRuleId}
          data-testid={`field-CodeRule-select`}
          size="large"
        >
          {children}
        </Select>
      </div>
    )
  }
}
