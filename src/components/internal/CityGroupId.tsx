
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Select } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'

const { Option } = Select

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'cityGroupId'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { form = {} } = props
    const hasForbiddenCityGroup = form.getFieldValue('hasForbiddenCityGroup')
    if (hasForbiddenCityGroup && !value) {
      return callback(i18n.get('请在城市字段中选择城市组'))
    }
    return callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  cityGroupList: state['@city-settings'].cityGroupList
}))
export default class CityGroupId extends PureComponent<any> {
  componentDidMount() {
    api.invokeService('@city-settings:get:cityGroupList')
  }

  handleValueChange = (key: string, value: any) => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    const { field, value, cityGroupList, form } = this.props
    const { style = {}, disabled = false, size } = field
    const hasForbiddenCityGroup = form.getFieldValue('hasForbiddenCityGroup')
    return (
      <div className='' style={...style}>
        {hasForbiddenCityGroup && <Select
            size={size}
            value={value}
            onChange={(i) => this.handleValueChange('cityGroupId', i)}
            placeholder={i18n.get('请选择城市组')}
            style={{ minWidth: 100, width: '70%' }}
            disabled={disabled}>
            {cityGroupList.map((i: any) => (
              <Option value={i.id} key={i.id} disabled={!i.status}>
                {!i.status ? `${i.name}${i18n.get('(已停用)')}` : i.name}
              </Option>
            ))}
          </Select>}
      </div>
    )
  }
}
