import loadable, { LoadableComponent } from '@loadable/component'

import { ENUM_TYPES } from './consts'
import { includes } from 'lodash'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:43.
 **************************************************/

const Attachment = loadableWithDescriptor(() => import('./dynamic/Attachment.readonly'), {
  type: 'attachments'
})
// 只读状态下和普通附件表现一致
const AIAttachment = loadableWithDescriptor(() => import('./dynamic/AIAttachment.readonly'), {
  type: 'aiAttachments'
})
const Details = loadableWithDescriptor(() => import('./dynamic/Details.readonly'), {
  test({ type = '' }) {
    return type === 'requisitionDetails' || type === 'details'
  }
})
const Money = loadableWithDescriptor(() => import('./dynamic/Money.readonly'), {
  type: 'money'
})
const PayeeInfo = loadableWithDescriptor(() => import('./dynamic/PayeeInfo.readonly'), {
  type: 'payeeInfo'
})
const Ref = loadableWithDescriptor(() => import('./dynamic/Ref.readonly'), {
  test({ type }) {
    return (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      !includes(ENUM_TYPES, type) &&
      type !== 'ref:basedata.Enum.currency'
    )
  }
})
const RefStaff = loadableWithDescriptor(() => import('./dynamic/RefStaff.readonly'), {
  type: 'ref:organization.Staff'
})
const Date = loadableWithDescriptor(() => import('./dynamic/Date.readonly'), {
  type: 'date'
})
const Number = loadableWithDescriptor(() => import('./dynamic/Number.readonly'), {
  type: 'number'
})
const DateRange = loadableWithDescriptor(() => import('./dynamic/DateRange.readonly'), {
  type: 'dateRange'
})
const Switcher = loadableWithDescriptor(() => import('./dynamic/Switcher.readonly'), {
  type: 'switcher'
})
const ApportionReadonly = loadableWithDescriptor(() => import('./dynamic/Apportion.readonly'), {
  type: 'apportions'
})
const DataLink = loadableWithDescriptor(() => import('./dynamic/DataLink.readonly'), {
  type: 'dataLink'
})
const InvoiceSelect = loadableWithDescriptor(() => import('./dynamic/InvoiceSelect.readonly'), {
  type: 'invoice'
})
// I/O Common Component
const Label = loadableWithDescriptor(() => import('./dynamic/Label.readonly'), {
  test({ type }) {
    return !!~['specification', 'text', 'textarea', 'list'].indexOf(type)
  }
})
const Separator = loadableWithDescriptor(() => import('./dynamic/Separator.readonly'), {
  type: 'separator'
})
const HABWidget = loadableWithDescriptor(() => import('./dynamic/HABWidget'), {
  type: 'widget'
})
const Annotation = loadableWithDescriptor(() => import('./dynamic/Annotation'), {
  type: 'annotation'
})
const City = loadableWithDescriptor(() => import('./dynamic/City.readonly'), {
  type: 'city'
})
const RefEnum = loadableWithDescriptor(() => import('./dynamic/RefEnum.readonly'), {
  test({ type = '' }) {
    return includes(ENUM_TYPES, type)
  }
})
const LinkRequisitionInfoReadonly = loadableWithDescriptor(() => import('./dynamic/LinkRequisitionInfo.readonly'), {
  type: 'linkRequisitionInfo'
})
const ExpenseLinkReadonly = loadableWithDescriptor(() => import('./dynamic/ExpenseLink.readonly'), {
  test({ type }) {
    return type === 'expenseLink' || type === 'expenseLinks'
  }
})
const SelectSearch = loadableWithDescriptor(() => import('./dynamic/SelectSearch.readonly'), {
  type: 'select_search'
})
const Trips = loadableWithDescriptor(() => import('./dynamic/Trips.readonly'), { type: 'trips' })
const VPhoto = loadableWithDescriptor(() => import('./dynamic/VPhoto.readonly'), {
  name: 'vphoto_order'
})
const MutilStaff = loadableWithDescriptor(() => import('./dynamic/MutilStaff.readonly'), {
  type: 'list:ref:organization.Staff'
})
const DataLinkEdit = loadableWithDescriptor(() => import('./dynamic/dataLinkEdit/DataLinkEdit.readonly'), {
  test(field) {
    const { type, referenceData, name } = field
    return (type === 'dataLinkEdits' && referenceData.type !== 'TRIP') || name === 'budgetAdjustDetails'
  }
})
const DataLinkList = loadableWithDescriptor(() => import('./dynamic/DataLinkList.readonly'), {
  type: 'dataLinks'
})
const RelatedDetails = loadableWithDescriptor(() => import('./dynamic/RelatedDetails.readonly'), {
  type: 'linkDetailEntities'
})
const TripDataLink = loadableWithDescriptor(() => import('./dynamic/dataLinkEdit/TripDataLink.readonly'), {
  test(field) {
    const { type, referenceData } = field
    return type === 'dataLinkEdits' && referenceData.type === 'TRIP'
  }
})
const MutilDimensionList = loadableWithDescriptor(() => import('./dynamic/MutilDimensionList.readonly'), {
  test({ type = '' }) {
    return type.startsWith('list:ref:basedata.Dimension')
  }
})
const SplitCalculation = loadableWithDescriptor(() => import('./dynamic/SplitCalculation.readonly'), {
  type: 'splitCalculation'
})
const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })
const InterConnection = loadableWithDescriptor(() => import('./dynamic/interConnection/index.readonly'), {
  test(field) {
    const { type } = field
    return type === 'engineConnect'
  }
})

const MutilPayeeInfo = loadableWithDescriptor(() => import('./dynamic/MutilPayeeInfo.readonly'), {
  type: 'list:ref:pay.PayeeInfo'
})
const RadioGroup = loadableWithDescriptor(() => import('./dynamic/RadioGroupSupplier.readonly'), {
  type: 'radio-group-supplier'
})
const SourceType = loadableWithDescriptor(() => import('./dynamic/SourceType.readonly'), {
  name: 'sourceType'
})
const paymentReadonly = loadableWithDescriptor(() => import('./dynamic/payment.readonly'), {
  name: 'payment'
})
const supplierTilte = loadableWithDescriptor(() => import('./dynamic/SupplierTitle'), {
  type: 'supplierTitle'
})
const Subsidy = loadableWithDescriptor(() => import('./dynamic/Subsidy'), {
  type: 'subsidy'
})
const CheckingBillForm = loadableWithDescriptor(() => import('./dynamic/CheckingBillForm'), {
  name: 'checkingBillForm'
})
const Association = loadableWithDescriptor(() => import('./dynamic/Association.readonly'), {
  name: 'isOpenAssociation'
})
const BlockUI = loadableWithDescriptor(() => import('./dynamic/BlockUI.readonly'), {
  type: 'engineBlockUI'
})
const Currency = loadableWithDescriptor(() => import('./dynamic/Currency.readonly'), {
  type: 'ref:basedata.Enum.currency'
})
const DataLinkStaff = loadableWithDescriptor(() => import('./dynamic/DataLinkStaff'), {
  type: 'list:ref:dataLink.Staff'
})

const TravelList = loadableWithDescriptor(() => import('./dynamic/TravelList/TravelList.readonly'), {
  type: 'travel'
})

const GroupTitle = loadableWithDescriptor(() => import('./dynamic/GroupTitle'), {
  type: 'group'
})
const FlowLinks = loadableWithDescriptor(() => import('./dynamic/FlowLinks/FlowLinks.readonly'), {
  type: 'flowLinks'
})
const Amortization = loadableWithDescriptor(() => import('./dynamic/Amortization.readonly'), {
  type: 'amortizes'
})
const ReconcileResultForm = loadableWithDescriptor(() => import('./dynamic/ReconcileResultForm'), {
  name: 'reconcileResultForm'
})
const CorporateExpenseCard = loadableWithDescriptor(() => import('./dynamic/EBusinessCard.readonly'), {
  type: 'corporateExpenseCard'
})
export const readonly = [
  Attachment,
  Date,
  Ref,
  RefStaff,
  Details,
  Money,
  PayeeInfo,
  Label,
  Separator,
  Annotation,
  Number,
  DateRange,
  City,
  LinkRequisitionInfoReadonly,
  ExpenseLinkReadonly,
  RefEnum,
  Switcher,
  ApportionReadonly,
  SelectSearch,
  DataLink,
  InvoiceSelect,
  Trips,
  VPhoto,
  MutilStaff,
  DataLinkEdit,
  DataLinkList,
  RelatedDetails,
  TripDataLink,
  MutilDimensionList,
  SplitCalculation,
  InterConnection,
  MutilPayeeInfo,
  RadioGroup,
  SourceType,
  paymentReadonly,
  supplierTilte,
  Unknown,
  Subsidy,
  Currency,
  CheckingBillForm,
  Association,
  BlockUI,
  DataLinkStaff,
  TravelList,
  FlowLinks,
  GroupTitle,
  Amortization,
  ReconcileResultForm,
  CorporateExpenseCard,
  HABWidget,
  AIAttachment
]
