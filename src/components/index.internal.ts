import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

/**************************************************
 * Created by kaili on 2017/7/24 下午4:19.
 **************************************************/
const SelectHabExtend = loadableWithDescriptor(() => import('./internal/SelectHabExtend'), {
  type: 'select:hab:extend'
})

const CheckBox = loadableWithDescriptor(() => import('./internal/CheckBox'), {
  type: 'checkbox'
})
const BillExceedCheckbox = loadableWithDescriptor(() => import('./internal/BillExceedCheckbox'), {
  type: 'bill-exceed-checkbox'
})
const LinkRequisitionControlled = loadableWithDescriptor(() => import('./internal/LinkRequisitionControlled'), {
  type: 'linkRequisitionControlled'
})
const CheckBoxGroup = loadableWithDescriptor(() => import('./internal/CheckBoxGroup'), {
  type: 'checkbox-group'
})
const CheckBoxScopeTags = loadableWithDescriptor(() => import('./internal/CheckBoxScopeTags'), {
  type: 'checkbox:scope:tags'
})
const CheckBoxFieldBackfill = loadableWithDescriptor(() => import('./internal/CheckBoxFieldBackfill'), {
  type: 'checkbox:field:backfill'
})
const CheckBoxTagsTree = loadableWithDescriptor(() => import('./internal/CheckBoxFeetypeTags'), {
  type: 'checkbox:feetype:tags'
})
const CheckBoxDefaultValue = loadableWithDescriptor(() => import('./internal/CheckBoxDefaultValue'), {
  type: 'checkbox:default:value'
})
const RadioGroup = loadableWithDescriptor(() => import('./internal/RadioGroup'), {
  type: 'radio-group'
})
const WhiteSpace = loadableWithDescriptor(() => import('./internal/WhiteSpace'), {
  type: 'whitespace'
})
const RadioGroupMore = loadableWithDescriptor(() => import('./internal/RadioGroupMore'), {
  type: 'radio-group-more'
})
const TagList = loadableWithDescriptor(() => import('./internal/TagList'), {
  type: 'tag-list'
})
const NumberRange = loadableWithDescriptor(() => import('./internal/NumberRange'), {
  type: 'numberRange'
})
const DepRuleCheckBox = loadableWithDescriptor(() => import('./internal/DepRuleCheckBox'), {
  type: 'depRule-checkbox'
})
const filterRuleCheckBox = loadableWithDescriptor(() => import('./internal/FilterRuleCheckBox'), {
  type: 'filter-rule-checkbox'
})
const descTitle = loadableWithDescriptor(() => import('./internal/DescTitle'), {
  type: 'descTitle'
})
const CheckBoxSelectTags = loadableWithDescriptor(() => import('./internal/CheckBoxSelectTags'), {
  type: 'checkbox:select:tags'
})
const ApplyContentRuleRadioGroup = loadableWithDescriptor(() => import('./internal/ApplyContentRuleRadioGroup'), {
  type: 'applyContentRule'
})
const SelectFlow = loadableWithDescriptor(() => import('./internal/SelectFlow'), {
  type: 'select:flow'
})
const DescText = loadableWithDescriptor(() => import('./internal/DescText'), {
  type: 'descText'
})
const ComplexSelect = loadableWithDescriptor(() => import('./internal/DataLinkComplexSelect'), {
  type: 'complex-select'
})
const SubmitterSelect = loadableWithDescriptor(() => import('./internal/SubmitterComplexSelect'), {
  type: 'submitter-select'
})
const WidgetComplexSelect = loadableWithDescriptor(() => import('./internal/WidgetComplexSelect'), {
  type: 'widget-complex-select'
})
const ComplexFilter = loadableWithDescriptor(() => import('./internal/DataLinkComplexFilter'), {
  type: 'complex-filter'
})
const DetailComplexSelect = loadableWithDescriptor(() => import('./internal/DetailComplexSelect'), {
  type: 'detail-complex-select'
})
const SubsidyComplexSelect = loadableWithDescriptor(() => import('./internal/SubsidyComplexSelect'), {
  type: 'subsidy-complex-select'
})
const BillTypeCheckboxGroup = loadableWithDescriptor(() => import('./internal/BillTypeCheckboxGroup'), {
  type: 'bill-checkbox-group'
})
const BillTypeRadioGroup = loadableWithDescriptor(() => import('./internal/BillTypeRadioGroup'), {
  type: 'bill-radio-group'
})
const BlockUI = loadableWithDescriptor(() => import('./internal/BlockUIConfig'), {
  type: 'blockUI'
})
const Icon = loadableWithDescriptor(() => import('./internal/Icon'), {
  type: 'icon'
})
const Color = loadableWithDescriptor(() => import('./internal/Color'), {
  type: 'color'
})
const RequisitionCanLoan = loadableWithDescriptor(() => import('./internal/RequisitionCanLoan'), {
  type: 'requisitionCanLoan'
})
const AutoShareRule = loadableWithDescriptor(() => import('./internal/autoShareRule'), {
  type: 'autoShareRule'
})
const tripPlatform = loadableWithDescriptor(() => import('./internal/tripPlatformJump'), {
  type: 'tripPlatform'
})
const InvoiceImportCheckboxGroup = loadableWithDescriptor(() => import('./internal/InvoiceImportCheckboxGroup'), {
  type: 'import-invoice-checkbox-group'
})
const Describe = loadableWithDescriptor(() => import('./internal/Describe'), {
  type: 'describe'
})
const Divider = loadableWithDescriptor(() => import('./internal/Divider'), {
  type: 'divider'
})
const Priority = loadableWithDescriptor(() => import('./internal/Priority'), {
  name: 'priority'
})
const ForbidEdit = loadableWithDescriptor(() => import('./internal/ForbidEdit'), {
  type: 'forbidEdit'
})
const ApportionRuleRadio = loadableWithDescriptor(() => import('./internal/ApportionRuleRadio'), {
  type: 'radio-select'
})
const ExpenseComplexSelect = loadableWithDescriptor(() => import('./internal/ExpenseComplexSelect'), {
  type: 'expense-complex-select'
})
const MultPayeesCheckBoxSelectTags = loadableWithDescriptor(() => import('./internal/MultPayeesCheckBoxSelectTags'), {
  type: 'multipayees:checkbox:select:tags'
})
const DescribeWithPopup = loadableWithDescriptor(() => import('./internal/DescribeWithPopup'), {
  type: 'describeWithPopup'
})
const DetailVisibleCheckBox = loadableWithDescriptor(() => import('./internal/DetailVisibleCheckBox'), {
  type: 'detailVisibleCheckBox'
})
const DestrictedTravelTypeSelect = loadableWithDescriptor(() => import('./internal/DestrictedTravelTypeSelect'), {
  type: 'destrictedTravelType'
})
const BeneficiaryAccountRule = loadableWithDescriptor(() => import('./internal/BeneficiaryAccountRule'), {
  type: 'beneficiaryAccountRule'
})
const ReceiptSummarySelect = loadableWithDescriptor(() => import('./internal/ReceiptSummarySelect'), {
  type: 'receiptSummarySelect'
})
const AllowedRepaymentSelect = loadableWithDescriptor(() => import('./internal/AllowedRepaymentSelect'), {
  type: 'allowedRepaymentSelect'
})
const WrittenOffRateType = loadableWithDescriptor(() => import('./internal/WrittenOffRateType'), {
  type: 'writtenOffRateType'
})

const RepaymentDateLimitField = loadableWithDescriptor(
  () => import('./internal/repaymentdate/RepaymentDateLimitField'),
  {
    type: 'repaymentdatelimit'
  }
)
const RepaymentDateModifyField = loadableWithDescriptor(
  () => import('./internal/repaymentdate/RepaymentDateModifyField'),
  {
    type: 'repaymentdatemodify'
  }
)

const TextFiled = loadableWithDescriptor(() => import('./internal/TextField'), {
  type: 'text-field'
})

const RequiredFieldConfig = loadableWithDescriptor(() => import('./internal/RequiredFieldConfig'), {
  type: 'required-field-config'
})

const HideFieldConfig = loadableWithDescriptor(() => import('./internal/HideFieldConfig'), {
  type: 'hide-field-config'
})
const SelectStaffCheckBox = loadableWithDescriptor(() => import('./internal/SelectStaffCheckBox'), {
  type: 'select-staff-checkbox'
})
const SelectDateTime = loadableWithDescriptor(() => import('./internal/SelectDateTime'), {
  type: 'select-date-time'
})
const AuditScopeSelect = loadableWithDescriptor(() => import('./internal/AuditScopeSelect'), {
  type: 'audit_scope_select'
})

const HideFieldsSelect = loadableWithDescriptor(() => import('./internal/HideFieldsSelect'), {
  type: 'hide-fields-checkbox'
})

const HasForbiddenCityGroup = loadableWithDescriptor(() => import('./internal/HasForbiddenCityGroup'), {
  type: 'hasForbiddenCityGroup'
})

const CityGroupId = loadableWithDescriptor(() => import('./internal/CityGroupId'), {
  type: 'cityGroupId'
})

const FeetypeDynamicItem = loadableWithDescriptor(() => import('./internal/FeetypeDynamicItem'), {
  type: 'feetype-dynamic-item'
})

const SpecificationDynamicItem = loadableWithDescriptor(() => import('./internal/SpecificationDynamicItem'), {
  type: 'specification-dynamic-item'
})

const ApplyGenerateFee = loadableWithDescriptor(() => import('./internal/ApplyGenerateFee'), {
  type: 'checkbox:generate:fee'
})

const StaffRangeRuleCheckBox = loadableWithDescriptor(() => import('./internal/StaffRangeRuleCheckBox'), {
  type: 'staff-Range-Rule-checkbox'
})

const onlyDetailDateCheckBox = loadableWithDescriptor(() => import('./internal/OnlyDetailDateCheckbox'), {
  type: 'only-detail-date-checkbox'
})

const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })

const CheckBoxSelectShares = loadableWithDescriptor(() => import('./internal/CheckBoxSelectShares'), {
  type: 'checkbox:select:shares'
})

const StaffRangeFormItem = loadableWithDescriptor(() => import('./internal/StaffRangeFormItem'), {
  type: 'staff-range-form-item'
})

const DisplayRuleConfig = loadableWithDescriptor(() => import('./internal/DisplayRuleConfig'), {
  type: 'display-rule-config'
})

const FlowLinksStateSettle = loadableWithDescriptor(() => import('./internal/FlowLinksStateSettle'), {
  type: 'flow-links-state'
})

const CheckBoxDefaultCurrency = loadableWithDescriptor(() => import('./internal/CheckBoxDefaultCurrency'), {
  type: 'checkbox:default:currency'
})

const SelectReceiveCurrency = loadableWithDescriptor(() => import('./internal/SelectReceiveCurrency'), {
  name: 'pay:currencyRange'
})

const limitOrderCondition = loadableWithDescriptor(() => import('./internal/limitOrderCondition'), {
  type: 'limitOrderCondition'
})

const CheckTravelCloseLoop = loadableWithDescriptor(() => import('./internal/CheckTravelCloseLoop'), {
  type: 'checkbox:travel:closed'
})

const CheckTravelOrderConfirm = loadableWithDescriptor(() => import('./internal/CheckTravelOrderConfirm'), {
  type: 'checkbox:order:confirmed'
})

const DataLinkSubTypeSelect = loadableWithDescriptor(() => import('./internal/DataLinkSubTypeSelect'), {
  type: 'dataLinkSubTypeIdSelect'
})

const ExpenseComplexSelectAmortize = loadableWithDescriptor(() => import('./internal/ExpenseComplexSelectAmortize'), {
  type: 'expense-complex-select-amortize'
})

const InternalSelect = loadableWithDescriptor(() => import('./internal/InternalSelect'), {
  type: 'checkbox:limit:select'
})

const BehaviorConfig = loadableWithDescriptor(() => import('./internal/BehaviorConfig'), { type: 'behavior-config' })

const AiAttachmentConfig = loadableWithDescriptor(() => import('./internal/AiAttachmentConfig'), {
  type: 'ai-attachment-config'
})

export const internal = [
  TextFiled,
  CheckBox,
  BillExceedCheckbox,
  CheckBoxGroup,
  CheckBoxScopeTags,
  CheckBoxFieldBackfill,
  CheckBoxTagsTree,
  CheckBoxDefaultValue,
  RadioGroup,
  WhiteSpace,
  RadioGroupMore,
  TagList,
  NumberRange,
  DepRuleCheckBox,
  filterRuleCheckBox,
  descTitle,
  CheckBoxSelectTags,
  ApplyContentRuleRadioGroup,
  SelectFlow,
  DescText,
  ComplexSelect,
  SubmitterSelect,
  WidgetComplexSelect,
  ComplexFilter,
  DetailComplexSelect,
  SubsidyComplexSelect,
  BillTypeCheckboxGroup,
  BillTypeRadioGroup,
  BlockUI,
  Icon,
  Color,
  InvoiceImportCheckboxGroup,
  Describe,
  Divider,
  RequisitionCanLoan,
  InvoiceImportCheckboxGroup,
  Priority,
  ForbidEdit,
  ApportionRuleRadio,
  ExpenseComplexSelect,
  MultPayeesCheckBoxSelectTags,
  LinkRequisitionControlled,
  DescribeWithPopup,
  DetailVisibleCheckBox,
  DestrictedTravelTypeSelect,
  BeneficiaryAccountRule,
  ReceiptSummarySelect,
  WrittenOffRateType,
  RepaymentDateLimitField,
  RepaymentDateModifyField,
  RequiredFieldConfig,
  HideFieldConfig,
  SelectStaffCheckBox,
  SelectDateTime,
  HideFieldsSelect,
  HasForbiddenCityGroup,
  CityGroupId,
  Unknown,
  tripPlatform,
  FeetypeDynamicItem,
  SpecificationDynamicItem,
  ApplyGenerateFee,
  AuditScopeSelect,
  AutoShareRule,
  StaffRangeRuleCheckBox,
  CheckBoxSelectShares,
  onlyDetailDateCheckBox,
  StaffRangeFormItem,
  DisplayRuleConfig,
  FlowLinksStateSettle,
  CheckBoxDefaultCurrency,
  AllowedRepaymentSelect,
  limitOrderCondition,
  CheckTravelCloseLoop,
  CheckTravelOrderConfirm,
  DataLinkSubTypeSelect,
  ExpenseComplexSelectAmortize,
  InternalSelect,
  BehaviorConfig,
  SelectHabExtend,
  SelectReceiveCurrency,
  AiAttachmentConfig
]
