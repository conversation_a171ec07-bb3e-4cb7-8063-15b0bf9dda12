import './less/CitiesPicker.less'
import React, { PureComponent, Fragment } from 'react'
import { Icon } from 'antd'
import queue from './utils/queue'

interface City {
    id: string
    name: string
    isLast: boolean
    parentId?: string
    [key: string]: any
  }
  
  interface HotCity {
    inland: City[]
    international: City[]
  }
  
  interface SelectProps {
    onChange: (value: City | City[]) => void
    fetchCity: (cityId?: string, cityType?: 'inland' | 'international', cityGroupId?: string) => void
    searchCity: (val: string) => void
    searchList: City[]
    searchValue: string
    hotCity?: HotCity
    cityList: City[]
    selectedCity: City[]
    multiple: boolean
    onReturn: () => void
    lang: { [key: string]: any }
    showRecently?: Boolean
    cityRange?: any[]
    cityGroupId?:string | undefined
    disabledCityList?: string[]
  }
  
  interface CityPath {
    id: string
    name: string
  }
  
  interface SelectState {
    parentId: undefined | string
    recentlyCity: City[]
    cityPath: CityPath[]
    currentCityList: City[]
    cityType: 'inland' | 'international'
  }
  
  export default class CitiesPicker extends PureComponent<SelectProps, SelectState> {
    constructor(props: SelectProps) {
      super(props)
  
      const recentlyCity = this.initRecentlyCity()
  
      this.state = {
        cityType: 'inland',
        parentId: undefined,
        recentlyCity,
        cityPath: [{ id: 'inland', name: props.lang.domestic }],
        currentCityList: []
      }
    }
  
    static defaultProps = {
      searchValue: ''
    }
  
    private STORAGE_KEY: string = '__RECENTLY_CITY__'
  
    componentDidMount() {
      this.props.fetchCity()
    }
  
    componentWillReceiveProps(nextProps: SelectProps) {
      if (nextProps.hotCity !== this.props.hotCity) {
        this.setState({
          cityPath: [{ id: 'inland', name: nextProps.lang.domestic }]
        })
      }
      
    }
  
    initRecentlyCity() {
      const cityQueue: string | null = window.localStorage.getItem(this.STORAGE_KEY)
      const { disabledCityList=[] } = this.props;
      if(cityQueue){
          const _cityQueue = JSON.parse(cityQueue).map(i => {
              return {
                  ...i,
                  disabled: disabledCityList.includes(i.id)
              }
          })
          return _cityQueue
      }else{
          return []
      }
    }
  
    // 向缓存中推最近选择
  
    pushToStorage(value: City) {
      // 判断城市范围，不予更新
      if (value.type === 'cityGroup') return
  
      const { recentlyCity } = this.state
      const MAX: number = 4
  
      // 判断重复，不予更新
  
      if (recentlyCity.some((city: City) => value.id === city.id)) {
        return
      }
  
      // 根据最大展示数量，维护一个队列，并在前端持久化
  
      const cityQueue: City[] = queue<City>(value, [...recentlyCity], MAX)
  
      this.setState({ recentlyCity: cityQueue })
      window.localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cityQueue))
    }
  
    // 选择城市
  
    onSelect(value: City) {
      const { onChange } = this.props
  
      // 更新缓存中的队列
      this.pushToStorage(value)
  
      onChange(value)
    }
  
    onTreeListClick(value: City) {
      // 末级直接选择
  
      if (value.isLast) {
        return this.onSelect(value)
      }
  
      const { fetchCity, cityGroupId  } = this.props
      const { cityPath, cityType} = this.state
  
      // 不是末级的情况下，需要进入下一级
  
      // 防止重复点击
      if (cityPath[cityPath.length - 1].id === value.id) {
        return
      }
  
      fetchCity(value.id, cityType, cityGroupId)
      cityPath.push(value)
      this.setState({
        cityPath
        // currentCityList: []
      })
    }
  
    // 城市面包屑回跳
    jumpLevel(id: string, index: number) {
      let { cityPath, cityType } = this.state
      const { fetchCity, cityGroupId } = this.props
  
      // 最后一级点击不生效
      if (index === cityPath.length - 1) {
        return
      }
  
      // 放弃缓存策略，直接异步获取
      // 更新面包屑栈
      const pid = id === 'inland' || id === 'international' ? '' : id
  
      fetchCity(pid, cityType, cityGroupId)
  
      cityPath = [...cityPath.slice(0, index + 1)]
  
      this.setState({
        //currentCityList: [],
        cityPath
      })
    }
  
    // 切换国内城市国外城市
  
    switchTab(type: 'inland' | 'international') {
      const { fetchCity, lang } = this.props
      const cityPathMap = {
        inland: [{ id: 'inland', name: lang.domestic }],
        international: [
          {
            id: 'international',
            name: lang.international
          }
        ]
      }
  
      this.setState({
        cityType: type,
        cityPath: cityPathMap[type],
        currentCityList: [],
      })
      fetchCity('', type)
    }
    onShowGroup = (city: any) => {
      const { fetchCity } = this.props
      if (city && city.id) {
        this.setState({
          currentCityList: [],
          cityType: 'inland',
          cityPath: [{ id: 'inland', name: city.name }]
        })
        fetchCity('', 'inland', city.id)
      }
    }
    clearCityGroup() {
      this.switchTab('inland')
    }
  
    // 渲染城市选择弹出层
    renderCityPanel() {
      const { cityType } = this.state
      const { searchValue, lang , cityGroupId} = this.props
      // 搜索的时候隐藏城市列表
      if (searchValue.trim()) {
        return null
      }
      if (cityGroupId) {
        return (
          <div className="cities-panel">
            <span onClick={this.clearCityGroup.bind(this)} className="back">
            <Icon type="left" />&nbsp;
            {lang.backTitle}</span>
            {this.renderCityList()}
          </div>
        )
      }
  
      return (
        <div className="cities-panel">
          {this.renderRecently()}
          {this.renderRange()}
          <nav>
            <ul className="city-tab">
              <li className={cityType === 'inland' ? 'active' : ''} onClick={this.switchTab.bind(this, 'inland')}>
                {lang.domestic}
              </li>
              <li
                className={cityType === 'international' ? 'active' : ''}
                onClick={this.switchTab.bind(this, 'international')}
              >
                {lang.international}
              </li>
            </ul>
          </nav>
  
          {this.renderHot()}
          {this.renderCityList()}
        </div>
      )
    }
  
    renderRange() {
      const { cityRange, lang } = this.props
      if (!cityRange || !lang.rangelabel || !Array.isArray(cityRange) || cityRange.length == 0) {
        return null
      }
  
      return (
        <div className="city-group">
          <h5>{lang.rangelabel}</h5>
          <ul>
            {cityRange.map(city => {
              return (
                <li key={city.id}>
                  <p onClick={this.onSelect.bind(this, city)}>{city.name}</p>
                  <span onClick={this.onShowGroup.bind(this, city)}>{lang.rangeTitle}</span>
                </li>
              )
            })}
          </ul>
        </div>
      )
    }
    renderRecently() {
      const { recentlyCity } = this.state
      const { selectedCity, lang, showRecently = true } = this.props
  
      if (!recentlyCity.length || !showRecently) {
        return null
      }
  
      return (
        <div className="recently">
          <h5>
            <span>{lang.recently}</span>
          </h5>
          <ul>
            {recentlyCity.map(city => {
              const selected = selectedCity.some((c: City) => c.id === city.id)
              let className = selected ? 'selected' : ''
              if(city.disabled) className += ' disabled-city'
              return (
                <li 
                  key={city.id} 
                  onClick={city.disabled ? () => {} : this.onSelect.bind(this, city)} 
                  className={className}
              >
                  {city.name}
                </li>
              )
            })}
          </ul>
        </div>
      )
    }
  
    renderHot() {
      const { hotCity, lang, disabledCityList=[] } = this.props
      const { cityType } = this.state
  
      if (!hotCity) {
        return null
      }
  
      const currentHotCity: City[] = hotCity[cityType]
      if (!currentHotCity.length) {
        return null
      }
  
      return (
        <div className="hot">
          <h5>{lang.hotCities}</h5>
          <ul>
            {currentHotCity.map(city => {
            const isDisabled = disabledCityList.includes(city.id) 
              return (
                  <li 
                      key={city.id} 
                      className={isDisabled ? 'disabled-city' : ''}
                      onClick={isDisabled ? () => {} : this.onSelect.bind(this, city)}
                  >
                      {city.name}
                  </li>
              )
            })}
          </ul>
        </div>
      )
    }
  
    renderCityList() {
      const { cityPath } = this.state
      const { selectedCity, cityList, lang, disabledCityList=[] } = this.props
  
      const breadcrumb = cityPath.map((p: CityPath, index: number) => {
        return (
          <span key={index} onClick={this.jumpLevel.bind(this, p.id, index)}>
            {p.name}
          </span>
        )
      })
  
      const list = (
        <ul>
          {cityList.map(city => {
            const selected = selectedCity.some((c: City) => c.id === city.id)
            const isDisabled = disabledCityList.includes(city.id)
            return (
              <li 
                key={city.id} 
                className={isDisabled ? 'disabled-city' : ''}
                onClick={isDisabled ? () => {} : this.onTreeListClick.bind(this, city)}
              >
                <label>{city.name}</label>
                {!city.isLast && (
                  <Icon
                    type="right"
                    style={{
                      color: 'rgba(29, 43, 61, 0.75)'
                    }}
                  />
                )}
                {city.isLast && selected && <Icon type="check" style={{ color: 'var(--brand-base)' }} />}
              </li>
            )
          })}
        </ul>
      )
  
      const loading = (
        <div className="more-loading">
          <Icon type="loading" /> {lang.loading}...
        </div>
      )
  
      const empty = <div>{lang.empty}</div>
  
      return (
        <div className="cities-list">
          <h5 className="breadcrumb">{breadcrumb}</h5>
          {cityList.length ? list : empty}
        </div>
      )
    }
  
    renderSearchList() {
      const { searchList, searchValue, selectedCity, onReturn, lang, disabledCityList=[] } = this.props
  
      if (!searchValue.trim()) {
        return null
      }
  
      return (
        <div className="search-panel">
          {!!searchList.length && (
            <div className="search-list">
              <h5>
                <Icon
                  type="arrow-left"
                  className="return"
                  onClick={onReturn}
                  style={{
                    fontSize: '14px',
                    color: 'var(--brand-base)'
                  }}
                />
                <span>{lang.results}</span>
              </h5>
              <ul>
                {searchList.map((c: City) => {
                  const { fullName = '' } = c
                  const path = fullName.replace(/, /g, ' / ')
                  const selected = selectedCity.some((city: City) => c.id === city.id)
                  const isDisabled = disabledCityList.includes(c.id)
                  return (
                    <li 
                      key={c.id} 
                      onClick={isDisabled ? () => {} : this.onSelect.bind(this, c)}
                      className={isDisabled ? 'disabled-city' : ''}
                    >
                      <div className="left">
                        <h4>{c.name}</h4>
                        <p>{path}</p>
                      </div>
                      {selected && <Icon type="check" style={{ color: 'var(--brand-base)' }} />}
                    </li>
                  )
                })}
              </ul>
            </div>
          )}
        </div>
      )
    }
  
    render() {
      return (
        <div className="cities-picker">
          {this.renderCityPanel()}
          {this.renderSearchList()}
        </div>
      )
    }
  }
  