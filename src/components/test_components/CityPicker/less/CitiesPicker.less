@import './color.less';
@import './radius.less';
@import './shadows.less';
@import './radius.less';
@import './typography.less';

.cities-picker {
  position: relative;
  width: 375px;
  box-shadow: @Modal-BG;
  &::after,
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    height: 16px;
    background: @White3;
    z-index: 3;
  }

  &::after {
    bottom: 0;
  }

  &::before {
    top: 0;
  }
}

.panel {
  max-height: 300px;
  padding: 16px;
  border-radius: @RadiusL;
  position: relative;
  background: @White1;
}

.cities-panel {
  .back{
    font-size: 14px;
    padding-bottom: 8px;
    cursor: pointer;
    display: inline-block;
    &:hover{
      color: @Brand1;
      i{
        color: @Brand1;
      }
    }
  }
  .panel;
  overflow-y: auto;

  h5 {
    .secondary-12pt;
  }

  .recently h5 {
    display: flex;
    align-items: center;
    span {
      flex: 1;
    }
  }

  .recently,
  .hot {
    margin-bottom: 16px;
    .disabled-city{
      cursor: not-allowed;
      color: #cbcbcb;
      background-color: #f5f5f5;
    }
    li {
      .body-12pt;
      display: inline-block;
      padding: 3px 8px;
      margin-right: 8px;
      margin-bottom: 8px;
      border: @Neutral-border;
      border-radius: @RadiusS;
      cursor: pointer;

      &.selected {
        color: @Gray4;
      }

      &:active {
        background: @Gray7;
      }
    }
  }
  .city-group {
    margin-bottom: 16px;

    li {
      display: inline-block;
      margin-right: 8px;
      margin-bottom: 8px;
      text-align: center;
      p {
        .body-12pt;
        display: inline-block;
        padding: 3px 8px;
        margin: 0px;
        border: @Neutral-border;
        border-radius: @RadiusS;
        cursor: pointer;

        &.selected {
          color: @Gray4;
        }

        &:active {
          background: @Gray7;
        }
      }
      span {
        color: @Brand1;
        display: block;
        cursor: pointer;
      }
    }
  }
  .city-tab {
    margin-bottom: 12px;

    li {
      display: inline-block;
      padding: 3px 12px;
      .body-12pt;
      border: @Black-border;
      border-right: 0;
      min-width: 72px;
      text-align: center;
      cursor: pointer;

      &.active {
        color: @White1;
        background: @Gray1;
      }

      &:first-child {
        border-radius: @RadiusS 0 0 @RadiusS;
      }

      &:last-child {
        border-radius: 0 @RadiusS @RadiusS 0;
        border-right: @Black-border;
      }
    }
  }

  .cities-list {
    .breadcrumb {
      span {
        color: @Brand1;
        cursor: pointer;

        &::after {
          content: '/';
          padding: 0 4px;
          .secondary-12pt;
        }
        &:last-child {
          .secondary-12pt;
          cursor: default;
        }

        &:last-child::after {
          content: '';
        }
      }
    }
    .disabled-city{
      cursor: not-allowed;
      color: #cbcbcb;
      background-color: #f5f5f5;
      label {
        cursor: not-allowed;
      }
    }
    li {
      .body-14pt;
      padding: 8px 0;
      display: flex;
      align-items: center;
      border-bottom: @Neutral-border;
      cursor: pointer;

      label {
        flex: 1;
        width: 100px;
        cursor: pointer;
      }

      &:last-child {
        border: 0;
      }
    }
  }
}

.search-panel {
  .panel;
  padding: 0 16px;
  min-height: 134px;
  display: flex;
  width: 343px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: content-box;

  .loading {
    min-height: 134px;
    .loading-text {
      margin-top: 10px;
      .disable-14pt;
    }
  }

  &:empty::after {
    .disable-14pt;
    content: '无搜索结果，要不换个关键词';
  }

  .search-list {
    width: 100%;
    padding-top: 16px;
    overflow-y: auto;

    h5 {
      margin-bottom: 8px;
      .secondary-14pt;
      display: flex;
      align-items: center;

      .return {
        margin-right: 10px;
      }

      span {
        flex: 1;
      }
    }

    .disabled-city{
      cursor: not-allowed;
      color: #cbcbcb;
      background-color: #f5f5f5;
    }

    li {
      display: flex;
      padding: 8px 0;
      border-bottom: @Neutral-border;
      cursor: pointer;

      &:last-child {
        border: 0;
      }

      .left {
        flex: 1;
      }

      i {
        margin-top: 6px;
      }

      h4,
      p {
        margin-bottom: 0;
      }

      h4 {
        .body-14pt;
      }

      p {
        .secondary-12pt;
      }
    }
  }
}
