/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-01-21 16:56:57
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2019-01-21 22:03:33
 */

@import './color.less';

// 字重
@Regular: 400;
@SemiBold: 600;

.TypographyBase(@c, @fz, @lh, @fw:@Regular) {
  color: @c;
  font-size: @fz;
  line-height: @lh;
  font-weight: @fw;
}

// 变量
// 标题 title
// 强调 emphasize
// 正文 body
// 次要 secondary
// 灰置 disable
// 链接 link
// 成功 success
// 警告 warning
// 错误 error
// 提示 inform

.title-12pt {
  .TypographyBase(@Gray1, 12px, 20px);
  font-weight: @SemiBold;
}

.emphasize-12pt {
  .TypographyBase(@Gray1, 12px, 20px);
}

.body-12pt {
  .TypographyBase(@Gray2, 12px, 20px);
}

.secondary-12pt {
  .TypographyBase(@Gray3, 12px, 20px);
}

.disable-12pt {
  .TypographyBase(@Gray4, 12px, 20px);
}

.link-12pt {
  .TypographyBase(@Brand1, 12px, 20px);
}

.success-12pt {
  .TypographyBase(@Success1, 12px, 20px);
}

.warning-12pt {
  .TypographyBase(@Warning1, 12px, 20px);
}

.error-12pt {
  .TypographyBase(@Error1, 12px, 20px);
}

.inform-12pt {
  .TypographyBase(@Inform1, 12px, 20px);
}

.title-14pt {
  .TypographyBase(@Gray1, 14px, 22px);
  font-weight: @SemiBold;
}

.emphasize-14pt {
  .TypographyBase(@Gray1, 14px, 22px);
}

.body-14pt {
  .TypographyBase(@Gray2, 14px, 22px);
}

.secondary-14pt {
  .TypographyBase(@Gray3, 14px, 22px);
}

.disable-14pt {
  .TypographyBase(@Gray4, 14px, 22px);
}

.link-14pt {
  .TypographyBase(@Brand1, 14px, 22px);
}

.success-14pt {
  .TypographyBase(@Success1, 14px, 22px);
}

.warning-14pt {
  .TypographyBase(@Warning1, 14px, 22px);
}

.error-14pt {
  .TypographyBase(@Error1, 14px, 22px);
}

.inform-14pt {
  .TypographyBase(@Inform1, 14px, 22px);
}

.title-16pt {
  .TypographyBase(@Gray1, 16px, 24px);
  font-weight: @SemiBold;
}

.emphasize-16pt {
  .TypographyBase(@Gray1, 16px, 24px);
}

.body-16pt {
  .TypographyBase(@Gray2, 16px, 24px);
}

.secondary-16pt {
  .TypographyBase(@Gray3, 16px, 24px);
}

.disable-16pt {
  .TypographyBase(@Gray4, 16px, 24px);
}

.link-16pt {
  .TypographyBase(@Brand1, 16px, 24px);
}

.success-16pt {
  .TypographyBase(@Success1, 16px, 24px);
}

.warning-16pt {
  .TypographyBase(@Warning1, 16px, 24px);
}

.error-16pt {
  .TypographyBase(@Error1, 16px, 24px);
}

.inform-16pt {
  .TypographyBase(@Inform1, 16px, 24px);
}

.title-20pt {
  .TypographyBase(@Gray1, 20px, 24px);
  font-weight: @SemiBold;
}

.emphasize-20pt {
  .TypographyBase(@Gray1, 20px, 28px);
}

.body-20pt {
  .TypographyBase(@Gray2, 20px, 28px);
}

.secondary-20pt {
  .TypographyBase(@Gray3, 20px, 28px);
}

.disable-20pt {
  .TypographyBase(@Gray4, 20px, 28px);
}

.link-20pt {
  .TypographyBase(@Brand1, 20px, 28px);
}

.success-20pt {
  .TypographyBase(@Success1, 20px, 28px);
}

.warning-20pt {
  .TypographyBase(@Warning1, 20px, 28px);
}

.error-20pt {
  .TypographyBase(@Error1, 20px, 28px);
}

.inform-20pt {
  .TypographyBase(@Inform1, 20px, 28px);
}
