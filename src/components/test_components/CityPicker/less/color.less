// Neutral Colors
@Gray1: #1d2b3d;
@Gray2: fadeout(@Gray1, 25%);
@Gray3: fadeout(@Gray1, 50%);
@Gray4: fadeout(@Gray1, 70%);
@Gray5: fadeout(@Gray1, 85%);
@Gray6: fadeout(@Gray1, 91%);
@Gray7: fadeout(@Gray1, 94%);
@Gray8: fadeout(@Gray1, 97%);

@White1: #fff;
@White2: fadeout(@White1, 15%);
@White3: fadeout(@White1, 35%);
@White4: fadeout(@White1, 55%);
@White5: fadeout(@White1, 75%);
@White6: fadeout(@White1, 85%);
@White7: fadeout(@White1, 91%);
@White8: fadeout(@White1, 96%);

// Primary Color
@Brand1: var(--brand-base);
@Brand2: fadeout(@Brand1, 30%);
@Brand3: fadeout(@Brand1, 70%);
@Brand4: fadeout(@Brand1, 90%);

// Functional Colors

// 此为报错基色，不可换选
@Error1: #fc3842;
@Error2: fadeout(@Error1, 30%);
@Error3: fadeout(@Error1, 70%);
@Error4: fadeout(@Error1, 90%);

// 此为警告基色，不可换选
@Warning1: #fa962a;
@Warning2: fadeout(@Warning1, 30%);
@Warning3: fadeout(@Warning1, 70%);
@Warning4: fadeout(@Warning1, 90%);

// 此为成功基色，不可换选
@Success1: #38b928;
@Success2: fadeout(@Success1, 30%);
@Success3: fadeout(@Success1, 70%);
@Success4: fadeout(@Success1, 90%);

// 此为提示基色，不可换选
@Inform1: #1890ff;
@Inform2: fadeout(@Inform1, 30%);
@Inform3: fadeout(@Inform1, 70%);
@Inform4: fadeout(@Inform1, 90%);

// Outline
@Black-border: 1px solid @Gray3;
@Neutral-border: 1px solid @Gray5;
@Dashed-border: 1px dashed @Gray4;
@Brand-border: 1px solid @Brand1;
@Error-border: 1px solid @Error1;

// TODO: older 需要清理掉

@disabled-color : #9C9C9C; // menu 内的禁用颜色,禁用input颜色
@disabled-color-2: #cbcbcb; // button 禁用颜色
@disabled-background: #f5f5f5;

// 背景与线条
@Bg1: fadeout(@Gray1, 94%);
@Bg2: fadeout(@Gray1, 97%);

@Line1: fadeout(@Gray1, 85%);
@Line2: fadeout(@Gray1, 91%);
