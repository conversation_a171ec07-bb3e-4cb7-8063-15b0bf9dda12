import zh_CN from 'antd/es/locale-provider/zh_CN'
import en_US from 'antd/es/locale-provider/en_US'
import EUIzhCN from '@hose/eui/es/locale/zh_CN'
import EUIenUS from '@hose/eui/es/locale/en_US'

export const ENUM_TYPES = [
  'ref:basedata.Enum.CabinType',
  'ref:basedata.Enum.TrainSeatType',
  'ref:basedata.Enum.CruiseCabinType',
  'ref:basedata.Enum.InvoiceType',
  'ref:basedata.Enum.OcrInvoiceType',
  'ref:basedata.Enum.InvoiceLineNatureEnum',
  'ref:basedata.Enum.InvoiceNature',
  'ref:basedata.Enum.PriceTaxMark',
  'ref:basedata.Enum.InvoiceListMark',
  'ref:basedata.Enum.SettlementMode',
  'ref:basedata.Enum.TaxpayerType',
  'ref:basedata.Enum.DiscountType'
]
export const ENUM_DATE_TYPE = {
  YEAR_MONTH: 'YEAR_MONTH',
  YEAR_MONTH_DAY: 'YEAR_MONTH_DAY',
  YEAR_MONTH_DAY_TIME: 'YEAR_MONTH_DAY_TIME'
}

export const getLocale = () => {
  return i18n.currentLocale === 'zh-CN'
    ? zh_CN
    : {
        ...en_US,
        lang: {
          ...en_US.lang,
          monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        }
      }
}

export const getEUILocale = () => {
  return i18n.currentLocale === 'zh-CN'
    ? EUIzhCN
    : {
        ...EUIenUS,
        lang: {
          ...EUIenUS.lang,
          monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        }
      }
}

export const DATE_TYPE = [
  {
    k: ENUM_DATE_TYPE.YEAR_MONTH,
    v: '年-月'
  },
  {
    k: ENUM_DATE_TYPE.YEAR_MONTH_DAY,
    v: '年-月-日'
  },
  {
    k: ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME,
    v: '年-月-日-时-分'
  }
]

export const DATE_TYPE_RANGE = [
  {
    k: ENUM_DATE_TYPE.YEAR_MONTH_DAY,
    v: '年-月-日'
  },
  {
    k: ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME,
    v: '年-月-日-时-分'
  }
]

export const summarySelectType = {
  receipt: 1,
  repay: 2
}
