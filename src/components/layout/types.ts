/*!
 * Copyright 2019 yangjunbao <yang<PERSON><PERSON>@shimo.im>. All rights reserved.
 * @since 2019-05-09 16:15:25
 */

import { WrappedFormUtils } from 'antd/es/form/Form'
import { ReactNode } from 'react'

export interface FormItemProps<T = any> {
  field: FieldInterface
  value: T
  onChange: (value: T) => void
  form: WrappedFormUtils & {
    [P: string]: any
  }
}

export interface FieldInterface {
  name: string
  label: string
  type: string
  placeholder: string
  optional: boolean
  editable: boolean
  entityDescribe?: boolean
  visible?: boolean
  inputVisible?: boolean
  mode?: string
  tags?: any[]
  describe?: string
  maxLength?: number
  subtags?: any[]
  optionFilterProp?: string
  labelView?: (label: string, classNames: string, props: FormItemProps) => ReactNode
  hideInput?: (props: FormItemProps) => boolean
  validator?: (
    this: FieldInterface,
    rule: any,
    value: any,
    callback: (error?: undefined | string | Error) => void
  ) => void
}
