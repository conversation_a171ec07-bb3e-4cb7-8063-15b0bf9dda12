/**
 *  Created by gym on 2019-07-04 10:55.
 */
import React from 'react'
import { Form } from 'antd'
import styles from './FormItem.module.less'
import classnames from 'classnames'
import FormItemLabelWrap from './FormItemLabelWrap/FormItemLabelWrap'
import RiskTag from '../../plugins/bills/riskWarning/RiskTag'

export function FormItem(props) {
  let {
    label,
    external,
    isForbid = true,
    labelCol,
    wrapperCol,
    flowId,
    noColon,
    isDetail,
    field,
    detailId,
    labelAlign = 'right',
    extraTag,
    style = {}
  } = props
  const fieldLabelClassName = field.fieldLabelClassName || ''
  const isApportions = field?.field === 'apportions'
  const cls = classnames(
    styles.form_item_4_wrapper,
    'form_item_4_wrapper',
    { [styles.form_item_label_left]: labelAlign === 'left' },
    {
      [styles.max_width_none]: field?.name === 'expenseLink' || field?.name === 'expenseLinks' || field.max_width_none
    },
    fieldLabelClassName
  )
  if (isApportions && external) {
    label = undefined
  }

  if(!isApportions && external){
    // 有风险属性间距间距增加风险本身的高度
    style = {...style, marginBottom: '48px'}
  }

  return (
    <div className={cls} id={field?.field}>
      <Form.Item
        {...props}
        style = {style}
        labelCol={labelCol ? labelCol : { span: 24 }}
        wrapperCol={wrapperCol ? wrapperCol : { span:24 }}
        label={
          label ? (
            external || flowId ? (
              <span>
                <FormItemLabelWrap
                  isEdit={true}
                  external={external}
                  isForbid={isForbid}
                  noColon={noColon}
                  field={field}
                  flowId={flowId}
                  isDetail={isDetail}
                  detailId={detailId}
                >
                  <span>{label}</span>
                </FormItemLabelWrap>
              </span>
            ) : (
              <span>
                {label} <span className="other-tag">{extraTag}</span>
              </span>
            )
          ) : (
            ''
          )
        }
      >
        { !isApportions && external  && <RiskTag external={external} style={{left: 'auto', bottom: '-38px'}}  />} 
        {props.children}
        {field?.customizeCalculateTip && (
          <span className={classnames('custom-tip')}>{field.customizeCalculateTip}</span>
        )}
      </Form.Item>
    </div>
  )
}
