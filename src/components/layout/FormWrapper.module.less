.form-item-wrapper {
  :global {
    .label-style {
      font-size: 12px;
      color: #595959;
      font-weight: 500;
    }
    .large-style {
      font-weight: 500;
      color: rgba(29, 43, 61, 1);
      font-size: 14px;
      margin-bottom: 8px;
    }
    .large-normal {
      color: #595959;
      font-size: 14px;
    }
  }
}
.img-wrapper {
  color: var(--eui-icon-disabled);
}
.form-item-group-title {
  font-weight: 500;
  font-size: 16px;
  color: #272e3b;
  margin-bottom: 16px;
  padding-top: 24px;
  border-top: 1px solid rgba(39, 46, 59, 0.12);
  &:first-child {
    border-top: none;
    padding-top: 0px;
  }
}
.form-item-group-sub-title {
  font-weight: 500;
  font-size: 14px;
  color: #272e3b;
  margin-bottom: 16px;
}

// 处理批注hover后的展示问题
.form-item-wrapper-editable {
  :global {
    .ant-form-item-label {
      overflow: visible;
      label {
        color: var(--eui-text-title);
        font: var(--eui-font-body-r1);
      }
    }
    .ant-form-item-required::after {
      display: inline-block;
      left: 0 !important;
      top: 0 !important;
      color: var(--eui-function-danger-500) !important;
      font: var(--eui-font-body-b1) !important;
      content: '*';
    }

    .ant-form-explain {
      color: var(--eui-function-danger-500) !important;
    }

    .has-error {
      .eui-input-affix-wrapper,
      .eui-select-selector,
      .eui-picker,
      .eui-input,
      .eui-input-number,
      .payee-info-wrapper,
      .receiving-currency-content,
      .payee-info-input {
        border-color: var(--eui-function-danger-500) !important;
      }
    }

    .risk-padding{
      margin-bottom: 48px;
    }

    .eui-select-arrow {
      color: var(--eui-icon-n2) !important;
    }

    .eui-input-number-group > .eui-input-number:first-child, .eui-input-number-group-addon:first-child {
      border-radius: 6px;
    }
    .number-input.with-unit {
      border-radius: 6px 0 0 6px !important;
    }
  }
}

.hidden-required {
  :global {
    .ant-form-item-required:before {
      display: none;
    }
    .ant-form-item-required:after {
      display: none;
    }
    .ant-form-item-control {
      line-height: 0;
    }
  }
}
.cancel-form-item-margin {
  margin-bottom: 0;
}
.cancel-form-item-margin2 {
  margin-bottom: 12px;
}
.auto-associate-order-config {
  margin-bottom: 0;
}
.apply-verify-travel-closed {
  margin-bottom: 0;
}
.destricted-travel-type-config {
  margin-bottom: 8px;
}

.cancel-form-item-margin-error {
  margin-bottom: 0;
  :global {
    .ant-form-item-control-wrapper {
      .has-error {
        .ant-form-explain {
          margin-left: 20px;
        }
      }
    }
  }
}
