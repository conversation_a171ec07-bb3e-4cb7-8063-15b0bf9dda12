/**************************************************
 * Created by nanyuanting<PERSON> on 12/07/2017 16:14.
 **************************************************/
@import '~@ekuaibao/eui-styles/less/token.less';

.form_item_4_readonly_wrapper {
  padding-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.45);
  // TODO 样式修改
  display: flex;

  &_vertical {
    flex-direction: column;
  }

  &:hover {
    :global {
      .addNoteBtn {
        display: inline-block;
      }
    }
  }
}

.wrapperRow {
  display: flex;
  padding-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.45);
  flex-wrap: wrap;
  align-items: center;
}

.form_item__label {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: 100%;
  position: static;
  color: var(--eui-text-placeholder);
  font: var(--eui-font-body-r1);
}

.form_item__label_unset {
  width: unset;
}

.float_none {
  float: unset !important;
}

.form_item__label_IE {
  width: 10%;
}

.form_item__content {
  .form_item__content_inline;
}
.form_item__content_blockUI{
  width: auto;
  overflow: hidden;
}

.form_item__content_inline {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  word-break: break-all;
  position: static;
  color: var(--eui-text-title);
  font: var(--eui-font-body-r1);
}

.vertical_form_item__content {
  margin-top: 4px;
}

.help_icon {
  margin-left: 5px;
  fill: #ffa700;
}

:global {
  .bill-field-display-mode-horizontal {
    :local(.form_item__label) {
      box-sizing: content-box;
    }
    &.bill-field-display-col-1 {
      :local(.form_item__label) {
        width: 13% !important;
      }
    }
    &.bill-field-display-col-2 {
      .form-list-item {
        &.form-list-item-one-row {
          --block-width: calc(100% - 1.5% - 16px);
          :local(.form_item__label) {
            width: calc(var(--block-width) / 2 * 22%);
          }
        }
      }
    }
    &.bill-field-display-col-3 {
      .form-list-item {
        &.form-list-item-one-row {
          --block-width: calc(100% - 3% - 32px);
          :local(.form_item__label) {
            width: calc(var(--block-width) / 3 * 22%);
          }
        }
      }
    }
  }
   body[data-locale="zh-CN"] {
    .bill-field-display-mode-horizontal {
      :local(.form_item__label) {
        max-width: 136px;
      }
      &.bill-field-font-size-small {
        :local(.form_item__label) {
          width: 22%;
          min-width: 60px;
        }
      }
      &.bill-field-font-size-standard {
        :local(.form_item__label) {
          width: 22%;
          min-width: 72px;
        }
      }
      &.bill-field-font-size-big {
        :local(.form_item__label) {
          width: 22%;
          min-width: 80px;
        }
      }
    }
  }
  body:not([data-locale="zh-CN"]) {
    .bill-field-display-mode-horizontal {
      :local(.form_item__label) {
        min-width: 120px;
        max-width: 150px;
        width: 22%;
      }
    }
  }
}
