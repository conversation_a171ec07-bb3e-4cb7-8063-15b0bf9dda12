/**************************************
 * Created By LinK On 2020/4/13 14:58.
 **************************************/
import React, { PureComponent } from 'react'
import { renderPopoverContent } from '../ExceedStandardRiskForField'
import styles from './NoteView.module.less'
import { app as api } from '@ekuaibao/whispered'
import EKBIcon from '../../../elements/ekbIcon'
import { get } from 'lodash'
import NoteContentLine from './NoteContentLine'
import classNames from 'classnames'
import { isIE } from '../../../lib/misc'
import { OutlinedEditDeleteTrash } from '@hose/eui-icons'


interface Props {
  external: any
  flowId: string
  field: any
  isDetail: boolean
  detailId?: string
  noteArr: any[]
  detailReadable: boolean
  canEditNote: boolean
  authorRemovable: boolean
  showBillNotesInHistory: boolean
}

export default class NoteView extends PureComponent<Props> {

  handleAddNote = () => {
    const { flowId, detailId, isDetail, field } = this.props
    // @ts-ignore
    api.open('@bills:NoteEditModal', { page: 'add', flowId, detailId, isDetail, field })
  }

  handleChangePageToDelete = (noteId) => {
    const { flowId } = this.props
    // @ts-ignore
    api.open('@bills:NoteEditModal', { page: 'delete', flowId, noteId })
  }

  renderNoteByType = (noteType) => {
    let { noteArr, detailReadable, canEditNote, authorRemovable, showBillNotesInHistory, field } = this.props
    const singleNoteArr = noteArr.filter(el => el.type === noteType)
    if (!singleNoteArr.length) return null
    const canDeleteNote = !showBillNotesInHistory && canEditNote && (authorRemovable || noteType === 'NORMAL')
    console.log(`[ 系统配置是否让编辑 ] >: canDeleteNote: ${canDeleteNote}
    !showBillNotesInHistory: ${!showBillNotesInHistory}
    canEditNote: ${canEditNote}
    (authorRemovable || noteType === 'NORMAL'): ${(authorRemovable || noteType === 'NORMAL')}
    authorRemovable: ${authorRemovable}
    noteType: ${noteType}
    `)
    return (<div className='note-item'>
      {field !== 'Credit-Note-Pop' && <div className='note-type'>
        {noteType === 'NORMAL' ? i18n.get('普通批注') : i18n.get('信用批注')}
      </div>}
      {singleNoteArr.map((note, idx) => {
        const { content, authorName, rule, authorId } = note
        const name = i18n.get(`{__k0}`, { __k0: authorName })
        //@ts-ignore
        const staffId = api.getState('@common.userinfo.staff.id')
        console.log(canDeleteNote, '[ 系统配置的 canDeleteNote  true 才对比 用户信息 ] >')
        let isShowDelete = canDeleteNote;
        if (canDeleteNote) {
          isShowDelete = authorId === staffId
          console.log(`[ authorId === staffId ] >: isShowDelete: ${isShowDelete}
          ${authorId} ${isShowDelete ? '同一个人' : "不同一个人"} ${staffId}
          `)
        }
        let text
        if (noteType === 'NORMAL') {
          text = <NoteContentLine name={name} text={content} />
        } else {
          const permissions = api.getState('@common.userinfo.permissions') || []
          isShowDelete = isShowDelete || !!~permissions?.indexOf('CREDIT_MANAGE')
          console.log(`[ permissions isShowDelete ] >: isShowDelete: ${isShowDelete}
          用户权限permissions: ${permissions}
          !!~permissions?.indexOf('CREDIT_MANAGE'): ${!!~permissions?.indexOf('CREDIT_MANAGE')}
          `)
          text = <NoteContentLine name={name} text={content} />
          if (detailReadable) {
            const label = get(rule, 'label')
            const score = get(rule, 'score', '')
            text = <NoteContentLine name={name} text={label} score={score} />
          }
        }
        console.log('[最后展示: isShowDelete ] >', isShowDelete)
        return (<div className={
          classNames("note-content", { "jc-sb": !isIE(), "option-line": !isIE(), 'pos-r': isIE() })
        } key={idx}>
          {text}
          {isShowDelete && (<div className={classNames('note-delete-btn-wrap', { 'ie-note-delete-btn-wrap': isIE() })}
            onClick={() => this.handleChangePageToDelete(note.id)}>
            <OutlinedEditDeleteTrash fontSize={14} className='note-delete-btn' />
          </div>)}
        </div>)
      })}
    </div>)
  }

  renderRiskContent = () => {
    const { external, field } = this.props
    if (field === 'Credit-Note-Pop' || !external) return null
    return (<div className='note-item'>{renderPopoverContent(external)}</div>)
  }

  renderContent = () => {
    const { field } = this.props
    return (<div className={classNames('note-content-wrap', { 'note-content-wrap-layout': !isIE() })}>
      {field !== 'Credit-Note-Pop' && this.renderNoteByType('NORMAL')}
      {this.renderNoteByType('CREDIT')}
    </div>)
  }

  renderBtn = () => {
    const { canEditNote, showBillNotesInHistory, field } = this.props
    if (field === 'Credit-Note-Pop' || !canEditNote || showBillNotesInHistory) return null
    return (<div className="note-btn-wrap">
      <div className='note-add-btn' onClick={this.handleAddNote}>{i18n.get('添加批注')}</div>
    </div>)
  }

  render() {
    return (<div className={styles['noteView-wrap']}>
      {this.renderContent()}
      {this.renderBtn()}
    </div>)
  }
}