@import '~@ekuaibao/eui-styles/less/token.less';

.noteView-wrap {
  width: 338px;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  :global {
    .note-content-wrap-layout {
      flex: 1;
      overflow: auto;
    }
    .note-content-wrap {
      .note-risk-content-wrap {
        margin-bottom: @space-7;
      }
      .note-item {
        margin-bottom: @space-6;
        &:last-child {
          margin-bottom: 0;
        }
        .note-type {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: @color-black-1;
          .font-weight-3;
          .font-size-3;
        }
        .note-content {
          color: @color-black-3;
          margin-top: @space-4;
          .font-size-2;
          line-height: 20px;
          .note-delete-btn-wrap {
            cursor: pointer;
            height: 100%;
            padding: 0 @space-4;
            .note-delete-btn {
              color: rgba(29, 33, 41, 0.6);
            }
          }
          .ie-note-delete-btn-wrap {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .ie-layout-wrap {
          float: right;
          margin-top: -22px;
        }
      }
    }
    .note-btn-wrap {
      flex-shrink: 0;
      margin-top: @space-6;
      .note-add-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        height: 32px;
        background: @color-brand-2;
        border-radius: @radius-2;
        color: @color-white-1;
        .font-size-2;
        .font-weight-3;
      }
    }
  }
}
