.note-content-text-wrap {
  font-size: 14px;
  :global {
    .note-content-text-authorName-wrap {
      flex-shrink: 0;
      font-size: 14px;
      width: 60px;
      display: flex;
    .note-content-text-authorName {
      font-size: 14px;
      max-width: 55px;
      color: rgba(29, 33, 41, 0.7);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      }
  }
    .note-content-text-content {
      flex: 1;
      color: rgba(29, 33, 41, 0.9);
      }

      .note-content-score {
        color: #00B42A;
      }

      .note-content-score-minus {
        color: #FF7D00;
    }
  }
}
.ie-note-content-text-wrap {
  max-width: 90%;
}