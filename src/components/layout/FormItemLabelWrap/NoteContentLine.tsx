/**************************************
 * Created By LinK On 2020/4/29 17:13.
 **************************************/
import React from 'react'
import styles from './NoteContentLine.module.less'
import classNames from 'classnames'
import { isIE } from '../../../lib/misc'
import { isNumber } from '@ekuaibao/helpers';
import { Tooltip } from 'antd';

interface Props {
  name: string
  text: string
  score?: number
}

export default function NoteContentLine(props: Props) {
  const { name, text, score } = props
  const isPositive = score >= 0
  const isIe = isIE()
  return (<div className={classNames(styles['note-content-text-wrap'], { 'dis-f': !isIe, [styles['ie-note-content-text-wrap']]: isIe })}>
    <Tooltip title={name}><div className='note-content-text-authorName-wrap'><div className='note-content-text-authorName'>{name}</div>{i18n.get('：')}</div></Tooltip>
    <span className={classNames('note-content-text-content')}>{text}</span>
    {isNumber(score) && <span className={classNames(isPositive ? 'note-content-score' : 'note-content-score-minus')}>&ensp;{isPositive ? `+${score}` : `${score}`}</span>}
  </div>)
}