@import '~@ekuaibao/eui-styles/less/token.less';

.risk-warning-for-field-wrapper {
  flex: 1;
  // white-space: pre-wrap;
  text-align: left;
  word-break: break-all;
  :global {
    .form-item-label-wrapper {
      &.edit {
        display: inline;
      }
      .risk-icon-svg {
        position: static !important;
      }
      .ie-risk-warning-label-horizontal {
        max-width: 80px !important;
      }
      .risk-warning-label-horizontal {
        max-width: 160px;
        display: inline-block;
        margin-right: -2px;
      }
      .risk-warning-label-icon-horizontal {
        // max-width: 54px;
      }
      &.risk-warning-horizontal {
        justify-content: flex-start;
      }
      &.is-align{
        width: 120px;
      }
    }
    .risk-view-note-icon {
      cursor: pointer;
      display: inline-block;
      position: absolute;
      margin: 0;
      left: -18px;
      width: 18px;
      top: 0;
      .icon {
        transform: rotateY(180deg);
        color: @color-black-3;
        font-size: 16px;
      }
    }
    .addNoteBtn {
      display: none;
    }
    .explanation {
      font-size: 12px;
    }
    .risk-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      .risk-hidden {
        opacity: 0;
        position: absolute;
        top: 0;
      }
    }
    .icon {
      font-size: 16px;
      color: var(--eui-function-danger-500);
    }
    .warning {
      color: var(--eui-function-danger-500);
    }
    .error {
      color: var(--eui-function-danger-500);
    }
    .wrapper-error {
      color: var(--eui-function-danger-500);
      .risk-icon {
        color: var(--eui-function-danger-500);
      }
    }
    .wrapper-warning {
      color: var(--eui-function-danger-500);;
    }
    .edit {
      .risk-icon {
        position: absolute;
        display: inline-block;
        height: 16px;
        width: 16px;
        text-align: center;
        line-height: 16px;
        margin-left: 4px;
        left: 100%;
        .risk-hidden {
          opacity: 0;
          position: absolute;
          top: 0;
        }
      }
    }
  }
}

.risk-warning-header {
  padding-bottom: 12px;
  :global {
    .header-content {
      font: var(--eui-font-body-b1);
      color: var(--eui-text-title)
    }
  }
}

.risk-warning-content {
  :global {
    .risk-content-title {
      margin-top: 12px;
      display: flex;
      .risk-content-title-name {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.7);
      }
      .risk-content-title-version {
        height: 20px;
        font-size: 12px;
        line-height: 1.67;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .risk-content-detail {
      margin-top: 8px;
      background-color: var(--eui-function-warning-50);
      border-radius: 10px;
      max-height: 400px;
      overflow-y: auto;
      &.error{
        background-color: var(--eui-function-danger-50);
      }
      .risk-content-detail-item {
        font-size: 14px;
        line-height: 26px;
        text-align: justify;
        color: rgba(0, 0, 0, 0.65);
        padding: 12px 10px;
        width: 100%;
      }
      .warning {
        color: var(--eui-function-danger-500); ;
      }
    }


    .risk-content-detail-v2 {
      margin-top: 8px;
      background-color: #fff8eb;
      border-radius: 10px;
      padding: 12px 10px;
      max-height: 400px;
      overflow-y: auto;
      .risk-item-label {
        width: 80px;
        text-align: right;
        padding-right: 20px;
        color: rgb(152 152 152);
      }
      .risk-item-info {
        color: rgb(47 54 66);
      }

    }

    .risk-content-detail-v2 + .risk-content-detail-v2 {
      margin-top: 8px;
    }

    .flex-container {

      display: flex;
    }
  }


}

.exceed-standard-risk-num-wrapper {
  flex-shrink: 0;
  :global {
    .inner-wrapper {
      width: 51px;
      height: 22px;
      color: #fff;
      text-align: center;
      border-radius: 50px;
    }
    .wrapper-error {
      background-color: var(--eui-function-danger-500);
    }
    .wrapper-warning {
      background-color: var(--eui-function-danger-500); ;
    }
    .icon {
      font-size: 14px;
    }
    .number {
      font-size: 14px;
      margin-left: 4px;
    }
  }
}

.note-view-popover-wrap {
  :global {
    .ant-popover-inner-content {
      padding-bottom: 65px;
      position: relative;
    }
    .eui-popover-inner-content {
      padding-bottom: 65px;
      position: relative;
    }
    .note-btn-wrap{
      position: absolute;
      bottom: 15px;
    }
  }
}

