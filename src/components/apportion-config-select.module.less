.apportion-config-select {
  :global {
  }
}

.apportion-config-comp-wrapper {
  :global {
    .eui-form-item-explain {
      margin-bottom: 0;
    }
    .eui-space-horizontal {
      align-items: flex-start;
      .eui-input-number {
        height: 32px;
      }
    }

    .eui-space-item {
      .eui-form-item {
        margin-bottom: 0;
      }
    }
    .flow-template-width {
      width: 340px;
    }
    .apportion-rule-width {
      width: 140px;
    }
    .apportion-value-width {
      width: 140px;
    }
    .form-list-label {
      display: flex;
      align-items: center;
      color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
      font: var(--eui-font-body-r1);
      margin-top: 12px;
      margin-bottom: 8px;
    }
  }
}
