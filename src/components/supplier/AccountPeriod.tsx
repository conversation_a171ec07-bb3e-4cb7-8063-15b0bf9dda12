
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Radio, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './AccountPeriod.module.less'
import { RadioChangeEvent } from 'antd/lib/radio'
import { SelectValue } from 'antd/lib/select'

const Group = Radio.Group
const Opton = Select.Option
/**
 * accountPeriodSource 账期来源
 * accountCycle 周期(月) 暂按照每月一次
 * day 出账日期
 * api api导入时 接口url
 */
const radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px',
}
type periodField = { accountPeriodSource: string, accountCycle: number, day: number, api: string }
interface Props {
    field: Partial<string>
    onChange: (ags) => void
    value: Partial<periodField>
}
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'accountPeriod'
    },
    validator: field => (rule, value: any = {}, callback) => {
        const { name } = field
        if (!value.day) {
            return callback(i18n.get('请选择每月对账日期'))
        }
        if (!value.accountPeriodSource) {
            return callback(i18n.get('请选择账期来源'))
        }
        callback()
    },
    initialValue(props) {
        const { type, value } = props
        if (type === 'create') {
            // 暂时只有手动设置 初始值处理为默认选中
            return { accountPeriodSource: 'MANUAL' }
        }
        return value
    },
    wrapper: wrapper()
})
export default class SupplierPeriod extends PureComponent<Props, any> {
    render() {
        const { value = {} } = this.props
        return (
            <div className={styles.wrapper}>
                <div className={styles.left}>
                    <Group onChange={this.periodSourceChange} value={value.accountPeriodSource}>
                        <Radio style={radioStyle} value={'MANUAL'}>{i18n.get('手动设置')}</Radio>
                    </Group>
                </div>
                <div className={styles.right}>
                    {i18n.get('每月')}
                    <Select value={value.day} style={{ width: '60px', margin: '0px 8px' }} onSelect={this.periodDayChange}>
                        <Opton value={1}>1</Opton>
                        <Opton value={15}>15</Opton>
                    </Select>
                    {i18n.get('日')}
                </div>
            </div>
        )
    }

    private periodSourceChange = (e: RadioChangeEvent) => {
        const { value } = this.props
        let obj: any = { ...value, accountPeriodSource: e.target.value }
        this.onChange(obj)
    }

    private periodDayChange = (e: SelectValue) => {
        const { value } = this.props
        let obj: any = { ...value, day: e }
        this.onChange(obj)
    }

    onChange = (newValue: Partial<periodField>) => {
        const { onChange } = this.props
        onChange && onChange(newValue)
    }
}
