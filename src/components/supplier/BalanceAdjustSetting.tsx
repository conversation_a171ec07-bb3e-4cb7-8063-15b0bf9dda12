
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Radio, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './BalanceAdjustSetting.module.less'
import { get } from 'lodash'
import { RadioChangeEvent } from 'antd/lib/radio'
import { SelectValue } from 'antd/lib/select'
const { Option } = Select
/**
 * accountPeriodSource 账期来源
 * accountCycle 周期(月) 暂按照每月一次
 * day 出账日期
 * api api导入时 接口url
 */

type periodField = { manualAdjustFlag: boolean, paymentBillFlag: boolean, paymentId: string }
interface Props {
    field: Partial<string>
    onChange: (ags) => void
    value: Partial<periodField>
    form: any
    paymentList: any[]
}

// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'balanceAdjustSetting'
    },
    validator: field => (rule, value: any = {}, callback) => {
        const { paymentBillFlag, paymentId } = value
        if (paymentBillFlag && !paymentId) {
            return callback(i18n.get('请选择付款单'))
        }
        callback()
    },
    wrapper: wrapper()
})
export default class BalanceAdjustSetting extends PureComponent<Props, any> {

    render() {
        const { value = {}, form, paymentList } = this.props
        const settlementType = form.getFieldValue('settlementType')
        return (
            <div className={styles.wrapper}>
                <Checkbox onChange={this.handlecheckbox.bind(this, 'manualAdjustFlag')} checked={value.manualAdjustFlag}>
                    <span>{i18n.get('手动调整')}</span>
                </Checkbox>
                {settlementType !== 'CREDIT' && (
                    <Checkbox onChange={this.handlecheckbox.bind(this, 'paymentBillFlag')} checked={value.paymentBillFlag}>
                        <span>{i18n.get('通过付款单充值')}</span>
                    </Checkbox>
                )}
                {settlementType !== 'CREDIT' && value.paymentBillFlag && (
                    <Select
                        placeholder={i18n.get('请选择付款单')}
                        value={value.paymentId}
                        style={{ width: 200 }}
                        onChange={this.handlecheckbox.bind(this, 'paymentId')}
                    >
                        {paymentList.map(v => (
                            <Option value={v.id}>{v.name}</Option>
                        ))}
                    </Select>
                )}
            </div>
        )
    }

    onChange = (newValue: Partial<periodField>) => {
        const { onChange } = this.props
        onChange && onChange(newValue)
    }

    private handlecheckbox = (key, e) => {
        const { value = {} } = this.props
        const checked = get(e, 'target.checked')
        value[key] = typeof checked === 'boolean' ? checked : e
        if (!('paymentBillFlag' in value)) {
            value.paymentBillFlag = false
        }
        this.onChange(value)
    }
}
