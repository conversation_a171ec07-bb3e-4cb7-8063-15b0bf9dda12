
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Radio, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './AccountPeriod.module.less'
import { RadioChangeEvent } from 'antd/lib/radio'
import { SelectValue } from 'antd/lib/select'

const Group = Radio.Group
const Opton = Select.Option
/**
 * accountPeriodSource 账期来源
 * accountCycle 周期(月) 暂按照每月一次
 * day 出账日期
 * api api导入时 接口url
 */
const radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px',
}
type periodField = { source: string, day: number, api: string }
interface Props {
    field: Partial<string>
    onChange: (ags) => void
    value: Partial<periodField>
}

// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'accountPaymentSetting'
    },
    validator: field => (rule, value: any = {}, callback) => {
        const { source, day } = value
        if (!source) {
            return callback(i18n.get('请选择账期来源'))
        }
        if (!day) {
            return callback(i18n.get('选择对账延后日期'))
        }

        callback()
    },
    wrapper: wrapper(),
    initialValue(props) {
        const { type, value } = props
        // 暂时只有手动设置 初始值处理为默认选中
        if (type === 'create') {
            return { source: 'MANUAL' }
        }
        return value
    },
})
export default class AccountPaymentSetting extends PureComponent<Props, any> {

    render() {
        const { value = {} } = this.props
        let options = this.buildOption()
        return (
            <div className={styles.wrapper}>
                <div className={styles.left}>
                    <Group onChange={this.periodSourceChange} value={value.source}>
                        <Radio style={radioStyle} value={'MANUAL'}>{i18n.get('手动设置')}</Radio>
                    </Group>
                </div>
                <div className={styles.right}>
                    {i18n.get('账单日后')}
                    <Select value={value.day} style={{ width: '60px', margin: '0px 8px' }} onSelect={this.periodDayChange}>
                        {options}
                    </Select>
                    {i18n.get('日')}
                </div>
            </div>
        )
    }

    buildOption = () => {
        let arr = []
        for (let index = 1; index <= 20; index++) {
            arr.push((<Opton key={index} value={index}>{index}</Opton>))
        }
        return arr
    }

    private periodSourceChange = (e: RadioChangeEvent) => {
        const { value } = this.props
        let obj: any = { ...value, source: e.target.value }
        this.onChange(obj)
    }


    private periodDayChange = (e: SelectValue) => {
        const { value } = this.props
        let obj: any = { ...value, day: e }
        this.onChange(obj)
    }
    onChange = (newValue: Partial<periodField>) => {
        const { onChange } = this.props
        onChange && onChange(newValue)
    }
}
