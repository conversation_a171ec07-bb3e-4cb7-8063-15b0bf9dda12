
import React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Checkbox, Radio, Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RuleList.module.less'
// import { RadioChangeEvent } from 'antd/lib/radio'
import { SelectValue } from 'antd/lib/select'
import { map } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
const EKBIcon = api.require<any>('@elements/ekbIcon')
const Group = Radio.Group
const Opton = Select.Option
type Field = { name: string, label: string, entity: string }
interface Props {
    field: Partial<string>
    onChange: (ags) => void
    value: string[]
    ruleFieldList: any[]
}
const noop = () => { }
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'ruleList'
    },
    validator: field => (rule, value: any = [''], callback) => {
        const errItem = value?.find(v => v === '')
        if (errItem === '') {
            return callback(i18n.get('请选择维度'))
        }
        if (value.length === 2 && value[0] === value[1]) {
            return callback(i18n.get('拆分维度配置重复'))
        }

        return callback()
    },
    wrapper: wrapper()
})
export default class RuleList extends React.Component<Props, any> {


    getList = (line: string) => {
        const { ruleFieldList = [], value } = this.props
        return ruleFieldList?.filter(v => !value?.includes(v.name) || v.name === line)
    }

    render() {
        const { value = [''] } = this.props
        return (
            <div className={styles.wrapper}>
                {value.map(this.runderLine)}
            </div>
        )
    }

    runderLine = (line, idx) => {
        const { ruleFieldList, value = [''] } = this.props
        const list = this.getList(line)
        return (
            <div key={idx} className={styles['line']}>
                <Select style={{ width: 260 }} onSelect={this.valueChange.bind(this, idx)} value={line}>
                    {list.map(v => {
                        return (<Opton key={v.name} value={v.name}>{v.label}</Opton>)
                    })}
                </Select>
                <div className={styles['icon-c']}>
                    <EKBIcon
                        name="#EDico-plus-default"
                        className={value.length === 2 ? styles['disabled'] : ''}
                        onClick={value.length >= 2 ? noop : this.handelAddLine}
                        style={{ width: 16, height: 16, color: '#18B694' }}
                    />
                    <EKBIcon
                        name="#EDico-scan-b"
                        className={value.length === 1 ? styles['disabled'] : ''}
                        style={{ width: 16, height: 16, color: '#18B694', marginLeft: 8 }}
                        onClick={value.length === 1 ? noop : this.handelDeleteLine.bind(this, idx)}
                    />
                </div>
            </div>
        )
    }

    private onChange = (value) => {
        const { onChange } = this.props
        onChange && onChange(value)
    }

    private valueChange(idx, val) {
        const { value = [''] } = this.props
        let arr = value?.map((v, i) => {
            if (i === idx) {
                return val
            }
            return v
        })
        this.onChange(arr)
    }

    handelAddLine = () => {
        const { value = [''] } = this.props
        this.onChange(value.concat(''))
    }

    handelDeleteLine = (idx) => {
        const { value } = this.props
        this.onChange(value.filter((v, i) => i !== idx))
    }
}
