import React, { FC } from 'react'
import { Form, DatePicker } from 'antd'
import { isDisable } from '../utils/fnDisableComponent'
import { required } from '../validator/validator'
import moment from 'moment'
import { ENUM_DATE_TYPE } from '../consts'
import getLabel from '../utils/fnGetFieldLabel'
import fnPredefine4Date, { formatDateTime, formatWithTime, getShowTime } from '../utils/fnPredefine4Date'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { get } from 'lodash'
import { WrappedFormUtils } from 'antd/lib/form/Form'
const { MonthPicker } = DatePicker
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  form: WrappedFormUtils
  value?: any
  vm: any
  hiddenFields?: string[]
  onChange?: (value: any) => void
}

const DateEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const validatorDate = (rule, value, callback) => {
    // const { defaultValue } = field
    // if (defaultValue && defaultValue.type === 'predefine' && defaultValue.value === 'repayment.date') {
    //   let loanDate = props.form.getFieldValue('loanDate')
    //   if (value && value < loanDate) return callback(i18n.get('还款日期应该晚于借款日期,请重新选择'))
    // }
    callback(required(fieldInfo, value))
  }
  const initValue = () => {
    if (get(record, dataIndex)) {
      return get(record, dataIndex)
    } else {
      return fnPredefine4Date(fieldInfo)
    }
  }
  const value = initValue()
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorDate, required: !fieldInfo?.optional }],
        initialValue: value ? moment(value) : undefined
      })(<DateEditWrapper {...props} />)}
    </FormItem>
  )
}
const DateEditWrapper: FC<IProps> = props => {
  const { field: fieldInfo, record, value, onChange, vm, form } = props
  const { withTime, isClear = false, dateTimeType } = fieldInfo
  const disabled = isDisable(props)
  const handleOnChange = date => {
    const time = formatWithTime(withTime, date, dateTimeType)
    onChange?.(time)
    updateCellValue(fieldInfo?.field, time, record, vm, form)
  }
  if (dateTimeType === ENUM_DATE_TYPE.YEAR_MONTH) {
    return (
      <MonthPicker
        value={typeof value === 'number' ? moment(value) : value}
        style={itemInnerStyle}
        allowClear={isClear}
        disabled={disabled}
        size="large"
        onChange={handleOnChange}
      />
    )
  }
  return (
    <DatePicker
      value={typeof value === 'number' ? moment(value) : value}
      style={itemInnerStyle}
      format={formatDateTime(withTime, dateTimeType)}
      allowClear={isClear}
      disabled={disabled}
      onChange={handleOnChange}
      size="large"
      showTime={getShowTime(withTime, dateTimeType)}
    />
  )
}
export default DateEditCell
