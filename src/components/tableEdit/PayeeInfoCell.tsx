import React, { FC } from 'react'
import EKBIcon from '../../elements/ekbIcon'
import styles from './Popover.module.less'
interface IProps {
  text: any
  showEmpty?: boolean
}
const renderIconObj = card => {
  if (card.sort === 'WALLET') {
    return { icon: <EKBIcon className="mr-5 stand-20-icon" name="#EDico-zhifubao" />, bank: i18n.get('钱包') }
  } else if (card.sort === 'ALIPAY') {
    const icon = <EKBIcon className="mr-5 stand-20-icon" name="#EDico-zhifubao" />
    return { icon, bank: i18n.get('支付宝') }
  } else {
    const icon = card.unionBank ? card.unionIcon : card.icon
    const bank = card.unionBank ? card.unionBank : card.bank
    return { icon, bank }
  }
}
const PayeeInfoCell: FC<IProps> = props => {
  const { text, showEmpty } = props
  if (!text?.id) {
    if (showEmpty) {
      return null
    }
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  }
  const iconObj = renderIconObj(text)
  return (
    <div className={styles.tableReadonlyWidth}>
      <span>{text.sort !== 'ALIPAY' ? <img src={iconObj.icon} width={20} height={20} /> : iconObj.icon}</span>
      <span>{text.accountNo || text.cardNo}</span>
    </div>
  )
}

export default PayeeInfoCell
