import React, { PureComponent } from 'react'
import { Col, Icon, Row, Select } from 'antd'
import styles from './InvoiceEditCell.module.less'
import style1 from './MoneyModal.module.less'
import RefInvoice from '../../elements/invoice-form/RefInvoice'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { fnFormatAttachment, getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { isDisable, detailIsDisable } from '../utils/fnDisableComponent'
import CardListView from '../../elements/InvoiceCard/InvoiceItem'
import { get, cloneDeep, isEmpty } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
import { invoiceItemOptions, parseAsShowValue, fnGetInvoiceManage } from '../../elements/invoice-form/utils/config'
import { invoiceOptions } from '../../lib/InvoiceUtil'
import { getV } from '@ekuaibao/lib/lib/help'
import InvoiceMappingValue from '../../lib/InvoiceMappingValue'
import InvoiceTaxInfo from '../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { standardValueMoney } from '../../lib/misc'
import { fnSortInvoice } from '../utils/fnInvoiceSelectUtil'
import { SortSelector } from '../utils/SortViewUtil'
import { updateInvoiceDeduction } from '../../lib/InvoicePriceTaxSeparated'
import { getCheckInvoiceAgain } from '../../plugins/bills/bills.action'
import { showMessage } from '@ekuaibao/show-util'
import classNames from 'classnames'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button } from '@hose/eui'
import { depthFirstTraversal } from '../dynamic/InvoiceSelect'
import { toJS } from 'mobx'
import { inputInvoiceImport } from '../../plugins/bills/parts/right-part/billInfo/BillImport/invoiceImport'
const Option = Select.Option
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')

@EnhanceConnect(state => ({
  feeTypes: state['@common'].feetypes.data,
  OCRPower: state['@common'].powers.OCR,
  OCRMedical: state['@common'].powers.OCRMedical,
  KA_INVOICE: state['@common'].powers.KA_INVOICE,
  DIGITAL_ORIGINAL_FILE: state['@common'].powers.DIGITAL_ORIGINAL_FILE,
  KA_PREVIEW_PDF_SHOW_MODAL: state['@common'].powers.KA_PREVIEW_PDF_SHOW_MODAL,
  userInfo: state['@common'].userinfo.data,
  corporationList: state['@invoice-manage'].corporationList,
}))
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class InvoiceSelectModal extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    let { invoiceType, importMode } = this.props.field
    const { flowPlanConfigId, configNodeId } = this.props.currentNode || {}
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    if (props.value) {
      const invoices = get(props.value, 'invoices') || []
      props.value.invoices = invoices.filter(v => v.master)
    }
    this.state = {
      type: undefined,
      corporationList: [],
      invoiceType: fnInvoiceType || [],
      imageVisible: false,
      importMode,
      fileList: get(props, 'value.attachments', []),
      invoiceImageList: [],
      formValue: undefined,
      canClick: true,
      isSort: true,
      curSortType: '2', // 默认按发票时间排序
      stateValue: props.value
    }
  }
  invoiceMappingValue = new InvoiceMappingValue()
  invoiceTaxInfo = new InvoiceTaxInfo()

  componentDidMount() {
    const { field, bus, riskData, corporationList } = this.props
    const { invoiceType, isSort, curSortType, importMode, stateValue: value } = this.state
    const { editable } = field
    if (value && value.type === 'exist' && field?.invoiceType?.isRequired) {
      bus.emit('update:calculate:template', { invoiceForm: { optional: false } })
    }
    if (importMode?.setVisible) {
      api.invokeService('@bills:get:feetype:invoice:importMode', importMode).then(res => {
        this.setState({ importMode: res.items })
      })
    }
    if (value && value.invoices) {
      const invoiceIds = value.invoices.map(o => o.master.id)
      if (invoiceIds.length) {
        api.invokeService('@bills:get:invoice:image:by:ids', invoiceIds).then(rep => {
          this.setState({ invoiceImageList: rep.items })
        })
        api.invokeService('@bills:get:invoice:info:by:ids', invoiceIds).then(items => {
          items?.forEach(oo => {
            const item = value.invoices.find(v => v.master.id === oo.master.id)
            item.master = oo.master
            item.details = oo.details
            item.originalData = oo
          })
          value.invoices = fnSortInvoice(value.invoices, isSort, curSortType, riskData)
          this.setState({ stateValue: value })
        })
      }
    }
    bus.on('invoice-Attachment-Upload', this.handleAttachment)
    bus.on('continue:add:detail:invoiceForm', this.handleContinueAdd)

    const ids = field.invoiceType.unify.limit ? field.invoiceType.unify.invoiceCorporation : undefined
    if ((ids && ids.length) || !field.invoiceType.unify.limit) {
      api.invokeService('@bills:get:invoice:corporation', ids).then(data => {
        this.setState({
          corporationList: data.items
        })
      })
    }

    const ishas = invoiceType?.find(v => v.type === value?.type)
    if (!ishas && editable) {
      this.handleChange(!!invoiceType.length && invoiceType[0].value)
    }

    if (corporationList && corporationList.length) {
      const didiObj = corporationList.find(item => item.channel == 'DIDI')
      if (field.canResetUnify && didiObj) {
        const newValue = {
          type: 'unify',
          invoiceCorporationId: didiObj.id,
          invoiceCorporation: didiObj
        }
        this.setState({ stateValue: newValue })
      }
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('invoice-Attachment-Upload', this.handleAttachment)
    bus.un('continue:add:detail:invoiceForm', this.handleContinueAdd)
  }

  handleContinueAdd = () => {
    //特殊处理再记一笔
    const {
      field: { invoiceType, defaultValue, editable },
      currentNode: { flowPlanConfigId, configNodeId } = {} as any
    } = this.props
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    this.setState({
      fileList: []
    })
    const type = invoiceType?.defaultInvoiceType || fnInvoiceType[0].type
    const fnvalue = editable ? { type } : { type: defaultValue.value }
    this.setState({
      stateValue: fnvalue
    })
  }

  handleInvoiceDelete = () => {
    this.setState({
      stateValue: { type: 'exist' }
    })
  }

  handleAttachment = upFileList => {
    const {
      fileList,
      stateValue: { invoices, invoiceConfirm }
    } = this.state
    let fileListData = upFileList.concat(fileList)
    this.setState({
      fileList: fileListData,
      stateValue: { type: 'exist', attachments: fileListData, invoices: invoices, invoiceConfirm }
    })
  }

  handleRemoveAttachment = line => {
    const {
      field: { editable }
    } = this.props
    const {
      stateValue: { type, invoices, attachments, invoiceConfirm }
    } = this.state
    let fileLists = attachments.length > 0 ? attachments : [line]
    let files = fnFormatAttachment(fileLists)
    let cloneList = files.slice(0)
    let imgIndex = cloneList.findIndex(v => v.key === line.key)
    cloneList.splice(imgIndex, 1)

    let invoiceType = type
    if (!editable && cloneList && !cloneList.length && invoices && !invoices.length) {
      invoiceType = 'noWrite'
    }
    this.setState({
      fileList: cloneList,
      stateValue: { type: invoiceType, attachments: cloneList, invoices, invoiceConfirm }
    })
  }

  handleFilePreview = line => {
    const { fileList } = this.state
    const fileLists = fileList.length > 0 ? fileList : [line] //审批中修改单据fileList为[]
    preview()({ value: fileLists, line })
  }

  handleFileDownload = line => {
    const url = getV(line, 'url', '')
    const fileName = getV(line, 'fileName', '')
    api.emit('@vendor:download', url, fileName)
  }

  handleChange = value => {
    const { invoiceType, corporationList, stateValue: invoices } = this.state
    const { field, bus, template } = this.props
    const list = invoiceType.filter(v => v.value === value)
    const type = !!list.length && list[0].type
    if (field?.invoiceType?.isRequired) {
      bus.emit('update:calculate:template', { invoiceForm: { optional: type !== 'exist' } })
    }
    const fnvalue =
      type === 'unify'
        ? {
            type: type,
            invoiceCorporationId: corporationList[0] && corporationList[0].id,
            invoiceCorporation: corporationList[0]
          }
        : { type: type }
    this.setState({ type: type, stateValue: fnvalue })
    if (Array.isArray(template) && (!invoices || !invoices.length)) {
      const taxs = template.filter(line => get(line, 'defaultValue.type', '') === 'invoiceSum')

      if (taxs.length) {
        bus.emit('money:tax:update:value')
      }
    }
  }

  handleChange2 = value => {
    const { corporationList } = this.state
    let corporation = corporationList.filter(v => v.name === value)
    const fnvalue = {
      type: 'unify',
      invoiceCorporationId: corporation && corporation[0].id,
      invoiceCorporation: corporation[0]
    }
    this.setState({ stateValue: fnvalue })
  }

  handleImportInvoiceClick = () => {
    let { feeTypes, KA_INVOICE } = this.props
    const {
      stateValue: { invoices = [] }
    } = this.state
    this.fnDetailsImportClick(feeTypes, invoices).then((data: any) => {
      if (data.length) {
        if (!!KA_INVOICE) {
          let idsOld = []
          if (invoices && invoices.length > 0) {
            idsOld = invoices.map(item => item.master.id)
          }
          let idsNew = []
          if (data[0]?.attachmentList && data[0]?.attachmentList.length > 0) {
            idsNew = data[0]?.attachmentList.map(v => v.master.id)
          }
          const ids = idsOld.concat(idsNew)
          ids &&
            ids.length &&
            api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
              this.setState({ invoiceImageList: rep.items })
            })
        }
        this.setInvoiceValue(data, true)
      }
    })
  }
  fnDetailsImportClick = (visibilityFeeTypes, invoices) => {
    return this.fnOpenModal(null, visibilityFeeTypes, invoices)
  }
  fnOpenModal = (ocr, types, invoicesList) => {
    let { bus, dataSource = {}, notShowModalIfAllInvoiceSuccess, record } = this.props
    //TODO dataSource
    let { id } = dataSource
    const { importMode } = record?.rowManager?.template?.find(item => item.field === 'invoiceForm')
    const invoices = getNodeValueByPath(record.feeTypeForm, 'invoiceForm.invoices', [])
    const isWholeInvoiceDetail = invoices.length
    return api
      .open('@bills:ImportInvoiceModal', { details: [record.feeTypeForm], flowId: id, multiple: true, bus })
      .then((data: any) => {
        if (!isEmpty(ocr)) {
          return api.open('@bills:ImportOCRListModal', { bus, types, state: ocr.state })
        }
        return api.open('@bills:ImportInvoiceDetailModal', {
          invoiceList: data.invoiceList || [],
          visibilityFeeTypes: types,
          source: 'addDetails',
          billSpecification: record?.specificationId,
          editDetailObject: {
            specification: record?.specificationId,
            feeType: record?.feeTypeId || record?.feeType,
            isWholeInvoiceDetail
          },
          importMode,
          importType: 'pdf',
          invoices: invoicesList,
          notShowModalIfAllInvoiceSuccess,
          attachmentList: data.invoiceList || []
        })
      })
  }

  handleImportInputInvoiceClick = () => {
    let { feeTypes, record } = this.props
    const {
      stateValue: { invoices }
    } = this.state
    const billDetails = [record.feeTypeForm] || []
    this.fnDetailsInputImportClick(feeTypes, billDetails, invoices).then(data => {
      if (data.length) {
        this.setInvoiceValue(data)
      }
    })
  }
  fnDetailsInputImportClick = (visibilityFeeTypes, billDetails, invoices) => {
    const { record, template = [] } = this.props
    let { autoCalFields = {} } = this.state
    let editDetailObject = { autoCalFields, specification: record?.specificationId, feeType: record?.feeTypeId }
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return inputInvoiceImport.apply(this, [
      { visibilityFeeTypes, details: billDetails, source: 'addDetails', editDetailObject, invoices, importMode }
    ])
  }
  handleImportAliPayInvoiceClick = async () => {
    let { feeTypes, bus } = this.props
    const {
      stateValue: { invoices }
    } = this.state
    const isAuth = await api.invokeService('@bills:check:aliPayCard:auth')
    if (!isAuth) return

    bus.invoke('element:details:aliPay:import:click', feeTypes, invoices).then(data => {
      if (data.length) {
        this.setInvoiceValue(data)
      }
    })
  }

  handleImportAifaPiaoInvoiceClick = async () => {
    const isAuth = await api.invokeService('@bills:check:aifapiao:auth')
    if (!isAuth) return null
    const url = await api.invokeService('@bills:get:aifapiao:public:url')
    const res = await api.open('@bills:IframeModal', {
      src: url,
      handleListener: this.handleImportAifapiao
    })
  }

  handleImportAifapiao = async data => {
    const { ids, type } = data
    let { feeTypes, submitterId } = this.props
    const {
      stateValue: { invoices }
    } = this.state
    if (type === 'importInvoice') {
      const { items } = await api.invokeService('@bills:import:invoice:from:aifapiao', {
        ids,
        staffId: submitterId?.id
      })
      this.fnDetailsAifapiaoImportClick(feeTypes, invoices, items).then((data: any) => {
        if (data.length) {
          this.setInvoiceValue(data)
        }
      })
    }
  }
  fnDetailsAifapiaoImportClick = (feeTypes, invoiceList, data) => {
    const { record, notShowModalIfAllInvoiceSuccess, template = [] } = this.props
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return api.open('@bills:ImportInvoiceDetailModal', {
      invoiceList: data || [],
      visibilityFeeTypes: feeTypes,
      source: 'addDetails',
      billSpecification: record?.specificationId,
      editDetailObject: { specification: record?.specificationId, feeType: record?.feeTypeId },
      importMode,
      importType: 'aifapiao',
      invoices: invoiceList,
      notShowModalIfAllInvoiceSuccess
    })
  }

  handleImportOCRMedicalClick = () => {
    this.handleImportOCRClick(true)
  }

  fnDetailsImportOCRClick = (visibilityFeeTypes, invoices, isMedical) => {
    const { bus, submitterId, notShowModalIfAllInvoiceSuccess = false, template, record } = this.props
    let { autoCalFields = {} } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    let editDetailObject = {
      autoCalFields,
      specification: record?.specificationId,
      feeType: record?.feeTypeId,
      feeTypeForm: [record.feeTypeForm]
    }
    return api.open('@bills:ImportUploadOCRModal', { isMedical }).then(attachmentList => {
      return api.open('@bills:ImportInvoiceDetailModal', {
        visibilityFeeTypes,
        invoiceList: attachmentList,
        attachmentList,
        editDetailObject,
        submitterId,
        isMedical,
        isOcr: true, // 智能识别入口
        source: 'addDetails', //editDetail
        importType: 'ocr',
        currentSpecification: record?.specificationId,
        importMode,
        invoices,
        bus,
        notShowModalIfAllInvoiceSuccess: !isMedical && notShowModalIfAllInvoiceSuccess
      })
    })
  }

  handleImportOCRClick = isMedical => {
    let { feeTypes } = this.props
    const {
      stateValue: { invoices, attachments, invoiceConfirm }
    } = this.state
    this.fnDetailsImportOCRClick(feeTypes, invoices, isMedical).then((data: any) => {
      if (data.length) {
        const { feeTypeForm, attachmentList } = data[0]
        const {
          invoiceForm: { invoices }
        } = feeTypeForm
        if (attachments && attachments.length) {
          feeTypeForm.invoiceForm.attachments = attachments
        }
        if (invoiceConfirm) {
          feeTypeForm.invoiceForm.invoiceConfirm = invoiceConfirm
        }
        const ids = invoices.map(item => item.master.id)
        ids &&
          ids.length &&
          api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
            this.setState({ invoiceImageList: rep.items })
          })
        this.handleExternal()
        const { invoiceForm, ...others } = feeTypeForm
        this.setState({ stateValue: invoiceForm, formValue: others })
      }
    })
  }

  deleteAutoFieldValue(values, isDelete = false) {
    // 自动计算和差旅标准赋值不能受导入发票影响
    const { autoCalFields, template } = this.props
    this.amountKeys.forEach(key => {
      // 删除的时候不计税金额需要更新字段值，所以忽略此字段
      const isTaxAmountValue = key !== 'noTaxAmount' && !isDelete
      if (
        (autoCalFields?.onFields && autoCalFields.onFields.includes(key) && isTaxAmountValue) ||
        template.find(v => v.name === key && v.defaultValue && v.defaultValue.type === 'costStandard')
      ) {
        delete values[key]
      }
    })
    return values
  }

  setInvoiceValue = async (data, isNotDeleteAutoField = false) => {
    const {
      stateValue: { attachments, invoiceConfirm }
    } = this.state
    const { feeTypeForm } = data[0]

    if (attachments && attachments.length) {
      feeTypeForm.invoiceForm.attachments = attachments
    }
    if (invoiceConfirm) {
      feeTypeForm.invoiceForm.invoiceConfirm = invoiceConfirm
    }
    let feeForm = feeTypeForm
    if (!isNotDeleteAutoField) {
      //TODO
      feeForm = this.deleteAutoFieldValue(feeTypeForm)
    }
    const { invoiceForm, ...others } = feeTypeForm
    this.setState({ stateValue: invoiceForm, formValue: others })
    this.handleExternal()
  }

  amountKeys = ['amount', 'taxAmount', 'noTaxAmount', 'taxRate']

  handleExternal = () => {
    const { external } = this.props
    if (external) {
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
  }

  handleOnchange = data => {
    this.handleExternal()
    this.setState({ stateValue: data })
  }

  handleInvoiceEdit = (dataSource, item, i, attachment) => {
    let index = i
    const { bus, template, feeDetailId, feeTypes, feeType } = this.props
    const {
      stateValue: value,
      stateValue: { invoices }
    } = this.state
    const flatFeeTypes = depthFirstTraversal(feeTypes)
    let currentFeeType = flatFeeTypes.find(item => item?.feeType?.id === feeDetailId)
    if (feeType) {
      currentFeeType = feeType
    }
    const regions = dataSource.filter(line => line.id !== item.id).map(line => line.region)
    let invoiceList = cloneDeep(invoices) || []
    index = invoiceList.findIndex(v => v.master.id === item.master.id)
    api.open('@bills:EditInvoice', { invoiceInfo: item, attachment, regions }).then(async (data: any) => {
      invoices[index].approveAmount && !invoices[index].comment && delete invoices[index].approveAmount
      delete invoices[index].taxAmount
      delete invoices[index].taxRate
      invoices[index].master.form = { ...invoices[index].master.form, ...data.master.form }
      bus.emit('savebtn:state:change', { disabled: true })

      // 可抵扣税额需要重新调用 updateInvoiceDeduction 获取相关值
      if (currentFeeType) {
        await updateInvoiceDeduction([currentFeeType], invoices, true)
      }

      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
      const newValue = this.deleteAutoFieldValue(values)
      const { invoiceForm, ...others } = newValue
      this.setState({ formValue: others })
      this.handleOnchange({ ...value, invoices })
      bus.emit('savebtn:state:change', { disabled: false })
    })
  }

  handleDeleteInvoice = async line => {
    let {
      field: { editable },
      bus,
      template
    } = this.props
    let {
      stateValue: { type, invoices, attachments, invoiceConfirm }
    } = this.state
    let invoiceList = cloneDeep(invoices) || [],
      invoiceSumField = {}
    let index = invoiceList.findIndex(v => v.master.id === line.master.id)
    invoiceList.splice(index, 1)

    if (!editable && (!attachments || !attachments.length) && (!invoiceList || !invoiceList.length)) {
      type = 'noWrite'
    }
    // 删除最后一张发票时，清除发票汇总上所有的金额价税合计上的金额数据
    if (!invoiceList?.length) {
      const summaryFields = template.filter(line => get(line, 'defaultValue.type') === 'invoiceSum')
      summaryFields.forEach(({ field }) => {
        invoiceSumField[field] = undefined
      })
    }
    bus.emit('savebtn:state:change', { disabled: true })

    let values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoiceList, template)
    values = this.deleteAutoFieldValue(values, true)

    const newValue = { ...invoiceSumField, ...values }
    const { invoiceForm, ...others } = newValue
    this.setState({ formValue: others })
    this.handleOnchange({ type, attachments, invoices: invoiceList, invoiceConfirm })
    bus.emit('savebtn:state:change', { disabled: false })
  }

  handleImgPreview = imageList => {
    // api.emit('@vendor:preview:images', [imageList], imageList)
    preview()({ value: [imageList], line: imageList })
  }

  fnGetcorporationById = (id, invoiceCorporation) => {
    const { corporationList } = this.state
    let corporation = corporationList.filter(v => v.id === id)
    if (!corporation.length && !corporationList.length) return
    if (!corporation.length && corporationList.length) {
      if (invoiceCorporation && id === invoiceCorporation.id) return invoiceCorporation.name
      this.setState({
        stateValue: {
          type: 'unify',
          invoiceCorporationId: corporationList[0].id,
          invoiceCorporation: corporationList[0]
        }
      })
      return corporationList[0].name
    }
    return corporation[0].name
  }

  handleEditTax = item => {
    const { flowId, bus, template } = this.props
    let {
      stateValue: { invoices },
      stateValue: value
    } = this.state
    const {
      master: { entityId, id, form },
      itemIds
    } = item
    const invoiceId = item.invoiceId || id
    const params = {
      flowId,
      invoiceId,
      itemIds
    }
    const taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
    const taxRate = this.invoiceTaxInfo.getTaxRate(item)
    api.open('@bills:InvoiceEditTaxModal', { entityId, taxAmount, taxRate, params, invoiceForm:form }).then(async (data: any) => {
      const deductibleInvoice = invoices.find(line => line.master.id === item.master.id)
      deductibleInvoice.taxAmount = standardValueMoney(data.taxAmount)
      deductibleInvoice.taxRate = data.taxRate
      bus.emit('savebtn:state:change', { disabled: true })
      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
      const { invoiceForm, ...others } = values
      this.setState({ formValue: others })
      this.handleOnchange({ ...value, invoices })
      bus.emit('savebtn:state:change', { disabled: false })
      api.invokeService('@bills:set:taxAmount:change:log', {
        dataId: invoiceId,
        dataBefore: { taxAmount },
        dataAfter: { taxAmount: Number(data.taxAmount) }
      })
    })
  }

  handleEditReviewAmount = item => {
    const { flowId, bus, template } = this.props
    let {
      stateValue: { invoices },
      stateValue: value
    } = this.state
    const {
      master: { entityId, form, id },
      itemIds,
      comment
    } = item
    let { approveAmount } = item
    const invoiceId = item.invoiceId || id
    const params = {
      flowId,
      invoiceId,
      itemIds
    }
    const totalMoney =
      entityId === i18n.get('system_发票主体')
        ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]?.standard
        : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })]?.standard ||
          form[i18n.get(`E_{__k0}_金额合计`, { __k0: entityId })]?.standard
    !approveAmount && (approveAmount = totalMoney)
    api
      .open('@bills:InvoiceApproveAmountModal', { params, approveAmount, comment, totalMoney })
      .then(async (data: any) => {
        const deductibleInvoice = invoices.find(line => line.master.id === item.master.id)
        deductibleInvoice.approveAmount = standardValueMoney(data.approveAmount)
        deductibleInvoice.comment = data.comment
        const amount = invoices.reduce(
          (total, oo) => {
            const {
              master: { entityId, form }
            } = oo
            //新上传的发票 提交人修改时还没有核发金额
            const approveamount =
              entityId === i18n.get('system_发票主体')
                ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]
                : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })]
            return new MoneyMath(total).add(oo.approveAmount || approveamount || 0).__value
          },
          { standard: '0' }
        )
        bus.emit('savebtn:state:change', { disabled: true })
        const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
        const { invoiceForm, ...others } = values
        let formValue = others
        this.handleOnchange({ ...value, invoices })
        if (Number(amount.standard) !== 0) {
          formValue = { ...formValue, amount }
        }
        this.setState({ formValue: formValue })
        bus.emit('savebtn:state:change', { disabled: false })
      })
  }

  handleRetryCheckerInvoiceClick = async (i, item) => {
    let index = i
    const { bus, template } = this.props
    const {
      canClick,
      stateValue: value,
      stateValue: { invoices }
    } = this.state
    if (!canClick) return
    this.setState({ canClick: false })
    try {
      bus.emit('savebtn:state:change', { disabled: true })
      const invoiceId = item.master.id
      let invoiceList = cloneDeep(invoices) || []
      index = invoiceList.findIndex(v => v.master.id === item.master.id)
      const data = await api.invokeService('@bills:retry:checker:invoice', invoiceId)
      invoices[index].details = data.value.details
      invoices[index].master = data.value.master
      invoices[index].originalData = data.value
      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)

      const feeTypeForm = this.deleteAutoFieldValue(values)
      const { invoiceForm, ...others } = feeTypeForm
      this.setState({ formValue: others })
      this.handleOnchange({ ...value, invoices })
      this.setState({ canClick: true })
      bus.emit('savebtn:state:change', { disabled: false })
    } catch (e) {
      this.setState({ canClick: true })
      bus.emit('savebtn:state:change', { disabled: false })
    }
  }

  handleCheckInvoiceAgain = (index, item) => {
    const invoiceId = item.master.id
    const fpdm = item.master.form['E_system_发票主体_发票代码']
    const fphm = item.master.form['E_system_发票主体_发票号码']
    const { bus, template } = this.props
    const {
      stateValue: { invoices }
    } = this.state
    getCheckInvoiceAgain({ invoiceId, fpdm, fphm })
      .then(async res => {
        if (res.value) {
          showMessage.success('查验成功')
          bus.emit('savebtn:state:change', { disabled: true })
          const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
          const { invoiceForm, ...others } = values
          this.setState({ formValue: others })
          this.handleUploadInvoiceMetaile(index, item)
        } else {
          showMessage.error('查验失败')
        }
        bus.emit('savebtn:state:change', { disabled: false })
      })
      .catch(e => {
        bus.emit('savebtn:state:change', { disabled: false })
        showMessage.error(e.message || e.msg)
      })
  }
  handleInvoiceStatusChange = (index, invoice) => {
    const {
      stateValue: value,
      stateValue: { invoices }
    } = this.state
    let invoiceList = cloneDeep(invoices) || []
    invoiceList[index] = invoice
    this.handleOnchange({ ...value, invoices: invoiceList })
  }
  handleUploadInvoiceMetaile = (index, invoice) => {
    const {
      stateValue: value,
      stateValue: { invoices }
    } = this.state
    let invoiceList = cloneDeep(invoices) || []
    invoiceList[index] = invoice
    const ids = invoices.map(item => item.master.id)
    api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
      this.setState({ invoiceImageList: rep.items })
    })
    this.handleOnchange({ ...value, invoices: invoiceList })
  }

  handleSortInvoice = () => {
    const { riskData } = this.props
    const {
      stateValue: value,
      stateValue: { invoices = [] },
      isSort,
      curSortType
    } = this.state
    if (!(invoices && invoices.length)) return
    const invoiceArr = fnSortInvoice(invoices, !isSort, curSortType, riskData)
    this.handleOnchange({ ...value, invoices: invoiceArr })
    this.setState({ isSort: !isSort })
  }

  // 切换排序类型
  handleSortChange = curValue => {
    const { riskData } = this.props
    const {
      stateValue: value,
      stateValue: { invoices = [] }
    } = this.state
    if (!invoices.length) return
    this.setState(
      {
        curSortType: curValue,
        isSort: true
      },
      () => {
        const invoiceArr = fnSortInvoice(invoices, true, curValue, riskData)
        this.handleOnchange({ ...value, invoices: invoiceArr })
      }
    )
  }
  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  handleModalSave = () => {
    const { stateValue, formValue } = this.state
    const { layer } = this.props
    layer?.emitOk({ invoiceForm: stateValue, formValue })
  }
  render() {
    const {
      invoiceType,
      corporationList,
      invoiceImageList,
      isSort,
      curSortType,
      importMode,
      stateValue: value
    } = this.state
    let {
      field,
      userInfo: { permissions },
      field: { editable },
      submitterId,
      OCRPower,
      OCRMedical,
      bus,
      billState,
      modifyApproveMoney,
      isRecordExpends,
      isQuickExpends,
      isModify
    } = this.props

    let type = value && value.type
    let disabled = isDisable(this.props)
    const modifyDisabled = detailIsDisable(this.props)
    let InvoiceValue
    if (!field.invoiceType.unify.limit) {
      InvoiceValue = (value && type !== 'noWrite' && invoiceItemOptions(value)) || ''
    }
    InvoiceValue = (value && invoiceItemOptions(value)) || ''
    const corporation =
      type === 'unify' && this.fnGetcorporationById(value.invoiceCorporationId, value.invoiceCorporation)
    const xs = corporation || type === 'unify' ? { span: 12 } : { span: 24 }
    const invoiceDetailList = (value && value.invoices) || []
    const fileList = value && value.attachments
    const showValue = parseAsShowValue(toJS(fileList), toJS(invoiceDetailList))
    const isInvoiceManagePermissions = !!fnGetInvoiceManage(permissions)
    const invoices = (value && value.invoices) || []
    return (
      <div className={style1['modal-invoice-wrapper']}>
        <div className="modal-invoice-header">
          <div className="flex">{i18n.get('添加发票')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-invoice-content">
          <div
            className={classNames(styles['invoice-select-wrapper'], {
              'invoice-select-wrapper-disabled': modifyDisabled
            })}
          >
            <Row>
              <Col xs={{ ...xs }}>
                {InvoiceValue ? (
                  <Select
                    value={InvoiceValue}
                    style={{ width: '100%' }}
                    disabled={!editable || disabled}
                    onChange={this.handleChange}
                  >
                    {invoiceType.map((v, index) => {
                      return (
                        <Option key={index} value={v.value}>
                          {v.label}
                        </Option>
                      )
                    })}
                  </Select>
                ) : (
                  <Select value={i18n.get('无需填写')} disabled style={{ width: '100%' }} />
                )}
              </Col>

              {corporation || type === 'unify' ? (
                <Col xs={{ span: 11, offset: 1 }}>
                  <Select
                    value={corporation}
                    style={{ width: '100%' }}
                    disabled={!editable}
                    onChange={this.handleChange2}
                  >
                    {corporationList.map((v, index) => {
                      return (
                        <Option key={index} value={v.name}>
                          {v.name}
                        </Option>
                      )
                    })}
                  </Select>
                </Col>
              ) : null}
            </Row>

            {type && (type === 'exist' || type === 'noWrite') && (
              <div className="invoice-content">
                <RefInvoice
                  value={fileList}
                  bus={bus}
                  OCRPower={OCRPower}
                  OCRMedical={OCRMedical}
                  importMode={importMode}
                  importWay={'addInvoice'}
                  disabled={modifyDisabled}
                  onImportInvoiceClick={this.handleImportInvoiceClick}
                  onImportInputInvoiceClick={this.handleImportInputInvoiceClick}
                  onImportOCRClick={this.handleImportOCRClick}
                  onImportOCRMedicalClick={this.handleImportOCRMedicalClick}
                  onImportAliPayInvoiceClick={this.handleImportAliPayInvoiceClick}
                  onImportAifaPiaoInvoiceClick={this.handleImportAifaPiaoInvoiceClick}
                />
                {!!invoices.length && (
                  <SortSelector
                    onTypeChange={this.handleSortChange}
                    value={curSortType}
                    onSortChange={this.handleSortInvoice}
                    isSort={isSort}
                  />
                )}
              </div>
            )}
            <CardListView
              bus={bus}
              dataSource={showValue || []}
              isEdit={!modifyDisabled}
              isModify={isModify}
              modifyApproveMoney={modifyApproveMoney}
              billState={billState}
              submitterId={submitterId}
              invoiceImgList={invoiceImageList}
              isRecordExpends={isRecordExpends}
              isQuickExpends={isQuickExpends}
              onDeleteItem={this.handleDeleteInvoice}
              handleImgPreview={this.handleImgPreview}
              onOpenEditInvoice={this.handleInvoiceEdit}
              isInvoiceManagePermissions={isInvoiceManagePermissions}
              onFileDownload={this.handleFileDownload}
              onFilePreview={this.handleFilePreview}
              onRemoveAttachment={this.handleRemoveAttachment}
              onOpenEditModal={this.handleEditTax}
              onOpenEditReviewAmount={this.handleEditReviewAmount}
              onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick}
              onUploadInvoiceMetaile={this.handleUploadInvoiceMetaile}
              riskData={this.props.riskData}
              checkInvoiceAgain={this.handleCheckInvoiceAgain}
              showCheckAginButton={true}
              onInvoiceStatusChange={this.handleInvoiceStatusChange}
            />
          </div>
        </div>
        <div className="modal-invoice-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalSave}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}
