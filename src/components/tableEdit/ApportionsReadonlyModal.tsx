import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { UIContainer as Container } from '@ekuaibao/whispered'
import styles from './MoneyModal.module.less'
import { Icon } from 'antd'
import { Button } from '@hose/eui'
import { flatten, get } from 'lodash'
import { toJS } from 'mobx'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
class ApportionsReadonlyModal extends PureComponent<any, any> {
  state = { feeAmount: 0, spcName: '', apportionfLebal: '' }

  componentDidMount(): void {
    this.getApportionfLebal()
  }

  getApportionfLebal = (): void => {
    const { value, template, record } = this.props
    if (!(value && value.length)) return
    const { specificationId } = value[0]
    const configs = get(specificationId, 'configs', [])
    let { apportionMoneyField } = configs.find(v => v.ability === 'apportion') || {}
    if (!apportionMoneyField) {
      apportionMoneyField = 'amount'  //没有找到的话用默认值
    }
    const list = template?.length ? (Array.isArray(template[0]) ? flatten(template) : template) : []
    const { label, name } = list.find(v => v.name === apportionMoneyField) || {}
    const formValue = record?.['feeTypeForm']
    this.setState({ feeAmount: formValue[name] ?? 0, apportionfLebal: label })
  }

  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
 
  render() {
    const {
      bus,
      value,
      external,
      isForbid,
      field: apportionField = {},
      apportionVisibleList,
      showAllFeeType,
      submitterId,
      detailId,
      billSpecification
    } = this.props
    if (!value) {
      return null
    }
    const { feeAmount, apportionfLebal } = this.state
    return (
      <div className={styles['modal-money-wrapper']}>
        <div className="modal-header modal-money-header">
          <div className="flex">{i18n.get('分摊明细')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-money-content">
          <Container
            name="@apportion:Apportion"
            value={toJS(value)}
            external={external}
            apportionfLebal={apportionfLebal}
            isForbid={isForbid}
            apportionField={apportionField}
            isEdit={false}
            feeAmount={feeAmount}
            apportionVisibleList={apportionVisibleList}
            showAllFeeType={showAllFeeType}
            submitterId={submitterId?.id}
            feeDetailId={detailId}
            billSpecificationId={billSpecification?.id}
            billBus={bus}
          />
        </div>
        <div className="modal-footer modal-money-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalClose}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}

export default ApportionsReadonlyModal
