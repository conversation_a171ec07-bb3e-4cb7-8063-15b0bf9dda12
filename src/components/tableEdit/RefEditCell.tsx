import React, { Component, FC} from 'react'
import { Form } from 'antd'
import { formatRefDataId, formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import { required } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import RefEditCellInner from '../../elements/puppet/Ref'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import { get } from 'lodash'
import { isObject, isString } from '@ekuaibao/helpers'
import { handleGetDataById } from '../utils/fnInitalValue'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  form: any
  getExpenseStandardItemsLength: any
  hiddenFields?: string[]
}
const RefEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props

  if (!fieldInfo) {
    return null
  }
  const { optional, defaultPlaceholder, dependence } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(
        <CustomSelectRef
          {...props}
          field={fieldInfo}
          isDependence={dependence && !!dependence.length}
          placeholder={placeholder}
          dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]}
        />
      )}
    </FormItem>
  )
}

class CustomSelectRef extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = { dimensionList: [] }
  }

  componentWillReceiveProps(np) {
    if (this.props.value !== np.value) {
      const { value, field, onChange } = np
      let id
      if (isString(value)) {
        id = value
      } else if (isObject(value) && value.id && !value.name) {
        id = value.id
      }
      if (id && this.props.value?.id !== id) {
        const promise = handleGetDataById(field, id)
        promise &&
        promise.then(result => {
          !!result ? onChange(result) : onChange()
        })
      }
    }
  }

  handleOnChange = (data?) => {
    const { onChange, field, record, vm, form } = this.props
    onChange && onChange(data)
    updateCellValue(field?.field, data, record, vm, form)
  }
  handleDimensionList = dimensionList => {
    this.filterLastChoice(dimensionList, undefined, this.props.value)
    this.setState({ dimensionList })
  }
  filterLastChoice = (list, preValue, nextValue) => {
    const { bus } = this.props
    if (nextValue && nextValue.id && (!nextValue.code || nextValue.needCheckValue)) {
      const map = treeDataToMap(list)
      let vv = map[nextValue.id]
      if (!vv || !vv.active) {
        bus.setValidateLevel(1)
        preValue ? this.handleOnChange(preValue) : this.handleOnChange()
        // TODO:
        // this.__value = nextValue
      } else {
        this.handleOnChange(vv)
      }
    }
  }
  render() {
    const { dimensionList = [] } = this.state
    const {
      field: fieldInfo,
      placeholder,
      value,
      isDependence,
      getExpenseStandardItemsLength,
      submitterId,
      isModify,
      fromSupplier,
      flowId,
      form,
      id: dimensionType,
      // TODO:
      dependenceListOnLoading,
      dependenceList
    } = this.props
    const {
      optional,
      allowCancelDependence,
      Component,
      multiple,
      selectRange,
      dataType = {},
      type,
      defaultValue,
      isCanViewAllDataWithResultBlank,
      allMatchList,
      hideCode,
      isShowFullPath
    } = fieldInfo
    const { entity = '' } = dataType
    const param = isModify ? { name: entity, flowId } : { name: entity }
    const disabled = isDisable(this.props)
    const { id, checkedId } = formatRefDataId(value)
    const data = {
      id: id ? id : checkedId,
      placeholder,
      disabled,
      optional: optional,
      onChange: this.handleOnChange,
      Component,
      multiple,
      getExpenseStandardItemsLength,
      allMatchList,
      hideCode,
      isShowFullPath,
      isChangePosition: true
    }
    return (
      <RefEditCellInner
        {...this.props}
        submitterId={submitterId?.id}
        fromSupplier={fromSupplier}
        data={data}
        allowCancelDependence={allowCancelDependence}
        dimensionList={dimensionList}
        param={param}
        onlyLeafCanBeSelected={'all' !== selectRange}
        onDimensionChange={this.handleDimensionList}
        isDependence={isDependence}
        dependenceList={dependenceList}
        dimensionType={dimensionType}
        type={type}
        isCanViewAllDataWithResultBlank={isCanViewAllDataWithResultBlank}
        defaultValue={defaultValue}
        dependenceListOnLoading={dependenceListOnLoading}
        form={form}
        field={fieldInfo}
      />
    )
  }
}

export default RefEditCell
