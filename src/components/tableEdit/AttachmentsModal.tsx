import { But<PERSON>, Icon } from 'antd'
import React, { Component } from 'react'
import styles from './MoneyModal.module.less'
import AttachmentWrapper from '../../elements/puppet/attachment'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
const download = api.invokeServiceAsLazyValue('@bills:file:download')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')
const Btn: any = Button
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
class AttachmentsModal extends Component<any, any> {
  constructor(props) {
    super(props)
    const { value = [] } = props?.params
    this.state = {
      uploaderFileList: [],
      successFileList: fnFormatAttachment(value)
    }
  }

  handleFinish = fileList => {
    let {
      params: {
        // onChange,
        bus
      }
    } = this.props
    this.setState({ uploaderFileList: [] })
    this.setState({ successFileList: fileList })
    // onChange && onChange(fileList)
    if (bus?.has('savebtn:state:change')) {
      bus?.emit('savebtn:state:change', { disabled: false })
    }
  }

  handleChange = uploaderFileList => {
    const {
      params: { bus }
    } = this.props
    if (bus?.has('savebtn:state:change')) {
      bus?.emit('savebtn:state:change', { disabled: true })
    }
    this.setState({ uploaderFileList })
  }

  handleError = error => {
    const {
      params: { bus }
    } = this.props
    if (bus?.has('savebtn:state:change')) {
      bus?.emit('savebtn:state:change', { disabled: false })
    }
  }

  handleRemoveAttachment = line => {
    const { successFileList } = this.state
    let fileList = fnFormatAttachment(successFileList)
    let cloneList = fileList.slice(0)
    let imgIndex = cloneList.findIndex(v => v.fileId === line.fileId)
    cloneList.splice(imgIndex, 1)
    this.setState({ successFileList: cloneList })
  }

  handleFileDownload = line => {
    download()(line)
  }

  handleFilePreview = line => {
    const {
      params: { value }
    } = this.props
    preview()({ value, line })
  }

  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  handleModalSave = () => {
    const { successFileList } = this.state
    const { layer } = this.props
    layer.emitOk(successFileList)
  }
  render() {
    const {
      params: { field, title }
    } = this.props
    let { uploaderFileList, successFileList } = this.state
    return (
      <div className={styles['modal-money-wrapper']}>
        <div className="modal-header modal-money-header">
          <div className="flex">{title}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-money-content">
          <AttachmentWrapper
            value={successFileList}
            field={field}
            uploaderFileList={uploaderFileList}
            handleChange={this.handleChange}
            handleFinish={this.handleFinish}
            handleRemoveAttachment={this.handleRemoveAttachment}
            onFileDownload={this.handleFileDownload}
            onFilePreview={this.handleFilePreview}
            onError={this.handleError}
            canSelectDP={true}
            suffixesPath="BILL"
            suffixesFiled={field?.name || field?.type}
          />
        </div>
        <div className="modal-footer modal-money-footer">
          <Btn className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Btn>
          <Btn type="primary" className="btn-ml ml-6" onClick={this.handleModalSave}>
            {i18n.get('确  定')}
          </Btn>
        </div>
      </div>
    )
  }
}

export default AttachmentsModal
