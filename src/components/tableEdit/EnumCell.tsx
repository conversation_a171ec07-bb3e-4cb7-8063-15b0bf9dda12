import React, { useEffect, useState } from 'react'
import { formatLang } from '../../lib/lib-util'
import styles from './Popover.module.less'
import { isString } from '@ekuaibao/helpers'
import { find, split } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { Tooltip } from '@hose/eui'
const EnumCell = ({ text, entity }) => {
  const [value, setValue] = useState(text)
  useEffect(() => {

    const entities = split(entity, '.')
    const code = entities ? entities[2] : undefined
    if (code) {
      api.invokeService('@common:get:enumitems', code).then(data => {
        const value = find(data.items, { code: text?.id || text }) || find(data.items, { id: text?.id || text })
        if (value) {
          setValue(value)
        }
      })
    }

  }, [])
  if (value && value[formatLang()]) {
    return <div className={styles.tableReadonlyWidth}>
      <Tooltip title={value[formatLang()]}>{value[formatLang()]}</Tooltip>
    </div>
  }
  return <div className={styles.tableReadonlyWidth}>{value && value[formatLang()] ? value[formatLang()] : i18n.get('无')}</div>
}

export default EnumCell