import React, { Component, createContext } from 'react'
import { Table, Popconfirm, Form, Pagination } from 'antd'
import { FormComponentProps } from 'antd/es/form'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './DataLinkEditTable.module.less'
import { get, includes, set } from 'lodash'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import EKBIcon from '../../elements/ekbIcon'
import { ENUM_TYPES } from '../consts'
import { formatCellValue, getShow } from './utils'
import DataLinksCell from './DataLinksCell'
import RefCell from './RefCell'
import AttachmentsCell from './AttachmentsCell'
import SimpleCell from './SimpleCell'
import CityCell from './CityCell'
import SwitchCell from './SwitchCell'
import MoneyCell from './MoneyCell'
import DateRangeCell from './DateRangeCell'
import DateCell from './DateCell'
import ListRefOrganizationStaffCell from './ListRefOrganizationStaffCell'
import RefOrganizationStaffCell from './RefOrganizationStaffCell'
import PayeeInfoCell from './PayeeInfoCell'
import EnumCell from './EnumCell'
import SimpleEditCell from './SimpleEditCell'
import NumberEditCell from './NumberEditCell'
import SwitchEditCell from './SwitchEditCell'
import DateEditCell from './DateEditCell'
import DateRangeEditCell from './DateRangeEditCell'
import EnumEditCell from './EnumEditCell'
import CityEditCell from './CityEditCell'
import PayeeInfoEditCell from './PayeeInfoEditCell'
import RefOrganizationStaffEditCell from './RefOrganizationStaffEditCell'
import ListRefOrganizationStaffEditCell from './ListRefOrganizationStaffEditCell'
import RefDepartmentEditCell from './RefDepartmentEditCell'
import RefEditCell from './RefEditCell'
import DataLinkEditCell from './DataLinkEditCell'
import DataLinkListEditCell from './DataLinkListEditCell'
import MoneyEditCell from './MoneyEditCell'
import AttachmentsEditCell from './AttachmentsEditCell'
import NumberCell from './NumberCell'
import MessageCenter from '@ekuaibao/messagecenter'
import FeetypeCell from './FeetypeCell'
import FeetypeEditCell from './FeetypeEditCell'
import { inject } from '@ekuaibao/react-ioc'
import { DetailTableVm } from '../../elements/puppet/details/TableMVC/DetailTableVm'
import InvoiceEditCell from './InvoiceEditCell'
import { observer } from 'mobx-react'
import InvoiceCell from './InvoiceCell'
import ListDimensionCell from './ListDimensionCell'
import { toJS } from 'mobx'
import ListDimensionEditCell from './ListDimensionEditCell'
import ApportionsEditCell from './ApportionsEditCell'
import ApportionsCell from './ApportionsCell'
import { uuid } from '@ekuaibao/helpers'
import DataLinkEditableCell from './DataLinkEditableCell'
import DataLinkEditableEditCell from './DataLinkEditableEditCell'
import CurrencyCell from './CurrencyCell'
import CurrencyEditCell from './CurrencyEditCell'
import { app as api } from '@ekuaibao/whispered'
import SelectSearchCell from './SelectSearchCell'
import SelectSearchEditCell from './SelectSearchEditCell'
import { showMessage } from '@ekuaibao/show-util'
import MutilDimensionListSearchEditCell from './MutilDimensionListSearchEditCell'
import MutilDimensionListSearchCell from './MutilDimensionListSearchCell'
import { formatFeeTypeList } from '../../elements/puppet/details/FormatDateUtils'

interface IProps extends FormComponentProps {
  isEdit?: boolean
  field?: any
  dataSource?: any[]
  onCreate?: () => void
  onGetData?: (data: any) => void
  editing?: boolean
  authStaffStaffMap?: any
  noRootPathMap?: any
  bus?: any
  columns?: any[]
  components?: any[]
  dataIndexPrefix?: string //每个column 的 dataIndex前缀
  noTransColumns?: boolean
  canCreate?: boolean
  indexKey?: string //序号key
  handleChange: (data: any[]) => void //更新选中
  billSpecification: any //单据模版
  currentNode: any
  onLineClick: (line: any, idx: number) => void
  feeChangeInfo: {}
}

interface IState {
  dataSource?: any[]
  editingKey?: string | number
}
const EditableContext = createContext(null)
class EditableCell extends Component<IProps> {
  renderCellItem = (props: any) => {
    const {
      field,
      field: { type, referenceData, name, behaviour, showType },
      record,
      vm
    } = props
    const newField = record?.rowManager?.template?.find((item: any) => item.field && item.field === field.field)
    if (!newField && type !== 'feeTypeId') {
      return <div style={{ width: 150 }} />
    }
    const hiddenFields = vm.getRecordHiddenFields(record)
    const authStaffStaffMap = api?.getState()['@common']?.authStaffStaffMap
    const newProps = { ...props, field: newField || field, authStaffStaffMap: authStaffStaffMap || {}, hiddenFields }
    if (type === 'textarea' || type === 'text') {
      return <SimpleEditCell {...newProps} />
    } else if (type === 'number') {
      return <NumberEditCell {...newProps} />
    } else if (type === 'switcher') {
      return <SwitchEditCell {...newProps} />
    } else if (type === 'date') {
      return <DateEditCell {...newProps} />
    } else if (type === 'dateRange') {
      return <DateRangeEditCell {...newProps} />
    } else if (includes(ENUM_TYPES, type)) {
      return <EnumEditCell {...newProps} />
    } else if (type === 'city') {
      return <CityEditCell {...newProps} />
    } else if (type === 'payeeInfo') {
      return <PayeeInfoEditCell {...newProps} />
    } else if (type === 'feeTypeId') {
      return <FeetypeEditCell {...newProps} />
    } else if (type === 'invoice') {
      return <InvoiceEditCell {...newProps} />
    } else if (type === 'apportions') {
      return <ApportionsEditCell {...newProps} />
    } else if (type === 'ref:organization.Staff') {
      return <RefOrganizationStaffEditCell {...newProps} />
    } else if (type === 'list:ref:organization.Staff') {
      return <ListRefOrganizationStaffEditCell {...newProps} />
    } else if (type === 'ref:organization.Department') {
      return <RefDepartmentEditCell {...newProps} />
    } else if (type === 'ref:basedata.Enum.currency') {
      //多币种
      return <CurrencyEditCell {...newProps} />
    } else if (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      type !== 'ref:organization.Department' &&
      type !== 'ref:basedata.Enum.currency' &&
      !includes(ENUM_TYPES, type)
    ) {
      return <RefEditCell {...newProps} />
    } else if (type === 'dataLink') {
      return <DataLinkEditCell {...newProps} />
    } else if ((type === 'dataLinkEdits' && referenceData.type !== 'TRIP' && showType === 'FIELD' && behaviour === 'INSERT') || name === 'budgetAdjustDetails') {
      //业务对象写入字段
      return <DataLinkEditableEditCell {...newProps} />
    } else if (type === 'dataLinks') {
      return <DataLinkListEditCell {...newProps} />
    } else if (type === 'money') {
      return <MoneyEditCell {...newProps} />
    } else if (type === 'attachments' || type === 'aiAttachments') {
      return <AttachmentsEditCell {...newProps} />
    } else if (type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select')) {
      //档案关系多选
      return <ListDimensionEditCell {...newProps} />
    } else if (type === 'select_search') {
      return <SelectSearchEditCell {...newProps} />
    } else if (type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select_search')) {
      // 自定义档案多选搜索
      return <MutilDimensionListSearchEditCell {...newProps} />
    } else {
      return (
        <div style={{ width: 100 }}>
          {i18n.get('暂不支持该类型，请使用其他展示方式编辑')}
        </div>
      )
    }
  }

  renderCell = ({ getFieldDecorator }) => {
    const { editing, children, ...restProps } = this.props
    return <td {...restProps}>{editing ? this.renderCellItem({ getFieldDecorator, ...this.props }) : children}</td>
  }

  render() {
    return <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
  }
}

@EnhanceConnect(state => ({
  authStaffStaffMap: state['@common'].authStaffStaffMap,
  noRootPathMap: state['@common'].department.noRootPathMap,
  feeChangeInfo: state['@bills'].feeChangeInfo
}))
@observer
class EditTableWrapper extends Component<IProps, IState> {
  @inject(DetailTableVm.NAME) vm?: DetailTableVm

  constructor(props) {
    super(props)
    this.state = {
      editingKey: ''
    }
  }
  componentWillMount(): void {
    const { bus = new MessageCenter() } = this.props
    bus.watch('get:edit:table:values', this.handleGetEditTableValues)
  }

  componentWillUnmount(): void {
    let { bus = new MessageCenter() } = this.props
    bus.un('get:edit:table:values', this.handleGetEditTableValues)
  }

  componentDidMount(): void {
    this.vm.form = this?.props?.form
  }
  handleGetEditTableValues = () => {
    return this.vm?.tableDataSource || []
  }


  fnCheckConfig = (newValue, record) => {
    let next = true
    const components =  record.rowManager.template
    const feeTypeForm = newValue?.feeTypeForm
    const invoices = feeTypeForm?.invoiceForm?.invoices || []
    if (invoices.length) {
      //明细模板配置了超额项的文字字段集合
      let textTemplate = []
      components.forEach(t => {
        const type = get(t, 'dataType.elemType.type') || get(t, 'dataType.type')
        const amountChecked = get(t, 'exceedConfigs.amountChecked', false)
        const invoiceAmount = get(t, 'exceedConfigs.invoiceAmount', 0)
        if (type === 'text' && amountChecked) {
          let obj = {
            field: t.field,
            label: t.label,
            invoiceAmount: invoiceAmount
          }
          textTemplate.push(obj)
        }
      })
      if (textTemplate.length > 0) {
        let hasExceedComErr = []
        let amountArr = []
        invoices.forEach(val => {
          const {
            master: { form }
          } = val
          let money = form?.E_system_发票主体_价税合计?.standard
          amountArr.push(money)
        })
        textTemplate.forEach(item => {
          let invoiceAmount = item.invoiceAmount
          let label = item.label
          let hasExceed = false
          amountArr.forEach(num => {
            if (Number(num) > Number(invoiceAmount)) {
              hasExceed = true
            }
          })
          //某个发票面额值大于相应文本超额值
          if (
            hasExceed &&
            (feeTypeForm[item.field] === '' || feeTypeForm[item.field] === null || feeTypeForm[item.field] === undefined)
          ) {
            hasExceedComErr.push(`有发票金额大于${invoiceAmount}，${label}不能为空`)
          }
        })
        if (hasExceedComErr.length > 0) {
          showMessage.error(i18n.get(hasExceedComErr[0]))
          next = false
        }
      }
    }
    return next
  }

  handleSave(form: any, key: number | string, record: any) {
    const { dataIndexPrefix } = this.props
    form.validateFieldsAndScroll((error: any, row: any) => {
      if (error) {
        const obj = error['feeTypeForm'] || {}
        const keys = Object.keys(obj)
        record?.rowManager?.onError(keys)
        return
      }
      const newValue = formatCellValue(row, record?.rowManager?.template || [], dataIndexPrefix)
      const oldData = this.vm.tableDataSource.find(item => key === item[this.props.indexKey || 'index'])
      const idKey = `${dataIndexPrefix}detailId`
      const detailId = get(oldData, idKey) || uuid(14)
      set(newValue, idKey, detailId)
      const next = this.fnCheckConfig(newValue, record)
      if (!next) {
        return
      }
      this.vm.TM.saveRow(JSON.parse(JSON.stringify(newValue)), key)
    })
  }

  handleCancel = () => {
    this.vm.TM.cancelRow(this.vm.editingKey)
  }

  handleEdit = key => {
    if (this.vm.isEdit) {
      return
    }
    this.setState({ editingKey: key })
    this.vm.editingKey = key
    this?.vm?.TM?.editRow(key)
  }

  handleDelete = (key, deleteDetail) => {
    this?.vm?.TM?.deleteRow(this.props.indexKey, key, deleteDetail)
  }
  getColumnWidth = (index: number) => {
    if (!this.vm.tableDataSource.length || index !== this.vm.tableComponents.length - 1) {
      return 150
    } else {
      return undefined
    }
  }
  getColumns = () => {
    const { authStaffStaffMap, noTransColumns, isEdit, dataIndexPrefix = '', bus } = this.props
    if (noTransColumns) {
      return this.vm.tableComponents
    }
    const columns =
      this.vm.tableComponents?.map((item, index) => {
        const { type, field, referenceData, name, dataType, showType, behaviour } = item
        return {
          title: fnGetFieldLabel(item),
          dataIndex: `${dataIndexPrefix}${field}`,
          key: `${dataIndexPrefix}${field}`,
          field: item,
          width: this.getColumnWidth(index),
          editable: true,
          render: (text, record) => {
            const newField = record?.rowManager?.template?.find(item => item.field && item.field === field)
            const hiddenFields = this.vm.getRecordHiddenFields(record)
            const show = getShow(newField, record?.rowManager?.validateError, hiddenFields)
            if ((!newField && type !== 'feeTypeId') || !show) {
              return <div style={{ width: 150 }} />
            }
            if (type === 'textarea' || type === 'text') {
              return <SimpleCell text={text} name={newField?.name} />
            } else if (type === 'number') {
              return <NumberCell text={text} field={newField} />
            } else if (type === 'city') {
              return <CityCell text={text} />
            } else if (type === 'switcher') {
              return <SwitchCell text={text} />
            } else if (type === 'money') {
              return <MoneyCell text={text} />
            } else if (type === 'dateRange') {
              return <DateRangeCell text={text} field={newField} />
            } else if (type === 'date') {
              return <DateCell text={text} field={newField} />
            } else if (type === 'feeTypeId') {
              return <FeetypeCell text={text} />
            } else if (type === 'invoice') {
              return <InvoiceCell vm={this.vm} text={text} bus={bus} field={newField} record={record} />
            } else if (type === 'apportions') {
              return <ApportionsCell vm={this.vm} text={text} bus={bus} field={newField} record={record} />
            } else if (type === 'list:ref:organization.Staff') {
              return <ListRefOrganizationStaffCell text={text} authStaffStaffMap={authStaffStaffMap} />
            } else if (type === 'ref:organization.Staff') {
              return <RefOrganizationStaffCell text={text} />
            } else if (type === 'payeeInfo') {
              return <PayeeInfoCell text={text} />
            } else if (includes(ENUM_TYPES.concat(['ref:basedata.Enum.InvoiceType', 'ref:basedata.Enum.TaxpayerType']), type)) {
              return <EnumCell text={text} entity={get(dataType, 'entity', '')} />
            } else if (type === 'ref:basedata.Enum.currency') {
              //多币种
              return <CurrencyCell text={text} />
            } else if (
              type.startsWith('ref') &&
              type !== 'ref:organization.Staff' &&
              !includes(ENUM_TYPES, type) &&
              type !== 'ref:basedata.Enum.currency'
            ) {
              // 包含部门
              return <RefCell text={text} field={newField} noRootPathMap={this.props?.noRootPathMap} />
            } else if (type === 'dataLink' || type === 'dataLinks') {
              return <DataLinksCell text={text} />
            } else if ((type === 'dataLinkEdits' && referenceData.type !== 'TRIP' && showType === 'FIELD' && behaviour === 'INSERT') || name === 'budgetAdjustDetails') {
              //业务对象写入字段
              return <DataLinkEditableCell vm={this.vm} text={text} bus={bus} field={newField} record={record} />
            } else if (type === 'attachments' || type === 'aiAttachments') {
              return <AttachmentsCell value={text} />
            } else if (type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select')) {
              //档案关系多选
              return <ListDimensionCell value={text} field={newField} />
            } else if (type === 'select_search') {
              return <SelectSearchCell value={text} field={newField} />
            } else if (type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select_search')) {
              // 自定义档案多选搜索
              return <MutilDimensionListSearchCell value={text} field={newField} />
            } else {
              return (
                <div style={{ width: 100 }}>
                  {i18n.get('暂不支持该类型，请使用其他展示方式编辑')}
                </div>
              )
            }
          }
        }
      }) || ([] as any[])
    const length = columns.length
    if (isEdit) {
      columns.push({
        title: i18n.get('操作'),
        dataIndex: 'operation',
        key: 'operation',
        width: 96,
        fixed: length === 0 ? undefined : 'right',
        render: (_text: any, record: any) => {
          const { rowManager } = record
          const editable = this.isEditing(record)
          return editable ? (
            <div
              className="btn-group"
              onClick={(e: any) => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              <EditableContext.Consumer>
                {form => (
                  <EKBIcon
                    className="icon-save icon-style stand-20-icon"
                    style={{ cursor: 'pointer' }}
                    name="#EDico-zf-save"
                    onClick={() => this.handleSave(form, record[this.props.indexKey || 'index'], record)}
                  />
                )}
              </EditableContext.Consumer>
              {rowManager?.isSaved && (
                <Popconfirm title={i18n.get('确定取消?')} onConfirm={() => this.handleCancel()}>
                  <EKBIcon
                    className="icon-cancel icon-style stand-20-icon"
                    name="#EDico-zf-off"
                    style={{ cursor: 'pointer' }}
                  />
                </Popconfirm>
              )}
              {!rowManager?.isSaved && ( //新添加数据，如果不想添加了的话直接直接删除，不会给取消按钮，如果有取消按钮的话会导致所有的值都是空的
                <Popconfirm
                  title={i18n.get('确定删除?')}
                  onConfirm={() => this.handleDelete(record[this.props.indexKey || 'index'], false)}
                >
                  <EKBIcon className="icon-style stand-20-icon" name="#EDico-zf-delete" style={{ cursor: 'pointer' }} />
                </Popconfirm>
              )}
            </div>
          ) : (
            <div
              className="btn-group"
              onClick={(e: any) => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              {record.disabledByState ? null : (
                <>
                  <EKBIcon
                    style={{ cursor: this.vm.isEdit ? 'not-allowed' : 'pointer' }}
                    className="icon-style stand-20-icon"
                    name="#EDico-zf-edit"
                    onClick={() => this.handleEdit(record[this.props.indexKey || 'index'])}
                  />
                  {this.vm.isEdit ? <EKBIcon
                    style={{ cursor: 'not-allowed' }}
                    className="icon-style stand-20-icon"
                    name="#EDico-zf-delete"
                  /> :
                    <Popconfirm
                      title={i18n.get('确定删除?')}
                      onConfirm={() => this.handleDelete(record[this.props.indexKey || 'index'], true)}
                    >
                      <EKBIcon
                        style={{ cursor: this.vm.isEdit ? 'not-allowed' : 'pointer' }}
                        className="icon-style stand-20-icon"
                        name="#EDico-zf-delete"
                      />
                    </Popconfirm>}
                </>
              )}
            </div>
          )
        }
      })
    }
    columns.unshift({
      dataIndex: 'feeTypeId',
      title: i18n.get('费用类型'),
      width: 150,
      editable: true,
      fixed: length === 0 ? undefined : 'left',
      key: 'feeTypeId',
      field: {
        type: 'feeTypeId',
        label: i18n.get('费用类型'),
        placeholder: i18n.get('请选择费用类型'),
        name: 'feeTypeId'
      },
      render: (text: any) => {
        return <FeetypeCell text={text} />
      }
    })

    return columns
  }

  isEditing = (record: any) => record[this.props.indexKey || 'index'] === this.vm.editingKey

  handleOnRowClick = (record: any, idx: number) => {
    const { onLineClick } = this.props
    if (!this.isEditing(record)) {
      if (this.vm.isEdit) {
        showMessage.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
        return
      }
      onLineClick && onLineClick(toJS(record), idx)
    }
  }

  render() {
    const { bus, form, indexKey, handleChange, currentNode, dataIndexPrefix, feeChangeInfo } = this.props
    const dataSource = formatFeeTypeList(toJS(this.vm?.tableDataSource || []), feeChangeInfo, true)
    let columns = this.getColumns()
    columns = columns.map(col => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: (record: any) => ({
          record,
          bus,
          dataIndex: col.dataIndex,
          dataIndexPrefix: dataIndexPrefix,
          title: col.title,
          field: col.field,
          editing: this.isEditing(record),
          vm: this.vm,
          currentNode,
          indexKey,
          form
        })
      }
    })
    const rowSelection = {
      selectedRowKeys: this.vm.selectedRows,
      onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
        this.vm?.TM?.updateSelectData(selectedRowKeys)
        handleChange(selectedRows)
      },
      getCheckboxProps: record => ({
        disabled: !record?.rowManager?.isSaved || this.isEditing(record) //没有保存过或者正在编辑的话就不能被选中，因为数据有可能是空的
      })
    }
    if (!columns.length) {
      return null
    }
    return (
      <div className={`${styles.dataLinkEditTableWrapper}`}>
        <EditableContext.Provider value={form}>
          <Table
            className={styles.dataLinkEditTable}
            components={{
              body: {
                cell: EditableCell
              }
            }}
            onRow={record => {
              return {
                onClick: () => this.handleOnRowClick(record, record.idx) // 点击行
              }
            }}
            rowSelection={this.vm.props?.selectAble ? rowSelection : undefined}
            bordered
            dataSource={dataSource}
            columns={columns}
            pagination={{ pageSize: 20 }}
            size="middle"
            loading={this?.vm?.tableLoading}
            scroll={{ x: 300 }}
          />
        </EditableContext.Provider>
      </div>
    )
  }
}

export default Form.create()(EditTableWrapper)
