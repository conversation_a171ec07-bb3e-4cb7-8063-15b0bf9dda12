import React, { FC } from 'react'
import { Form, DatePicker } from 'antd'
import { required } from '../validator/validator'
import moment from 'moment'
import { formatDateTime, getShowTime } from '../utils/fnPredefine4Date'
import { isDisable } from '../utils/fnDisableComponent'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { get, isArray } from 'lodash'
import getLabel from '../utils/fnGetFieldLabel'
import fnPredefine4DateRange from '../utils/fnPredefine4DateRange'
const { RangePicker } = DatePicker
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  value?: any
  vm: any
  form?: any
  hiddenFields?: string[]
  onChange?: (value: any) => void
}
const DateRangeEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const validatorDate = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  const getInitValue = () => {
    const value = get(record, dataIndex)
    if (value) {
      const start = moment(value?.start)
      const end = moment(value?.end)
      return [start, end]
    }
    const initV = fnPredefine4DateRange(fieldInfo)
    if (initV) {
      const start = moment(initV?.start)
      const end = moment(initV?.end)
      return [start, end]
    }
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorDate, required: !fieldInfo?.optional }],
        initialValue: getInitValue()
      })(<DateRangeEditWrapper {...props} />)}
    </FormItem>
  )
}
const DateRangeEditWrapper: FC<IProps> = props => {
  const { field: fieldInfo, record, value, onChange, vm, form } = props
  const { withTime, isClear = false, dateTimeType } = fieldInfo
  const disabled = isDisable(props)
  const handleOnChange = date => {
    const [start, end] = date
    const newDate = { start: moment(start).valueOf(), end: moment(end).valueOf() }
    onChange?.(newDate)
    updateCellValue(fieldInfo?.field, newDate, record, vm, form)
  }
  const dateTime = formatDateTime(withTime, dateTimeType)
  const dateType = dateTime === 'YYYY-MM' ? 'month' : 'date'
  return (
    <RangePicker
      value={isArray(value) ? value : [moment(value?.start), moment(value?.end)]}
      style={itemInnerStyle}
      format={dateTime}
      allowClear={isClear}
      disabled={disabled}
      size="large"
      onChange={handleOnChange}
      onPanelChange={handleOnChange}
      showTime={getShowTime(withTime, dateTimeType)}
      mode={[dateType, dateType]}
    />
  )
}
export default DateRangeEditCell
