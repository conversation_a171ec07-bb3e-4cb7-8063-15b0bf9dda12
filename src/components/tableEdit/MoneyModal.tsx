import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Icon } from 'antd'
import { cloneDeep, get, isEmpty, isObject } from 'lodash'
import React, { Component } from 'react'
import CurrencyMoney from '../../elements/currency/currency-money'
import { fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { app as api } from '@ekuaibao/whispered'
import styles from './MoneyModal.module.less'
import { moneyStrToStandardCurrencyMoney } from '@ekuaibao/money-math'
import { EnhanceConnect } from '@ekuaibao/store'
import { showMessage } from '@ekuaibao/show-util'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const Btn: any = Button
@EnhanceConnect(state => {
  return {
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    standardCurrency: state['@common'].standardCurrency,
    historyCurrencyInfo: state['@bills'].historyCurrencyInfo,
    allCurrencyRates: state['@common'].allCurrencyRates
  }
})
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
class MoneyModal extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = { data: props.params?.value }
  }

  componentWillReceiveProps(nextProps) {
    const {
      params: { isDetail, value },
      dimentionCurrencyInfo
    } = this.props
    if (isDetail && nextProps?.params?.value && nextProps?.params?.value !== value) {
      this._postEvent(nextProps.value)
    }
    if (dimentionCurrencyInfo?.currency?.id !== nextProps?.dimentionCurrencyInfo?.currency?.id) {
      this.initValue(nextProps?.dimentionCurrencyInfo?.currency)
    }
  }

  moneyStr2StandardMoneyValue = money => {
    const { standardCurrency } = this.props
    return moneyStrToStandardCurrencyMoney(money, standardCurrency)
  }

  _valueChange = ({ isValidator = true, data }) => {
    const {
      params: { getExpenseStandardItemsLength, external }
    } = this.props
    if (!isNaN(Number(data.standard)) && Number(data.standard) !== Number(this.state.data?.standard || 0)) {
      this.setState({ data })
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
    getExpenseStandardItemsLength && this.getExpenseStandardItemsLength(data)
    if (isValidator) return this.setValue(data)
    this._postEvent(data)
  }

  getExpenseStandardItemsLength = data => {
    let {
      params: { getExpenseStandardItemsLength }
    } = this.props
    let { standard = 0 } = data
    let re = new RegExp(`^(-?([1-9]\\d*)|0)(\\.\\d{1,2})?$`)
    if (re.test(standard)) {
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  setValue = val => {
    let {
      params: { field }
    } = this.props
    if (val && typeof val === 'object' && !val.standard && field.editable) {
      //选填的时候如果standard为空后台会报错，所以当standard为空的时候清空数据，但是自动计算的话不能清空所以加一个field.editable条件
      val = undefined
    }
    this._postEvent(val)
    this.handleChange(val)
  }

  handleChange = val => {
    // const { onChange } = this.props.params
    // onChange && onChange(val)
    this.setState({ data: val })
  }

  _postEvent = (val?) => {
    const {
      params: { field, bus, isDetail }
    } = this.props
    if (field.name === 'loanMoney' || field.name === 'payMoney') {
      bus && bus.emit('loanMoney:changed', val)
    }
    // 分摊指定金额 不再是由amount 或者 apportionMoney 驱动
    // if (!!~['amount', 'apportionMoney'].indexOf(field.name)) {
    isDetail && bus && bus.invoke('amount:changed', { field: field.name, amount: val }).catch(e => {})
    // }
  }

  async componentWillMount() {
    let {
      params: {
        bus,
        field: { field: fieldName },
        value
      }
    } = this.props
    !!~['amount', 'loanMoney'].indexOf(fieldName) && bus.on('money:changed:value', this.setValue)
    if (!!~['amount'].indexOf(fieldName)) bus && bus.invoke('amount:changed', value).catch(e => {})
    bus.on('details:change', this.onDetailsChange)
    bus.on('trip:change', this.onDetailsChange)
    bus.on('money:tax:update:value', this.onTaxChange)
    bus.on('detail:amount:change', this.handleDetailAmountChange)
    await Promise.all([
      api.dataLoader('@common.currencyConfig').load(),
      api.invokeService('@currency-manage:get:currency:modifiable'),
    ])
  }

  componentWillUnmount() {
    let {
      params: {
        bus,
        field: { field: fieldName }
      }
    } = this.props
    !!~['amount', 'loanMoney'].indexOf(fieldName) && bus.un('money:changed:value', this.setValue)
    bus.un('details:change', this.onDetailsChange)
    bus.un('trip:change', this.onDetailsChange)
    bus.un('money:tax:update:value', this.onTaxChange)
    bus.un('detail:amount:change', this.handleDetailAmountChange)
  }

  handleDetailAmountChange = amount => {
    const {
      params: { field, isDetail }
    } = this.props
    if (field.field === 'amount' && isDetail) {
      this.handleChange(standardValueMoney(amount))
    }
  }

  componentDidMount() {
    const { dimentionCurrencyInfo } = this.props
    this.initValue(dimentionCurrencyInfo?.currency)
  }

  componentDidUpdate() {
    this.delayTodo && this.delayTodo()
  }

  initValue = currency => {
    const {
      params: { originalValue = {} }
    } = this.props
    const defaultValue = get(this, 'props.field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      const details = originalValue.details || originalValue.trips
      let sumValue = standardValueMoney(0, currency)
      const { standardScale } = sumValue
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          const defVal = isObject(form[defaultValue.value])
            ? form[defaultValue.value].standard
            : form[defaultValue.value]
          if (defVal) {
            sumValue.standard = new Big(sumValue.standard).plus(Number(defVal)).toFixed(standardScale)
          }
        })
      this.handleChange(sumValue)
      this._postEvent()
    }
  }

  delayTodo = null
  onTaxChange = () => {
    const {
      params: { field }
    } = this.props
    const type = get(field, 'defaultValue.type')

    if (type === 'invoiceSum') {
      this.delayTodo = () => {
        this.setValue(undefined)
        this.delayTodo = null
      }
    }
  }

  onDetailsChange = details => {
    const { dimentionCurrencyInfo } = this.props
    const defaultValue = get(this.props, 'field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      let data = standardValueMoney(0, dimentionCurrencyInfo?.currency)
      const { standardScale } = data
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          if (form[defaultValue.value]) {
            data.standard = new Big(data?.standard || 0)
              .plus(Number(form[defaultValue.value]?.standard || 0))
              .toFixed(standardScale)
          }
        })
      this.handleChange(data)
      this.setState({ details })
      this._valueChange({ isValidator: true, data })
    }
  }

  isSelectCurrencyDisable = () => {
    const {
      params: { field, selectCurrencyDisable, foreignCurrencyLoan }
    } = this.props
    const fieldName = field?.field
    return fieldName === 'amount' || fieldName === 'companyRealPay' || !!foreignCurrencyLoan
      ? selectCurrencyDisable
      : false
  }

  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  handleModalSave = () => {
    const { data } = this.state
    const { layer, params } = this.props
    if (!data) {
      showMessage.error(`请填写${params?.title || '金额'}`)
      return
    }
    const max = get(params, 'field.max')
    const min = get(params, 'field.min')
    const standard = data?.standard
    if (standard * 1 > max * 1 || standard * 1 < min * 1) {
      showMessage.error(`金额允许填写范围：${min} ~ ${max}`)
      return
    }
    layer.emitOk(data)
  }
  render() {
    const {
      params: {
        value,
        field,
        autoCalFields,
        form,
        bus,
        currencySelAble = true,
        external,
        isForbid,
        isModify,
        cannotEditAmountField,
        foreignCurrencyLoan,
        foreignNumCode,
        billSpecification = {},
        isManualRepayment = false,
        showAllFeeType,
        template,
        currencySwitch,
        title
      },
      standardCurrency,
      allCurrencyRates,
      dimentionCurrencyInfo,
      historyCurrencyInfo
    } = this.props
    let { field: fieldName } = field
    let placeholder = getPlaceholder(field)
    const { configs = [] } = billSpecification
    const isAuto = fnDefineIsFormula(fieldName, autoCalFields, field)
    placeholder = isAuto && !isEmpty(value) ? i18n.get('自动计算生成') : placeholder
    let disabled = isDisable(this.props.params)
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disabled && cannotEditAmountField) disabled = true
    const writtenOff = configs.find(item => item.ability === 'writtenOff')
    let dimentionCurrency = cloneDeep(dimentionCurrencyInfo)
    const historyCurrencyNumCode = get(historyCurrencyInfo, 'currency')
    const dimentionCurrencyNumCode = get(dimentionCurrencyInfo, 'currency.numCode')
    if (historyCurrencyNumCode && dimentionCurrencyNumCode && historyCurrencyNumCode === dimentionCurrencyNumCode) {
      dimentionCurrency.rates = historyCurrencyInfo.rates
    }
    let allCurrency = cloneDeep(allCurrencyRates)
    if (historyCurrencyNumCode) {
      allCurrency = historyCurrencyInfo.rates
    } else {
      allCurrency = allCurrency.filter(i => i.originalId === standardCurrency?.numCode)
    }
    return (
      <div className={styles['modal-money-wrapper']}>
        <div className="modal-header modal-money-header">
          <div className="flex">{title}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-money-content">
          <CurrencyMoney
            foreignCurrencyLoan={foreignCurrencyLoan}
            isManualRepayment={isManualRepayment}
            foreignNumCode={foreignNumCode}
            writtenOff={writtenOff}
            selectCurrencyDisable={this.isSelectCurrencyDisable()}
            value={value}
            form={form}
            field={field}
            disabled={isAuto || disabled}
            placeholder={placeholder}
            isAuto={isAuto}
            bus={bus}
            valueChange={this._valueChange}
            currencySelAble={currencySelAble}
            external={external}
            isEdit={true}
            isForbid={isForbid}
            template={template}
            currencySwitch={currencySwitch}
            standardCurrency={standardCurrency}
            dimentionCurrency={dimentionCurrency}
            allCurrencyRates={allCurrency}
            showLabel={true}
          />
        </div>
        <div className="modal-footer modal-money-footer">
          <Btn className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Btn>
          <Btn type="primary" className="btn-ml ml-6" onClick={this.handleModalSave}>
            {i18n.get('确  定')}
          </Btn>
        </div>
      </div>
    )
  }
}

export default MoneyModal
