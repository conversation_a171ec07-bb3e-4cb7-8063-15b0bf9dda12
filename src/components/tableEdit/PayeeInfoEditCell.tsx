import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formatPayeeInfoField, formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import CustomInput from './CustomInput'
import PayeeInfoCell from './PayeeInfoCell'
import { get } from 'lodash'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'

const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  // TODO:
  template: any
  hiddenFields?: string[]
}
const PayeeInfoEditCell: FC<IProps> = props => {
  const [multiplePayeesMode, setMultiplePayeesMode] = useState(false)
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, bus, title, hiddenFields } = props
  const initState = () => {
    setMultiplePayeesMode(api.getState('@bills').multiplePayeesMode)
  }
  useEffect(() => {
    initState()
    bus?.on('on:dependence:change', handleDependenceChange)
    return () => {
      bus?.un('on:dependence:change', handleDependenceChange)
    }
  }, [])

  if (!fieldInfo) {
    return null
  }
  
  const label = fnGetFieldLabel(fieldInfo)
  const { dependence, source } = fieldInfo
  const validator = (rule, value, callback) => {
    if (multiplePayeesMode) {
      if (!value?.id && source && source === 'dataLink') {
        return callback(i18n.get('not-empty', { label: i18n.get(title) }))
      }
      return callback(undefined)
    }
    callback(required(fieldInfo, value))
  }

  const handleDependenceChange = () => {
    // TODO:
  }

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(
        <InputWrapper
          {...props}
          isDependence={dependence && !!dependence.length}
          dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]}
        />
      )}
    </FormItem>
  )
}

class InputWrapper extends Component<any> {
  handleClick = () => {
    const { field: fieldInfo, bus, template, isDependence, dependenceList, value } = this.props
    const { dependence, name } = fieldInfo
    let list
    const isFeeDetailPayeeId = name === 'feeDetailPayeeId'
    if (isDependence) {
      if (isFeeDetailPayeeId) {
        const depFieldNames = dependence.map(el => el.dependenceId)
        const hasDepField = !!template.find(el => depFieldNames.includes(el.name))
        if (hasDepField) {
          list = dependenceList
        }
      } else {
        list = dependenceList
      }
    }
    bus
      .invoke('element:ref:select:payee', value, list, isFeeDetailPayeeId, {
        allowCancelDependence: fieldInfo?.allowCancelDependence
      })
      .then(data => {
        if (data) {
          this.handleChange(data)
        }
      })
  }
  handleChange = data => {
    const { onChange, field, record, vm, form } = this.props
    onChange && onChange(data)
    updateCellValue(field?.field, data, record, vm, form)
  }

  render() {
    const {
      value,
      form: { getFieldError },
      dataIndex
    } = this.props
    const error = getFieldError(dataIndex)
    const { fakeInputDisabled, placeholderValue, payeeValue } = formatPayeeInfoField(this.props, value)
    return (
      <CustomInput placeholder={placeholderValue} onClick={this.handleClick} error={error} disabled={fakeInputDisabled}>
        <PayeeInfoCell text={payeeValue} showEmpty />
      </CustomInput>
    )
  }
}

export default PayeeInfoEditCell
