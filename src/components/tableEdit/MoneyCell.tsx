import React from 'react'
import styles from './Popover.module.less'
import { getMoneyInfo } from './utils'
import { Tooltip } from '@hose/eui'

export function getMoneyText(value: any) {
  const foreign = getMoneyInfo(value, 'foreign')
  const standard = getMoneyInfo(value, 'standard') || '0.00'
  const budget = getMoneyInfo(value, 'budget')
  return (
    <div>
      {foreign && (
        <div>
          {i18n.get('原币')}：{foreign}
        </div>
      )}
      {foreign && (
        <div>
          {i18n.get('核算汇率')}：{value?.rate}
        </div>
      )}
      <div>
        {i18n.get('本位币')}：{standard}
      </div>
      {budget && (
        <div>
          {i18n.get('预算币')}：{budget}
        </div>
      )}
      {budget && (
        <div>
          {i18n.get('预算汇率')}：{value?.budgetRate}
        </div>
      )}
    </div>
  )
}

const getInfo = (value: any) => {
  if (value?.foreign) {
    return `${value?.foreignStrCode} ${value?.foreign}`
  } else if (value?.standard) {
    return `${value?.standardStrCode} ${value?.standard}`
  } else {
    return i18n.get('无')
  }
}

const MoneyCell = ({ text }) => {
  return (
    <div className={styles.tableReadonlyWidth}>
      <Tooltip title={getMoneyText(text)}>{getInfo(text)}</Tooltip>
    </div>
  )
}

export default MoneyCell
