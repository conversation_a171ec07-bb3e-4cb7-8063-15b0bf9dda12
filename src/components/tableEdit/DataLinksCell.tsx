import { app as api } from '@ekuaibao/whispered'
import React, { useEffect, useState } from 'react'
import { isArray, get } from 'lodash'
import { getValue } from '../../elements/dataLink-card/utils/dataLinkUtils'
import { Popover } from 'antd'
import styles from './Popover.module.less'
import { toJS } from 'mobx'
const DataLinksCell = ({ text }) => {
  const [data, setData] = useState([])
  useEffect(() => {
    const jsText = toJS(text)
    const  newText = isArray(jsText) ? jsText : [jsText]
    const dataLinkIds = newText.map(i => i?.id || i?.data?.dataLink?.id || i).filter(i => i)
    if (dataLinkIds.length) {
      api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds }).then((values: any) => {
        const data = values.items || []
        data.forEach((i: any) => {
          i.id = i.data.dataLink.id
          const titleField = get(i, 'template.content.expansion.title.fields')
          const data = get(i, 'data')
          const name = getValue(titleField, data)
          i.name = name
        })
        setData(data)
      })
    } else {
      setData(newText)
    }
  }, [text])

  if (data.length === 1) {
    return <div className={styles.tableReadonlyWidth}>{data.map(i => i?.name)}</div>
  } else if (data.length === 0) {
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  } else {
    const content = (
      <div className={styles.popoverContainer}>
        {data.map((item, index) => (
          <div className={styles.item} key={index}>
            {item?.name}
          </div>
        ))}
      </div>
    )
    return (
      <div className={styles.tableReadonlyWidth}>
        <Popover placement="topLeft" content={content}>
          {data.map(i => i?.name).join(i18n.get('、'))}
        </Popover>
      </div>
    )
  }
}

export default DataLinksCell
