import React from 'react'
import { timeConvert } from '../utils/fnPredefine4Date'
import styles from './Popover.module.less'
import { Tooltip } from '@hose/eui'
const DateRangeCell = ({ text, field }) => {
  const { withTime, dateTimeType } = field
  // @ts-ignore
  const { start, end } = text || {}
  const str = timeConvert(withTime, dateTimeType, start, true) + ' ~ ' + timeConvert(withTime, dateTimeType, end, true) 
  return (
    <div className={styles.tableReadonlyWidth}>
      <Tooltip title={str}>{str || i18n.get('无')}</Tooltip>
    </div>
  )
}

export default DateRangeCell
