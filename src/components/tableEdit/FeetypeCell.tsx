import React from 'react'
import styles from './Popover.module.less'
import { Tooltip } from '@hose/eui'
const FeetypeCell = ({ text }) => {
  const textValue = text?.fullname || text?.name || i18n.get('无')
  return <div className={styles.tableReadonlyWidth}>
    <Tooltip title={textValue}>{textValue}{text.actionValue && <span className="color-red">({text.actionValue})</span>}</Tooltip>
  </div>
}

export default FeetypeCell
