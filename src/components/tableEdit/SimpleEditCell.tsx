import React, { FC } from 'react'
import { Input, Form } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { checkedAddress, checkedEmail, required } from '../validator/validator'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
const FormItem = Form.Item
import { get } from 'lodash'
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  vm:any
  value?: any
  form:any
  onChange?: (value: any) => void
  hiddenFields?: string[]
}
const SimpleEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, title, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const { maxLength, minLength, name, editable, validator } = fieldInfo
  const label = getLabel(fieldInfo)
  const validatorInput = (rule, value, callback) => {
    if (!editable && value && value.length > 1000) {
      return callback(i18n.get('cannot-exceed-words', { label: title, maxLength: 1000, length: value.length }))
    }
    if (editable && value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label: title, maxLength , length: value.length}))
    }
    if (editable && value && value.length < minLength) {
      return callback(i18n.get('cannot-less-words', { label: title, minLength }))
    }
    if (name === 'receiver_email' || name === 'email') {
      return callback(checkedEmail(value))
    }
    if (name === 'receiver_address') {
      return callback(checkedAddress(value))
    }
    if (!!value && !!validator?.(value)) {
      return callback(validator(value))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorInput, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(<InputWrapper {...props} />)}
    </FormItem>
  )
}
const InputWrapper: FC<IProps> = props => {
  const { record, field: fieldInfo, value, onChange, vm ,form} = props
  if (!fieldInfo) {
    return null
  }
  const handleOnChange = e => {
    onChange && onChange(e.target.value)
    updateCellValue(fieldInfo?.field, e.target.value, record, vm,form)
  }
  const { optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const disabled = isDisable(props)
  return (
    <Input
      value={value}
      onChange={handleOnChange}
      style={itemInnerStyle}
      placeholder={placeholder}
      disabled={disabled}
    />
  )
}
export default SimpleEditCell
