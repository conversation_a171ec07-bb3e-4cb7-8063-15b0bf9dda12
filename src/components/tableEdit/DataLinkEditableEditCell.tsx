import React, { FC } from 'react'
import { dataLinkEditValidator, required } from '../validator/validator'
import { Form } from 'antd'
import { app } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { fnGetValidExternal, formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import CustomInput from './CustomInput'
import { canModifyApproveMoney } from '../../plugins/bills/util/parse'
import { toJS } from 'mobx'
const FormItem = Form.Item
const BEHAVIOUR = 'UPDATE'
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  form: any
  currentNode: any
  hiddenFields?: string[]
}
const DataLinkEditableEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const { optional, behaviour = 'INSERT', showType } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const validator = (rule, value, callback) => {
    if (behaviour !== BEHAVIOUR && showType !== 'TABLE') {
      return dataLinkEditValidator(fieldInfo, rule, value, callback, true)
    }
    return callback(required(fieldInfo, value))
  }

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(<DataLinkEditableWrapper {...props} template={record?.rowManager?.template || []} />)}
    </FormItem>
  )
}

export default DataLinkEditableEditCell

const DataLinkEditableWrapper: FC<any> = props => {
  const {
    dataIndex,
    bus,
    field: fieldInfo,
    vm,
    value,
    field,
    record,
    form,
    onChange,
    form: { getFieldError }
  } = props
  if (!fieldInfo) {
    return null
  }
  const placeholder = getPlaceholder(fieldInfo)
  const handleClick = () => {
    const params = {
      bus,
      value: toJS(value),
      record,
      form,
      external: fnGetValidExternal(vm?.props?.external, record),
      billSpecification: vm.billSpecification,
      isForbid: vm?.props?.isForbid,
      field: JSON.parse(JSON.stringify(field)),
      detailId: record?.feeTypeForm?.detailId,
      submitterId: vm?.submitterId,
      feeDetailId: vm?.props?.feeDetailId,
      showAllFeeType: vm?.props?.showAllFeeType,
      billTemplate: vm?.props?.billTemplate,
      template: record?.rowManager?.template,
      isModify: vm?.props?.isModify,
      billState: vm?.billState,
      flowId: vm?.flowId,
      feeTypes: vm?.feeTypes,
      modifyApproveMoney: canModifyApproveMoney(vm.billSpecification),
      isRecordExpends: vm?.props?.isRecordExpends,
      isQuickExpends: vm?.props?.isQuickExpends,
      riskData: toJS(vm?.props?.riskData),
      details: toJS(vm?.tableDataSource),
      isDetail: true
    }
    app.open('@bills:DataLinkEditableModal', params).then((res: any) => {
      onChange && onChange(res)
      updateCellValue(fieldInfo?.field, res, record, vm, form)
    })
  }
  const error = getFieldError(dataIndex)
  const name = field?.referenceData?.name

  return (
    <CustomInput placeholder={placeholder} onClick={handleClick} error={error} disabled={false}>
      <a>{name}</a>
    </CustomInput>
  )
}
