import React, { Component, FC } from 'react'
import { Form } from 'antd'
import { required } from '../validator/validator'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { formItemStyle, updateCellValue, getFormItemLabel } from './utils'
import { get } from 'lodash'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import SelectSearchComp from '../dynamic/SelectSearchComp'
const FormItem = Form.Item
interface IProps {
    getFieldDecorator: any
    dataIndex: string
    title: string
    record: any
    field: any
    value?: any
    vm: any
    lastChoice?: any
    onChange?: (value: any) => void
    hiddenFields?: string[]
}
const SelectSearchEditCell: FC<IProps> = (props) => {
    const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
    const label = fnGetFieldLabel(fieldInfo)
    const getInitValue = () => {
        const { value = get(record, dataIndex), lastChoice } = props
        if (!value) {
            const constValue = constantValue(fieldInfo)
            if (constValue) {
                return [constValue]
            }
            const lastVal = lastSelectValue(fieldInfo, lastChoice)
            if (lastVal) {
                return lastVal.split(',')
            }
        }
        return value
    }

    const validator = (rule, value, callback) => {
        callback(required(fieldInfo, value))
    }
    return (
        <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
            {getFieldDecorator(dataIndex, {
                rules: [{ validator: validator, required: !fieldInfo?.optional }],
                initialValue: getInitValue()
            })(<SelectSearchEditCellList {...props} />)}
        </FormItem>
    );
};

export default SelectSearchEditCell;


class SelectSearchEditCellList extends Component<any, any> {
    constructor(props) {
        super(props)
    }
    handleChange = (data) => {
        const { onChange, field, record, vm, form } = this.props
        onChange && onChange(data)
        updateCellValue(field?.field, data, record, vm, form)
    }
    render() {
        return (
            <SelectSearchComp {...this.props} onChange={this.handleChange} />
        )
    }
}
