import React from 'react';
import styles from './Popover.module.less'
const SelectSearchCell = ({ value, field }) => {
    const hideCode = field?.hideCode
    const name = value?.name
    const active = value?.active
    const code = value?.code
    return (
        <div className={styles.tableReadonlyWidth}>
            {name ? <span>
                {name}
                {active === false && i18n.get('(已停用)')}
                {hideCode ? `(${code})` : null}
            </span> : i18n.get('无')}
        </div>
    );
};

export default SelectSearchCell;