import React, { Component, FC } from 'react'
import { Form, Input } from 'antd'
import {
  formatListRefOrganizationStaffField,
  formItemStyle,
  itemInnerStyle,
  getFormItemLabel,
  updateCellValue
} from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { castArray, get } from 'lodash'
import { Modal } from 'antd'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { clearableTripDataLink } from '../../lib/lib-util'
import { toJS } from 'mobx'
import { Popover } from '@hose/eui'
import styles from './Popover.module.less'

const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  hiddenFields?: string[]
}
const ListRefOrganizationStaffEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, title, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const { editable, allowExternalStaff, dependence } = fieldInfo
  const placeholder = getPlaceholder(fieldInfo)
  const disabled = !isDisable(props)
  const validator = (rule, value, callback) => {
    if (value && editable && !allowExternalStaff) {
      if (value.find(o => o.external)) {
        const externalRoot = api.getState()['@common'].externalDepartment?.data
        callback(
          i18n.get(`{label}的取值规则不允许选择`, { label: title }) +
            (externalRoot ? externalRoot[0].name : i18n.get('外部人员'))
        )
      }
    }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(
        <InputWrapper
          {...props}
          disabled={!disabled}
          placeholder={placeholder}
          dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]}
          isDependence={dependence && !!dependence.length}
        />
      )}
    </FormItem>
  )
}

class InputWrapper extends Component<any> {
  handleClick = async () => {
    let {
      bus,
      value = [],
      field: fieldInfo,
      travelManagementConfig,
      form,
      submitterId,
      dependenceList,
      isDependence
    } = this.props
    const { allowExternalStaff, allowCancelDependence, valueRangeFilter } = fieldInfo
    const newValue = castArray(toJS(value)).map(item => (typeof item === 'string' ? item : item?.id))
    if (isDependence && !dependenceList.length) {
      if (allowCancelDependence) {
        const shouldSelectAllUser = await new Promise((resolve, reject) => {
          Modal.confirm({
            title: i18n.get('您没有可以选择的员工'),
            okText: i18n.get('知道了'),
            cancelText: i18n.get('选择所有员工'),
            onCancel: () => resolve(true),
            onOk: () => resolve(false)
          })
        })
        if (!shouldSelectAllUser) {
          return
        }
        // 取消依赖
        dependenceList = undefined
      } else {
        return showModal.error({
          title: i18n.get('您没有可以选择的员工')
        })
      }
    }
    let staffRangeRule = false
    if (valueRangeFilter && valueRangeFilter !== 'false') {
      //后端定义这个“false”表示取消限制
      staffRangeRule = valueRangeFilter
    }
    if (allowExternalStaff) {
      let checkedList = []
      const internalStaffIds = value.filter(staff => !staff.external).map(line => line.id)
      checkedList.push({
        type: 'department-member',
        multiple: true,
        checkedKeys: internalStaffIds
      })
      const externalStaffIds = value.filter(staff => staff.external).map(line => line.id)
      checkedList.push({
        type: 'external',
        multiple: true,
        checkedKeys: externalStaffIds
      })
      bus?.invoke('element:ref:select:staffs', { checkedList, allowExternalStaff, staffRangeRule }).then(data => {
        let staffs = []
        data.checkedList.forEach(checked => (staffs = staffs.concat(checked.checkedData || [])))
        this.handleChange(staffs)
        bus?.emit('set:tripdatalink:traveler:change')
      })
    } else {
      bus
        ?.invoke('element:ref:select:staffs', {
          checkedKeys: newValue,
          multiple: true,
          dataSource: isDependence ? dependenceList : undefined,
          allowExternalStaff,
          staffRangeRule
        })
        .then(async data => {
          const checkStaffs = () => {
            if (data.length > 100) {
              showMessage.error(fieldInfo.label + i18n.get('不能超过100人'))
              return false
            }
            this.handleChange(data)
            return true
          }
          bus?.emit('set:tripdatalink:traveler:change')
          // 存在[出行人][行程规划]且行程管理配置黑名单时，切换出行人时需校验提示
          if (
            fieldInfo.field === 'travelers' &&
            travelManagementConfig?.contextDetail?.length &&
            form.getFieldValue('u_行程规划')?.length // @i18n-ignore
          ) {
            const { clearable, travelerId } = clearableTripDataLink(data, value, submitterId)
            if (clearable) {
              showModal.confirm({
                title: i18n.get('提示'),
                content: i18n.get('根据行程配置，切换出行人时，您的行程明细将被清空，是否继续？'),
                cancelText: i18n.get('取消'),
                okText: i18n.get('确定'),
                onOk() {
                  if (checkStaffs()) {
                    bus?.emit('set:tripdatalink:value')
                    bus?.emit('set:tripdatalink:traveler:id', travelerId)
                  }
                }
              })
            } else {
              if (checkStaffs()) {
                bus?.emit('set:tripdatalink:traveler:id', travelerId)
              }
            }
          } else {
            checkStaffs()
          }
        })
    }
  }
  handleChange = data => {
    const { onChange, field, record, vm, form } = this.props
    onChange && onChange(data)
    updateCellValue(field?.field, data, record, vm, form)
  }
  handleContent = (myValue) => {
    const { authStaffStaffMap } = this.props
    const content = (
      <div className={styles.popoverContainer}>
        {myValue.map((item, index) => (
          <div className={styles.item} key={index}>
            {authStaffStaffMap[item?.id || item]?.name}
          </div>
        ))}
      </div>
    )
    return content
  }
  render() {
    const { disabled = false, value, placeholder } = this.props
    const { myValue } = formatListRefOrganizationStaffField(toJS(value))
    return (
      value?.length > 0 ?
        <Popover placement="topLeft" content={this.handleContent(value)}>
          <Input
            disabled={disabled}
            style={itemInnerStyle}
            placeholder={placeholder}
            value={myValue}
            onClick={this.handleClick}
            onFocus={e => {
              e.target.blur()
            }}
          />
        </Popover> : <Input
          disabled={disabled}
          style={itemInnerStyle}
          placeholder={placeholder}
          value={myValue}
          onClick={this.handleClick}
          onFocus={e => {
            e.target.blur()
          }}
        />
    )
  }
}

export default ListRefOrganizationStaffEditCell
