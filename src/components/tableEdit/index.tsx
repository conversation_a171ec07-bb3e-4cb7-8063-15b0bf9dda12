import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import styles from './DataLinkEditTable.module.less'
import { cloneDeep, isEqual } from 'lodash'
import EditTableWrapper from './EditTableWrapper'

interface IProps {
  isEdit: boolean
  field: any
  dataSource: any[]
  templates: any[]
  templatesold: any[]
  onChange: (data: any) => void
  onCreate?: () => void
  flowBus: any
  bus: any
}

interface IState {
  data: any[]
  dataLinkTemplateId: string
  oldDataLinkTemplateId: string
  template: any
  templates: any[]
}
class DataLinkEditTable extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      data: props.dataSource || [],
      templates: [],
      template: props.field?.name === 'budgetAdjustDetails' ? [] : {},
      dataLinkTemplateId: '',
      oldDataLinkTemplateId: ''
    }
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  componentDidMount(): void {
    this.fnUpdateProps(this.props.templates, this.props.templatesold, this.props.dataSource)
  }

  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (
      nextProps.templates.length != this.props.templates.length ||
      nextProps.templatesold.length != this.props.templatesold.length ||
      !isEqual(nextProps.dataSource, this.props.dataSource)
    ) {
      this.fnUpdateProps(nextProps.templates, nextProps.templatesold, nextProps.dataSource)
    }
  }

  fnUpdateProps = (templates, templatesold, dataSource) => {
    if (this.props.field?.name === 'budgetAdjustDetails') {
      this.setState({ template: templates, data: dataSource })
      return
    }
    let { dataLinkTemplateId, oldDataLinkTemplateId } = this.state
    this.setState({ data: cloneDeep(dataSource) })
    let arrp = templates.filter(i => {
      return i.entity.parentId === ''
    })
    // @ts-ignore
    let arrt = [...templates]
    if (dataLinkTemplateId !== '') {
      let obj = templatesold.find(t => t.templateId === dataLinkTemplateId)
      let oldobj = templatesold.find(t => t.templateId === oldDataLinkTemplateId)
      if (oldobj && oldobj.entity.active === false) {
        arrt.push(oldobj)
      }
      let newst = {} as any

      if (obj && obj.components) {
        newst.template = cloneDeep(obj)
      } else if (arrp.length > 0) {
        newst.template = cloneDeep(arrp[0])
        newst.dataLinkTemplateId = arrp[0].templateId
      }
      this.setState({
        ...newst,
        templates: arrt
      })
    } else {
      if (arrt.length == 0 && arrp.length > 0) {
        this.setState({
          dataLinkTemplateId: arrp[0].templateId,
          template: cloneDeep(arrp[0]),
          templates: []
        })
      } else if (arrt.length > 0) {
        this.setState({
          dataLinkTemplateId: arrt[0].templateId,
          template: cloneDeep(arrt[0]),
          templates: arrt
        })
      }
    }
  }

  handleGetData = data => {
    const { onChange } = this.props
    const newData = data.map(item => {
      const dataLinkId = item.dataLinkId || null
      const dataLinkTemplateId = item.dataLinkTemplateId
      delete item.disabledByState
      delete item.dataLinkId
      delete item.dataLinkTemplateId
      delete item.index
      return {
        dataLinkId,
        dataLinkTemplateId: dataLinkTemplateId || this.state.dataLinkTemplateId,
        dataLinkForm: item
      }
    })
    onChange?.(newData)
  }

  handleChangeId = dataLinkTemplateId => {
    const { templates } = this.state
    const template = templates.find(t => t.templateId === dataLinkTemplateId)
    this.setState({ dataLinkTemplateId, template })
  }

  getDataSource = (data, dataLinkTemplateId, name, template) => {
    const parentId = template?.entity?.parentId
    const dataSource = data
      .filter(item => item.dataLinkTemplateId === dataLinkTemplateId)
      .map((item, index) => {
        if (name === 'E_system_payment_plan_write') {
          this.props.flowBus.$_settleAmountTotal = data.reduce((pre, curr) => {
            return pre + Number(curr.dataLinkForm[`E_${parentId}_settleAmount`]?.standard) || 0
          }, 0)
        }
        const disabledByState = ['COR_SETTLE_PENDING', 'COR_SETTLE_PAYING', 'COR_SETTLE_PAID'].includes(
          item.dataLinkForm[`E_${parentId}_settleState`]
        )
        const { dataLinkForm = {}, dataLinkId, dataLinkTemplateId } = item
        return { ...dataLinkForm, dataLinkId, dataLinkTemplateId, disabledByState, index }
      })
    return dataSource
  }

  getCanCreate = (importMode, dataSource) => {
    if (importMode === 'SINGLE' && dataSource?.length > 0) {
      return false
    }
    return true
  }

  render() {
    const { templates, template, dataLinkTemplateId, data } = this.state
    const {
      field,
      isEdit,
      field: { importMode, name },
      bus,
      onCreate
    } = this.props
    const isBudgetAdjust = name === 'budgetAdjustDetails'
    const components = isBudgetAdjust ? template : template.components
    const dataSource = isBudgetAdjust ? data : this.getDataSource(data, dataLinkTemplateId, name, template) || []
    const canCreate = this.getCanCreate(importMode, dataSource)
    return (
      <div className={styles.dataLinkEditTableWrapper}>
        <EditTableWrapper
          components={components}
          dataSource={dataSource}
          noTransColumns={isBudgetAdjust}
          isEdit={isEdit}
          canCreate={isEdit && canCreate && !isBudgetAdjust}
          onGetData={this.handleGetData}
          bus={bus}
          onCreate={onCreate}
        />
      </div>
    )
  }
}

export default DataLinkEditTable
