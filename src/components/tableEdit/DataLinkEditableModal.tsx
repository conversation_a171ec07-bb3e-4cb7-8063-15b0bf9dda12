import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './MoneyModal.module.less'
import { Icon } from 'antd'
import { Button } from '@hose/eui'
import DataLinkEditComp from '../../components/dynamic/dataLinkEdit/DataLinkEditComp'
import MessageCenter from '@ekuaibao/messagecenter'
import { editable } from '../../components'
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
class DataLinkEditableModal extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    props.overrideGetResult(this.getResult)
    this.state = { data: props.value }
  }
  fieldBus: any = new MessageCenter()
  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  getResult = () => {
   return this.state.data
  }
  handleModalSave = () => {
    const { layer, field } = this.props
    const { importMode } = field
    if (importMode === 'SINGLE' && this.state?.data?.length) {
      this.fieldBus.getValueWithValidate(0).then(formValue => {
        const value = this.state.data?.[0] || {}
        layer?.emitOk([
          { dataLinkForm: formValue, dataLinkId: value.dataLinkId, dataLinkTemplateId: value.dataLinkTemplateId }
        ])
      })
    } else {
      layer?.emitOk()
    }
  }
  handleOnChange = value => {
    this.setState({ data: value })
  }
  render() {
    return (
      <div className={styles['modal-invoice-wrapper']}>
        <div className="modal-invoice-header">
          <div className="flex">{i18n.get('业务对象')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-invoice-content">
          <DataLinkEditComp
            {...this.props}
            onChange={this.handleOnChange}
            showHeader={false}
            elements={editable}
            fieldBus={this.fieldBus}
          />
        </div>
        <div className="modal-invoice-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalSave}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}

export default DataLinkEditableModal
