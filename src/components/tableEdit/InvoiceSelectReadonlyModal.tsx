import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { QuerySelect } from 'ekbc-query-builder'
import { EnhanceConnect } from '@ekuaibao/store'
import { Button } from '@hose/eui'
import styles from './InvoiceEditCell.module.less'
import style1 from './MoneyModal.module.less'
import RefInvoice from '../../elements/invoice-form/RefInvoice'
import CardListView from '../../elements/InvoiceCard/InvoiceItem'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { invoiceItemOptions, parseAsShowValue, fnGetInvoiceManage } from '../../elements/invoice-form/utils/config'
import { BasePureComponent } from '../dynamic/BasePureComponent'
import { ReviewedInvoiceTypes } from '../../elements/CarouselInvoiceReviewer/utils/InvoiceDetailUtil'
import { getV } from '@ekuaibao/lib/lib/help'
import Big from 'big.js'
import { cloneDeep } from 'lodash'
import InvoiceTaxInfo from '../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import FormItemLabelWrap from '../layout/FormItemLabelWrap/FormItemLabelWrap'
import { fnSortInvoice } from '../utils/fnInvoiceSelectUtil'
import { SortSelector } from '../utils/SortViewUtil'
import { standardValueMoney, getMoney } from '../../lib/misc'
import { MoneyMath } from '@ekuaibao/money-math'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { getNeedSubmitRiskReasonList } from '../../plugins/bills/util/billUtils'
import { updateEditApproveAmount, getCheckInvoiceAgain } from '../../plugins/bills/bills.action'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Icon } from 'antd'
import { toJS } from 'mobx'
const EKBIcon = api.require<any>('@elements/ekbIcon')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  OCRPower: state['@common'].powers.OCR,
  OCRMedical: state['@common'].powers.OCRMedical,
  INVOICEMANAGE: state['@common'].powers.INVOICEMANAGE
}))
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class InvoiceSelectReadonlyModal extends BasePureComponent {
  constructor(props) {
    super(props)
    this.state = {
      imageVisible: false,
      urls: [],
      imgIndex: 0,
      imageList: [],
      invoiceImageList: [],
      canClick: true,
      isSort: true,
      curSortType: '2',
      importMode: props.field.importMode,
      waitingBatchInvoice: null,
      previewChangeData: {},
      riskData: {},
      external: undefined
    }
  }

  invoiceTaxInfo = new InvoiceTaxInfo()

  componentDidMount() {
    const { bus, value } = this.props
    const { importMode } = this.state
    bus.on('viewer-approve-amount-edit', this.handleApproveAmountChange)
    bus.on('viewer-close', this.handleViewerClose)
    api.on('viewer-update-riskdata', this.handleUpdateRiskData)
    api.on('viewer-update-exteral', this.handleUpdateExteral)
    this.handleSortInvoice()
    if (importMode?.setVisible) {
      api.invokeService('@bills:get:feetype:invoice:importMode', importMode).then(res => {
        this.setState({ importMode: res.items })
      })
    }
    if (value && value.invoices) {
      let { flowId } = this.props
      let ids = value.invoices.map(o => o.master.id)
      ids && ids.length && this.fnFetchInvoiceImage(ids)
    }
    if (value?.supplementInvoiceBatchId) {
      this.loadSupplementInvoiceBatch(value?.supplementInvoiceBatchId)
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('viewer-approve-amount-edit', this.handleApproveAmountChange)
    bus.un('viewer-close', this.handleViewerClose)
  }

  loadSupplementInvoiceBatch = async (supplementInvoiceBatchId: any) => {
    const query = new QuerySelect().filterBy(
      `batchNo.containsIgnoreCase("${supplementInvoiceBatchId}")||id.containsIgnoreCase("${supplementInvoiceBatchId}")`
    )
    const res = await api.invokeService('@invoice-manage:search:WaitingBatchInvoice', query.value(), {
      join$1: `detailForm.details.feeTypeId,feeTypeId,/v1/form/feeTypes`,
      join$2: `detailForm.flowId,flow,/flow/v1/flows`,
      join$3: 'invoices.invoiceId,master,/v2/invoice'
    })
    const waitingBatchInvoice = res?.items[0]
    let { flowId } = this.props
    if (waitingBatchInvoice?.invoices) {
      const ids = waitingBatchInvoice.invoices.map(el => el.invoiceId)
      this.setState({ waitingBatchInvoice })
      this.fnFetchInvoiceImage(ids)
    }
  }

  handleSupplementInvoiceBatch = () => {
    const { waitingBatchInvoice } = this.state
    if (waitingBatchInvoice) {
      api.open('@invoice-manage:BatchInvoiceDetailModal', {
        batchData: waitingBatchInvoice,
        isEdit: true,
        reload: this.refreshSupplementInvoiceBatch,
        onConfirmCheck: this.getConfirmCheck
      })
    } else {
      showMessage.info(i18n.get('暂无数据'))
    }
  }

  refreshSupplementInvoiceBatch = (isDeleteAll = false) => {
    const { value, bus } = this.props
    if (isDeleteAll) {
      bus.emit('update:billInfo')
    } else {
      this.loadSupplementInvoiceBatch(value?.supplementInvoiceBatchId)
    }
  }

  getConfirmCheck = ({ batchInfo }, cb) => {
    let title = i18n.get('是否确认发票')
    if (Number(batchInfo?.TotalMoney?.standard) > Number(batchInfo?.importInvoiceMoney?.standard)) {
      title = i18n.get('存在费用金额大于发票金额，如确认发票，则不可继续补充发票')
    }
    showModal.confirm({
      title,
      onOk: () => {
        const flowIds = batchInfo.detailForm.map(el => el.flowId)
        api
          .invokeService('@invoice-manage:confirm:added:invoice', {
            flowIds,
            batchId: batchInfo.batchNo,
            detailIds: []
          })
          .then(() => {
            this.loadSupplementInvoiceBatch(batchInfo.batchNo)
            cb && cb()
          })
      }
    })
  }

  handleUpdateRiskData = riskData => {
    this.setState({
      riskData
    })
  }

  handleUpdateExteral = ({ external }: any = {}) => {
    const { dataSource } = this.props
    const detailId = dataSource?.feeTypeForm?.detailId
    if (detailId && external) {
      this.setState({
        external: external[detailId]?.invoiceForm || []
      })
    }
  }

  fnFetchInvoiceImage = ids => {
    api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
      this.setState({ invoiceImageList: rep.items })
    })
  }

  // 是否是单子的所有者
  isOwnerBill = (ownerId, permissions) => {
    const flag = permissions && permissions.find(item => item === 'SUPPLEMENT_INVOICE')
    const {
      staff: { id }
    } = this.props.userInfo
    return flag || (!!ownerId && ownerId.id === id)
  }

  handleImportInvoiceClick = () => {
    let { feeTypes } = this.props
    let { bus } = this.props
    bus.invoke('supplement:invoice:import:click', feeTypes).then(data => {
      bus.emit('save:supplement:invoice:import', data)
    })
  }

  handleImportInputInvoiceClick = () => {
    let { feeTypes, bus } = this.props
    bus.invoke('supplement:invoice:input:import:click', feeTypes).then(data => {
      bus.emit('save:supplement:invoice:import', data)
    })
  }

  handleImportOCRMedicalClick = () => {
    this.handleImportOCRClick(true)
  }

  handleImportOCRClick = isMedical => {
    let { feeTypes, bus } = this.props
    bus
      .invoke('supplement:invoice:import:ocr:click', feeTypes, isMedical, { notShowModalIfAllInvoiceSuccess: true })
      .then(data => {
        bus.emit('save:supplement:invoice:import', data)
      })
  }

  handleImportAliPayInvoiceClick = async () => {
    let { bus } = this.props
    const isAuth = await api.invokeService('@bills:check:aliPayCard:auth')
    if (!isAuth) return

    bus.invoke('supplement:invoice:import:aliPay:click').then(data => {
      if (data.length) {
        bus.emit('save:supplement:invoice:import', data)
      }
    })
  }

  handleImportAifaPiaoInvoiceClick = async () => {
    const isAuth = await api.invokeService('@bills:check:aifapiao:auth')
    if (!isAuth) return null
    const url = await api.invokeService('@bills:get:aifapiao:public:url')
    const res = await api.open('@bills:IframeModal', {
      src: url,
      handleListener: this.handleImportAifapiao
    })
  }

  handleImportAifapiao = async data => {
    const { ids, type } = data
    let {
      feeTypes,
      bus,
      submitterId,
      value: { invoices }
    } = this.props
    if (type === 'importInvoice') {
      const { items } = await api.invokeService('@bills:import:invoice:from:aifapiao', {
        ids,
        staffId: submitterId?.id
      })
      bus.invoke('supplement:invoice:import:aifapiao:click', feeTypes, invoices, items).then(data => {
        if (data.length) {
          bus.emit('save:supplement:invoice:import', data)
        }
      })
    }
  }

  handleConfirmInvoice = () => {
    let { flowId, bus } = this.props
    // 确认发票前先看看有没有风险需要填写原因
    getNeedSubmitRiskReasonList(flowId).then(needSubmitRiskReasonList => {
      const someReasonNotExist = needSubmitRiskReasonList.some(reasonItem => {
        const { riskWarningContent = [] } = reasonItem
        return riskWarningContent.some(contentItem => !contentItem.invoiceRiskExplainContent)
      })
      if (someReasonNotExist) {
        showMessage.error(i18n.get('发票违规：允许提交且说明原因'))
      } else {
        bus.emit('supplement:invoice:confirm')
      }
    })
  }

  handleSubmitRiskReasonPopup = () => {
    let { bus, flowId } = this.props
    // 确认发票前先看看有没有风险需要填写原因
    getNeedSubmitRiskReasonList(flowId).then(needSubmitRiskReasonList => {
      if (needSubmitRiskReasonList && needSubmitRiskReasonList.length > 0) {
        api
          .open('@bills:BillRiskReasonModal', {
            flowId,
            invoiceRiskArr: needSubmitRiskReasonList
          })
          .then(() => {
            api.invoke('bills:update:for:detail:changed', { id: flowId })
          })
      }
    })
  }

  handleImgPreview = imageList => {
    preview()({ value: [imageList], line: imageList })
  }

  handleDeleteInvoice = line => {
    const {
      value: { invoices },
      bus
    } = this.props
    let invoiceList = invoices || []
    let index = invoiceList.findIndex(v => v.master.id === line.master.id)
    const invoice = invoiceList[index]
    bus.emit('detail:delete:invoice', { invoice })
  }

  handleRemoveAttachment = line => {
    const { value, bus } = this.props
    let fileList = value && value.attachments
    let files = fnFormatAttachment(fileList)
    let attachment = files.filter(v => v.fileId === line.fileId)
    bus.emit('detail:delete:invoice', { attachment })
  }

  handleInvoiceEdit = (dataSource, item, i, attachment) => {
    let index = i
    const {
      value: { invoices },
      bus
    } = this.props
    const regions = dataSource.filter(line => line.id !== item.id).map(line => line.region)
    let invoiceList = cloneDeep(invoices) || []
    index = invoiceList.findIndex(v => v.master.id === item.master.id)
    api.open('@bills:EditInvoice', { invoiceInfo: item, attachment, regions }).then((data: any) => {
      let form = { ...invoices[index].master.form, ...data.master.form }
      invoices[index].master.form = form
      bus.emit('supplement:invoice:edit', invoices)
    })
  }

  handleOnChange = (isDeductible, id) => {
    const { bus, isInHistory } = this.props
    if (isInHistory) return
    const params = { isDeductible, id }
    api.invokeService('@bills:invoice-isDeductible', params).then(res => {
      bus.emit('update:billInfo')
    })
  }

  handleInvoicePreview = (items, invoiceImageList, submitter, invoiceManageable, showValue, riskData) => {
    const {
      value: { invoices },
      flowId,
      bus,
      billState,
      modifyApproveMoney,
      isFlowEditable,
      details,
      source
    } = this.props
    api.emit(
      '@layout::open:invoiceReviewer',
      items,
      invoiceImageList,
      submitter,
      invoiceManageable,
      showValue,
      flowId,
      billState,
      bus,
      riskData,
      invoices,
      modifyApproveMoney,
      isFlowEditable,
      details,
      source
    )
  }
  handleApproveAmountChange = items => {
    const {
      value: { invoices },
      dataSource,
      bus,
      detailId,
      details,
      flowId,
      ...others
    } = this.props
    const fee = details.find(v => v.feeTypeForm.detailId === detailId) || {}
    fee.feeTypeForm.invoiceForm.invoices = items
    const req = {
      flowId,
      name: 'freeflow.editApproving',
      form: { feeTypeForm: fee.feeTypeForm },
      formType: 'expense'
    }
    this.setState({ previewChangeData: req })
  }
  handleViewerClose = () => {
    const { previewChangeData } = this.state
    if (!previewChangeData.flowId) {
      return
    }
    const {
      form: {
        feeTypeForm: { amount, apportions, invoiceForm }
      },
      flowId
    } = previewChangeData
    if (flowId) {
      let tipMsg = ''
      if (apportions) {
        const config = apportions[0].specificationId?.configs?.find(v => v.ability === 'apportion')
        if (config?.apportionMoneyField === 'amount') {
          tipMsg = i18n.get('系统将根据费用金额、分摊比例，自动调整分摊金额', { label: i18n.get('费用金额') })
        }
      }
      return api.open('@bills:BillInfoModifyReasonModal', { tipMsg, needRevert: true }).then((data: any) => {
        const { editReason, revert } = data
        if (revert) {
          this.props.bus.emit('update:billInfo')
        } else {
          const newAmount =
            invoiceForm?.invoices?.reduce(
              (total, oo) => {
                const {
                  master: { entityId, form }
                } = oo
                const approveamount =
                  entityId === i18n.get('system_发票主体')
                    ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]
                    : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })]
                return new MoneyMath(total).add(oo.approveAmount || approveamount || 0).__value
              },
              { standard: '0' }
            ) || false
          previewChangeData.form.feeTypeForm.amount = newAmount
          tipMsg != '' && this.updateApportions(apportions, newAmount)
          previewChangeData.params = { backLogOwnerId: this.props.backLogOwnerId, editReason }
          this.handleSubmit(previewChangeData, tipMsg)
        }
      })
    }
  }
  updateApportions = (apportions, feeAmountInfo) => {
    const feeAmount = getMoney(feeAmountInfo)
    let totalAmount: any = 0
    const length = apportions.length
    let restAmount = 0
    apportions.forEach((value, index) => {
      const {
        apportionForm: {
          apportionMoney: { standardScale },
          apportionPercent
        }
      } = value
      let amount: any = new Big(feeAmount)
        .times(apportionPercent)
        .div(100)
        .toFixed(Number(standardScale))
      if (index === length - 1) {
        amount = new Big(feeAmount).minus(totalAmount).toFixed(Number(standardScale))
        if (amount < 0 && Number(feeAmount) > 0) {
          restAmount = amount
        }
      }
      totalAmount = new Big(totalAmount).plus(amount).toFixed(Number(standardScale))
      value.apportionForm.apportionMoney = standardValueMoney(amount)
      if (!restAmount) {
      }
    })
    // 最后一行小于0的化,所有余额重新计算一边不做四舍五入处理
    if (restAmount) {
      totalAmount = 0
      apportions.forEach((value, index) => {
        const {
          apportionForm: {
            apportionMoney: { standardScale },
            apportionPercent
          }
        } = value
        let amount = new Big(feeAmount)
          .times(apportionPercent)
          .div(100)
          .round(standardScale, 0)
          .toFixed(Number(standardScale))
        if (index === length - 1) {
          amount = new Big(feeAmount).minus(totalAmount).toFixed(Number(standardScale))
        }
        totalAmount = new Big(totalAmount).plus(amount).toFixed(Number(standardScale))
        value.apportionForm.apportionMoney = standardValueMoney(amount)
      })
    }
  }
  handleSubmit = (param, tipMsg) => {
    updateEditApproveAmount(param)
      .then(() => {
        this.props.bus.emit('update:billInfo')
        showMessage.success(i18n.get('保存成功'))
        tipMsg != '' && this.props.bus.emit('feedetails:approveamount:change', this.props.detailId)
      })
      .catch(err => {
        console.log(err)
        showMessage.error(err.errorMessage)
        this.props.bus.emit('update:billInfo')
      })
  }
  handleEditTax = item => {
    const {
      master: { entityId, form },
      invoiceId,
      itemIds
    } = item
    const { flowId, bus } = this.props
    const params = {
      flowId,
      invoiceId,
      itemIds
    }
    const taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
    const taxRate = this.invoiceTaxInfo.getTaxRate(item)
    api.open('@bills:InvoiceEditTaxModal', { entityId, params, isApprove: true, taxAmount, taxRate, invoiceForm:form }).then(data => {
      bus.emit('update:billInfo')
    })
  }

  handleRetryCheckerInvoiceClick = async (index, item) => {
    const { canClick } = this.state
    if (!canClick) return
    await this.setState({ canClick: false })
    try {
      const invoiceId = item.master.id
      const { bus } = this.props
      await api.invokeService('@bills:retry:checker:invoice', invoiceId)
      bus.emit('update:billInfo')
      this.setState({ canClick: true })
    } catch (e) {
      this.setState({ canClick: true })
    }
  }

  handleCheckInvoiceAgain = (index, item) => {
    const invoiceId = item.master.id
    const fpdm = item.master.form['E_system_发票主体_发票代码']
    const fphm = item.master.form['E_system_发票主体_发票号码']
    getCheckInvoiceAgain({ invoiceId, fpdm, fphm })
      .then(res => {
        if (res.value) {
          showMessage.success('查验成功')
          this.handleUploadInvoiceMetaile()
        } else {
          showMessage.error('查验失败')
        }
      })
      .catch(e => {
        showMessage.error(e.message || e.msg)
      })
  }
  handleInvoiceStatusChange = () => {
    const { bus } = this.props
    bus.emit('update:billInfo')
  }
  handleUploadInvoiceMetaile = () => {
    const { bus } = this.props
    bus.emit('update:billInfo')
  }

  handleSortInvoice = () => {
    const { value, onChange, riskData } = this.props
    const { isSort, curSortType } = this.state
    if (!(value?.invoices && value?.invoices?.length)) return
    const invoiceArr = fnSortInvoice(value?.invoices, !isSort, curSortType, riskData)
    onChange && onChange({ ...value, invoices: invoiceArr })
    this.setState({ isSort: !isSort })
  }

  // 切换排序类型
  handleSortChange = curValue => {
    const {
      value: { invoices = [] },
      value,
      onChange,
      riskData
    } = this.props
    if (!invoices.length) return
    this.setState(
      {
        curSortType: curValue,
        isSort: true
      },
      () => {
        const invoiceArr = fnSortInvoice(invoices, true, curValue, riskData)
        onChange && onChange({ ...value, invoices: invoiceArr })
      }
    )
  }

  renderSupplementInvoiceBatch = ({
    LabelComponent,
    supportReviewedItems,
    invoiceImageList,
    submitterId,
    isInvoiceManagePermissions,
    showValue,
    riskData
  }) => {
    const { billState } = this.props
    return (
      <div className={styles['invoice-select-content']}>
        <div className={styles['invoice-select-label']}>
          <div className={styles['invoice-label-title']}>{LabelComponent}</div>
          {supportReviewedItems.length > 0 && !['draft', 'new'].includes(billState) && (
            <div className={styles['invoice-review-button']}>
              <div
                style={{
                  display: 'inline-flex',
                  alignItems: 'center'
                }}
                onClick={() =>
                  this.handleInvoicePreview(
                    supportReviewedItems,
                    invoiceImageList,
                    submitterId,
                    isInvoiceManagePermissions,
                    showValue,
                    riskData
                  )
                }
              >
                <EKBIcon name="#EDico-invoice-review2" className="w-20 h-20 mr-5" />
                <span>{i18n.get('审阅模式')}</span>
              </div>
            </div>
          )}
        </div>
        <div className="invoice-type-title">
          <div className="confirm-invoice" onClick={this.handleSupplementInvoiceBatch}>
            <Button> {i18n.get('查看批量补充发票')} </Button>
          </div>
        </div>
      </div>
    )
  }
  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  render() {
    const {
      value,
      userInfo: { permissions },
      suppleInvoiceBtn,
      source,
      submitterId,
      bus,
      isInHistory,
      OCRPower,
      OCRMedical,
      external: externalProp,
      isForbid,
      ownerId,
      billState,
      flowId,
      isDetail,
      detailId,
      field,
      canEditNote,
      modifyApproveMoney,
      riskData: riskDataProp,
      INVOICEMANAGE
    } = this.props //source:统一开票管理页面
    const { noColon } = field
    const {
      invoiceImageList,
      isSort,
      curSortType,
      importMode,
      waitingBatchInvoice,
      riskData: riskDataState,
      external: exteralState
    } = this.state
    const external = exteralState || externalProp
    const riskData = { ...riskDataProp, ...riskDataState }
    const needSubmitRiskReason = riskData?.singleInvoiceRiskWarning?.some(
      riskWarningItem => riskWarningItem?.pathValueId === detailId && !!riskWarningItem?.riskWarningReason?.length
    )
    const fileList = value && value.attachments
    let invoiceDetail = []
    const isSupplementInvoiceBatch = value?.supplementInvoiceBatchId
    if (isSupplementInvoiceBatch && waitingBatchInvoice?.invoices) {
      invoiceDetail = waitingBatchInvoice.invoices.map(el => ({ ...el, details: [], isWaitingBatchInvoice: true }))
    } else if (value?.invoices) {
      invoiceDetail = value.invoices
    }
    const showValue = parseAsShowValue(toJS(fileList), toJS(invoiceDetail))
    const supportReviewedItems = showValue.filter(item => {
      // 过滤掉不支持审阅模式的发票类型
      const { master, type: entityType } = item
      const entityId = master && master.entityId
      const entity = entityId || entityType
      return ReviewedInvoiceTypes.indexOf(entity) > -1
    })
    const isInvoiceManagePermissions = !!fnGetInvoiceManage(permissions)
    const isVisible = source ? true : suppleInvoiceBtn && this.isOwnerBill(ownerId, permissions)
    const InvoiceType = value && invoiceItemOptions(value)
    const type = value && value.type
    let isEdit = false
    const isConfirm = type === 'exist' && value.invoiceConfirm === 'false'
    if (isConfirm && !isInHistory && isVisible) {
      isEdit = true
    }
    const label = getV(this.props, 'field.label', '')
    const invoices = (value && value.invoices) || []
    const LabelComponent = (
      <FormItemLabelWrap
        external={external}
        isForbid={isForbid}
        noColon={noColon}
        field={field}
        flowId={flowId}
        isDetail={true}
        detailId={detailId}
        canEditNote={canEditNote}
      >
        {label}
      </FormItemLabelWrap>
    )

    if (isSupplementInvoiceBatch) {
      return this.renderSupplementInvoiceBatch({
        LabelComponent,
        supportReviewedItems,
        invoiceImageList,
        submitterId,
        isInvoiceManagePermissions,
        showValue,
        riskData
      })
    }

    return (
      <div className={style1['modal-invoice-wrapper']}>
        <div className="modal-invoice-header">
          <div className="flex">{i18n.get('发票详情')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-invoice-content">
          <div className={styles['invoice-select-content']}>
            <div className={styles['invoice-select-label']}>
              <div className={styles['invoice-label-title']}>{LabelComponent}</div>
              {supportReviewedItems.length > 0 && (
                <div className={styles['invoice-review-button']}>
                  {!['draft', 'new'].includes(billState) ? (
                    <div
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center'
                      }}
                      onClick={() =>
                        this.handleInvoicePreview(
                          supportReviewedItems,
                          invoiceImageList,
                          submitterId,
                          isInvoiceManagePermissions,
                          showValue,
                          riskData
                        )
                      }
                    >
                      <EKBIcon name="#EDico-invoice-review2" className="w-20 h-20 mr-5" />
                      <span>{i18n.get('审阅模式')}</span>
                    </div>
                  ) : (
                    <div />
                  )}
                  {!!invoices.length && (
                    <SortSelector
                      onTypeChange={this.handleSortChange}
                      value={curSortType}
                      onSortChange={this.handleSortInvoice}
                      isSort={isSort}
                    />
                  )}
                </div>
              )}
            </div>
            <div className="invoice-type-title">
              {type === 'exist' && showValue.length > 0 ? null : <div className="invoice-type">{InvoiceType}</div>}
              {(type === 'wait' || isConfirm) && isVisible && (
                <>
                  <div className="add-invoice">
                    <RefInvoice
                      text={i18n.get('+补充发票')}
                      bus={bus}
                      OCRPower={OCRPower}
                      OCRMedical={OCRMedical}
                      importMode={importMode}
                      importWay={'supInvoice'}
                      onImportOCRClick={this.handleImportOCRClick}
                      onImportOCRMedicalClick={this.handleImportOCRMedicalClick}
                      onImportInvoiceClick={this.handleImportInvoiceClick}
                      onImportInputInvoiceClick={this.handleImportInputInvoiceClick}
                      onImportAliPayInvoiceClick={this.handleImportAliPayInvoiceClick}
                      onImportAifaPiaoInvoiceClick={this.handleImportAifaPiaoInvoiceClick}
                    />
                  </div>
                  {type === 'exist' && needSubmitRiskReason && (
                    <div className="submit-risk-reason" onClick={this.handleSubmitRiskReasonPopup}>
                      <Button category="secondary"> {i18n.get('请填写原因')} </Button>
                    </div>
                  )}
                </>
              )}
              {isConfirm &&
                (source === 'confirmInvoiceManage' ||
                  (source === 'approving' && INVOICEMANAGE && isInvoiceManagePermissions) ||
                  (source === 'payment' && INVOICEMANAGE && isInvoiceManagePermissions)) && (
                  <div className="confirm-invoice" onClick={this.handleConfirmInvoice}>
                    <Button> {i18n.get('确认发票')} </Button>
                  </div>
                )}
              {type === 'unify' && <div className="unify-invoice"> | {value.invoiceCorporation?.name}</div>}
            </div>
            <CardListView
              bus={bus}
              external={external}
              isForbid={isForbid}
              isEdit={isEdit}
              isReadOnlyPage={true}
              isInHistory={isInHistory} // 审批中已修改的历史单据不能编辑发票的可抵扣税额和税率
              billState={billState}
              dataSource={showValue}
              submitterId={submitterId}
              invoiceImgList={invoiceImageList}
              isInvoiceManagePermissions={isInvoiceManagePermissions}
              handleImgPreview={this.handleImgPreview}
              onDeleteItem={this.handleDeleteInvoice}
              onOpenEditInvoice={this.handleInvoiceEdit}
              onFileDownload={this.handleFileDownload}
              onFilePreview={this.handleFilePreview}
              onRemoveAttachment={this.handleRemoveAttachment}
              onChange={this.handleOnChange}
              modifyApproveMoney={modifyApproveMoney}
              onOpenEditModal={this.handleEditTax}
              onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick}
              onUploadInvoiceMetaile={this.handleUploadInvoiceMetaile}
              riskData={riskData}
              checkInvoiceAgain={this.handleCheckInvoiceAgain}
              showCheckAginButton={true}
              onInvoiceStatusChange={this.handleInvoiceStatusChange}
            />
          </div>
        </div>
        <div className="modal-invoice-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalClose}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}
