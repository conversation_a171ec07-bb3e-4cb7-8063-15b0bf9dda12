.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.popoverContainer {
    width: 180px;
    max-height: 204px;
    border-radius: 8px;
    overflow-y: auto;
    font-size: 14px;
    color: var(--eui-text-title);

    .fileItem {
        display: flex;
        align-items: center;

        .fileName {
            margin-left: 10px;
            .ellipsis
        }

        margin-bottom: 12px;

        &:last-of-type {
            margin-bottom: 0;
        }
    }

    .item {
        width: 100%;
        margin-bottom: 12px;

        &:last-of-type {
            margin-bottom: 0;
        }
    }
}


.tableReadonlyWidth {
    max-width: 200px;
    font-size: 14px;
    min-width: 150px;
    color: var(--eui-text-caption);
    // display: flex;
    // align-items: center;

    span {
        .ellipsis
    }

    .fileItem {
        display: flex;
        align-items: center;
        width: 100%;

        .fileName {
            margin-left: 10px;
            .ellipsis
        }
    }

    .ellipsis
}