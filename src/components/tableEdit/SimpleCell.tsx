import React from 'react'
import { Tooltip } from '@hose/eui'
import styles from './Popover.module.less'
import { formatLinkText } from '../../elements/DataLinkTable/tableUtil'
const SimpleCell = ({ text, name }) => {
  const { textValue, domValue } = formatLinkText(text, name)
  if (text) {
    return (
      <div className={styles.tableReadonlyWidth}>
        <Tooltip title={textValue}>{domValue || i18n.get('无')}</Tooltip>
      </div>
    )
  }
  return <div className={styles.tableReadonlyWidth}>{textValue || i18n.get('无')}</div>
}

export default SimpleCell
