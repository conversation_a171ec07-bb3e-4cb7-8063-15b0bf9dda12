@import "~@ekuaibao/eui-styles/less/token.less";

.customInputWrapper {
    width: 100%;
    height: 32px;
    background: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    padding: 4px 7px;
    display: flex;
    align-items: center;

    &:hover {
        border: 1px solid var(--brand-base);
    }
}

.customInputWrapper:empty::before {
    color: lightgrey;
    content: attr(placeholder);
}

.customInputWrapperError {
    width: 100%;
    height: 28px;
    background: #ffffff;
    border: 1px solid #ff7c7c;
    border-radius: 2px;
    padding: 4px 7px;
    display: flex;
    align-items: center;
}

.customInputWrapperError:empty::before {
    color: lightgrey;
    content: attr(placeholder);
}

.disabledWrapper {
    width: 100%;
    height: 28px;
    background: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    padding: 4px 7px;
    display: flex;
    align-items: center;
    cursor: not-allowed;
}

.disabledWrapper:empty::before {
    color: lightgrey;
    content: attr(placeholder);
}