import React, { FC } from 'react'
import { required } from '../validator/validator'
import { Form } from 'antd'
import { get } from 'lodash'
import { formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import CurrencyComponent from '../dynamic/CurrencyComponent'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  form: any
  currentNode: any
  hiddenFields?: string[]
}
const CurrencyEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  const { optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const validator = (_rule: any, value: any, callback: any) => {
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(<DataLinkEditableWrapper {...props} />)}
    </FormItem>
  )
}

export default CurrencyEditCell

const DataLinkEditableWrapper: FC<any> = props => {
  const handleOnChange = value => {
    const { onChange, field, record, vm, form } = props
    onChange && onChange(value)
    updateCellValue(field?.field, value, record, vm, form)
  }
  return <CurrencyComponent {...props} onChange={handleOnChange} />
}
