import React from 'react'
import styles from './Popover.module.less'
import { app } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import { Tooltip } from '@hose/eui'
const ApportionsCell = ({ field, text, vm, bus, record }) => {
  const handleClick = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const params = {
      bus,
      value: toJS(text || []),
      record,
      external: vm?.props?.external, //TODO
      billSpecification: vm.billSpecification,
      isForbid: vm?.props?.isForbid,
      field,
      detailId: record?.feeTypeForm?.detailId,
      submitterId: vm?.submitterId,
      feeDetailId: vm?.props?.feeDetailId,
      showAllFeeType: vm?.props?.showAllFeeType,
      billTemplate: vm?.props?.billTemplate,
      template: record?.rowManager?.template,
      isModify: vm?.props?.isModify,
      apportionVisibleList: vm?.props?.apportionVisibleList
    }
    app.open('@bills:ApportionsReadonlyModal', params)
  }
  if (!text?.length) {
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  }
  return (
    <div className={styles.tableReadonlyWidth} onClick={handleClick}>
      <Tooltip title={`${`根据「${text?.[0]?.specificationId?.name}」分摊`}
      ${`${text?.length || 0}条`}`}>
        <a>
          {`根据「${text?.[0]?.specificationId?.name}」分摊`}
          {`${text?.length || 0}条`}
        </a>
      </Tooltip>
    </div>
  )
}

export default ApportionsCell
