import React, { FC } from 'react'
import { Form, Switch } from 'antd'
import { isAllowModifyFiled } from '../utils/fnDisableComponent'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { get } from 'lodash'
import { formItemStyle, getFormItemLabel, updateCellValue } from './utils'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  record: any
  field: any
  currentNode: any
  value?: any
  checked?: any
  vm:any
  form:any
  hiddenFields?: string[]
  onChange?: (value: any) => void
}

const SwitchEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const label = fnGetFieldLabel(fieldInfo)
  return (
    <FormItem {...getFormItemLabel(label)} style={{ ...formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields), margin: 0 }}>
      {getFieldDecorator(dataIndex, {
        valuePropName: 'checked',
        rules: [{ required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(<SwitchWrapper {...props} />)}
    </FormItem>
  )
}
const SwitchWrapper: FC<IProps> = props => {
  const { record, field: fieldInfo, currentNode, value, onChange ,vm,form} = props
  if (!fieldInfo) {
    return null
  }
  const handleOnChange = checked => {
    onChange && onChange(checked)
    updateCellValue(fieldInfo?.field, checked, record,vm,form)
  }
  const { editable, name } = fieldInfo
  const disabled = !isAllowModifyFiled(currentNode, name) || !editable
  return <Switch checked={props?.checked} disabled={disabled} onChange={handleOnChange} />
}

export default SwitchEditCell
