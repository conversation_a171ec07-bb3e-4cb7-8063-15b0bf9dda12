@import '~@ekuaibao/eui-styles/less/token.less';

.modal-money-wrapper {
  :global {
    .modal-header {
      &.modal-money-header {
        height: 60px;
        font-size: 20px;
        font-weight: 500;
        line-height: 1.4;
        text-align: justify;
        color: #000000;
        border-bottom: none;
        padding: 0 24px;

        .flex {
          padding-left: 8px;
        }

        .cross-icon {
          cursor: pointer;
        }
      }
    }

    .modal-money-content {
      padding: 24px;
    }

    .modal-footer {
      &.modal-money-footer {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: @space-10;
        padding: 0 @space-6;
        justify-content: flex-end;
        box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.2), 0 1px 0 0 rgba(29, 43, 61, 0.15);
        background-color: #fff;

        button {
          font-size: 14px;
          line-height: 1.57;
        }
      }
    }
  }
}
.modal-invoice-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  :global {
    .modal-invoice-header {
      padding: 16px 24px 0;
      background: #ffffff;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #1d2b3d;
      border-bottom: none;
      margin-bottom: 6px;
      .flex {
        flex-grow: 1;
      }
      .cross-icon {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .modal-invoice-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
    }
    .modal-invoice-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: @space-10;
      padding: 0 @space-6;
      justify-content: flex-end;
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.2), 0 1px 0 0 rgba(29, 43, 61, 0.15);
      background-color: #fff;
    }
  }
}
