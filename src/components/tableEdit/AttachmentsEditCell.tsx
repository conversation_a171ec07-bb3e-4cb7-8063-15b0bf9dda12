import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { app as api } from '@ekuaibao/whispered'
import AttachmentsCell from './AttachmentsCell'
import CustomInput from './CustomInput'
import { get } from 'lodash'
import { FeetypeInfo } from './type'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: FeetypeInfo
  field: any
  hiddenFields?: string[]
}

const AttachmentsEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    //如果这个费用明细没有这个字段的话就不展示
    return null
  }
  const label = fnGetFieldLabel(fieldInfo)
  const validator = (rule, value, callback) => {
    if (!fieldInfo) {
      return callback()
    }
    callback(required(fieldInfo, value))
  }

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex) || []
      })(<InputWrapper {...props} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any, any> {
  handleClick = () => {
    const { onChange, record, field, vm, form } = this.props
    api.open('@bills:AttachmentsModal', { params: { ...this.props } }).then(res => {
      onChange?.(res)
      updateCellValue(field?.field, res, record, vm, form)
    })
  }
  render() {
    const {
      value,
      form: { getFieldError },
      dataIndex
    } = this.props
    const disabled = isDisable(this.props)
    const error = getFieldError(dataIndex)
    return (
      <CustomInput placeholder="请上传文件" disabled={disabled} onClick={this.handleClick} error={error}>
        <AttachmentsCell value={value} showEmpty />
      </CustomInput>
    )
  }
}

export default AttachmentsEditCell
