import React, { FC } from 'react'
import { required } from '../validator/validator'
import { Form } from 'antd'
import { app } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { invoiceOptions } from '../../lib/InvoiceUtil'
import { isZero } from '../../lib/misc'
import { fnGetValidExternal, formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { getInvoiceOptions } from '../../lib/third-data'
import CustomInput from './CustomInput'
import { canModifyApproveMoney } from '../../plugins/bills/util/parse'
import { invoiceFormMap } from '../../elements/puppet/details/FormatDateUtils'
import { toJS } from 'mobx'
const FormItem = Form.Item

interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  form: any
  currentNode: any
  hiddenFields?: string[]
}
const InvoiceEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, currentNode = {}, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const { optional, invoiceType } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const validator = (rule, value, callback) => {
    let { type, invoiceCorporationId, attachments, invoices, invoiceConfirm } = value
    let amountObj = props?.form?.getFieldValue('amount')
    let isZeroAmount = isZero(amountObj?.standard)
    if (type === 'noExist' && invoiceType?.noExistConfig) {
      const { flowPlanConfigId, configNodeId } = currentNode
      const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
      const options = invoiceOptions(invoiceType, currentFlowNode)
      if (!options?.some(i => i.type === type)) {
        return callback(i18n.get('请添加发票'))
      }
    }
    if (
      invoiceType.isRequired &&
      type === 'exist' &&
      (!attachments || !attachments.length) &&
      (!invoices || !invoices.length) &&
      !invoiceConfirm &&
      !isZeroAmount &&
      !optional
    ) {
      return callback(i18n.get('请添加发票'))
    }
    return callback(required(fieldInfo, value))
  }
  const getInitValue = () => {
    const { flowPlanConfigId, configNodeId } = currentNode
    let value = get(record, dataIndex)
    const { invoiceType, defaultValue, editable } = fieldInfo
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    if (!value || (!!value && value.type === 'noWrite')) {
      const fnvalue = editable
        ? { type: invoiceType?.defaultInvoiceType || (!!fnInvoiceType.length && fnInvoiceType[0].type) }
        : { type: defaultValue.value ? defaultValue.value : 'noWrite' }
      return fnvalue
    } else if (value && value.type && value.type === 'unify' && !invoiceType.unify.choose && editable) {
      value = getInvoiceOptions(invoiceType)
      return value[0]
    } else {
      return value
    }
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: getInitValue()
      })(<InvoiceSelectWrapper {...props} template={record?.rowManager?.template || []} />)}
    </FormItem>
  )
}

export default InvoiceEditCell

const InvoiceSelectWrapper: FC<any> = props => {
  const {
    dataIndex,
    bus,
    field: fieldInfo,
    vm,
    value,
    field,
    record,
    form,
    onChange,
    form: { getFieldError }
  } = props
  if (!fieldInfo) {
    return null
  }
  const placeholder = getPlaceholder(fieldInfo)
  const handleClick = () => {
    const params = {
      bus,
      value: toJS(value),
      record,
      form,
      external: fnGetValidExternal(vm?.props?.external, record),
      billSpecification: vm.billSpecification,
      isForbid: vm?.props?.isForbid,
      field,
      detailId: record?.feeTypeForm?.detailId,
      submitterId: vm?.submitterId,
      feeDetailId: vm?.props?.feeDetailId,
      showAllFeeType: vm?.props?.showAllFeeType,
      billTemplate: vm?.props?.billTemplate,
      template: record?.rowManager?.template,
      isModify: vm?.props?.isModify,
      billState: vm?.billState,
      flowId: vm?.flowId,
      feeTypes: vm?.feeTypes,
      modifyApproveMoney: canModifyApproveMoney(vm.billSpecification),
      isRecordExpends: vm?.props?.isRecordExpends,
      isQuickExpends: vm?.props?.isQuickExpends,
      riskData: toJS(vm?.props?.riskData),
      details: toJS(vm?.tableDataSource)
    }
    app.open('@bills:InvoiceSelectModal', params).then((res: any) => {
      const { invoiceForm, formValue } = res
      onChange && onChange(invoiceForm)
      if (formValue) {
        record?.rowManager?.onFormValueChange(formValue, vm, form) //更新所有联动赋值字段
      }
      updateCellValue(fieldInfo?.field, invoiceForm, record, vm, form) //更新发票字段
    })
  }
  const error = getFieldError(dataIndex)

  return (
    <CustomInput placeholder={placeholder} onClick={handleClick} error={error} disabled={false}>
      {`${invoiceFormMap()[value.type]}`}
    </CustomInput>
  )
}
