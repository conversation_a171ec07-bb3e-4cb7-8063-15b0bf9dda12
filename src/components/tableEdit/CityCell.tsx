import { Popover } from 'antd'
import React from 'react'
import { Tooltip } from '@hose/eui'
import styles from './Popover.module.less'
const CityCell = ({ text }) => {
  let cityList = []
  try {
    cityList = JSON.parse(text) || []
  } catch (error) { }
  if (cityList.length === 1) {
    return <div className={styles.tableReadonlyWidth}><Tooltip title={cityList.map(item => item.label)}>{cityList.map(item => item.label)}</Tooltip></div>
  } else if (cityList.length === 0) {
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  } else {
    const content = (
      <div className={styles.popoverContainer}>
        {cityList.map((item, index) => (
          <div className={styles.item} key={index}>
            {item.label}
          </div>
        ))}
      </div>
    )
    return (
      <div className={styles.tableReadonlyWidth}>
        <Popover placement="topLeft" content={content}>
          {cityList.map(v => v.label).join(i18n.get('，'))}
        </Popover>
      </div>
    )
  }
}

export default CityCell
