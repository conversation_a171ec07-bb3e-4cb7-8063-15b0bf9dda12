import { Popover } from 'antd'
import React from 'react'
import styles from './Popover.module.less'
const ListRefOrganizationStaffCell = ({ authStaffStaffMap, text }) => {
  if (typeof text == 'string') {
    text = [text]
  }
  if (typeof text === 'undefined') {
    text = []
  }
  if (text.length === 1) {
    return (
      <div className={styles.tableReadonlyWidth}>{text.map(item => authStaffStaffMap[item?.id || item]?.name)}</div>
    )
  } else if (text.length === 0) {
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  } else {
    const content = (
      <div className={styles.popoverContainer}>
        {text.map((item, index) => (
          <div className={styles.item} key={index}>
            {authStaffStaffMap[item?.id || item]?.name}
          </div>
        ))}
      </div>
    )
    return (
      <div className={styles.tableReadonlyWidth}>
        <Popover placement="topLeft" content={content}>
          {text.map(item => authStaffStaffMap[item?.id || item]?.name).join(i18n.get(','))}
        </Popover>
      </div>
    )
  }
}

export default ListRefOrganizationStaffCell
