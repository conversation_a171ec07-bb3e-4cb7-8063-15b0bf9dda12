import React, { useEffect, useState } from 'react'
import styles from './Popover.module.less'
import { cloneDeep, isArray } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import { Tooltip } from '@hose/eui'
const ListDimensionCell = ({ value: oValue, field }) => {
  const [dimension, setDimension] = useState([])
  useEffect(() => {
    initDimension()
  }, [])
  const initDimension = () => {
    const nValue = toJS(oValue)
    const value = isArray(nValue) ? nValue : [nValue]
    if (value && value?.length) {
      let val = null
      try {
        val = cloneDeep(value).map(v => {
          if (typeof v !== 'string') {
            return v?.id
          }
          return v
        })
      } catch (e) {
        val = cloneDeep(toJS(value)).map(v => {
          if (typeof v !== 'string') {
            return v?.id
          }
          return v
        })
      } finally {
        if (!Array.isArray(val)) {
          val = []
        }
      }
      if (val.length > 300) {
        const promiseArr = []
        for (let index = 0; index < Math.ceil(val.length / 300); index++) {
          const list = []
          for (let j = index * 300; j < 300 * (index + 1); j++) {
            const element = val[j]
            list.push(element)
          }
          promiseArr.push(api.invokeService('@bills:get:dimension', list.join(',')))
        }
        Promise.all(promiseArr).then((res: any) => {
          let result = []
          res.forEach(element => {
            result = result.concat(element.items)
          })
          setDimension(result)
        })
      } else {
        api.invokeService('@bills:get:dimension', val.join(',')).then((res: any) => {
          setDimension(res.items)
        })
      }
    }
  }
  const str = dimension
    .map(item => {
      return !field?.hideCode ? ` ${field?.isShowFullPath ? item?.fullPath : item.name}(${item.code}) ` : ` ${item.name}`
    })
    .join(',')
  return (
    <div className={styles.tableReadonlyWidth}>
      <Tooltip placement="top" title={str}>
        <span>{dimension.length ? str : i18n.get('无')}</span>
      </Tooltip>
    </div>
  )
}

export default ListDimensionCell
