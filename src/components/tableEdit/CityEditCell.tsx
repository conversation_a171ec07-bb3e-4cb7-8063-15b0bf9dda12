import React, { FC } from 'react'
import { app } from '@ekuaibao/whispered'
import { Form } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { required } from '../validator/validator'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { get } from 'lodash'
const CityPicker = app.require<any>('@components/dynamic/CityPickerComponent')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  vm: any
  form: any
  hiddenFields?: string[]
}
const CityEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, form, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const { optional, multiple = false } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const disabled = isDisable(props)
  const value = get(record, dataIndex)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  const updateCell = (res: any) => {
    const { field, record, vm } = props
    updateCellValue(field?.field, res, record, vm, form)
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: value
      })(
        <CityPicker
          {...props}
          updateCell={updateCell}
          isAuto={disabled}
          submitterId={props?.vm?.submitterId}
          otherStyle={itemInnerStyle}
          placeholder={placeholder}
          optional={optional}
          multiple={multiple}
        />
      )}
    </FormItem>
  )
}

export default CityEditCell
