import React, { FC } from 'react'
import { Form } from 'antd'
import { isDisable } from '../utils/fnDisableComponent'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { formItemStyle, updateCellValue, getFormItemLabel } from './utils'
import { get } from 'lodash'
import { isObject, isArray } from '@ekuaibao/helpers'
import RefView from '../../elements/puppet/Ref'
import { getRecordLink } from '../dynamic/helpers/getRecordLink'
import { constantValue, lastSelectValue, formatTreeToArray } from '../utils/fnInitalValue'
import styles from './ListDimensionEditCell.module.less'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  value?: any
  vm: any
  lastChoice?: any
  onChange?: (value: any) => void
  hiddenFields?: string[]
}
const ListDimensionEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  const label = getLabel(fieldInfo)
  const getInitValue = () => {
    const { value = get(record, dataIndex), lastChoice } = props
    if (!value) {
      const constValue = constantValue(fieldInfo)
      if (constValue) {
        return [constValue]
      }
      const lastVal = lastSelectValue(fieldInfo, lastChoice)
      if (lastVal) {
        return lastVal.split(',')
      }
    }
    return value
  }

  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: getInitValue()
      })(<MutilDimensionList {...props}
        dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]} />)}
    </FormItem>
  )
}

export default ListDimensionEditCell
@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo
  }),
  {
    getRecordLink: getRecordLink
  }
)
class MutilDimensionList extends React.Component<any, any> {
  constructor(props) {
    super(props)
    this.state = this.initState()
  }
  initState = () => {
    const { field } = this.props
    const { dependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    return {
      dimensionList: [],
      dependenceList: [],
      isDependence,
      useDependenceData: false,
      dependenceListOnLoading: false,
      dependenceMap
    }
  }
  hasEmitHandleDependenceChange: boolean = false
  componentDidMount() {
    const { onComponentLoadFinished, field } = this.props
    if (onComponentLoadFinished && !this.hasEmitHandleDependenceChange) {
      onComponentLoadFinished(field)
    }
  }
  handleDimensionList = dimensionList => {
    this.setState({ dimensionList })
  }
  onChange = (value: any[]) => {
    const { onChange, field, record, vm, form } = this.props
    onChange?.(value)
    updateCellValue(field?.field, value, record, vm, form)
  }
  render() {
    const { dimensionList, isDependence, dependenceListOnLoading } = this.state
    const { flowId, field, value = [], form, dependenceList, vm } = this.props
    const { multiple, optional, selectRange, hideCode } = field
    let placeholder = getPlaceholder(field)
    const disabled = isDisable(this.props)
    const data = {
      multiple,
      onChange: this.onChange,
      id: isArray(value) ? value.map((el: any) => (isObject(el) ? el.id : el)) : value?.id,
      placeholder: placeholder,
      disabled: disabled,
      optional: optional,
      hideCode,
      isChangePosition: true
    }
    const param = { name: get(field, 'dataType.elemType.entity'), flowId }
    return (
      <div className={styles['dimensionList']}>
        <RefView
          submitterId={vm?.submitterId?.id}
          onlyLeafCanBeSelected={'all' !== selectRange}
          onDimensionChange={this.handleDimensionList}
          data={data}
          allowCancelDependence={field?.allowCancelDependence}
          param={param}
          dimensionList={dimensionList}
          dependenceList={dependenceList}
          isDependence={isDependence}
          dependenceListOnLoading={dependenceListOnLoading}
          form={form}
          field={field}
        />
      </div>
    )
  }
}
