.dataLinkEditTableWrapper {
  width: 100%;

  :global {

    .ant-table tbody tr td:first-child,
    .ant-table thead tr th:first-child {
      padding-left: 8px !important;
    }

    .btn-group {
      display: flex;
      align-items: center;

      .icon-style {
        color: var(--eui-icon-n2);

        &:first-of-type {
          margin-right: 20px;
        }
      }

      .icon-save {
        color: #30bf78;
      }

      .icon-cancel {
        color: var(--eui-function-danger-500);
      }
    }


  }

  .dataLinkEditTable {
    width: 100%;
  }

  .addContainer {
    margin-top: 12px;
  }
}

.dataLinkEditEmptyWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 168px;
  border: 1px dashed rgba(29, 33, 41, 0.2);
  border-radius: 6px;
}

.itemCreateBtn {
  cursor: not-allowed;
  font-size: 14px;
  color: var(--eui-text-link-normal);
  cursor: pointer;
}

.datalink-export-warning {
  color: var(--eui-function-danger-500);
}