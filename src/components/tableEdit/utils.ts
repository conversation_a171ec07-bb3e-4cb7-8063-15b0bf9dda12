import { app as api } from '@ekuaibao/whispered'
import { debounce, get, isArray, set } from 'lodash'
import { getValue } from '../../elements/dataLink-card/utils/dataLinkUtils'
import { getStaffShowValue } from '../../elements/utilFn'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { formatWithTime } from '../utils/fnPredefine4Date'
import { fnHideFieldsNote, isHiddenFieldsInclude } from "../utils/fnHideFields";


const getShow = (field: any, validateError: [string], hiddenFields = []) => {
  const configs = field?.configs
  const hide = field?.hide
  const name: string = field?.field
  const attributeHide = field?.attributeHide
  const hideVisibility = field?.hideVisibility
  const open = field?.open
  const ability = field?.ability
  // let hasStaffs = true
  // const { departments, roles, staffs } = hideVisibility || { departments: [], roles: [], staffs: [] }
  // if (!departments?.length && !roles?.length && !staffs?.length) {
  //   hasStaffs = false
  // }
  let flowFieldShow = true
  let hasAuto = hideVisibility === null ? false : configs?.find(r => r?.property === 'hide') === undefined ? false : true
  if (
    !hide ||
    (hide && !hasAuto) ||
    (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
    (hide && isHiddenFieldsInclude(hiddenFields, field) && !hasAuto)
  ) {
    let isShow =
      !hide ||
      (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
      validateError?.includes(name)
    if (!isShow) { flowFieldShow = false }
  } else if (hide && hasAuto && isHiddenFieldsInclude(hiddenFields, field)) {
    let isShow = (hide && !attributeHide) || validateError?.includes(name)
    if (!isShow) { flowFieldShow = false }
  }
  if (open && ability === 'apportion') {
    return true
  }
  return flowFieldShow
}

const formItemStyle = (field: any, validateError: [string], hiddenFields = []) => {
  const flowFieldShow = getShow(field, validateError, hiddenFields)
  if (flowFieldShow) {
    return { margin: 0, width: 220 }
    //pointerEvents: "none"  //不能点击
  } else {
    return { margin: 0, minWidth: 120, height: 0, overflow: 'hidden', padding: 0 }
  }
}

const itemInnerStyle = { width: '100%' }

const formatCellValue = (row, components, dataIndexPrefix) => {
  const prefix = dataIndexPrefix ? dataIndexPrefix.slice(0, -1) : ''
  const vk = Object.keys(prefix ? row[prefix] : row)
  vk.forEach(item => {
    const curr = components.find(i => i.name === item)
    const key = dataIndexPrefix && item !== 'feeTypeId' ? `${dataIndexPrefix}${item}` : item
    const v = get(row, key)
    if (curr?.type === 'date') {
      const { withTime, dateTimeType } = curr
      set(row, key, formatWithTime(withTime, v, dateTimeType))
    }
    if (curr?.type === 'dateRange') {
      const { withTime, dateTimeType } = curr
      if (!v) {
        set(row, key, undefined)
      } else {
        let start = v?.[0] || v?.start
        let end = v?.[1] || v?.end
        start = start ? formatWithTime(withTime, start, dateTimeType) : start
        end = end ? formatWithTime(withTime, end, dateTimeType) : end
        set(row, key, { start, end })
      }
    }
  })
  return row
}

const formatPayeeInfoField = (props, value) => {
  const { field: fieldInfo, multiplePayeesMode, isRecordExpends, noDefault = false } = props
  const { isFeeDetail = false, source, forbidEdit } = fieldInfo

  let placeholder = getPlaceholder(fieldInfo)
  const disabled = isDisable(props)
  let fakeInputDisabled = multiplePayeesMode ? (isFeeDetail ? disabled : multiplePayeesMode) : disabled
  if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled) {
    fakeInputDisabled = multiplePayeesMode && disabled
  } else if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled === false) {
    fakeInputDisabled = multiplePayeesMode && !disabled
  } else if (multiplePayeesMode && source && source === 'dataLink') {
    fakeInputDisabled = multiplePayeesMode && disabled
  }
  let placeholderValue =
    !isFeeDetail && multiplePayeesMode && source !== 'dataLink' ? i18n.get('多收款人') : placeholder
  let payeeValue = value
  if (multiplePayeesMode && !isFeeDetail && source !== 'dataLink' && !isRecordExpends) {
    payeeValue = i18n.get('多收款人')
  } else if (!multiplePayeesMode && !value && !noDefault) {
    payeeValue = api.getState('@common.defaultPayee')
  }
  if (isRecordExpends) {
    fakeInputDisabled = false
    payeeValue = value
    placeholderValue = placeholder
  }
  if (forbidEdit !== undefined && forbidEdit) {
    fakeInputDisabled = true
  }
  return { fakeInputDisabled, placeholderValue, payeeValue }
}

const formatRefOrganizationStaffField = (value, props) => {
  const staffDisplayConfig = api.getState('@common').organizationConfig.staffDisplayConfig
  const {
    ownerId,
    field: { field }
  } = props
  let myValue = ''
  if (value && value.name) {
    const showValue = getStaffShowValue(value, staffDisplayConfig)
    myValue = `${value.name}${showValue}`
  }
  const userName = get(ownerId, 'name', '')
  if (myValue && userName && field === 'submitterId' && value.name !== userName) {
    myValue = i18n.get('generation-submit', { __k0: myValue, __k1: userName })
  }
  return { myValue }
}

const formatListRefOrganizationStaffField = value => {
  const staffDisplayConfig = api.getState('@common').organizationConfig.staffDisplayConfig
  let myValue = ''
  let users = []
  if (staffDisplayConfig.length < 2) {
    users =
      isArray(value) && value?.length > 0 ? value.filter(item => !!item).map(item => item.name) : [value?.name || '']
  } else {
    users =
      isArray(value) && value?.length > 0
        ? value
          .filter(item => !!item)
          .map(line => {
            const showValue = getStaffShowValue(line, staffDisplayConfig)
            return `${line.name}${showValue}`
          })
        : [value?.name || '']
  }
  myValue = users.join(',')
  return { myValue }
}

const formatRefDataId = (value = {}) => {
  let checkedId = []
  if (typeof value === 'string') {
    value = {
      id: value
    }
  } else if (value instanceof Array) {
    let arr = []
    value.length < 1
      ? (checkedId = [])
      : value.forEach(v => {
        arr.push(v.id || v)
        checkedId = arr
      })
  }
  // @ts-ignore
  const id = value?.id
  return { id, checkedId }
}

const formatDataLink = data => {
  data = isArray(data) ? data : [data]
  const value = data.map(item => {
    const d = get(item, 'data')
    const titleField = get(item, 'template.content.expansion.title.fields')
    const title = getValue(titleField, d) || item?.name
    return title
  })
  return value.join('、')
}

export {
  formItemStyle,
  getShow,
  itemInnerStyle,
  formatCellValue,
  formatPayeeInfoField,
  formatRefOrganizationStaffField,
  formatListRefOrganizationStaffField,
  formatRefDataId,
  formatDataLink
}

const showLabel = true
export function getFormItemLabel(label: string) {
  if (showLabel) {
    return { label: label }
  } else {
    return {}
  }
}

export const updateCellValue = debounce((field, value, record, tableVM, form) => {
  record?.rowManager?.onCellChange(field, value, tableVM, form)
}, 500)

export const fnGetValidExternal = (external, detail) => {
  //获取到对应该条分摊明细的超标提醒
  if (!external) return void 0
  let validExternal = void 0
  for (let key in external) {
    if (key === detail.feeTypeForm.detailId) {
      validExternal = external[key]
    }
  }
  return validExternal?.['invoiceForm']
}

export const isDialog = (value, dimentionCurrency) => {
  //判断金额组件是展示对话框还是直接展示input
  const isForeign = !!value?.foreignStrCode //金额里面有外币的情况
  const isBudget = !!value?.budgetStrCode //金额里面有预算币
  return isForeign || isBudget || standSelectAble(dimentionCurrency)
}
const standSelectAble = dimentionCurrency => {
  const allCurrencyRates = api.getState()['@common'].allCurrencyRates
  const standardCurrency = api.getState()['@common'].standardCurrency
  if (dimentionCurrency) {
    //法人实体多币种选择了就用法人实体多币种对应的外币列表
    return dimentionCurrency?.rates?.length
  } else {
    const rates = allCurrencyRates?.filter(i => i?.originalId === standardCurrency?.numCode)
    //否则取当前企业的多币种列表
    return rates?.length
  }
}
export function getMoneyInfo(value, prefix) {
  if (!value) {
    return undefined
  }
  const title = value[`${prefix}StrCode`]
  const info = value[`${prefix}`]
  if (title) {
    return `${title} ${info}`
  }
  return undefined
}
