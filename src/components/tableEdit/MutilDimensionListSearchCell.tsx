import React, { useEffect, useState, ReactElement } from 'react';
import styles from './Popover.module.less'
import { cloneDeep } from 'lodash';
import { toJS } from 'mobx'
import { app as api } from '@ekuaibao/whispered'
import { Tooltip } from '@hose/eui';
const MutilDimensionListSearchCell = ({ value, field }) => {
    const [showText, setShowText] = useState<ReactElement[] | string>()
    const initDimension = () => {
        if (value && value?.length) {
            let val = null
            try {
                val = cloneDeep(value).map(v => {
                    if (typeof v !== 'string') {
                        return v.id
                    }
                    return v
                })
            } catch (e) {
                val = cloneDeep(toJS(value)).map(v => {
                    if (typeof v !== 'string') {
                        return v.id
                    }
                    return v
                })
            } finally {
                if (!Array.isArray(val)) {
                    val = []
                }
            }
            if (val.length > 300) {
                const promiseArr = []
                for (let index = 0; index < Math.ceil(val.length / 300); index++) {
                    const list = []
                    for (let j = index * 300; j < 300 * (index + 1); j++) {
                        const element = val[j]
                        list.push(element)
                    }
                    promiseArr.push(api.invokeService('@bills:get:dimension', list.join(',')))
                }
                Promise.all(promiseArr).then((res: any) => {
                    let result = []
                    res.forEach(element => {
                        result = result.concat(element.items)
                    })
                    getShowText(result)
                })
            } else {
                api.invokeService('@bills:get:dimension', val.join(',')).then((res: any) => {
                    getShowText(res.items)
                })
            }
        }

    }

    const getShowText = (dimension) => {
        if (dimension.length) {
            const showText = dimension.map((item: any, idx: number) => (
                <span>
                    {!field?.hideCode ? ` ${item.name}(${item.code}) ` : ` ${item.name}`}
                    {idx !== dimension.length - 1 && '、'}
                </span>
            ))
            setShowText(showText)
        }
    }

    useEffect(() => {
        initDimension()
    }, [])
    return (
        <div className={styles.tableReadonlyWidth}>
            {showText ? <Tooltip title={showText}>{showText}</Tooltip> : i18n.get('无')}
        </div>
    );
};

export default MutilDimensionListSearchCell;