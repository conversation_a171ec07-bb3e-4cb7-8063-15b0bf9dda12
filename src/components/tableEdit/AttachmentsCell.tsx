import React, { FC, memo } from 'react'
import { AttachmentIF, fetchAttachment } from '../utils/FetchAttachment'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { BasePureComponent } from '../dynamic/BasePureComponent'
import styles from './Popover.module.less'
import {
  OutlinedDataPpt,
  OutlinedDataExcel,
  OutlinedDataWord,
  OutlinedDataText,
  OutlinedDataOtherfile,
  OutlinedDataZip,
  OutlinedDataPdf,
  OutlinedDataVideo,
  OutlinedDataAudio,
  OutlinedDataImage,
  OutlinedDataOfd
} from '@hose/eui-icons'
import { isEqual } from 'lodash'
import { Popover } from 'antd'
const videoRegExp = new RegExp('(.|/)(flv|f4v|mkv|rm|rmvb|mpg|mpeg|mpeg4|wmv|3gp|mp4|mov|avi|asx|asf|ts|qt)$', 'gi')
const audioRegExp = new RegExp('(.|/)(opus|flac|webm|weba|wav|ogg|m4a|mp3|oga|mid|amr|aiff|wma|au|aac)$', 'gi')
const imageRegExp = new RegExp('(.|/)(xbm|tif|pjp|svgz|jpg|jpeg|ico|tiff|gif|svg|jfif|webp|png|bmp|pjpeg|avif)$', 'gi')
const pdfRegExp = new RegExp('(.|/)(pdf)$', 'gi')
const wordRegExp = new RegExp('(.|/)(doc|docx)$', 'gi')
const pptRegExp = new RegExp('(.|/)(ppt|pps|pptx)$', 'gi')
const excelRegExp = new RegExp('(.|/)(xls|xlsx)$', 'gi')
const zipRegExp = new RegExp('(.|/)(7z|rar|zip|apz|ar|bz|car|dar|cpgz|ha|f|hbc|hbc2|hbe|hpk|hyp)$', 'gi')
const textRegExp = new RegExp('(.|/)(txt)$', 'gi')
const ofdRegExp = new RegExp('(.|/)(ofd)$', 'gi')

export default class AttachmentsCell extends BasePureComponent {
  constructor(props) {
    super(props)
    this.state = {
      value: this.props.value
    }
  }

  componentDidMount(): void {
    this.formatAttachmentsList(this.props.value)
  }

  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    if (!isEqual(nextProps.value, this.props.value)) {
      this.formatAttachmentsList(nextProps.value)
    }
  }

  formatAttachmentsList = async value => {
    const values = await fetchAttachment(value)
    this.setState({ value: values })
  }

  render() {
    const { showEmpty } = this.props
    const { value } = this.state
    const fileList = fnFormatAttachment(value)
    if (fileList.length === 1) {
      return (
        <div className={styles.tableReadonlyWidth}>
          {fileList.map((item) => (
            <AttachmentItem key={item.id} file={item} />
          ))}
        </div>
      )
    } else if (fileList.length === 0) {
      if (showEmpty) {
        return null
      }
      return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
    } else {
      const content = (
        <div className={styles.popoverContainer}>
          {fileList.map((item) => (
            <AttachmentItem key={item.id} file={item} />
          ))}
        </div>
      )
      return (
        <div className={styles.tableReadonlyWidth}>
          <Popover placement="topLeft" content={content}>
            {fileList.slice(0, 5).map((item, index) => (
              <RenderIcon key={index} fileName={item.fileName} />
            ))}
            <span style={{ marginLeft: '10px' }}>共{fileList.length}个附件</span>
          </Popover>
        </div>
      )
    }
  }
}

const AttachmentItem: FC<{ file: AttachmentIF }> = props => {
  const { fileName, key, url, fileId } = props.file
  return (
    <div className={styles.fileItem}>
      <div>
        <RenderIcon fileName={fileName} />
      </div>
      <span className={styles.fileName}>{fileName}</span>
    </div>
  )
}

const RenderIcon = memo<{ fileName: string }>(({ fileName }) => {
  if (videoRegExp.test(fileName)) {
    return <OutlinedDataVideo fontSize={16} />
  } else if (audioRegExp.test(fileName)) {
    return <OutlinedDataAudio fontSize={16} />
  } else if (imageRegExp.test(fileName)) {
    return <OutlinedDataImage fontSize={16} />
  } else if (pdfRegExp.test(fileName)) {
    return <OutlinedDataPdf fontSize={16} />
  } else if (wordRegExp.test(fileName)) {
    return <OutlinedDataWord fontSize={16} />
  } else if (pptRegExp.test(fileName)) {
    return <OutlinedDataPpt fontSize={16} />
  } else if (excelRegExp.test(fileName)) {
    return <OutlinedDataExcel fontSize={16} />
  } else if (zipRegExp.test(fileName)) {
    return <OutlinedDataZip fontSize={16} />
  } else if (textRegExp.test(fileName)) {
    return <OutlinedDataText fontSize={16} />
  } else if (ofdRegExp.test(fileName)) {
    return <OutlinedDataOfd fontSize={16} />
  } else {
    return <OutlinedDataOtherfile fontSize={16} />
  }
})
