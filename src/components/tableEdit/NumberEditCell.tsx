import React, { FC } from 'react'
import { InputNumber, Form } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { required } from '../validator/validator'
import styles from "./NumberEditCell.module.less"
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { get } from 'lodash'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  value?: any
  vm: any
  form: any
  onChange?: (value: any) => void
  hiddenFields?: string[]
}
const NumberEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, title, record, field: fieldInfo, hiddenFields } = props
  const { editable, dataType = {}, max, min, scale: filedScale = 0 } = fieldInfo
  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const validator = (rule, value, callback) => {
    let re = /^-?[0-9]+$/
    let { scale = 0 } = dataType
    if (filedScale) {
      scale = filedScale
    }
    if (scale) {
      re = new RegExp(`^-?(([1-9]\\d*)|0)(\\.\\d{1,${scale}})?$`)
    }
    if (value || value === 0) {
      let msg = scale ? i18n.get('please-number-format', { scale }) : i18n.get('请输入整数')
      if (!re.test(value)) return callback(msg)
      if (!editable && value * 1 > 10000000000) {
        return callback(i18n.get('cannot-be-greater', { label: title, max: 10000000000 }))
      }
      if (!editable && value * 1 < -10000000000) {
        return callback(i18n.get('cannot-be-less', { label: title, min: -10000000000 }))
      }
      if (editable && value * 1 > max * 1) {
        return callback(i18n.get('cannot-be-greater', { label: title, max }))
      }
      if (editable && value * 1 < min * 1) {
        return callback(i18n.get('cannot-be-less', { label: title, min }))
      }
      if (fieldInfo.name === 'min' || fieldInfo.name === 'minLength') {
        let fieldName = fieldInfo.name === 'min' ? 'max' : 'maxLength'
        let max = props.form.getFieldValue(fieldName)
        if (max && value && value * 1 > max * 1) {
          return callback(i18n.get('最小值不能大于最大值'))
        }
      }
    }
    if (value === 0) {
      return callback()
    }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(<InputNumberWrapper {...props} />)}
    </FormItem>
  )
}
const InputNumberWrapper: FC<IProps> = props => {
  const { record, field: fieldInfo, onChange, value, vm, form } = props
  const { optional, dataType = {}, unit: unitFiled = '', step: stepField } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const unit = dataType.unit || unitFiled
  const step = dataType.scale ? 1 / Math.pow(10, dataType.scale) : stepField
  const disabled = isDisable(props)
  const handleOnChange = value => {
    onChange?.(value)
    updateCellValue(fieldInfo?.field, value, record, vm, form)
  }
  return (
    <div className={styles.number_wrap}>
      <div className="number">
        <InputNumber
          value={value}
          style={itemInnerStyle}
          placeholder={placeholder}
          size='large'
          onChange={handleOnChange}
          step={step}
          disabled={disabled}
        />
        {unit && <span className="unit-text">{unit}</span>}
      </div>
    </div>
  )
}
export default NumberEditCell
