import React, { Component, FC } from 'react'
import { Form, Input } from 'antd'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue, isDialog } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required, validatorMoney } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { cloneDeep, get, isObject } from 'lodash'
import { fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import { EnhanceConnect } from '@ekuaibao/store'
import { isEmpty } from '@ekuaibao/helpers'
import { moneyStrToStandardCurrencyMoney } from '@ekuaibao/money-math'
import CurrencyMoney from '../../elements/currency/currency-money'
import { Tooltip } from '@hose/eui'
import { getMoneyText } from './MoneyCell'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  billSpecification: any
  form: any
  vm: any
  hiddenFields?: string[]
}

const MoneyEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, form, bus, vm, hiddenFields } = props
  const dimentionCurrency = api.getState()['@bills'].dimentionCurrencyInfo
  if (!fieldInfo) {
    return null
  }
  let { optional, max, min, name } = fieldInfo
  const label = getLabel(fieldInfo)
  const validator = (rule, value, callback) => {
    const type = get(fieldInfo, 'defaultValue.type')
    if (type === 'invoiceSum') {
      const { invoices } = form.getFieldValue('invoiceForm') || {}

      if (!optional && (!invoices || !invoices.length)) {
        return callback(i18n.get('该字段需根据导入的发票自动计算，请添加发票'))
      }
    }
    if (vm.billSpecification) {
      let chargeAgainst = vm.billSpecification.configs.find(v => v.ability === 'chargeAgainst')
      if (chargeAgainst && chargeAgainst.isChargeAgainst) {
        min = min * 1 < 0 ? min : -max
      }
    }
    if (value && value.standard) {
      const result = validatorMoney(value, max, min, callback, fieldInfo)
      if (name === 'contractAmount' && bus.$_settleAmountTotal) {
        if (Number(value?.standard || 0) < bus.$_settleAmountTotal) {
          return callback('合同金额不能小于结算总额')
        }
      }
      return result ?? callback(required(fieldInfo, value))
    }
    if (!optional && (!value || value.standard === undefined)) {
      const errStr = i18n.get('not-empty', { label: i18n.get(label) })
      return callback(errStr)
    }
    callback(required(fieldInfo, value))
  }
  const renderContent = () => {
    if (isDialog(get(record, dataIndex), dimentionCurrency)) {
      //如果能切换多币种的话就用对话框的方式填写金额
      return <InputWrapper {...props} />
    } else {
      //否则直接引用金额组件
      return <MoneyEditCellWrapper {...props} />
    }
  }

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: get(record, dataIndex)
      })(renderContent())}
    </FormItem>
  )
}
class InputWrapper extends Component<any, any> {
  handleClick = () => {
    const { onChange, field, record, vm, form } = this.props
    api.open('@bills:EditMoneyModal', { params: { ...this.props } }).then(res => {
      onChange?.(res)
      updateCellValue(field?.field, res, record, vm, form)
    })
  }

  render() {
    const { value, field } = this.props
    const placeholder = getPlaceholder(field)
    const disabled = isDisable(this.props)
    return (
      <Tooltip title={getMoneyText(value)}>
        <Input
          size="large"
          addonBefore={value?.foreignStrCode || value?.standardStrCode || 'CNY'} //有原币的时候展示原币，没有原币的时候展示本位币
          value={value?.foreignStrCode ? value?.foreign : value?.standard}
          style={itemInnerStyle}
          placeholder={placeholder}
          disabled={disabled}
          onClick={this.handleClick}
          onFocus={e => {
            e.target.blur()
          }}
        />
      </Tooltip>
    )
  }
}
@EnhanceConnect(state => {
  return {
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    standardCurrency: state['@common'].standardCurrency,
    historyCurrencyInfo: state['@bills'].historyCurrencyInfo,
    allCurrencyRates: state['@common'].allCurrencyRates
  }
})
class MoneyEditCellWrapper extends Component<any, any> {
  constructor(props) {
    super(props)
    const data = this.moneyStr2StandardMoneyValue('')
    this.state = { data }
  }

  componentWillReceiveProps(nextProps) {
    const { isDetail = true, value, dimentionCurrencyInfo } = this.props
    if (isDetail && nextProps?.value && nextProps?.value !== value) {
      this._postEvent(nextProps.value)
    }
    if (dimentionCurrencyInfo?.currency?.id !== nextProps?.dimentionCurrencyInfo?.currency?.id) {
      this.initValue(nextProps?.dimentionCurrencyInfo?.currency)
    }
  }

  moneyStr2StandardMoneyValue = money => {
    const standCurrency = api.getState('@common.standardCurrency')
    return moneyStrToStandardCurrencyMoney(money, standCurrency)
  }

  preGetValue = () => {
    const { value } = this.props
    if (value && typeof value === 'object' && value.standard && !value.standardStrCode) {
      return this.moneyStr2StandardMoneyValue(value.standard)
    }
    return value
  }

  _valueChange = ({ isValidator = true, data }) => {
    const { getExpenseStandardItemsLength, external } = this.props
    if (!isNaN(Number(data.standard)) && Number(data.standard) !== Number(this.state.data.standard)) {
      this.setState({ data })
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
    getExpenseStandardItemsLength && this.getExpenseStandardItemsLength(data)
    if (isValidator) return this.setValue(data)
    this._postEvent(data)
  }

  getExpenseStandardItemsLength = data => {
    let { getExpenseStandardItemsLength } = this.props
    let { standard = 0 } = data
    let re = new RegExp(`^(-?([1-9]\\d*)|0)(\\.\\d{1,2})?$`)
    if (re.test(standard)) {
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  handleChange = val => {
    const { onChange, field, record, vm, form } = this.props
    onChange?.(val)
    updateCellValue(field?.field, val, record, vm, form)
  }

  setValue = val => {
    let { field } = this.props
    if (val && typeof val === 'object' && !val.standard && field.editable) {
      //选填的时候如果standard为空后台会报错，所以当standard为空的时候清空数据，但是自动计算的话不能清空所以加一个field.editable条件
      val = undefined
    }
    this._postEvent(val)
    this.handleChange(val)
  }

  _postEvent = (val?) => {
    const { field, bus, isDetail } = this.props
    if (field.name === 'loanMoney' || field.name === 'payMoney') {
      bus && bus.emit('loanMoney:changed', val)
    }
    // 分摊指定金额 不再是由amount 或者 apportionMoney 驱动
    // if (!!~['amount', 'apportionMoney'].indexOf(field.name)) {
    isDetail && bus && bus.invoke('amount:changed', { field: field.name, amount: val }).catch(e => {})
    // }
  }

  async componentWillMount() {
    let {
      bus,
      field: { field: fieldName },
      value
    } = this.props
    !!~['amount', 'loanMoney'].indexOf(fieldName) && bus.on('money:changed:value', this.setValue)
    if (!!~['amount'].indexOf(fieldName)) bus && bus.invoke('amount:changed', value).catch(e => {})
    bus.on('details:change', this.onDetailsChange)
    bus.on('trip:change', this.onDetailsChange)
    bus.on('money:tax:update:value', this.onTaxChange)
    bus.on('detail:amount:change', this.handleDetailAmountChange)
    await Promise.all([
      api.dataLoader('@common.currencyConfig').load(),
      api.invokeService('@currency-manage:get:currency:modifiable'),
    ])
  }

  componentWillUnmount() {
    let {
      bus,
      field: { field: fieldName }
    } = this.props
    !!~['amount', 'loanMoney'].indexOf(fieldName) && bus.un('money:changed:value', this.setValue)
    bus.un('details:change', this.onDetailsChange)
    bus.un('trip:change', this.onDetailsChange)
    bus.un('money:tax:update:value', this.onTaxChange)
    bus.un('detail:amount:change', this.handleDetailAmountChange)
  }

  handleDetailAmountChange = amount => {
    const { onChange, field, isDetail } = this.props
    if (field.field === 'amount' && isDetail) {
      onChange && this.handleChange(standardValueMoney(amount))
    }
  }

  async componentDidMount() {
    const { dimentionCurrencyInfo } = this.props
    this.initValue(dimentionCurrencyInfo?.currency)
    await api.dataLoader('@common.allCurrencyRates').reload()
  }

  componentDidUpdate() {
    this.delayTodo && this.delayTodo()
  }

  initValue = currency => {
    const { originalValue = {}, onChange } = this.props
    const defaultValue = get(this, 'props.field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      const details = originalValue.details || originalValue.trips
      let sumValue = standardValueMoney(0, currency)
      const { standardScale } = sumValue
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          const defVal = isObject(form[defaultValue.value])
            ? form[defaultValue.value].standard
            : form[defaultValue.value]
          if (defVal) {
            sumValue.standard = new Big(sumValue.standard).plus(Number(defVal)).toFixed(standardScale)
          }
        })
      onChange && this.handleChange(sumValue)
      this._postEvent()
    }
  }

  delayTodo = null
  onTaxChange = () => {
    const { field } = this.props
    const type = get(field, 'defaultValue.type')

    if (type === 'invoiceSum') {
      this.delayTodo = () => {
        this.setValue(undefined)
        this.delayTodo = null
      }
    }
  }

  onDetailsChange = details => {
    const { onChange, dimentionCurrencyInfo } = this.props
    const defaultValue = get(this.props, 'field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      let data = standardValueMoney(0, dimentionCurrencyInfo?.currency)
      const { standardScale } = data
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          if (form[defaultValue.value]) {
            data.standard = new Big(data?.standard || 0)
              .plus(Number(form[defaultValue.value]?.standard || 0))
              .toFixed(standardScale)
          }
        })
      this.handleChange(data)
      this.setState({ details })
      this._valueChange({ isValidator: true, data })
    }
  }

  isSelectCurrencyDisable = () => {
    const { field, selectCurrencyDisable, foreignCurrencyLoan } = this.props
    const fieldName = field?.field
    return fieldName === 'amount' || fieldName === 'companyRealPay' || !!foreignCurrencyLoan
      ? selectCurrencyDisable
      : false
  }
  render() {
    // currencySelAble 是否可以修改币种，分摊时不能修改币种
    // cannotEditAmountField 不可编辑金额相关字段；仅在审批中修改时的费用明细中有取值为true的可能
    const {
      value,
      field,
      autoCalFields,
      form,
      bus,
      currencySelAble = true,
      external,
      isForbid,
      isModify,
      cannotEditAmountField,
      foreignCurrencyLoan,
      foreignNumCode,
      billSpecification = {},
      isManualRepayment = false,
      showAllFeeType,
      template,
      currencySwitch,
      shouldSaveFeetype,
      standardCurrency,
      dimentionCurrencyInfo,
      historyCurrencyInfo,
      allCurrencyRates
    } = this.props
    let { field: fieldName } = field
    let placeholder = getPlaceholder(field)
    const { configs = [] } = billSpecification
    const isAuto = fnDefineIsFormula(fieldName, autoCalFields, field)
    placeholder = isAuto && !isEmpty(value) ? i18n.get('自动计算生成') : placeholder
    let disabled = isDisable(this.props)
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disabled && cannotEditAmountField) disabled = true
    const writtenOff = configs.find(item => item.ability === 'writtenOff')
    let dimentionCurrency = cloneDeep(dimentionCurrencyInfo)
    const historyCurrencyNumCode = get(historyCurrencyInfo, 'currency')
    const dimentionCurrencyNumCode = get(dimentionCurrencyInfo, 'currency.numCode')
    if (historyCurrencyNumCode && dimentionCurrencyNumCode && historyCurrencyNumCode === dimentionCurrencyNumCode) {
      dimentionCurrency.rates = historyCurrencyInfo.rates
    }
    let allCurrency = cloneDeep(allCurrencyRates)
    if (historyCurrencyNumCode) {
      allCurrency = historyCurrencyInfo.rates
    } else {
      allCurrency = allCurrency.filter(i => i.originalId === standardCurrency?.numCode)
    }
    return (
      <div>
        <CurrencyMoney
          foreignCurrencyLoan={foreignCurrencyLoan}
          isManualRepayment={isManualRepayment}
          foreignNumCode={foreignNumCode}
          writtenOff={writtenOff}
          selectCurrencyDisable={this.isSelectCurrencyDisable()}
          value={value}
          form={form}
          field={field}
          disabled={isAuto || disabled}
          placeholder={placeholder}
          isAuto={isAuto}
          bus={bus}
          valueChange={this._valueChange}
          currencySelAble={currencySelAble}
          external={external}
          isEdit={true}
          isForbid={isForbid}
          template={template}
          currencySwitch={currencySwitch}
          shouldSaveFeetype={shouldSaveFeetype}
          standardCurrency={standardCurrency}
          dimentionCurrency={dimentionCurrency}
          allCurrencyRates={allCurrency}
          showLabel={false}
        />
      </div>
    )
  }
}

export default MoneyEditCell
