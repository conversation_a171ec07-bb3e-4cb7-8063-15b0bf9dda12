import React, { FC } from 'react'
import { Form } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { required } from '../validator/validator'
import { formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { get } from 'lodash'
import EnumEditCellInner from '../../elements/puppet/RefEnum'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  vm:any
  field: any
  form:any
  hiddenFields?: string[]
}

const EnumEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo,vm ,form, hiddenFields} = props
  if (!fieldInfo) {
    return null
  }
  const { optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const disabled = isDisable(props)
  const value = get(record, dataIndex)
  const entity = get(fieldInfo, 'dataType.entity', '')
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  const updateCell = value => {
    updateCellValue(fieldInfo?.field, value, record,vm,form)
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: value
      })(
        <EnumEditCellInner
          {...props}
          disabled={disabled}
          entity={entity}
          style={itemInnerStyle}
          placeholder={placeholder}
          updateCell={updateCell}
          optional={optional}
        />
      )}
    </FormItem>
  )
}

export default EnumEditCell
