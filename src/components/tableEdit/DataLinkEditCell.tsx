import React, { Component, FC, useEffect, useState } from 'react'
import { Form, Input } from 'antd'
import { formatDataLink, formItemStyle, itemInnerStyle, getFormItemLabel, updateCellValue } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { get } from 'lodash'
import { showModal } from '@ekuaibao/show-util'
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  hiddenFields?: string[]
}

const DataLinkEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any>()
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  const data = get(record, dataIndex)
  const initState = () => {
    let id = ''
    if (data) {
      id = typeof data === 'string' ? data : data.id || get(data, 'data.dataLink.id')
    }
    if (!id) {
      setValue(data)
    } else {
      api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(res => {
        setValue({ ...res.value, id: id })
      })
    }
  }
  useEffect(() => {
    initState()
  }, [data])

  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const { optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  placeholder = optional ? i18n.get('(选填)') + placeholder : placeholder
  const disabled = isDisable(props)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: data
      })(<InputWrapper {...props} objValue={value} placeholder={placeholder} disabled={disabled} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      islock: false,
      ledgerLockList: []
    }
  }
  handleClick = () => {
    const {
      bus,
      flowId,
      external,
      objValue:value,
      field: { allMatchList, referenceData, label, filterId, dependence, allowCancelDependence, field }
    } = this.props
    const { islock, ledgerLockList, dependenceParams } = this.state
    const groupType = get(referenceData, 'platformId.groupType')
    const type = get(referenceData, 'platformId.type')
    fnCheckNeedPhone()({ groupType, type }).then(res => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal', { type }).then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }
      return bus
        ?.invoke('element:select:dataLink', {
          referenceData,
          flowId,
          selectedEntity: value,
          islock,
          ledgerLockList,
          filterId,
          dataLink: { id: referenceData.id, selectedEntity: value, type, name: label },
          dependenceParams: dependence && dependence.length ? dependenceParams : undefined,
          allowCancelDependence: allowCancelDependence,
          allMatchList,
          field
        })
        .then(result => {
          const { data } = result
          this.getDataLinkTempById(data.dataLink.id)
          //TODO 这个是什么功能？？
          // this.setTripBasedata(data, type)
          // emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
        })
    })
  }

  getDataLinkTempById = (id) => {
    const { onChange, field, record,vm ,form} = this.props
    if (!id) {
      onChange && onChange()
      updateCellValue(field?.field, undefined, record,vm, form)
      return
    }
    api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(async res => {
      onChange && onChange({ ...res.value, id: id })
      updateCellValue(field?.field, { ...res.value, id: id }, record,vm, form)
    })
  }

  // 将明细中的坐席赋值
  setTripBasedata = (data, type) => {
    let { isDetail, form } = this.props
    let tripType = get(data, 'dataLink.entity.type')
    let id = get(data, 'dataLink.entity.parentId')
    if (isDetail && type == 'TRAVEL_MANAGEMENT' && id && (tripType == 'FLIGHT' || tripType == 'TRAIN')) {
      let name = this.getTemplateTrip(tripType)
      let value = this.getDataLinkValue(data, id, tripType)
      if (name && value) {
        let obj = {}
        obj[name] = value
        form && form.setFieldsValue(obj)
      }
    }
  }
  getTemplateTrip = type => {
    let { template = [] } = this.props
    const Map = {
      TRAIN: 'ref:basedata.Enum.TrainSeatType',
      FLIGHT: 'ref:basedata.Enum.CabinType'
    }
    let name = ''
    template &&
      template.forEach(i => {
        if (i && i.type && i.type == Map[type]) {
          name = i.name
        }
      })
    return name
  }
  getDataLinkValue = (data, id, tripType) => {
    let value = ''
    // @i18n-ignore
    const F = {
      经济舱: 'ECONOMY',
      商务舱: 'BUSINESS',
      头等舱: 'FIRST'
    }
    // @i18n-ignore
    const T = {
      硬座: 'YZ',
      软座: 'RZ',
      硬卧: 'YW',
      软卧: 'RW',
      高级软卧: 'GJRW',
      一等座: 'YD',
      二等座: 'ED',
      商务座: 'SW',
      高铁动卧: 'DW'
    }
    if (tripType == 'FLIGHT') {
      value = get(data, `dataLink.E_${id}_航班舱型`)
      value = value ? F[value] : ''
    } else if (tripType == 'TRAIN') {
      value = get(data, `dataLink.E_${id}_火车坐席`) || get(data, `dataLink.E_${id}_火车席位`)
      value = value ? T[value] : ''
    }
    return value
  }
  render() {
    const { disabled, objValue:value, placeholder } = this.props
    const v = formatDataLink(value)
    return (
      <Input
        disabled={disabled}
        style={itemInnerStyle}
        placeholder={placeholder}
        value={v}
        onClick={this.handleClick}
        onFocus={e => {
          e.target.blur()
        }}
      />
    )
  }
}

export default DataLinkEditCell
