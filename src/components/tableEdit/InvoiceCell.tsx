import React from 'react'
import styles from './Popover.module.less'
import { app } from '@ekuaibao/whispered'
import { canModifyApproveMoney } from '../../plugins/bills/util/parse'
import { invoiceFormMap } from '../../elements/puppet/details/FormatDateUtils'
import { toJS } from 'mobx'
import { fnGetValidExternal } from './utils'
const InvoiceCell = ({ field, text, vm, bus, record }) => {
  const handleClick = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const params = {
      bus,
      value: toJS(text),
      record,
      external: fnGetValidExternal(vm?.props?.external, record),
      billSpecification: vm.billSpecification,
      isForbid: vm?.props?.isForbid,
      field,
      detailId: record?.feeTypeForm?.detailId,
      submitterId: vm?.submitterId,
      feeDetailId: vm?.props?.feeDetailId,
      showAllFeeType: vm?.props?.showAllFeeType,
      billTemplate: vm?.props?.billTemplate,
      template: record?.rowManager?.template,
      isModify: vm?.props?.isModify,
      billState: vm?.billState,
      flowId: vm?.flowId,
      feeTypes: vm?.feeTypes,
      modifyApproveMoney: canModifyApproveMoney(vm.billSpecification),
      isRecordExpends: vm?.props?.isRecordExpends,
      isQuickExpends: vm?.props?.isQuickExpends,
      riskData: toJS(vm?.props?.riskData),
      details: toJS(vm?.tableDataSource)
    }
    app.open('@bills:InvoiceSelectReadonlyModal', params)
  }

  if (text?.type === 'exist') {
    return (
      <div className={styles.tableReadonlyWidth} onClick={handleClick}>
        <a>{`${invoiceFormMap()[text?.type]}`}</a>
      </div>
    )
  }
  return (
    <div className={styles.tableReadonlyWidth}>
      <span>{`${invoiceFormMap()[text?.type]}`}</span>
    </div>
  )
}

export default InvoiceCell
