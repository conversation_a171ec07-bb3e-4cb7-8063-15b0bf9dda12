import React, { CSSProperties, PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { UIContainer as Container } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import ApportionsHeader from '../../elements/puppet/ApportionsHeader'
import { showModal, showMessage } from '@ekuaibao/show-util'
import styles from './MoneyModal.module.less'
import { MoneyMath } from '@ekuaibao/money-math'
import Big from 'big.js'
import { getMoney, fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import { isDisable } from '../utils/fnDisableComponent'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { formatApportionDateTime } from '../utils/fnDetailsApportion'
import { ApportionContext } from '../dynamic/types'
import { fnHideFieldsNote } from '../utils/fnHideFields'
import { fnCheckApportionOtherConfig, fnValidateMoneyItemEqual } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { Icon } from 'antd'
import { Button } from '@hose/eui'
import { toJS } from 'mobx'
const fnDefineIsFromThirdParty = (template: any[]) => {
  return template.filter(t => t.editable === false && t.isFromThirdParty).length > 0
}
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data
}))
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
class ApportionsModal extends PureComponent<any, any> {
  // 当前组件的值，通过 formBus 拿到
  currentValue = undefined
  formBus = new MessageCenter()

  constructor(props: any) {
    super(props)
    const { value, isModify, showAllFeeType, cannotEditAmountField, feeAmount } = props
    const checked = value ? !!value.length : false
    let disable = isDisable(props)
    let isEdit = true
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disable && cannotEditAmountField) {
      disable = true
      isEdit = false
    }
    this.state = {
      feeAmount: feeAmount,
      apportionFieldError: '',
      checked,
      checkedSource: 'manual', // check 值的来源
      disable,
      isEdit,
      apportionTemplateAmountField: 'amount',
      apportionfLebal: '',
      apportions: value
    }
  }

  _contextValue = undefined
  get contextValue() {
    const { form } = this.props
    const { _contextValue } = this

    if (_contextValue === form) {
      return this._contextValue
    } else {
      const result = { form }
      this._contextValue = result
      return result
    }
  }

  //框架提供的
  preGetValue = validate => {
    if (this.formBus.has('get:apportions:values')) {
      return this.formBus.invoke('get:apportions:values', validate)
    }
    return undefined
  }

  componentWillMount() {
    let { bus } = this.props
    bus.on('@form:did:mount', this.setMoney)
    bus.watch('amount:changed', this.handleFeeAmountChanged)
    bus.watch('update:feeType:amount:save', this.handleFeeAmountUpdated)
    bus.on('continue:add:detail', this.handleContinueAdd)
    bus.on('exist:apportion:empty:item', this.checkEmptyItem)
    this.formBus.watch('update:feeType:amount', this.handleUpdateFeeTypeChanged)
    this.formBus.watch('apportion:template:change', this.apportionTemplateChange)
    this.formBus.on('apportion:update:error:msg', this.handleUpdateErrorMsg)
    this.formBus.on('apportion:update:apportions', this.handleFeeApportionsUpdated)
  }

  componentDidMount() {
    const { value, field } = this.props
    // const checked = value ? !!value.length : false
    // this.setState({ checked })
    const { checkedSource } = this.state
    const isAutoChecked = checkedSource === 'auto'

    // 自动计算中是否含有隐藏字段
    // const isHideHasCalc =
    //   field?.configs?.find?.(item => item.ability === 'caculate' && item.property === 'hide') ?? false
    // let isHide = false
    // if (isHideHasCalc) {
    //   // attributeHide 是自动计算出来的隐藏逻辑，如果自动计算没有值，那么默认 hide 是 false，等待自动计算完成才进入清理值的逻辑
    //   if (field?.attributeHide) {
    //     isHide = field?.attributeHide && !fnHideFieldsNote(field.hideVisibility)
    //   }
    // } else {
    //   isHide = field?.hide && !fnHideFieldsNote(field.hideVisibility)
    // }
    /**
     * 隐藏的时候需要清除对应的值
     * 但是 setFieldsValue 会引发这个生命周期，所以需要判断 checked 为 true 的情况下
     */
    // if (isHide && !field?.open && checked) {
    //   api.emit('external:APPORTION:empty')
    //   this.setState({ checked: false, apportions: undefined })
    // }

    if (!field.open && isAutoChecked && !value?.length) {
      // 只有在自动勾选的情况下，才在自动计算后清除对应的 checked 状态
      // 而且一定要是没值的情况
      this.setState({ checked: false })
    } else if (field.open) {
      this.setState({ checked: true, checkedSource: 'auto' })
    }
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('amount:changed', this.handleFeeAmountChanged)
    bus.un('update:feeType:amount:save', this.handleFeeAmountUpdated)
    bus.un('@form:did:mount', this.setMoney)
    bus.un('continue:add:detail', this.handleContinueAdd)
    bus.un('exist:apportion:empty:item', this.checkEmptyItem)
    this.formBus.un('update:feeType:amount', this.handleUpdateFeeTypeChanged)
    this.formBus.un('apportion:update:error:msg', this.handleUpdateErrorMsg)
    this.formBus.un('apportion:update:apportions', this.handleFeeApportionsUpdated)
  }

  handleFeeApportionsUpdated = () => {}

  checkEmptyItem = () => {
    this.formBus.emit('exist:apportion:empty:item')
  }

  setMoney = field => {
    let { form } = this.props
    const value = form?.getFieldsValue()?.['feeTypeForm']
    field && this.setState({ apportionTemplateAmountField: field })
    if (field && !(field in value)) {
      this.setState({
        feeAmount: field ? value[field] : value['amount'],
        apportionFieldError: '分摊模版中指定金额不存在'
      })
      return
    }
    this.setState({ feeAmount: field ? value[field] : value['amount'], apportionFieldError: '' })
  }

  /**
   * 费用类型金额改变
   * @param amount
   */
  handleFeeAmountChanged = (res: any = {}) => {
    const { field, amount } = res
    const { apportionTemplateAmountField } = this.state
    if (!amount || apportionTemplateAmountField !== field) return
    this.setState({ feeAmount: amount }, () => {
      this.formBus.emit('total:amount:changed', amount)
    })
  }

  handleFeeAmountUpdated = apportions => {
    const { apportionTemplateAmountField, feeAmount } = this.state
    const { layer } = this.props
    if (!this.state.isEdit) {
      return layer?.emitOk({
        apportions,
        [apportionTemplateAmountField]: feeAmount,
        apportion_amount_Key: apportionTemplateAmountField
      })
    }
    const amount = feeAmount
    let apportionMoneyTotal = apportions?.reduce((sum, line) => {
      return new MoneyMath(sum).add(line.apportionForm.apportionMoney).value
    }, 0)
    let apportionTotalPercent = apportions?.reduce((sum, line) => {
      return new Big(sum).plus(line.apportionForm.apportionPercent || 0)
    }, 0)
    let { isEqual, title } = this.fnValidateEqual(apportionMoneyTotal, apportionTotalPercent, amount, apportions)
    if (!isEqual) {
      showMessage.error(title)
      this.setState({ errorMsg: title })
      return
    }
    return layer?.emitOk({
      apportions: formatApportionDateTime(apportions),
      [apportionTemplateAmountField]: feeAmount,
      apportion_amount_Key: apportionTemplateAmountField
    })
  }

  handleContinueAdd = () => {
    this.setState({ feeAmount: standardValueMoney(0) })
    this.formBus.emit('clear:apportion:data')
  }

  fnValidateEqual = (apportionTotalMoney, apportionTotalPercent, amount, apportions) => {
    let title = '',
      isEqual = false
    const {
      form,
      field: { rule }
    } = this.props
    const { apportionfLebal } = this.state
    if (amount && amount.foreign) {
      isEqual = new Big(getMoney(amount))
        .minus(new Big(getMoney(apportionTotalMoney)))
        .abs()
        .lte(new Big(0.05))
    } else {
      isEqual = new Big(getMoney(apportionTotalMoney)).eq(new Big(getMoney(amount)))
    }

    if (!isEqual) {
      title = i18n.get('费用金额 ≠ 总分摊金额，请修改后重新提交', { lable: apportionfLebal })
      return { title, isEqual }
    }
    isEqual = new Big(apportionTotalPercent.toFixed(2)).eq(100)
    if (!isEqual) {
      title = i18n.get('分摊比例 ≠ 100%，请修改后重新提交')
      return { title, isEqual }
    }
    const itemEqual = fnValidateMoneyItemEqual(apportions, getMoney(apportionTotalMoney))
    if (!itemEqual.zeroPass || !itemEqual.percentMoneyPass || !itemEqual.apportionMoneyZeroPass) {
      if (!itemEqual.apportionMoneyZeroPass) {
        title = i18n.get('存在分摊金额为0的数据，请修改')
      } else if (!itemEqual.zeroPass) {
        title = i18n.get('存在分摊金额和分摊比例都为0的数据,请修改')
      } else {
        title = i18n.get('存在费用金额 * 分摊比例 ≠ 分摊金额的数据,请修改')
      }
      return { title, isEqual: false }
    }
    const length = apportions.length
    // 检查分摊模板其它配置
    const hasOtherConfigErr = fnCheckApportionOtherConfig(apportions)
    if (hasOtherConfigErr) {
      title = hasOtherConfigErr
      return { title }
    }
    rule &&
      rule !== 'PERCENTAGE' &&
      apportions.some((apportion, index) => {
        if (index < length - 2) {
          //最后两行不校验
          const { apportionMoney, apportionPercent } = apportion.apportionForm
          const { standard } = apportionMoney
          const total = getMoney(amount)
          const percent = Number(total) === 0 ? new Big(total) : new Big(standard).div(total)
          isEqual = percent
            .minus(new Big(apportionPercent).div(100))
            .abs()
            .lt(0.01)
          if (!isEqual) {
            title = i18n.get('存在费用金额*分摊比例≠分摊金额的数据，请修正', { lable: apportionfLebal })
            return true
          }
        }
      })

    // 校验其他金额字段
    const apportionSpecification = apportions[0]?.specificationId
    const otherMoneyFields = this.getOtherMoneyFieldsFromSpecification(apportionSpecification, form)
    if (otherMoneyFields?.length) {
      apportions.forEach((apportion, idx) => {
        otherMoneyFields.forEach(el => {
          el.totalAmountCounted = new Big(getMoney(apportion.apportionForm[el.name]))
            .plus(el.totalAmountCounted)
            .toFixed(2)
          if (length - 1 === idx) {
            if (!new Big(getMoney(el.totalAmount)).eq(el.totalAmountCounted)) {
              isEqual = false
              title = `${el.label} ≠ ${el.label}的总分摊金额，请更新后重新提交`
            }
          }
        })
      })
    }
    return { title, isEqual }
  }

  // 取其他金额字段对应的总金额
  getOtherMoneyFieldsFromSpecification = (apportionSpecification, form) => {
    const otherMoneyFields = []
    const apportionComponents = apportionSpecification?.components || []
    apportionComponents.forEach(el => {
      const compConfigs = el?.configs || []
      const otherMoneyField = compConfigs.find(config => config.sourceField === 'otherApportionMoney')
      if (otherMoneyField && form) {
        const obj: any = {}
        obj.totalAmount = form.getFieldValue(otherMoneyField.targetField)
        obj.name = el.field
        obj.label = el.label
        obj.totalAmountCounted = 0
        otherMoneyFields.push(obj)
      }
    })
    return otherMoneyFields
  }

  handleUpdateFeeTypeChanged = amount => {
    const { template, autoCalFields } = this.props
    const { apportionTemplateAmountField } = this.state
    const component = (template || []).find(element => element.field === apportionTemplateAmountField)

    const isAmountCalculate = fnDefineIsFormula(component.field, autoCalFields, undefined)
    const isFromThirdParty = fnDefineIsFromThirdParty(template)
    if (isAmountCalculate || isFromThirdParty) {
      return this.updateApportionsMoney()
    }
    this.setState({ feeAmount: amount })
    let { bus } = this.props
    let obj = {}
    obj[apportionTemplateAmountField] = amount
    bus?.setFieldsValue({ ...obj })
  }

  handleUpdateErrorMsg = () => {
    this.setState({ errorMsg: '' })
  }
  updateApportionsMoney = () => {
    showMessage.error(i18n.get('费用金额不可修改，请修改分摊明细'))
  }

  handleCheckedChanged = (checked: boolean) => {
    if (checked) {
      // 开启费用分摊时  从费用金额取值
      this.setMoney(undefined)
      this.setState({ checked, checkedSource: 'manual' })
    } else {
      showModal.confirm({
        title: i18n.get('关闭费用分摊'),
        content: i18n.get('关闭费用分摊填写的分摊明细会被清空，是否关闭？'),
        cancelText: i18n.get('取消'),
        okText: i18n.get('确定'),
        onOk: () => {
          api.emit('external:APPORTION:empty')
          this.setState({ checked, apportions: undefined })
        }
      })
    }
  }
  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  handleModalSave = async () => {
    const { apportionTemplateAmountField, feeAmount } = this.state
    const { layer } = this.props
    if (this.formBus.has('get:apportions:values')) {
      const value = await this.formBus.invoke('get:apportions:values', true)
      this.handleFeeAmountUpdated(value)
    } else {
      layer?.emitOk({
        apportions: undefined,
        [apportionTemplateAmountField]: feeAmount,
        apportion_amount_Key: apportionTemplateAmountField
      })
    }
  }
  render() {
    //cannotEditAmountField 不可编辑金额相关字段；仅在审批中修改时的费用明细中有取值为true的可能
    let {
      bus,
      layout,
      external,
      billSpecification,
      isForbid,
      field: apportionField = {},
      submitterId = {},
      feeDetailId = '',
      showAllFeeType,
      billTemplate,
      template,
      apportionVisibleList
    } = this.props
    let {
      feeAmount,
      errorMsg,
      checked,
      disable,
      isEdit,
      apportionfLebal,
      apportionFieldError,
      apportions: value
    } = this.state
    let style: CSSProperties = disable ? { pointerEvents: 'none', opacity: isEdit ? 0.4 : 1 } : null
    let headerStyle = disable && !isEdit ? { opacity: 0.4 } : {}

    const contextValue = this.contextValue
    return (
      <div className={styles['modal-money-wrapper']}>
        <div className="modal-header modal-money-header">
          <div className="flex">{i18n.get('编辑分摊')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-money-content">
          <ApportionContext.Provider value={contextValue}>
            {
              <div style={style}>
                {!apportionField.open && (
                  <ApportionsHeader
                    style={headerStyle}
                    checked={checked}
                    onCheckedValueChanged={this.handleCheckedChanged}
                    label={fnGetFieldLabel(apportionField)}
                  />
                )}
                {checked && (
                  <Container
                    name="@apportion:Apportion"
                    value={toJS(value)}
                    billTemplate={billTemplate}
                    apportionfLebal={apportionfLebal}
                    feetypeTemplate={template}
                    bus={this.formBus}
                    layout={layout}
                    feeAmount={feeAmount}
                    external={external}
                    isForbid={isForbid}
                    apportionField={apportionField}
                    isEdit={isEdit}
                    errorMsg={errorMsg}
                    apportionFieldError={apportionFieldError}
                    billSpecificationId={billSpecification.id}
                    submitterId={submitterId.id}
                    feeDetailId={feeDetailId}
                    apportionVisibleList={apportionVisibleList}
                    showAllFeeType={showAllFeeType}
                    billBus={bus}
                  />
                )}
              </div>
            }
          </ApportionContext.Provider>
        </div>
        <div className="modal-footer modal-money-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalSave}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }

  apportionTemplateChange = template => {
    const { baseDataProperties } = this.props
    const { configs = [] } = template
    const config = configs.find(v => v.ability === 'apportion')
    // 取一下指定金额的 label 记下来
    const fLebal = baseDataProperties.find(f => f.name === config?.apportionMoneyField)?.label
    this.setState({ apportionfLebal: fLebal })
    this.setMoney(config && config.apportionMoneyField)
  }
}

export default ApportionsModal
