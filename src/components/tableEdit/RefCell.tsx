import React from 'react'
import { cloneDeep, isArray } from 'lodash'
import { Tooltip } from '@hose/eui'
import styles from './Popover.module.less'

const RefCell = ({ text, field, noRootPathMap }) => {
  const hideCode = field?.hideCode
  const isShowFullPath = field?.isShowFullPath
  // 处理完整路径
  const showAllPath = field && field.readStatePathRule === 'fullPath'
  if (showAllPath) {
    const fullPath = getFullPath(noRootPathMap, text, field)
    return <div className={styles.tableReadonlyWidth}>
      <Tooltip title={fullPath?.name}>{fullPath?.name}</Tooltip>
    </div>
  }

  if (isArray(text)) {
    return <div className={styles.tableReadonlyWidth}>{text.map(i => `${isShowFullPath ? i?.fullPath : i?.name}${!hideCode && i?.code ? `(${i?.code})` : ''}`).join(i18n.get('、'))}</div>
  }
  const str = `${isShowFullPath ? text?.fullPath : text?.name}${!hideCode && text?.code ? `(${text?.code})` : ''}` || i18n.get('无')
  return <div className={styles.tableReadonlyWidth}><Tooltip title={str}>{str}</Tooltip></div>
}

export default RefCell

const getFullPath = (noRootPathMap, value, field) => {
  if (JSON.stringify(field) === '{}') { return value }
  const entity = field?.dataType?.entity
  if (entity?.indexOf?.('Department') === -1) { return value }
  if (!value || !noRootPathMap || JSON.stringify(noRootPathMap) === '{}') { return value }
  const _value = cloneDeep(value)
  if (_value && _value.name && _value.id && noRootPathMap.hasOwnProperty(_value.id)) {
    _value.name = noRootPathMap[_value.id]
  }
  return _value
}