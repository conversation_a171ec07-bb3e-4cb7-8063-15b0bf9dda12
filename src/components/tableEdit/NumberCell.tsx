import React from 'react'
import { Tooltip } from 'antd'
import styles from './Popover.module.less'
import Big from 'big.js'
const NumberCell = ({ text, field }) => {
  const unit = field?.unit || field?.dataType?.unit || ''
  if (text) {
    text = new Big(text).toFixed(field?.dataType?.scale)
  }
  return (
    <div className={styles.tableReadonlyWidth}>
      <Tooltip title={text}>
        {text || i18n.get('无')}
        {unit}
      </Tooltip>
    </div>
  )
}

export default NumberCell
