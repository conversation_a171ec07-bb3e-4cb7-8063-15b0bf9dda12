import React, { FC } from 'react'
import { Form, message } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { required } from '../validator/validator'
import { formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import { get } from 'lodash'
import { getMoney, standardValueMoney } from '../../lib/misc'
import CustomInput from './CustomInput'
import { app } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import { MoneyMath } from '@ekuaibao/money-math'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  vm: any
  field: any
  value: any
  form: any
  bus: any
  hiddenFields?: string[]
}

const ApportionsEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, hiddenFields } = props
  if (!fieldInfo) {
    return null
  }
  const { optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  const label = getLabel(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const value = get(record, dataIndex)
  const validator = (rule, value, callback) => {
    if (value?.length) {
      return handleFeeAmountUpdated(value, callback)
    }
    callback(required(fieldInfo, value))
  }
  const handleFeeAmountUpdated = (value, callback: any) => {
    const config = value?.[0]?.specificationId?.configs?.find(item => item.ability === 'apportion')
    const apportionTemplateAmountField = config?.apportionMoneyField
    const amount = get(record, `feeTypeForm.${apportionTemplateAmountField}`) || standardValueMoney('0')
    let apportionMoneyTotal = value?.reduce((sum, line) => {
      return new MoneyMath(sum).add(line.apportionForm.apportionMoney).value
    }, 0)
    let apportionTotalPercent = value?.reduce((sum, line) => {
      return new Big(sum).plus(line.apportionForm.apportionPercent || 0)
    }, 0)
    let { isEqual, title } = fnValidateEqual(
      apportionMoneyTotal,
      apportionTotalPercent,
      amount,
      value,
      fieldInfo,
      apportionTemplateAmountField
    )
    if (!isEqual) {
      message.error(title)
      callback(title)
    } else {
      callback()
    }
  }
  const fnValidateEqual = (
    apportionTotalMoney,
    apportionTotalPercent,
    amount,
    apportions,
    field,
    apportionTemplateAmountField
  ) => {
    const baseDataProperties = app.getState()['@common'].globalFields.data
    let title = ''
    let isEqual = false
    const { rule } = field
    const apportionfLebal = baseDataProperties.find(f => f.name === apportionTemplateAmountField)?.label
    if (amount && amount.foreign) {
      isEqual = new Big(getMoney(amount))
        .minus(new Big(getMoney(apportionTotalMoney)))
        .abs()
        .lte(new Big(0.05))
    } else {
      isEqual = new Big(getMoney(apportionTotalMoney)).eq(new Big(getMoney(amount)))
    }
    if (!isEqual) {
      title = i18n.get('费用金额 ≠ 总分摊金额，请修改后重新提交', { lable: apportionfLebal })
      return { title, isEqual }
    }
    isEqual = new Big(apportionTotalPercent.toFixed(2)).eq(100)
    if (!isEqual) {
      title = i18n.get('分摊比例 ≠ 100%，请修改后重新提交')
      return { title, isEqual }
    }
    const length = apportions.length
    rule &&
      rule !== 'PERCENTAGE' &&
      apportions.some((apportion, index) => {
        if (index < length - 2) {
          //最后两行不校验
          const { apportionMoney, apportionPercent } = apportion.apportionForm
          const { standard } = apportionMoney
          const total = getMoney(amount)
          const percent = Number(total) === 0 ? new Big(total) : new Big(standard).div(total)
          isEqual = percent
            .minus(new Big(apportionPercent).div(100))
            .abs()
            .lt(0.01)
          if (!isEqual) {
            title = i18n.get('存在费用金额*分摊比例≠分摊金额的数据，请修正', { lable: apportionfLebal })
            return true
          }
        }
        return false
      })

    // 校验其他金额字段
    const apportionSpecification = apportions[0]?.specificationId
    const otherMoneyFields = getOtherMoneyFieldsFromSpecification(apportionSpecification)
    if (otherMoneyFields?.length) {
      apportions.forEach((apportion, idx) => {
        otherMoneyFields.forEach(el => {
          el.totalAmountCounted = new Big(getMoney(apportion.apportionForm[el.name]))
            .plus(el.totalAmountCounted)
            .toFixed(2)
          if (length - 1 === idx) {
            if (!new Big(getMoney(el.totalAmount)).eq(el.totalAmountCounted)) {
              isEqual = false
              title = `${el.label} ≠ ${el.label}的总分摊金额，请更新后重新提交`
            }
          }
        })
      })
    }
    return { title, isEqual }
  }
  // 取其他金额字段对应的总金额
  const getOtherMoneyFieldsFromSpecification = apportionSpecification => {
    const otherMoneyFields = []
    const apportionComponents = apportionSpecification?.components || []
    apportionComponents.forEach(el => {
      const compConfigs = el?.configs || []
      const otherMoneyField = compConfigs.find(config => config.sourceField === 'otherApportionMoney')
      if (otherMoneyField) {
        const obj: any = {}
        obj.totalAmount = get(record, `feeTypeForm.${otherMoneyField.targetField}`)
        obj.name = el.field
        obj.label = el.label
        obj.totalAmountCounted = 0
        otherMoneyFields.push(obj)
      }
    })
    return otherMoneyFields
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: value
      })(<ApportionsWrapper {...props} />)}
    </FormItem>
  )
}

const ApportionsWrapper: FC<any> = props => {
  const {
    dataIndex,
    bus,
    field: fieldInfo,
    vm,
    value,
    field,
    record,
    form,
    onChange,
    form: { getFieldError }
  } = props
  if (!fieldInfo) {
    return null
  }
  const placeholder = getPlaceholder(fieldInfo)
  const handleClick = () => {
    const params = {
      bus,
      value: toJS(value || []),
      form,
      external: vm?.props?.external,
      billSpecification: vm.billSpecification,
      isForbid: vm?.props?.isForbid,
      field,
      record,
      vm,
      submitterId: vm?.submitterId,
      feeDetailId: vm?.props?.feeDetailId,
      showAllFeeType: vm?.props?.showAllFeeType,
      billTemplate: vm?.props?.billTemplate,
      template: record?.rowManager?.template,
      isModify: vm?.props?.isModify,
      apportionVisibleList: vm?.props?.apportionVisibleList,
      dataIndexPrefix: props.dataIndexPrefix
    }
    app.open('@bills:ApportionsModal', params).then((res: any) => {
      const { apportion_amount_Key, apportions } = res
      onChange && onChange(apportions)
      record?.rowManager?.onCellChange('apportions', apportions, vm, form) //更新分摊字段
      if (apportion_amount_Key) {
        //更新金额字段
        updateCellValue(apportion_amount_Key, res[apportion_amount_Key], record, vm, form)
      }
    })
  }
  const error = getFieldError(dataIndex)
  return (
    <CustomInput placeholder={placeholder} onClick={handleClick} error={error} disabled={false}>
      {value?.length ? (
        <a>
          {' '}
          {`根据「${value?.[0]?.specificationId?.name}」分摊`}
          {`${value?.length || 0}条`}
        </a>
      ) : (
        <a>{`${value?.length || 0}条分摊明细`}</a>
      )}
    </CustomInput>
  )
}
export default ApportionsEditCell
