import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formatRefDataId, formItemStyle, getFormItemLabel, updateCellValue } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { cloneDeep, get, isObject } from 'lodash'
import TreeSelectSingle from '../../elements/puppet/TreeSelectSingle'
import { getIdByTreeDataIsExist } from '../../elements/puppet/rc-tree/util'
import { canSelectParent } from '../utils/fnInitalValue'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId?: string
  form: any
  onChange?: () => void
  getExpenseStandardItemsLength: any
  vm: any
  hiddenFields?: string[]
}

const RefDepartmentEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any>()
  const [isDependence, setIsDependence] = useState<boolean | undefined>()
  const [departments, setDepartments] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, vm, hiddenFields } = props
  const submitterId = props?.submitterId || vm?.submitterId
  const initState = () => {
    const isDependence = dependence && !!dependence.length
    setIsDependence(isDependence)
    const value = get(record, dataIndex)
    setValue(value)
    getDept(value)
  }

  useEffect(() => {
    initState()
  }, [])

  if (!fieldInfo) {
    return null
  }
  const label = getLabel(fieldInfo)
  const { dependence, optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) { placeholder = i18n.get('（选填）') + i18n.get(placeholder) }
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  const fnFilterDepartmentSelectable = (
    department,
    rangeArr,
    selectableDepIdArr,
    onlyLeafCanBeSelected,
    parentDepartmentArr,
    visibleDepsArr
  ) => {
    const active = get(department, 'active', true)
    // @ts-ignore
    const depParentIdArr = [...parentDepartmentArr, department.id]
    if (onlyLeafCanBeSelected && department.children && department.children.length) { department.selectable = false }
    if (active && department.selectable !== false) {
      department.selectable = rangeArr.includes(department.id)
      if (department.selectable) {
        visibleDepsArr.push(...depParentIdArr)
        selectableDepIdArr.push(department.id)
      }
    }

    if (department.children && department.children.length) {
      department.children = department.children.map(dep =>
        fnFilterDepartmentSelectable(
          dep,
          rangeArr,
          selectableDepIdArr,
          onlyLeafCanBeSelected,
          depParentIdArr,
          visibleDepsArr
        )
      )
    }
    return department
  }

  // 检查模版中是否配置了选项，让提交人只能选择其所在部门
  const fnFilterDeptForOnlyBelongDepartment = (field, departments, submitterId) => {
    if (field && field.onlyBelongDepartment) {
      const submitterDepartments = get(submitterId, 'departments', [])
      const onlyLeafCanBeSelected = !canSelectParent(field)
      if (submitterDepartments.length) {
        const submitterDepartmentsArr = submitterDepartments.map(dep => {
          if (typeof dep === 'string') { return dep }
          return dep.id
        })
        const deptArr = cloneDeep(departments)
        // 1. 获取可选部门: selectableDepIdArr
        const selectableDepIdArr = []
        // 2. 获取可展示出来的部门id，是否有必要去重？ visibleDepsArr
        const visibleDepsArr = []
        let deps = deptArr.map(dep =>
          fnFilterDepartmentSelectable(
            dep,
            submitterDepartmentsArr,
            selectableDepIdArr,
            onlyLeafCanBeSelected,
            [],
            visibleDepsArr
          )
        )
        // @ts-ignore
        if (!selectableDepIdArr.length > 0) { return [] }
        // 3. 给部门加上hide属性
        deps = deps.map(dep => fnAddAttributeInDep(dep, visibleDepsArr))
        if (selectableDepIdArr.length === 1) {
          const departmentsMap = api.getState('@common').departmentVisibility.mapData
          if (Object.keys(departmentsMap).length > 0) {
            const depValue = departmentsMap[selectableDepIdArr[0]]
            if (depValue) {
              const { id, name, code, form } = depValue
              setValue({ id, name, code, form })
            }
          }
        }
        return deps
      }
    }
    return departments
  }

  const fnAddAttributeInDep = (department, visibleDepsArr) => {
    if (!visibleDepsArr.includes(department.id)) { department.hide = true }
    if (department.children && department.children.length) {
      department.children = department.children.map(dep => fnAddAttributeInDep(dep, visibleDepsArr))
    }
    return department
  }

  const getDept = (value) => {
    // @ts-ignore
    const id = isObject(value) ? value.id : value
    Promise.all([
      api.dataLoader('@common.departmentVisibility').load(),
      api.invokeService('@common:get:department:by:id', id)
    ])
      .then(result => {
        const departmentVisibility = result[0]
        const dept = result[1]
        let originDepartments = get(departmentVisibility, 'data', [])
        const active = get(dept, 'active', true)
        if (!active) {
          // @ts-ignore
          originDepartments = [{ ...dept, name: dept.name }, ...originDepartments]
        }
        const departments = fnFilterDeptForOnlyBelongDepartment(fieldInfo, originDepartments, submitterId)
        setDepartments(departments)
      })
      .catch(() => {
        setLoading(false)
      })
  }
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: value
      })(
        <CustomSelectRefDepartment
          {...props}
          field={fieldInfo}
          placeholder={placeholder}
          isDependence={isDependence}
          dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]}
          departments={departments}
        />
      )}
    </FormItem>
  )
}

class CustomSelectRefDepartment extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = { useDependenceData: true }
  }
  handleOnChange = data => {
    const { onChange, field, record, vm, form } = this.props
    onChange && onChange(data)
    updateCellValue(field?.field, data, record, vm, form)
  }
  getDependenceListData = () => {
    const { useDependenceData } = this.state
    const { isDependence, dependenceList, departments, value } = this.props
    if (isDependence && value?.id && !getIdByTreeDataIsExist(dependenceList, value?.id)) {
      return departments
    } else if (useDependenceData && isDependence) {
      return dependenceList
    } else {
      return departments
    }
  }
  render() {
    const { useDependenceData } = this.state
    const { field: fieldInfo, placeholder, value, isDependence, getExpenseStandardItemsLength, record } = this.props
    const { editable, optional, allowCancelDependence, Component, multiple, selectRange } = fieldInfo
    const disabled = isDisable(this.props)
    let { id, checkedId } = formatRefDataId(value)
    // 如果是已停用 并且 未勾选（允许使用已停用部门）
    if(!fieldInfo?.allowUseDeactivatedData && value?.active === false) {
      id = []
    }
    const data = {
      id: id ? id : checkedId,
      refKey: 'RefDepartmentTreeSelect',
      placeholder,
      treeNodeData: this.getDependenceListData(),
      mode: useDependenceData && isDependence ? 'dependence' : 'normal',
      optional: optional,
      disabled,
      Component,
      multiple,
      onlyLeafCanBeSelected: !canSelectParent(fieldInfo),
      onChange: this.handleOnChange,
      getExpenseStandardItemsLength,
      range: selectRange,
      displayValue: value,
      // TODO:
      isChangePosition: true,
      notFoundContent:
        allowCancelDependence && useDependenceData ? (
          <div className="cancel-dependence">
            {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
            <a
              href="javascript:void 0"
              onClick={() => {
                record?.rowManager?.cancelDependence(fieldInfo)
                this.setState({ useDependenceData: false })
              }}
            >
              {i18n.get('查看全量数据')}
            </a>
          </div>
        ) : (
          undefined
        )
    }
    return <TreeSelectSingle {...this.props} disabled={!editable} field={fieldInfo} data={data} showActive useEUI/>
  }
}

export default RefDepartmentEditCell
