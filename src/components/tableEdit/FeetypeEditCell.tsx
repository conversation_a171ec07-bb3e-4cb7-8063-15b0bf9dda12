import React, { FC, useState } from 'react'
import { app } from '@ekuaibao/whispered'
import { Form } from 'antd'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { required } from '../validator/validator'
import { formItemStyle, getFormItemLabel } from './utils'
import { get } from 'lodash'
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select')
import { DetailTableVm } from '../../elements/puppet/details/TableMVC/DetailTableVm'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  form: any
  onChange: any
  vm: DetailTableVm
  indexKey: string
}
const FeetypeEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props

  const label = getLabel(fieldInfo)
  const validator = (_rule, value, callback) => {
    return callback(required(fieldInfo, value))
  }
  const value = get(record, dataIndex)
  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: true }],
        initialValue: value?.id ? get(record, dataIndex) : undefined
      })(<FeeTypeSelectWrapper {...props} />)}
    </FormItem>
  )
}

const FeeTypeSelectWrapper: FC<IProps> = props => {
  const { dataIndex, record, field: fieldInfo, vm, indexKey, onChange, form } = props
  const [value, setValue] = useState(get(record, dataIndex))
  const { optional, field } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder

  const feeTypeMap = app.getState('@common').feetypes.map
  const currentVisibleFeeTypes = app.getState('@bills').currentVisibleFeeTypes || []
  const feeTypeId = record.feeTypeId
  if (feeTypeId?.active === false) {
    if (!currentVisibleFeeTypes.find(i => i.id === feeTypeId.id)) {
      currentVisibleFeeTypes.unshift(feeTypeId)
    }
  }
  const handleFeeTypeChange = async (id: string) => {
    if (id === value?.id) {
      return
    }
    form?.resetFields()
    const feeType = feeTypeMap[id]
    vm?.TM?.updateFeetype(record[indexKey], feeType)
    onChange && onChange(feeType)
    setValue(feeType)
  }
  return (
    <FeeTypeSelect
      size="large"
      style={{ width: '100%' }}
      dropdownWidth={220}
      treeCheckable={false}
      disabledCheckedFather
      showFullPath={true}
      disabledPopupContainer={true}
      multiple={field?.multiple || false}
      feeTypes={currentVisibleFeeTypes}
      checkedKeys={value ? [value.id] : undefined}
      allowClear={false}
      onChange={handleFeeTypeChange}
    />
  )
}
export default FeetypeEditCell
