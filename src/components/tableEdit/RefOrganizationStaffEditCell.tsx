import React, { Component, FC, useEffect, useState } from 'react'
import { Form, Input } from 'antd'
import {
  formatRefOrganizationStaffField,
  formItemStyle,
  itemInnerStyle,
  getFormItemLabel,
  updateCellValue
} from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import getLabel, { getPlaceholder } from '../utils/fnGetFieldLabel'
import { isDisable } from '../utils/fnDisableComponent'
import { get, isObject } from 'lodash'
import { Modal } from 'antd'
import { showModal } from '@ekuaibao/show-util'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  // TODO:
  template: any
  isModify: boolean
  ownerId: string
  billSpecification: any
  submitterId: string
  PayPlanStore: any
  hiddenFields?: string[]
}
const RefOrganizationStaffEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<Record<string, any> | string>()
  const [delegatorList, setDelegatorList] = useState<any[]>()
  const [userInfo, setUserInfo] = useState<any>()
  const { getFieldDecorator, dataIndex, record, field: fieldOld, isModify, bus, hiddenFields } = props
  const fieldInfo = record?.rowManager?.template?.find(item => item.name === fieldOld.name)

  const initState = () => {
    const delegatorList = api.getState('@common').delegators
    const userInfo = api.getState('@common').userinfo.data
    setDelegatorList(delegatorList)
    setUserInfo(userInfo)
    const value = get(record, dataIndex)
    setValue(value)
  }

  useEffect(() => {
    initState()
    bus?.on('on:dependence:change', handleDependenceChange)
    bus?.on('on:submitterId:change', handleSubmitterIdChange)
    return () => {
      bus?.un('on:dependence:change', handleDependenceChange)
      bus?.un('on:submitterId:change', handleSubmitterIdChange)
    }
  }, [])
  if (!fieldInfo) {
    return null
  }
  const { field, optional } = fieldInfo
  const label = getLabel(fieldInfo)
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  let disabled = !isDisable(props)
  if (isModify && field === 'submitterId') {
    disabled = false
  }
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  const handleDependenceChange = () => {}

  const handleSubmitterIdChange = () => {}

  return (
    <FormItem {...getFormItemLabel(label)} style={formItemStyle(fieldInfo, record?.rowManager?.validateError, hiddenFields)}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator, required: !fieldInfo?.optional }],
        initialValue: value
      })(
        <InputWrapper
          {...props}
          disabled={!disabled}
          placeholder={placeholder}
          delegatorList={delegatorList}
          userInfo={userInfo}
          dependenceList={record?.rowManager?.dependListMap[fieldInfo.field]}
        />
      )}
    </FormItem>
  )
}

class InputWrapper extends Component<any> {
  handleClick = async () => {
    let { field: fieldInfo, delegatorList, userInfo, value = {}, dependenceList, vm } = this.props
    const { billSpecification ,submitterId} = vm
    const { name, dependence, allowCancelDependence } = fieldInfo
    let isSubmitter = name === 'submitterId'
    let delegatorCopyList = isSubmitter
      ? delegatorList.filter(de => {
          return (
            de.delegateType === billSpecification.type &&
            (!de.specIds ||
              de.specIds.length === 0 ||
              de.specIds.indexOf(billSpecification?.originalId?.id || billSpecification?.originalId) > -1)
          )
        })
      : delegatorList.filter(item => item.delegateType === billSpecification.type)
    let obj = {}
    delegatorCopyList = delegatorCopyList.reduce(function(item, next) {
      if (!obj[next.id]) {
        obj[next.id] = true
        item.push(next)
      }
      return item
    }, [])

    delegatorCopyList.push(userInfo.staff)
    let ds = void 0
    let checkedKeys = void 0
    let disabledKeys = void 0
    if (isSubmitter) {
      value = value || submitterId
      ds = delegatorCopyList
    }

    if (isObject(value)) {
      // @ts-ignore
      checkedKeys = value.id ? [value.id] : []
    }

    if (typeof value === 'string' && value) {
      checkedKeys = [value]
    }

    if (ds && ds.length === 1) {
      disabledKeys = checkedKeys
    }

    if (dependence && dependence.length) {
      ds = dependenceList
    }

    if (ds && !ds.length) {
      if (allowCancelDependence) {
        const shouldSelectAllUser = await new Promise((resolve, reject) => {
          Modal.confirm({
            title: i18n.get('您没有可以选择的员工'),
            okText: i18n.get('知道了'),
            cancelText: i18n.get('选择所有员工'),
            onCancel: () => resolve(true),
            onOk: () => resolve(false)
          })
        })
        if (!shouldSelectAllUser) {
          return
        }
        // 取消依赖
        ds = undefined
      } else {
        return showModal.error({
          title: i18n.get('您没有可以选择的员工')
        })
      }
    }
    this.openStaffModal(ds, checkedKeys, isSubmitter, disabledKeys)
  }
  openStaffModal = (ds, checkedKeys, isSubmitter, disabledKeys) => {
    const { field: fieldInfo, billSpecification, bus, userInfo, value } = this.props
    const { name, allowExternal, valueRangeFilter } = fieldInfo
    const formType = billSpecification?.type
    let staffRangeRule = false
    if (valueRangeFilter && valueRangeFilter !== 'false') {
      //后端定义这个“false”表示取消限制
      staffRangeRule = valueRangeFilter
    }
    if (name === 'directorLeader' || name === 'branchLeader') {
      let checkedList = [
        {
          type: allowExternal ? 'external' : 'department-member',
          multiple: false,
          // @ts-ignore
          checkedKeys: value ? [value?.id] : []
        }
      ]
      return bus
        ?.invoke('element:ref:select:staff', { checkedList, allowExternalStaff: allowExternal, staffRangeRule })
        .then(data => {
          this.handleChange(data?.checkedList?.[0]?.checkedData?.[0])
        })
    }
    bus
      ?.invoke('element:ref:select:staff', {
        dataSource: ds,
        checkedKeys,
        closeable: isSubmitter,
        disabledKeys,
        staffRangeRule
      })
      .then(async data => {
        // data[0] 单选,但是的组件返回的是一个多选的组件、
        if (!data || !data.length) {
          name !== 'submitterId' && this.handleChange()
          return
        }
        let checkPayeeConfig = false
        // @ts-ignore
        if (name === 'submitterId' && data[0]?.id !== value?.id) {
          const res = await api.invokeService('@bills:get:delegate:config', data[0]?.id)
          checkPayeeConfig = res?.value?.[`${formType}Config`]?.applyPayeeAccount
        }
        if (checkPayeeConfig) {
          showModal.confirm({
            content: (
              <div>{i18n.get('切换提交人后，收款信息将展示切换后的人员可见的收款账户，已填写的收款账户将会清空')}</div>
            ),
            onCancel: () => {
              this.fnChangeValue(value)
            },
            onOk: async () => {
              this.fnClearPayeeId(data[0], data[0]?.id)
            }
          })
        } else {
          // @ts-ignore
          const id = data[0]?.id !== value?.id ? userInfo?.staff?.id : data[0]?.id
          name === 'submitterId' ? this.fnClearPayeeId(data[0], id) : this.fnChangeValue(data[0])
        }
      })
  }
  fnClearPayeeId = async (data, submitterId) => {
    const { billSpecification, bus, PayPlanStore, vm } = this.props
    const multiplePayeesMode = api.getState('@bills').multiplePayeesMode
    const formType = billSpecification?.type
    if (multiplePayeesMode) {
      let details = vm.tableDataSource || []
      details = details.map(v => {
        if (v?.feeTypeForm?.feeDetailPayeeId) {
          v.feeTypeForm.feeDetailPayeeId = {}
        }
        return v
      })
      bus.setFieldsValue({ details })
      PayPlanStore?.clearObjs([])
      this.fnChangeValue(data)
    } else {
      const res = await api.invokeService('@bills:get:default:payee', {
        formType,
        submitterId
      })
      bus.setFieldsValue({ payeeId: res?.value || {} })
      this.fnChangeValue(data)
    }
  }

  fnChangeValue = data => {
    const { field: fieldInfo, bus, value } = this.props
    const { name } = fieldInfo
    if (name === 'submitterId') {
      bus.emit('set:LinkRequisitionInfo', data, value)
      bus.emit('set:delegator', data)
      bus.emit('update:blockUI', data)
      api.invokeService('@bills:set:submitter:data', data)
    }

    this.fnUpdateForm(data)
    this.handleChange(data)
  }
  // TODO:
  fnUpdateForm = data => {}

  handleChange = (value?) => {
    const { onChange, field, record, vm, form } = this.props
    onChange(value)
    updateCellValue(field?.field, value, record, vm, form)
  }
  render() {
    const { disabled, value, placeholder } = this.props
    const { myValue } = formatRefOrganizationStaffField(value, this.props)
    return (
      <Input
        disabled={disabled}
        style={itemInnerStyle}
        value={myValue}
        placeholder={placeholder}
        onClick={this.handleClick}
        onFocus={e => {
          e.target.blur()
        }}
      />
    )
  }
}

export default RefOrganizationStaffEditCell
