import React, { useEffect, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from './Popover.module.less'
const CurrencyCell = ({ text }) => {
  const [allData, setAllData] = useState([])
  useEffect(() => {
    api
      .dataLoader('@common.getCurrencyAll')
      .load()
      .then(res => {
        setAllData(res)
      })
  }, [])
  let valueName = allData?.find(i => text == i?.numCode || text?.id === i?.numCode || text?.numCode === i?.numCode)
  return (
    <div className={styles.tableReadonlyWidth}>
      {valueName ? `${valueName?.name} (${valueName?.strCode})` : <span className="currency_span">-</span>}
    </div>
  )
}

export default CurrencyCell
