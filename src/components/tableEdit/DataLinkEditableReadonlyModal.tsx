import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './MoneyModal.module.less'
import { Icon } from 'antd'
import { Button } from '@hose/eui'
import DataLinkEdit from '../../components/dynamic/dataLinkEdit/DataLinkEditReadonlyComp'
import MessageCenter from '@ekuaibao/messagecenter'
import { readonly } from '../../components'
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
class DataLinkEditableReadonlyModal extends PureComponent<any, any> {
  fieldBus: any = new MessageCenter()
  handleModalClose = () => {
    const { layer } = this.props
    layer?.emitCancel()
  }
  render() {
    return (
      <div className={styles['modal-invoice-wrapper']}>
        <div className="modal-invoice-header">
          <div className="flex">{i18n.get('业务对象')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-invoice-content">
          <DataLinkEdit {...this.props} elements={readonly} showHeader={false} fieldBus={this.fieldBus} />
        </div>
        <div className="modal-invoice-footer">
          <Button category="secondary" className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取  消')}
          </Button>
          <Button className="btn-ml ml-6" onClick={this.handleModalClose}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}

export default DataLinkEditableReadonlyModal
