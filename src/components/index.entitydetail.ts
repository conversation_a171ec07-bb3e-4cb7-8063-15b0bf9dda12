import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

const InterconInput = loadableWithDescriptor(() => import('./interconnectal/InterconInput'), {
  type: 'interconnectal-text'
})
const ImportMethod = loadableWithDescriptor(() => import('./interconnectal/ImportMethod'), {
  type: 'interconnectal-import-way'
})
const InterconSelect = loadableWithDescriptor(() => import('./interconnectal/InterconSelect'), {
  type: 'interconnectal-select'
})
const InterconSelectReadonly = loadableWithDescriptor(() => import('./interconnectal/InterconSelect.readonly'), {
  type: 'interconnectal-select'
})
const InterconRadioGroupReadOnly = loadableWithDescriptor(() => import('./interconnectal/InterconRadioGroup.readonly'), {
  type: 'interconnectal-select'
})
const InterconSelectRelationReadOnly = loadableWithDescriptor(() => import('./interconnectal/InterconSelectRelation.readonly'), {
  type: 'interconnectal-select-relation'
})
const EntityFieldReadonly = loadableWithDescriptor(() => import('./interconnectal/EntityField.readonly'), {
  type: 'entity-field'
})
const InterconTagSelect = loadableWithDescriptor(() => import('./interconnectal/InterconTagSelect'), {
  type: 'interconnectal-tag-select'
})
const InterconLabel = loadableWithDescriptor(() => import('./interconnectal/InterconLable'), {
  type: 'interconnectal-label'
})
const RecordLogCheckBox = loadableWithDescriptor(() => import('./interconnectal/RecordLogCheckBox'), {
  type: 'reacord-log-checkbox'
})
const CheckBox = loadableWithDescriptor(() => import('./internal/CheckBox'), {
  type: 'checkbox'
})


const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })

const interconnectalDataClear = loadableWithDescriptor(() => import('./interconnectal/interconnectalDataClear'), {
  type: 'interconnectal-dataclear'
})

const interInitDataCurrency = loadableWithDescriptor(() => import('./interconnectal/interInitDataCurrency'), {
  type: 'interconnectal-data-currency'
})

const interInitExcelDataSet = loadableWithDescriptor(() => import('./interconnectal/interconnectal-excel-dataset'), {
  type: 'interconnectal-excel-dataset'
})

export const entityDetail = [
  InterconInput,
  InterconRadioGroupReadOnly,
  InterconSelectReadonly,
  InterconSelect,
  InterconSelectRelationReadOnly,
  EntityFieldReadonly,
  ImportMethod,
  InterconLabel,
  RecordLogCheckBox,
  InterconTagSelect,
  CheckBox,
  Unknown,
  interconnectalDataClear,
  interInitDataCurrency,
  interInitExcelDataSet
]
