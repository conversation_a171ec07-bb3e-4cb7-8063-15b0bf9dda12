import React, { useState, useEffect, useRef } from 'react'
import { Tooltip } from '@hose/eui'
import './index.module.less'

export default function TooltipEllipsis(props) {
  let childrenRef = useRef(null)
  const { children, title, width, ...other } = props

  const [showTooltip, setShowTooltip] = useState(false)

  useEffect(() => {
    validShowTooltip()
  }, [title, width])

  const validShowTooltip = () => {
    if (!childrenRef) {
      return
    }
    const { scrollWidth, clientWidth } = childrenRef as any;
    setShowTooltip(scrollWidth > clientWidth)
  }

  const refChildren = (ref: any) => {
    childrenRef = ref;
  }

  const renderChildren = () => {
    return (
      React.cloneElement(
        <span className="ellipsis-children">{children}</span>, {
          ref: refChildren
        }
      )
    )
  }

  if (showTooltip) {
    return (
      <Tooltip
        title={title}
        trigger="hover"
        placement="top"
        {...other}
      >
        { renderChildren() }
      </Tooltip>
    )
  }
  return renderChildren()
}