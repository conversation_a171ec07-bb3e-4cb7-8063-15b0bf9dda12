/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EKBSelect, EKBInput } from '../../ekb-components/index'
import styles from './InterconSelectRelation.module.less'
import { required } from '../validator/validator'
@EnhanceField({
  descriptor: {
    type: 'interconnectal-select-relation'
  },
  validator: (field, props) => (rule, value, callback) => {
    let { disableStrategy, maxUsageCount } = value
    let flagMethod = required(field, disableStrategy)
    let flagLimit = ''
    if (props.bus._$CHECKED_CONSTANT_ERROR && disableStrategy === 'LIMIT_COUNT') {
      if (maxUsageCount <= 0) {
        return callback(i18n.get('限制次数必须大于0'))
      }
      flagLimit = required(field, maxUsageCount)
      props.bus._$CHECKED_CONSTANT_ERROR = false
    }
    let str = flagMethod || flagLimit
    if (str) {
      return callback(str)
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class InterconSelectRelation extends PureComponent {
  onChange = disableStrategy => {
    let { onChange, value } = this.props
    value = { ...value, disableStrategy }
    onChange && onChange(value)
  }

  onInputChange = maxUsageCount => {
    let { onChange, value } = this.props
    value = { ...value, maxUsageCount }
    onChange && onChange(value)
  }

  render() {
    let { value, field } = this.props
    let { disableStrategy, maxUsageCount } = value || {}
    let { placeholder, editable, tags } = field
    let params = { value: disableStrategy, placeholder, disabled: !editable, tags }
    return (
      <div className={styles['interselerelat-wrapper']}>
        <div className="interselerelat-content">
          <div>
            <EKBSelect {...params} onChange={this.onChange} />
          </div>
          {disableStrategy === 'LIMIT_COUNT' && (
            <div>
              <EKBInput type="number" className="number-wrapper" value={maxUsageCount} onChange={this.onInputChange} />
            </div>
          )}
        </div>
        {disableStrategy === 'LIMIT_COUNT' ? (
          <div className="describe">
            <div>
              {i18n.get(
                '即引用的次数不得超过所设定值，超过后则自动停用（管理员可单独对某个实例修改引用次数上限，但不得小于已引用次数）'
              )}
            </div>
            <div>
              {i18n.get(
                '停用方式一旦确定，就不可修改。上述次数为默认值，扩展管理员可以单独或批量修改每条数据的次数限制。'
              )}
            </div>
          </div>
        ) : (
          <div className="describe">
            <div>
              {i18n.get('由扩展管理员手动维护停启用状态')} {i18n.get('，')}
              <span className="orange">{i18n.get('停用方式一旦确定，就不可修改。')}</span>
            </div>
            <div></div>
          </div>
        )}
      </div>
    )
  }
}
