/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17 下午5:52.
 */
import React, { PureComponent } from 'react'
import { Checkbox } from 'antd'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'

const CheckboxGroup = Checkbox.Group
const options = [
  { label: 'Excel', value: 'excel' },
  { label: 'API', value: 'api' },
  { label: i18n.get('单据写入'), value: 'flow' },
  { label: i18n.get('直接写入'), value: 'directly' }
]
const optionsEbot = [
  { label: 'Excel', value: 'excel' },
  { label: 'API', value: 'api' },
  { label: 'EBot', value: 'ebot' },
  { label: i18n.get('单据写入'), value: 'flow' },
  { label: i18n.get('直接写入'), value: 'directly' }
]

@EnhanceField({
  descriptor: {
    type: 'interconnectal-import-way'
  },
  validator: field => (_, value, callback) => {
    if (!value || !value.length) {
      callback(i18n.get('请选择导入方式'))
    }
    callback()
  },
  initialValue(props) {
    return ['excel']
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    ebot: state['@common'].powers.EBOT
  }
})
export default class ImportMethod extends PureComponent {
  constructor(props){
    super(props)
    this.initValue=this.props.value || []
  }
  handleChange = value => {
    this.valueChange(value)
  }

  valueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
  // todo zhb 导入方式
    const {
      value = [],
      ebot,
      field: { editable, status },
      disabledEdit
    } = this.props
    let option = options
    if (ebot) {
      option = optionsEbot
    }
    if (status && status === 'update' && this.initValue.indexOf('flow') > -1) {
      option = option.map(i => {
        const inew = { ...i }
        if (inew.value === 'flow') {
          inew.disabled = true
        }
        return inew
      })
    }
    return (
      <CheckboxGroup
        options={option}
        defaultValue={value}
        disabled={!editable || disabledEdit}
        onChange={this.handleChange}
      />
    )
  }
}
