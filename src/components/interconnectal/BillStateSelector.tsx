import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { SelectorProps, SelectorState } from '../types'
import { billStates } from '../utils/utilFunctionsParams'
import styles from './BillStateSelector.module.less'
import { pullAll } from 'lodash'
import { Select } from 'antd'
import { FieldInterface } from '../layout/types'
import { required } from '../validator/validator'
import { wrapper } from '../layout/FormWrapper'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'limit:bill:state'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    if (field.validator) {
      field.validator(rule, value, callback)
      return
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class BillStateSelector extends PureComponent<SelectorProps, SelectorState> {
  render() {
    let { onChange, field, hidden, value } = this.props
    if (value && !!value.length && value.join(',').indexOf('pending') > -1) {
      value = pullAll(value, ['pending,approving', 'pending', 'approving'])
      value.push('pending,approving')
    }
    if (hidden) return <></>
    const { placeholder } = field
    return (
      <div className={styles.bill_state_selector}>
        <Select
          defaultValue={value}
          mode="multiple"
          size="large"
          placeholder={placeholder}
          style={{ width: '100%' }}
          onChange={onChange}
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {billStates.map(item => {
            return (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            )
          })}
        </Select>
      </div>
    )
  }
}
