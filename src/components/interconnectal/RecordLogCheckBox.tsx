/**
 *  Created by liyang on 2019/10/15 下午6:33.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { Checkbox } from 'antd'
import { RadioFieldInterface } from '../types'
import styles from './RecordLogCheckBox.module.less'
interface Iprops {
  field: RadioFieldInterface
  value: string
  bus: any
  form: any
  onChange: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'reacord-log-checkbox'
  },
  initialValue(props) {
    const { field = {}, value } = props
    const { defaultValue = false } = field
    return defaultValue
  },
  validator: field => (level, value, callback) => {
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class RecordLogCheckBox extends PureComponent<Iprops> {
  onChange = e => {
    const { onChange, bus } = this.props
    const val = e.target.checked
    bus.emit('reacord:log:checkbox:changed', { isRecord: val })
    onChange && onChange(val)
  }
  componentDidMount() {
    const { field, bus } = this.props
    const { defaultValue } = field
    const val = this.props.value ? this.props.value : defaultValue
    bus.emit('reacord:log:checkbox:changed', { isRecord: val })
  }

  /**
   * 这个类可以优化掉，可以用其它组件替换，为了上线先不动了，等上线完成在下一个迭代有@李阳替换
   */

  render() {
    const { field, value } = this.props
    const { label } = field
    return (
      <Checkbox onChange={this.onChange} className={styles.record_log_checkbox_wrapper} checked={!!value}>
        <span className="checkbox_lable">{label}</span>
      </Checkbox>
    )
  }
}
