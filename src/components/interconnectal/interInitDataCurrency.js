/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
 import React, { PureComponent } from 'react'
 import { EnhanceField } from '@ekuaibao/template'
 import { wrapper } from '../layout/FormWrapper'
 import { app as api } from '@ekuaibao/whispered'
 import styles from './interInitDataCurrency.module.less'
 
 import { Checkbox } from 'antd'
 @EnhanceField({
   descriptor: {
     type: 'interconnectal-data-currency'
   },
   validator: (field, props) => (rule, value, callback) => {
     return callback()
   },
   wrapper: wrapper()
 })
 export default class InterconSelectRelation extends PureComponent {
    onChange = (e) => {
     let { onChange } = this.props
     onChange && onChange(e?.target?.checked)
   }
   render() {
     let { value} = this.props
     return (
       <div className={styles['interconnectal-data-currency']}>
         
        <Checkbox onChange={this.onChange}  checked={value || false }>{i18n.get('开启后，每一条业务对象可以独立配置原币币种，并以原币执行计算，默认为本位币。')}</Checkbox>
       </div>
     )
   }
 }
 