/**
 *  Created by gym on 2019-05-07 11:13.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EKBSelect } from '../../ekb-components/index'
import { Row, Col } from 'antd'
import { FieldInterface } from '../layout/types'
import { required } from '../validator/validator'

interface Props {
  value: any
  field: FieldInterface
  onChange: Function
  onChangeImg?: Function
}

interface valueInterface {
  FatherValue?: String
  chilValue?: any
  chilValue2?: any
}

interface Map {
  [key: string]: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'interconnectal-select-cascader'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    const { label } = field
    const cascaderField = field.tags.filter(v => v.children2) //三级选择
    const maxLength = field.tags.filter(v => v.maxLength) //二级选择，限制选择个数时
    if (value.FatherValue) {
      if (
        cascaderField.length &&
        value.FatherValue === cascaderField[0].value &&
        (!value.chilValue || !value.chilValue2)
      ) {
        return callback(i18n.get('not-empty', { label: i18n.get(field.label) }))
      } else if (value.FatherValue && value.FatherValue !== 'NONE' && (!value.chilValue || !value.chilValue.length)) {
        return callback(i18n.get('not-empty', { label: i18n.get(label) }))
      } else if (
        maxLength.length &&
        Array.isArray(value.chilValue) &&
        value.chilValue.length > maxLength[0].maxLength
      ) {
        const max = maxLength[0].maxLength
        return callback(i18n.get('cannot-count-greater', { label, max }))
      }
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconSelectCascader extends PureComponent<Props> {
  onChange = (value: String) => {
    const valueObj = { FatherValue: value }
    this.handleOnChange(valueObj)
  }

  onChildrenChange = (values: String) => {
    const { value } = this.props
    const valueObj = { FatherValue: value.FatherValue, chilValue: values }
    this.handleOnChange(valueObj)
  }

  onChildrenChange2 = (values: String) => {
    const { value } = this.props
    const valueObj = { FatherValue: value.FatherValue, chilValue: value.chilValue, chilValue2: values }
    this.handleOnChange(valueObj)
  }

  handleOnChange = (value: valueInterface) => {
    const { onChange, onChangeImg } = this.props
    onChange && onChange(value)
    onChangeImg && onChangeImg(value)
  }

  formatTags = (tags: any) => {
    let obj: Map = {}
    tags.map((item: any) => (obj[item.value] = item))
    return obj
  }

  render() {
    const { value, field } = this.props
    const { placeholder, editable, tags, mode, optionFilterProp } = field
    const params = { value: value && value.FatherValue, placeholder, disabled: !editable, tags, mode, optionFilterProp }
    const newTag = this.formatTags(tags || [])
    const childrenTags = value && newTag[value.FatherValue] && newTag[value.FatherValue].children
    const childrenTags2 = value && newTag[value.FatherValue] && newTag[value.FatherValue].children2
    const xs = childrenTags ? { span: 8 } : { span: 24 }
    const Cxs = childrenTags2 ? { span: 7, offset: 1 } : { span: 15, offset: 1 }
    return (
      <>
        <Row>
          <Col xs={{ ...xs }}>
            <EKBSelect {...params} onChange={this.onChange} />
          </Col>
          {childrenTags && (
            <Col xs={{ ...Cxs }}>
              <EKBSelect
                tags={childrenTags.tags}
                mode={childrenTags.mode}
                value={value && value.chilValue}
                onChange={this.onChildrenChange}
              />
            </Col>
          )}
          {childrenTags2 && (
            <Col xs={{ span: 7, offset: 1 }}>
              <EKBSelect
                tags={childrenTags2.tags}
                mode={childrenTags2.mode}
                value={value && value.chilValue2}
                onChange={this.onChildrenChange2}
              />
            </Col>
          )}
        </Row>
      </>
    )
  }
}
