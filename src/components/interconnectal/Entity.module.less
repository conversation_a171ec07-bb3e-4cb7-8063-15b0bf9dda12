@import '~@ekuaibao/web-theme-variables/styles/default';

.entity_wrapper {
  :global {
    span {
      font-size: 14px;
      color: #bfbfbf;
    }
    .add {
      user-select: none;
      cursor: pointer;
      font-size: 14px;
      color: @primary-6;
      min-width: 60px;
    }
    .entity {
      //height: 80px;
      padding: 14px;
      border: 1px solid @body-background;
      margin-bottom: 8px;
      .card-content {
        .left {
          > div {
            line-height: 20px;
          }
        }
      }
    }
    .entity:hover {
      border: 1px solid @primary-6;
      box-shadow: 0 1px 16px 0 var(--brand-fadeout-10);
    }
  }
}
