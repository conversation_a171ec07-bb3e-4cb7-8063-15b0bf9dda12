.custom-dimension-filter-wrapper {
  .filter-group {
    padding-right: 32px;
  }

  .filter-group-content {
    background-color: #f7f7f7;
    padding: 8px;
    border-radius: 4px;
  }

  .group-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .group-header-title {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .group-content-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
  }

  .or-text {
    background: #fff;
    padding: 8px 0;

    span {
      display: inline-block;
      width: 30px;
      height: 24px;
      border-radius: 2px;
      text-align: center;
      font-size: 14px;
      color: #fff;
      line-height: 24px;
      background-color: rgba(0, 0, 0, .85);
    }
  }

  .add-group-button{
    margin-top: 8px;
  }
}