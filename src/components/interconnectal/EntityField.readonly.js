import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { FieldView } from '../../elements/puppet/entity-detail/entity-detail'
import { wrapper } from '../layout/FormWrapper'
import { entityFieldItemData } from '../utils/fnEntityDataParse'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceField({
  descriptor: {
    type: 'entity-field'
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback()
  },
  wrapper: wrapper(
    false,
    {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    },
    { color: '#8C8C8C' }
  )
})
@EnhanceConnect(state => ({
  enumerateDimension: state['@custom-dimension'].enumerateDimension
}))
export default class EntityFieldReadonly extends PureComponent {
  state = {
    dimension: undefined
  }

  getDimension = async () => {
    // const id = get(this.props, 'entityInfo.id')
    // if (id) {
    //   const result = await api.invokeService('@third-party-manage:get:dataLink:dimension', { id })
    //   this.setState({ dimension: result.value })
    // }
  }

  handleEdit = () => {
    const {
      field: { showAction = 0, elemenetAdd = 0 },
      sourceType,
      entityInfo,
      entityMap
    } = this.props
    const { dimension } = this.state
    const path = sourceType === 'fxiaoke' ? 'EditFieldsForFXiaoKeModal' : 'FieldConfigEditModal'
    const params =
      sourceType === 'fxiaoke'
        ? { entityInfo, entityMap }
        : {
            dimension,
            fields: this.props.value,
            entityInfo: this.props.entityInfo,
            allEntityList: this.props.allEntityList,
            enumerateDimension: this.props.enumerateDimension,
            entityList: this.props.entityList,
            showAction: showAction,
            elemenetAdd: elemenetAdd,
            updateLabel:true
          }
    api.open(`@third-party-manage:${path}`, params).then(res => {
      const isUpdateNow = get(res, 'isUpdateNow')
      isUpdateNow ? this.props.bus.emit('load:entity:for:fxiaoke') : this.props.bus.emit('load:entity')
      sourceType !== 'fxiaoke' && this.getDimension()
    })
  }

  render() {
    // todo zhb 字段编辑
    const {
      value = [],
      allEntityList,
      field: { editVisible },
      disabledEdit,
      enumerateDimension
    } = this.props
    const { dimension } = this.state
    return (
      <>
        {value?.filter(i=>i.entity !=='basedata.Enum.currency')?.map((item, index) => {
          const { title, subtitle, desc } = entityFieldItemData(item, allEntityList, dimension, enumerateDimension)
          return <FieldView key={index} title={title} subtitle={subtitle} desc={desc} />
        })}
        {editVisible && !disabledEdit && (
          <div
            onClick={this.handleEdit}
            style={{
              marginTop: '8px',
              color: 'var(--brand-base)',
              fontSize: '14px',
              lineHeight: '22px',
              cursor: 'pointer'
            }}
          >
            {i18n.get('编辑')}
          </div>
        )}
      </>
    )
  }
}
