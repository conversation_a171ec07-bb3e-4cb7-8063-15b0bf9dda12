import React, { PureComponent } from 'react'
import { RoleSelected } from '../../ekb-components'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { find } from 'lodash'
const isEmpty = value => {
  if (!value) {
    return true
  }
  const { departments, roles, staffIds = [] } = value
  return !staffIds.length
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'select:staff'
  },
  validator: field => (level, value, callback) => {
    const { optional, label } = field
    if (!optional && isEmpty(value)) {
      return callback(i18n.get('not-empty', { label: i18n.get(label) }))
    }
    callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  staffs: state['@common'].staffs
}))
export default class SelectStaff extends PureComponent<any> {
  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  valueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  handleChange = result => {
    const ids = result.map(staff => staff.id)
    const { value } = this.props
    value.staffIds = ids
    this.valueChange(value)
  }

  setVisibleList = data => {
    const { checkedList, departmentsIncludeChildren } = data
    const depart = find(checkedList, line => line.type === 'department')
    const role = find(checkedList, line => line.type === 'role')
    const staff = find(checkedList, line => line.type === 'department-member')
    const staffList = staff?.checkedKeys || []
    const departmentList = depart?.checkedKeys || []
    const roleList = role?.checkedKeys || []
    const result = {
      staffIds: staffList.filter(v => v),
      departments: departmentList.filter(v => v),
      roles: roleList.filter(v => v),
      type: 'DESIGNATION',
      departmentsIncludeChildren
    }
    this.valueChange(result)
  }

  onClick = () => {
    const checkedList = [{ type: 'department-member', multiple: true, checkedKeys: this.props.value?.staffIds || [] }]
    // @ts-ignore
    api
      .open('@layout:SelectStaffsModal', { checkedList, departmentsIncludeChildren: false, multiple: true })
      .then(data => {
        this.setVisibleList(data)
      })
  }

  getSelectStaffs = () => {
    const { value, staffs } = this.props
    return value ? staffs.filter(staff => !!~value?.staffIds?.indexOf(staff.id)) : []
  }

  render() {
    const selectedStaffs = this.getSelectStaffs()

    return <RoleSelected tags={selectedStaffs} onClick={this.onClick} onChange={this.handleChange} />
  }
}
