import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { InfoView } from '../../elements/puppet/entity-detail/entity-detail'
import { wrapper } from '../layout/FormWrapper'
import { entityScope } from '../utils/fnEntityDataParse'

@EnhanceField({
  descriptor: {
    type: 'interconnectal-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback()
  },
  wrapper: wrapper(
    false,
    {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    },
    { color: '#8c8c8c' }
  )
})
export default class InterconRadioGroupReadOnly extends PureComponent {
  render() {
    const { value } = this.props
    const { label, desc } = entityScope(value)
    return <InfoView title={i18n.get(label)} desc={i18n.get(desc)} />
  }
}
