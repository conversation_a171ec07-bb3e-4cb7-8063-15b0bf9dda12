import React, { Component } from 'react'
import { cloneDeep, isEqual } from 'lodash'
import CustomDimensionFilter from './CustomDimensionFilter'
import { FilterInterface, FieldInterface } from '../types'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { But<PERSON>, Popconfirm } from '@hose/eui'
import { OutlinedEditDeleteTrash } from '@hose/eui-icons'
import { isValidCustomDimensions } from './CustomDimensionFilter.isValidCustomDimensions'
import './CustomDimensionFilterWrapper.less'

interface Props {
  value: FilterInterface[][] | FilterInterface[]
  onChange?(value: FilterInterface[][] | FilterInterface[]): void
  field: FieldInterface[]
  hidden?: boolean
  globalFieldsList?: any[]
  globalFields?: { [P: string]: any }
  records?: { [P: string]: any }
  departmentTree?: any[]
  dimensions?: { [K: string]: any[] }
  dimensionMap?: { [K: string]: any }
  enumerateMap?: { [key: string]: any[] }
  staffsVisibility?: any[]
}

interface State {
  value: FilterInterface[][] | FilterInterface[]
  rowKeys: string[]
}

@((EnhanceField as any)({
  descriptor: {
    type: 'custom-dimension-filter-wrapper'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    if (field.validator) {
      return field.validator(rule, value, callback)
    }
   
    // 检查必填
    const error = required(field, value)
    if (error) {
      return callback(error)
    }
    if (Array.isArray(value) && value.length > 0) {
      // 验证每一行
      for (let rowIndex = 0; rowIndex < value.length; rowIndex++) {
        const row = value[rowIndex]

        // 检查行是否为空
        if (!Array.isArray(row) || row.length === 0) {
          return callback(i18n.get('条件组 {0} 不能为空', { 0: rowIndex + 1 }))
        }


        // 检查行内容是否有效
        if (!isValidCustomDimensions(row, field?.source)) {
          return callback(i18n.get('条件组 {0} 填写不完整', { 0: rowIndex + 1 }))
        }

        // 可以添加更多行级别的验证规则
        // 例如：检查行间的重复
        const duplicateRowIndex = value.findIndex((otherRow: any[], otherIndex: number) =>
          otherIndex !== rowIndex && isEqual(otherRow,row)
        )
        if (duplicateRowIndex >= 0) {
          return callback(i18n.get('条件组 {0} 与条件组 {1} 重复', { 0: rowIndex + 1, 1: duplicateRowIndex + 1 }))
        }
      }
    }
    callback(undefined)
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
}))
export default class CustomDimensionFilterWrapper extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    // 初始化时如果是一维数组,转换为二维数组
    const value = props.value || []
    this.state = {
      value: this.convertTo2DArray(value),
      rowKeys: (value).map(() => this.generateUniqueKey())
    }
  }

  // 生成唯一的 key
  private generateUniqueKey = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  componentDidUpdate(prevProps: Props) {
    if (prevProps.value !== this.props.value) {
      // 更新时也需要转换
      this.setState({
        value: this.convertTo2DArray(this.props.value || []),
        rowKeys: (this.props.value || []).map(() => this.generateUniqueKey())
      })
    }
  }

  // 判断是否为二维数组
  private is2DArray = (value: FilterInterface[][] | FilterInterface[]): boolean => {
    return Array.isArray(value) && Array.isArray(value[0])
  }

  // 转换为二维数组
  private convertTo2DArray = (value: FilterInterface[][] | FilterInterface[]): FilterInterface[][] => {
    if (this.is2DArray(value)) {
      return value as FilterInterface[][]
    }
    return value.length ? [(value as FilterInterface[])] : [[{
      left: undefined,
      operator: undefined,
      right: this.props.field?.source === 'apportionRule' ? { type: '', value: [] } : [],
      includeChildren: false,
      fromWhere: '',
      isShowInput: false,
      middleTags: []
    }]]
  }

  private handleAdd = () => {
    const { onChange } = this.props
    const { value, rowKeys } = this.state
    if (!onChange) return

    const newValue = cloneDeep(value as FilterInterface[][])
    newValue.push([{
      left: undefined,
      operator: undefined,
      right: this.props.field?.source === 'apportionRule' ? { type: '', value: [] } : [],
      includeChildren: false,
      fromWhere: '',
      isShowInput: false,
      middleTags: []
    }])
    // 为新行添加唯一的 key
    const newRowKeys = Array.from(rowKeys).concat([this.generateUniqueKey()])
    this.setState({
      value: newValue,
      rowKeys: newRowKeys
    })
    onChange(newValue)
  }

  private handleDelete = (rowIndex: number) => () => {
    const { onChange } = this.props
    const { value, rowKeys } = this.state
    if (!onChange) return
    if (value.length === 1) {
      // 剩下一个就不必删除了
      this.setState({ value: value, rowKeys: rowKeys })
      onChange(value)
      return
    }

    const newValue: any = value.filter((_, idx) => idx !== rowIndex)
    // 同步更新 rowKeys
    const newRowKeys = rowKeys.filter((_, idx) => idx !== rowIndex)
    this.setState({ value: newValue, rowKeys: newRowKeys })
    onChange(newValue)
  }

  render() {
    const { field,hidden } = this.props
    const { value, rowKeys } = this.state
    const value2D = value as FilterInterface[][]
    const maxLength = (field as any)?.maxLength || 5

    if (hidden) {
      return null
    }

    return (
      <div className="custom-dimension-filter-wrapper">
        {value2D.map((item, rowIndex) => (
          <div key={rowKeys[rowIndex]} className="filter-group">
            {rowIndex > 0 && <div className="or-text"><span>或</span></div>}
            <div className="filter-group-content">
              {value2D.length > 1 && (
                <div className="group-header-wrapper">
                  <div className="group-header-title">条件组 {rowIndex + 1}</div>
                  <Popconfirm
                    title="确定删除此条件组？"
                    onConfirm={this.handleDelete(rowIndex)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      className="delete-group-button"
                      theme="default"
                      category="text"
                      size="small"
                      icon={<OutlinedEditDeleteTrash />}
                    />
                  </Popconfirm>

                </div>
              )}
              <CustomDimensionFilter
                {...this.props}
                value={item}
                deleteRowDisabled={value2D.length === 1}
                onChange={(newValue) => {
                  const { onChange } = this.props
                  const { value } = this.state
                  if (!onChange) return
                  const newArray = cloneDeep(value as FilterInterface[][])
                  if (newValue.length > 0) {
                    newArray[rowIndex] = newValue
                    onChange(newArray)
                  } else {
                    this.handleDelete(rowIndex)()
                  }
                }}
              />
            </div>
          </div>
        ))}
        {!(maxLength && value2D.length >= maxLength) && <Button
          category="text"
          theme="highlight"
          onClick={this.handleAdd}
          disabled={maxLength && value2D.length >= maxLength}
          size="small"
          className="add-group-button"
        >添加条件组</Button>}
      </div>
    )
  }
} 