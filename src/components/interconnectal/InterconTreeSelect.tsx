/**
 *  Created by pw on 2019-03-12 11:48.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EKBTreeSelect } from '../../ekb-components'
import { wrapper } from '../layout/FormWrapper'
import { FieldInterface } from '../layout/types'
import { required } from '../validator/validator'

interface InterconTreeSelectInterface {
  field: any
  onChange: Function
  value: any
  hidden?: boolean
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'interconnectal:tree:select'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    if (field.validator) {
      field.validator(rule, value, callback)
      return
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconTreeSelect extends PureComponent<InterconTreeSelectInterface> {
  render() {
    const { field, onChange, value, hidden } = this.props
    if (hidden) {
      return null
    }
    const { tags = [], placeholder } = field
    return (
      <EKBTreeSelect
        className="w-100b"
        value={value}
        treeData={tags}
        treeCheckable={true}
        isShowParent={true}
        treeNodeFilterProp="name"
        treeNodeLabelProp="name"
        placeholder={placeholder}
        dropdownClassName={'standard-select'}
        size={'large'}
        onChange={onChange}
      />
    )
  }
}
