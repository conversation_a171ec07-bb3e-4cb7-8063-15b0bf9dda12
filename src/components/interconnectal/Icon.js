import * as React from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { ImageUploade } from '../../ekb-components/index'
import DEFAULT_IMAGE from '../../images/platform-default-icon.png'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'

import styles from './Icon.module.less'

@EnhanceField({
  descriptor: {
    type: 'icon'
  },
  validator: field => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Icon extends React.PureComponent {
  handleFinish = file => {
    const { onChange } = this.props
    delete file.thumbUrl
    delete file.url
    onChange && onChange(file)
  }

  render() {
    const {
      value,
      field: { editable }
    } = this.props
    const imageSrc = getNodeValueByPath(value, 'fileId.thumbUrl') || DEFAULT_IMAGE
    return (
      <div className={styles.imageuploade_wrapper}>
        <div className="imageuploade">
          {editable ? (
            <ImageUploade imageSrc={imageSrc} onFinish={this.handleFinish} />
          ) : (
            <img src={imageSrc} style={{ with: 88, height: 88 }} />
          )}
        </div>
        <div className="descrip">
          <span>{i18n.get("图标要求：")}</span>
          <span>{i18n.get("大小不超过 2M。")}</span>
          <span>{i18n.get("格式可为 jpg、jpeg、png。")}</span>
        </div>
      </div>
    )
  }
}
