/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import styles from './interconnectal-excel-dataset.module.less'

import { Radio } from 'antd'
@EnhanceField({
  descriptor: {
    type: 'interconnectal-excel-dataset'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value) {
      return callback(i18n.get('数据配置不能为空'))
    }
    return callback()

  },
  wrapper: wrapper()
})
export default class InterconSelectRelation extends PureComponent {
  onChange = (e) => {
    let { onChange } = this.props
    onChange && onChange(e?.target?.value)
  }
  componentDidMount(){
    let {value}=this.props
    if(value===undefined){
      this.props?.onChange('CODE_DUPLI_OVERRIDE')
    }
  }
  render() {
    let { value } = this.props
    return (
      <div className={styles['interconnectal-excel-dataset']}>

        <Radio.Group onChange={this.onChange} value={value}>
          <Radio value="CODE_DUPLI_OVERRIDE">{i18n.get('覆盖原数据')}</Radio>
          <Radio value="CODE_DUPLI_ERROR">{i18n.get('不覆盖并错误提示')}</Radio>
        </Radio.Group>

      </div>
    )
  }
}
