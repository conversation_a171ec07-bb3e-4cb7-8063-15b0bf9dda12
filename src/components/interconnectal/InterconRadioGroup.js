/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:43.
 */

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import EKBRadioGroup from '../../elements/radio-group/EKBRadioGroup'

@EnhanceField({
  descriptor: {
    type: 'interconnectal-radio-group'
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconRadioGroup extends PureComponent {
  handleChange = e => {
    let { onChange } = this.props
    let val = e.target.value
    onChange && onChange(val)
  }

  render() {
    let { field, value } = this.props
    return <EKBRadioGroup field={field} value={value} handleChange={this.handleChange} />
  }
}
