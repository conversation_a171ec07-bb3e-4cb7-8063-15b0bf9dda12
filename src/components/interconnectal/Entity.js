import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { DataInterConCard } from '../../ekb-components'
import { app as api } from '@ekuaibao/whispered'
import Loading from '@ekuaibao/loading'
import { cloneDeep, isEqual as deepEqual } from 'lodash'
import styles from './Entity.module.less'

@EnhanceField({
  descriptor: {
    type: 'entities'
  },
  validator: field => (_, value, callback) => {
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Entity extends PureComponent {
  orginValue = []

  //记录手动改变开启状态的对应的业务对象
  changedValue = {}

  constructor() {
    super()
    this.state = {
      status: 'loading'
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.watch('check:entity:ismodify', this.checkModify)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('check:entity:ismodify')
  }

  componentDidMount() {
    this.refreshEntityList()
  }

  checkModify = () => {
    const { value } = this.props
    return new Promise((resolve, reject) => {
      const modifyValues = value.filter((item, index) => !deepEqual(item, this.orginValue[index]))
      resolve(!!modifyValues.length)
    })
  }

  refreshEntityList = () => {
    const { bus, platformId } = this.props
    this.setState({ status: 'loading' })
    bus
      .invoke('get:entity:list', platformId)
      .then(result => {
        this.setState({ status: 'done' })
        this.orginValue = cloneDeep(result)
        result.forEach(item => {
          if (this.changedValue[item.id]) {
            item.active = this.changedValue[item.id].active
          }
        })
        this.valueChange(result)
      })
      .catch(_ => {
        this.setState({ status: 'done' })
      })
  }

  valueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value.slice())
  }

  onClick = item => {
    const { platform, value, allEntityList } = this.props
    api
      .open('@third-party-manage:EntityDetailModal', {
        item,
        platformName: platform.name,
        entityList: value,
        platformGroupType: platform.groupType,
        allEntityList: allEntityList,
        platformType: platform.type
      })
      .then(_ => {
        this.refreshEntityList()
      })
  }

  onChange = (item, e) => {
    item.active = e
    this.changedValue[item.id] = { active: e }
    const { value } = this.props
    this.valueChange(value)
  }

  handleAddAction = () => {
    const { platform, value, allEntityList } = this.props
    api.open('@third-party-manage:AddDataEntitiyModal', { platform, entityList: value, allEntityList }).then(_ => {
      this.refreshEntityList()
    })
  }

  renderEntitys = () => {
    const {
      field: { editable }
    } = this.props
    const { status } = this.state
    if (status === 'loading') {
      return <Loading className="center" style={{ width: '30px', height: '80px' }} color="var(--brand-base)"/>
    }
    const { value } = this.props
    if (!value || !value.length) {
      return <span className="empty_text">{i18n.get('你暂未添加业务对象，请添加')}</span>
    }
    return value.map(item => {
      const { id, name, active } = item
      return (
        <DataInterConCard
          key={id}
          className="entity"
          actionName={i18n.get('编辑')}
          isShowSwitch={editable}
          isOpen={active}
          id={id}
          name={name}
          onClick={this.onClick.bind(this, item)}
          onChange={this.onChange.bind(this, item)}
        />
      )
    })
  }

  render() {
    const {
      field: { editable }
    } = this.props
    return (
      <div className={styles.entity_wrapper}>
        {this.renderEntitys()}
        {editable && (
          <div className="add" onClick={this.handleAddAction}>
            {i18n.get('点击添加')}
          </div>
        )}
      </div>
    )
  }
}
