/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EKBSelect } from '../../ekb-components/index'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './InterconSelect.module.less'
import { Checkbox, Button, Tag, Tooltip, Icon } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { find, concat, cloneDeep, get, set } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { getObjectByIds } from '@ekuaibao/lib/lib/selectStaffsVisibility'
import EKBIcon from '../../elements/ekbIcon'
//'@elements/ekbIcon')
@EnhanceField({
  descriptor: {
    type: 'interconnectal-select'
  },
  validator: (field, props, state) => (rule, value, callback) => {
    let visibility = props?.visibility
    if (field.validator) {
      field.validator(rule, value, callback)
      return
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data
}))
export default class InterconSelect extends PureComponent {
  constructor(props) {
    super(props)
    const { staffs, roles, departmentTree, visibility } = props

    const params = { staffs, roles, departmentTree, visibility: cloneDeep(visibility) }
    const visibilityList = getObjectByIds(params) || []
    this.state = {
      ownerDepts: props.ownerDepts || false,
      visibility: visibility || {},
      visibilityList: visibilityList
    }
  }
  onChange = value => {
    const { onChange, bus, field } = this.props
    if ((field && field.name === 'statisticsEntityId') || field.name === 'dataLinkEntityId') {
      bus.emit('ledger:intercon:select:change', { id: value })
    } else if (field && field.name === 'scoped') {
      if (value == 'false') {
        bus.emit('set:ownerDepts', false)
        this.handleCheckVi({ fullVisible: false }, value)
      } else {
        this.handleCheckVi({}, value)
      }
    }
    onChange && onChange(value)
  }
  handleCheck = e => {
    this.setState(
      {
        ownerDepts: e.target.checked
      },
      () => {
        this.handleCheckVi({ ownerDepts: e.target.checked })
      }
    )
    this.props.bus.emit('set:ownerDepts', e.target.checked)
  }
  handleCheckChildren = e => {
    let visibility = this.state.visibility
    visibility.departmentsIncludeChildren = e.target.checked
    this.setState(
      {
        visibility
      },
      () => {
        this.handleCheckVi({ visibility })
      }
    )
  }

  render() {
    const { value, field, hidden, disabledEdit } = this.props
    let {
      ownerDepts,
      visibility: { departmentsIncludeChildren }
    } = this.state
    if (hidden) return null
    // todo zhb 参与人
    const { placeholder, editable, tags, entityDescribe = true, describe, mode, optionFilterProp, otherView } = field
    const params = { value, placeholder, disabled: !editable || disabledEdit, tags, mode, optionFilterProp }
    const valueTrue = value === 'true' ? i18n.get('则在导入数据时，需要指定参与人。') : null
    return (
      <div className={styles['select-wrapper']}>
        <EKBSelect {...params} onChange={this.onChange} />

        {entityDescribe ? (
          <div className="describe">
            <span>{i18n.get('参与人可以查看，使用业务对象')}</span>
            {valueTrue}
          </div>
        ) : null}
        {describe ? <span className="describe-intercon">{describe}</span> : null}
        {otherView && otherView(this.props)}

        {field && field.name == 'scoped' && value == 'true' && (
          <>
            <Checkbox
              checked={departmentsIncludeChildren}
              onChange={this.handleCheckChildren}
              style={{ paddingLeft: '0px' }}
            >
              {i18n.get('勾选后，参与人选择的部门包含全部子部门')}
              <EKBIcon
                name="#EDico-help"
                tooltipTitle={i18n.get(
                  '选择本选项后，再选择是否默认为负责人所在部门或按照部门设置参与人白名单时参与人范围均会包含其子部门。保存后，历史数据全部更新'
                )}
                className="help_icon"
              />
            </Checkbox>
            <br />

            <Checkbox onChange={this.handleCheck} checked={ownerDepts} style={{ paddingLeft: '0px' }}>
              {i18n.get('勾选后，参与人默认为负责人所在部门')}
            </Checkbox>
            <p className="describe-p">
              {i18n.get('只有新建数据时默认把负责人部门添加到参与人范围，不变动历史参与人信息')}
            </p>
          </>
        )}

        {field && field.name == 'scoped' && value == 'true' && this.reanderCheck()}
      </div>
    )
  }
  handleCheckVi = (data, newvalue) => {
    let { visibility, ownerDepts } = this.state
    let { value, onChange } = this.props
    let result = { departments: [], staffs: [], roles: [], ownerDepts, ...visibility, ...data }

    this.setState({
      visibility: result
    })
    this.props.bus.emit('set:visibility', { ...result, scoped: newvalue || value })
    onChange && onChange(newvalue || value)
  }
  handleClick = () => {
    const { visibility } = this.state

    const checkedList = [
      { type: 'department-member', multiple: true, checkedKeys: visibility.staffs || [] },
      { type: 'department', multiple: true, checkedKeys: visibility.departments || [] },
      { type: 'role', multiple: true, checkedKeys: visibility.roles || [] }
    ]

    api
      .open('@layout:SelectStaffsModal', {
        checkedList,
        departmentsIncludeChildren: true,
        multiple: true,
        isNeedSubDept: false
      })
      .then(params => {
        const { checkedList, departmentsIncludeChildren } = params
        const depart = find(checkedList, line => line.type === 'department')
        const role = find(checkedList, line => line.type === 'role')
        const staff = find(checkedList, line => line.type === 'department-member')
        const staffList = staff.checkedData || []
        const departmentList = depart.checkedData || []
        const roleList = role.checkedData || []
        const visibility = {
          staffs: staffList.map(i => i.id).filter(v => v),
          departments: departmentList.map(i => i.id).filter(v => v),
          roles: roleList.map(i => i.id).filter(v => v)
        }
        let visibilityList = concat([], depart.checkedData || [])
        visibilityList = concat(visibilityList, role.checkedData || [])
        visibilityList = concat(visibilityList, staff.checkedData || [])
        this.handleCheckVi(visibility)
        this.setState({ visibilityList: visibilityList })
      })
  }
  handleClose(_tag) {
    const visibilityList = [...this.state.visibilityList].filter(tag => tag.id !== _tag.id)
    let visibility = this.state.visibility

    visibility.departments = visibility.departments.filter(tag => tag != _tag.id)

    visibility.roles = visibility.roles.filter(tag => tag != _tag.id)

    visibility.staffs = visibility.staffs.filter(tag => tag != _tag.id)

    this.setState({
      visibilityList
    })
    this.handleCheckVi(visibility)
  }
  reanderCheck = () => {
    let { visibility, visibilityList } = this.state
    return (
      <div>
        <Checkbox
          onChange={e => this.handleCheckVi({ fullVisible: e.target.checked })}
          checked={visibility?.fullVisible}
          style={{ paddingLeft: '0px' }}
        >
          {i18n.get('设置参与人白名单')}
        </Checkbox>
        {visibility?.fullVisible && (
          <div className="tags">
            {visibilityList?.map(tag => {
              return (
                tag && (
                  <Tag key={tag.id} closable onClose={() => this.handleClose(tag)}>
                    <span className="name" title={tag.name}>
                      {tag.name}
                    </span>
                  </Tag>
                )
              )
            })}
            <Button className="btn" size="small" onClick={this.handleClick}>
              {i18n.get('编辑')}
            </Button>
          </div>
        )}
      </div>
    )
  }
}
