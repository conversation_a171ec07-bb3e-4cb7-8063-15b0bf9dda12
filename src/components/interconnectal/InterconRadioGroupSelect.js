import React, { PureComponent, Fragment } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { EKBSelect } from '../../ekb-components/index'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { Radio } from 'antd'
import { isArray } from '@ekuaibao/helpers'

@EnhanceField({
  descriptor: {
    type: 'interconnectal-radio-group-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (field.validator) {
      return field.validator(rule, value, callback)
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconRadioGroupSelect extends PureComponent {
  constructor(props) {
    super(props)
    const { tags, defaultValue } = props.field
    const value = props.value || { current: defaultValue }
    this.state = {
      value,
      tags: tags[value.current] || []
    }
  }

  onChange = () => {
    const { onChange, bus, field } = this.props
    const { value } = this.state
    const isList = field?.tags?.[value?.current]?.find(item => item.id === value?.billRefFieldName)?.type === 'list'
    try {
      bus.emit('refFieldName:select:change', { isList, value })
    } catch (error) {}

    onChange && onChange(value)
  }

  fnGetTags = (tags = [], name, value) => {
    if (isArray(tags)) {
      return tags.filter(v => (v.source ? v.source === name : v))
    }
    return tags?.[value?.current] || tags?.[name]
  }

  handleChange = e => {
    const { tags } = this.props.field
    const current = e.target.value
    this.setState(
      {
        value: { current },
        tags: tags[current]
      },
      this.onChange
    )
  }

  handleSelectChange(name) {
    return val => {
      if (val !== 'add') {
        let { value } = this.state
        this.setState(
          {
            value: { ...value, [name]: val }
          },
          this.onChange
        )
      }
    }
  }

  renderSelect = (line, key) => {
    const { tags, value } = this.state
    return line.names.map((name, index) => {
      const rows = line?.row?.[name]
      if (rows) {
        return (
          <div className="dis-f" style={{ marginTop: index > 0 ? 12 : 0 }}>
            {rows.map((row, rowIndex) => {
              return (
                <EKBSelect
                  key={`${key}${index}`}
                  tags={tags[row]}
                  groupOption={tags[`${row}GroupOption`]}
                  value={value[row]}
                  placeholder={line.placeholder[index]}
                  onChange={this.handleSelectChange(row)}
                  addHandel={this.props.field.addHandel}
                  style={{ width: '100%', marginLeft: rowIndex === 0 ? 0 : 10 }}
                  className="dis-b"
                />
              )
            })}
          </div>
        )
      }
      return (
        <EKBSelect
          key={`${key}${index}`}
          tags={this.fnGetTags(tags, name, value)}
          value={value[name]}
          placeholder={line.placeholder[index]}
          onChange={this.handleSelectChange(name)}
          addHandel={this.props.field.addHandel}
          style={{ marginTop: index > 0 ? 12 : 0 }}
          className="dis-b"
          addTitle={this.props.field.addTitle}
        />
      )
    })
  }

  render() {
    const { field } = this.props
    const { current } = this.state.value

    return (
      <Radio.Group value={current} onChange={this.handleChange} className="dis-b">
        {field.options
          .filter(v => v.visible)
          .map((line, index) => (
            <Fragment key={index}>
              <Radio key={index} value={line.value} className="dis-b" style={{ height: 36, lineHeight: '36px' }}>
                {line.label}
              </Radio>
              {line.value === current && this.renderSelect(line, current)}
            </Fragment>
          ))}
      </Radio.Group>
    )
  }
}
