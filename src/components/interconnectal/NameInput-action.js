import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { EKBInput } from '../../ekb-components/index'
import { Button } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'

import styles from './InterconInput.module.less'

@EnhanceField({
  descriptor: {
    type: 'interconnectal-text'
  },
  validator: field => (lavel, value, callback) => {
    const { label, maxLength } = field
    if (value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength, length: value.length }))
    }
    return callback(required(field, value))
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 20 }
  })
})
export default class NameInput extends PureComponent {
  onChange = value => {
    let { onChange } = this.props
    onChange && onChange(value)
  }

  handleSave = () => {
    const { bus } = this.props
    bus.emit('save:entity')
  }

  render() {
    const { value, field } = this.props
    let { placeholder, editable } = field
    let params = { value, placeholder, disabled: !editable }
    return (
      <div className={styles['input-wrapper']}>
        <EKBInput className="ekbinput" {...params} onChange={this.onChange} />
        {editable && (
          <Button type="primary" onClick={this.handleSave}>
            {i18n.get('保 存')}
          </Button>
        )}
      </div>
    )
  }
}
