import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { DataTreeCard } from '../../ekb-components'
import { app as api } from '@ekuaibao/whispered'
import Loading from '@ekuaibao/loading'
import { isEqual as deepEqual, cloneDeep } from 'lodash'
import styles from './EntityTree.module.less'

@EnhanceField({
  descriptor: {
    type: 'entitiestree'
  },
  validator: field => (_, value, callback) => {
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class EntityTree extends PureComponent {
  orginValue = []

  //记录手动改变开启状态的对应的业务对象
  changedValue = {}

  constructor() {
    super()
    this.state = {
      status: 'loading'
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.watch('check:entity:ismodify', this.checkModify)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('check:entity:ismodify')
  }

  componentDidMount() {
    this.refreshEntityList()
  }
  componentWillReceiveProps(nextPro) {
    if (nextPro.platform !== this.props.platform) {
      this.refreshEntityList(nextPro.platformId)
    }
  }

  checkModify = () => {
    const { value } = this.props
    return new Promise((resolve, reject) => {
      const modifyValues = value.filter((item, index) => !deepEqual(item, this.orginValue[index]))
      resolve(!!modifyValues.length)
    })
  }

  refreshEntityList = (platformId = this.props.platformId) => {
    const { bus } = this.props
    this.setState({ status: 'loading' })
    bus
      .invoke('get:entity:list', platformId)
      .then(result => {
        this.setState({ status: 'done' })
        this.orginValue = cloneDeep(result)
        result.forEach(item => {
          if (this.changedValue[item.id]) {
            item.active = this.changedValue[item.id].active
          }
          item.children &&
            item.children.forEach(i => {
              if (this.changedValue[i.id]) {
                i.active = this.changedValue[i.id].active
              }
            })
        })
        this.valueChange(result)
      })
      .catch(_ => {
        this.setState({ status: 'done' })
      })
  }

  valueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value.slice())
  }

  onClick = item => {
    const {
      platform,
      value,
      allEntityList,
      field: { showType = '' },
      disabledEdit
    } = this.props
    // todo zhb 弹窗入口
    api.invokeService('@third-party-manage:get:temconfig:candidate', item.id).then(res => {
      api
        .open('@third-party-manage:EntityDetailModal', {
          item: { ...item, _showType: showType },
          platformName: platform.name,
          entityList: value,
          platformGroupType: platform.groupType,
          allEntityList: allEntityList,
          disabledEdit
        })
        .then(_ => {
          this.refreshEntityList()
        })
    })
  }

  onChange = (item, e) => {
    item.active = e
    this.changedValue[item.id] = { active: e }
    const { value } = this.props
    if (e == false && item.children.length > 0) {
      item.children.forEach(i => {
        i.active = e
        this.changedValue[i.id] = { active: e }
      })
    }
    this.valueChange(value)
  }

  handleAddAction = item => {
    const { platform, value, allEntityList } = this.props
    api
      .open('@third-party-manage:AddDataEntitiyModal', {
        platform,
        entityList: value,
        allEntityList,
        child: false,
        item: item
      })
      .then(_ => {
        this.refreshEntityList()
      })
  }
  handleAddChild = item => {
    const { platform, value, allEntityList } = this.props //platform:是扩展对象 value:业务对象数组 item: 选中业务对象
    api
      .open('@third-party-manage:AddDataEntitiyModal', {
        platform,
        entityList: value,
        item,
        allEntityList,
        child: true
      })
      .then(_ => {
        this.refreshEntityList()
      })
  }
  renderTree = () => {
    const {
      field: { editable, showType = '' },
      disabledEdit
    } = this.props
    const { status } = this.state
    if (status === 'loading') return <Loading className="center" style={{ width: '30px', height: '80px' }} color="var(--brand-base)"/>
    const { value } = this.props
    if (!value || !value.length) return <span className="empty_text">{i18n.get('你暂未添加业务对象，请添加')}</span>
    return value.map((item, index) => {
      const { id, name, active } = item
      return (
        <DataTreeCard
          key={index + id}
          actionName={i18n.get('配置')}
          childName={disabledEdit ? null : i18n.get('添加类型')}
          showType={showType}
          name={name}
          id={id}
          onClickChild={() => this.handleAddChild(item)}
          isShowSwitch={editable}
          isOpen={active}
          onClick={item => this.onClick(item)}
          onChange={(item, e) => this.onChange(item, e)}
          isChild={true}
          item={item}
          last={index === value.length - 1}
          disabledEdit={disabledEdit}
        />
      )
    })
  }
  render() {
    const {
      field: { editable, showType = '', style = {} },
      disabledEdit
    } = this.props
    return (
      <div className={styles.entitytree_wrapper} style={style}>
        {this.renderTree()}
        {editable && showType === '' && !disabledEdit && (
          <div className="add" onClick={this.handleAddAction}>
            {i18n.get('添加业务对象')}
          </div>
        )}
      </div>
    )
  }
}
