/**
 *  Created by gym on 2019-05-10 11:13.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import TagSelector from '../../elements/tag-selector'
import './InterconTagSelect.less'
import { app as api } from '@ekuaibao/whispered'
import { TransferModalProps } from '../../elements/transfer/TransferModal'
import { FieldInterface } from '../layout/types'

interface Props {
  value: any
  onChange: Function
  field: FieldInterface
  form: any
  bus: any
}
interface State {
  isRecord: boolean
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'interconnectal-tag-select'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    const { label, maxLength } = field
    if (maxLength && value.length && value.length > field.maxLength) {
      const max = maxLength
      return callback(i18n.get('cannot-count-greater', { label, max }))
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconTagSelect extends PureComponent<Props, State> {
  componentWillMount() {
    const { bus } = this.props
    bus.on('reacord:log:checkbox:changed', this.handleRadioChange)
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('reacord:log:checkbox:changed', this.handleRadioChange)
  }
  handleRadioChange = value => {
    this.setState({
      isRecord: value.isRecord
    })
  }
  initUsed = (value: string[]) => {
    const keyKey = this.getKey()
    return value.map((item: any) => item[keyKey])
  }
  state = {
    isRecord: !!this.props.value && !!this.props.value.length
  }
  getKey = () => {
    // 台账和计划的key取id
    const {
      field: { tags }
    } = this.props
    return tags.find(item => item.label) ? 'name' : 'id'
  }
  initValue = () => {
    let { value } = this.props
    const { field } = this.props
    const { tags = [] } = field
    const { isRecord } = this.state
    if (field.name === 'logFields' && tags.length > 0) {
      if (!isRecord) {
        return []
      }
      value = value.map((item: any) => {
        const type = typeof item === 'object'
        if (type) {
          return item
        } else {
          return tags.find((v: any) => {
            return v.name === item
          })
        }
      }).filter(i => !!i)
      return value
    }else if(value?.length>0 && tags?.length>0){
      value = value.map((item: any) => {
        const type = typeof item === 'object'
        if (type) {
          let u=tags?.find((v:any)=>{
            return v?.name==item?.name
          })
          return u || item;
        } else {
          return tags.find((v: any) => {
            return v.name === item
          })
        }
      }).filter(i => !!i)
      return value
    }
    return value
  }

  handleClick = () => {
    const { field, onChange } = this.props
    const { placeholder, tags } = field
    const value = this.initValue()
    const used = value ? this.initUsed(value) : []
    const displayKey = tags.find(item => item.label) ? 'label' : 'name'
    const keyKey = this.getKey()
    api
      .open('@layout:TransferModal', {
        placeholder,
        items: tags,
        used: used,
        keyKey: keyKey,
        displayKey: displayKey,
        name: i18n.get('字段')
      } as TransferModalProps<any>)
      .then((result: any[]) => {
        const selectedItem = result.map(v => tags.find((o: any) => o[keyKey] === v))
        onChange && onChange(selectedItem)
      })
  }

  handleTagChange = (data: any[], deleteItem: any) => {
    const { onChange } = this.props
    onChange && onChange(data)
  }

  render() {
    const { field } = this.props
    const { isRecord } = this.state
    const { placeholder, describe } = field
    const { label, name } = field
    // const value = name === 'logFields' ? this.initValue() : this.props.value
    const value=this.initValue()
    const cls = name === 'logFields' ? 'intercon-tag-select-entity' : ''
    if (name === 'logFields' && !isRecord) {
      return <></>
    }

    return (
      <div className={cls}>
        {name === 'logFields' && <label>{label}</label>}
        <TagSelector
          value={value || []}
          className="intercon-tag-select-content"
          onClick={this.handleClick}
          onChange={this.handleTagChange}
          marginTop={1}
          marginLeft={11}
          placeholder={placeholder}
        />
        {describe ? <span className="intercon-input-describ">{describe}</span> : null}
      </div>
    )
  }
}
