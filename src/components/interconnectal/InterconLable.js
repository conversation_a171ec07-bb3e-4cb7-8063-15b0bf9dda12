/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午5:10.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './InterconLable.module.less'

@EnhanceField({
  descriptor: {
    type: 'interconnectal-label'
  },
  validator: field => (level, value, callback) => {
    const { label, maxLength } = field
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class InterconInput extends PureComponent {
  onChange = value => {
    let { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    const { value, field } = this.props
    let { placeholder, editable, describe } = field
    let params = { value, placeholder, disabled: !editable }
    return <span className={styles.label}>{placeholder}</span>
  }
}
