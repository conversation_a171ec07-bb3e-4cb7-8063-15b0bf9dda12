/**
 *  Created by pw on 2019-03-11 13:54.
 */
import React, { PureComponent } from 'react'
import './InterconSeparator.less'
import { EnhanceField } from '@ekuaibao/template'

interface SeparatorProps {
  cls?: string
  field?: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'interconnectal-separator'
  }
})
export default class InterconSeparator extends PureComponent<SeparatorProps> {
  render() {
    const { field } = this.props
    const { cls } = field
    return <div className={`intercon_separator ${cls}`} />
  }
}
