@import '~@ekuaibao/eui-styles/less/token.less';

.select-wrapper {
  :global {
    .help_icon {
      width: @space-6;
      height: @space-6;
      color: @color-black-3;
      margin-left: @space-2;
      transform: translate(0px, 2px);
    }
    .select-content {
      display: flex;
    }
    .input-wrapper,
    .ant-checkbox-wrapper {
      padding-left: 10px;
    }
    .describe {
      font-size: 12px;
      color: #8c8c8c;
      height: 20px;
      line-height: 20px;
      margin-top: 4px;
    }
    .describe-p {
      margin: 0px;
      line-height: 20px;
      padding-left: 22px;
      color: #8c8c8c;
    }
    .describe-intercon {
      color: @color-black-3;
      .font-size-1;
    }
    .tags {
      position: relative;
      width: 100%;
      border-radius: 4px;
      min-height: 92px;
      max-height: 200px;
      overflow-y: auto;
      background-color: #ffffff;
      border: solid 1px #e8e8e8;
      padding: 5px;
      display: flex;
      flex-wrap: wrap;
      div,
      a,
      img,
      i,
      span {
        margin: 0;
        padding: 0;
      }
      .ant-tag {
        position: relative;
        display: flex;
        align-items: center;
        min-width: 70px;
        max-width: 130px;
        height: 32px;
        margin: 0 8px 8px 0;
        border: none;
        img {
          width: 20px;
          height: 20px;
          border-radius: 2px;
          margin: 0 6px;
        }
        .name {
          height: 22px;
          line-height: 22px;
          font-size: 14px;
          max-width: 70px;
          min-width: 28px;
          margin-right: 8px;
          margin-left: 8px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .anticon-cross {
          font-size: 14px;
          margin-right: 6px;
          font-weight: 400;
        }
      }
      .btn.ant-btn {
        width: 64px;
        height: 32px;
        display: inline-block;
      }
      .btn:hover {
        color: #32b5c5;
        border-color: #32b5c5;
      }
      .btn:focus {
        color: #333;
        border-color: #e6e6e6;
      }
    }
  }
}
