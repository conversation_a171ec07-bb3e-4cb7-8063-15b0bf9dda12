@import '~@ekuaibao/web-theme-variables/styles/default';

.interconnectal-custom-dimension-filter-row {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  width: 100%;

  > .ant-row {
    width: 92%;
  }

  //> :first-child {
  //  flex-grow: 1;
  //}

  > .action {
    width: 48px;
    box-sizing: border-box;
    padding-left: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    > img {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
  .del-defalut-style[type='number'] {
    -moz-appearance: textfield;
  }
  .del-defalut-style[type='number']::-webkit-inner-spin-button,
  .del-defalut-style[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .picker-box{
    width:100%;
    .ant-input{
      height:30px;
    }
  }
}
.Cascader-styles {
  width: 160px;
  .ant-cascader-menu {
    width: 100%;
    height: auto;
    max-height: 180px;
  }
  .ant-cascader-menu:last-child{
    background: @btn-primary-color;
    border: 1px solid @border-color-base;
  }
}


.custom-dimension-filter-row-wrapper{
  display: flex;
  flex-direction: row;
  align-items: center;
  &-operator{
    width: 20px;
    text-align: center;
    margin-right: 4px;
  }
  + .custom-dimension-filter-row-wrapper{
    margin-top: 8px;
  }
}
