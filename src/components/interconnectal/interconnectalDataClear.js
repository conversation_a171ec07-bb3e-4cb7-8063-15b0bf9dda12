/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import styles from './interconnectalDataClear.module.less'

import { Button } from 'antd'
@EnhanceField({
  descriptor: {
    type: 'interconnectal-dataclear'
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback()
  },
  wrapper: wrapper()
})
export default class InterconSelectRelation extends PureComponent {
  handleClick = () => {
    let { value, field, entityInfo } = this.props
    api
      .open('@third-party-manage:ConfigDataClear', {
        entityInfo
      })
      .then(res => {})
  }
  render() {
    let { value, field, entityInfo } = this.props
    return (
      <div className={styles['interconnectal-dataclear']}>
        <div className="div1">{i18n.get('初始化将清空该业务对象下的所有数据，但不会改变该业务对象的配置信息')}</div>
        <Button type="danger" onClick={this.handleClick}>
          {i18n.get('初始化')}
        </Button>
      </div>
    )
  }
}
