/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17 下午6:33.
 */
import React, { PureComponent } from 'react'
import { RoleSelected } from '../../ekb-components'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'

@EnhanceField({
  descriptor: {
    type: 'select:admin'
  },
  validator: field => (level, value, callback) => {
    callback(required(field, value))
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
})
@EnhanceConnect(state => ({
  staffs: state['@common'].staffs
}))
export default class PlatFormAdmin extends PureComponent {
  selectedStaffs = []

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  handleChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  valueChange = value => {
    const ids = value.map(staff => staff.id)
    this.handleChange(ids)
  }

  onClick = value => {
    const users = this.selectedStaffs.map(staff => staff.id)
    api.emit('@vendor:' + 'select-multiple-user', {
      checkedKeys: users,
      callback: staffs => {
        const ids = staffs.map(staff => staff.id)
        this.handleChange(ids)
      }
    })
  }

  getSelectStaffs = () => {
    const { value, staffs } = this.props
    // 这能不卡?
    const selectedStaffs = value ? staffs.filter(staff => !!~value.indexOf(staff.id)) : []
    this.selectedStaffs = selectedStaffs
    return selectedStaffs
  }

  render() {
    const selectedStaffs = this.getSelectStaffs()

    return <RoleSelected tags={selectedStaffs} onClick={this.onClick} onChange={this.valueChange} />
  }
}
