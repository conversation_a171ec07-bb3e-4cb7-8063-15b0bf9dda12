import React, { PureComponent } from 'react'
import { Select, Radio } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import { EKBSelect } from '../../ekb-components/index'
import style from './TaxRuleField.module.less'
import { wrapper } from '../layout/FormWrapper'
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'interconnectal-tax-rule'
    },
    validator: (field, props) => (rule, value = {}, callback) => {
        const { rule, legalEntity, taxpayerType } = value
        if (!rule || (!legalEntity && !taxpayerType)) {
            callback('为选择相关选项,请检查')
        } else {
            callback()
        }
    },
    wrapper: wrapper()
})
export default class TaxRuleField extends PureComponent<any, any> {
    onChange = (type, vv) => {
        const { value = {}, onChange } = this.props
        const newValue = { ...value, [type]: vv }
        onChange && onChange(newValue)
    }

    render() {
        const { value = {}, field, isEdit } = this.props
        const { dimensionList, taxPayerTypeList } = field
        const { rule, legalEntity, taxpayerType } = value
        const params = { value: legalEntity, placeholder: "请选择法人实体", disabled: false, tags: dimensionList, mode: 'multiple' }

        return (
            <div className={style.taxRuleField}>
                <div className="type">
                    <div className="label">{i18n.get('请选择规则类型')}</div>
                    <Select
                        style={{ width: 148 }}
                        onChange={this.onChange.bind(this, 'rule')}
                        value={rule}
                        disabled={isEdit}
                    >
                        {[{ name: '按纳税人类型', key: 'TAXPAYER' }, { name: '按法人实体', key: 'LEGAL' }].map((v, index) => {
                            return (
                                <Select.Option key={v.key} value={v.key}>
                                    {v.name}
                                </Select.Option>
                            )
                        })}
                    </Select>
                </div>
                {value.rule === 'LEGAL' && <div className="dimensionType">
                    <div className="label">{i18n.get('法人实体')}</div>
                    <EKBSelect {...params} onChange={(value) => this.onChange('legalEntity', value)} />
                </div>}
                {value.rule === 'TAXPAYER' && <div className="taxerType">
                    <div className="label">{i18n.get('纳税人类型')}</div>
                    <Radio.Group className="tax-radio" onChange={(e) => this.onChange('taxpayerType', e.target.value)} value={taxpayerType}>
                        {taxPayerTypeList?.map(v => (<Radio key={v.id} value={v.id}>
                            {v.name}
                        </Radio>
                        ))}

                    </Radio.Group>
                </div>}
            </div>
        )
    }
}



