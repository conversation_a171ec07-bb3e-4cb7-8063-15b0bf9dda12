import React from 'react'
import { Checkbox } from 'antd'
import { FormItemProps } from '../layout/types'

export function checkboxLabel(label: string, classNames: string, props: FormItemProps) {
  const visible = props.field.inputVisible !== false
  return (
    <Checkbox
      checked={visible}
      onChange={e => {
        props.field.inputVisible = e.target.checked
        if (!props.field.inputVisible) {
          // props.form.validateFields([props.field.name], () => void 0)
        }
        props.form.setFields({})
      }}
      className={classNames}
    >
      {visible ? label + i18n.get('：') : label}
    </Checkbox>
  )
}

export function hideInput(props: FormItemProps) {
  return props.field.inputVisible === false
}

export function isEmpty(value: any) {
  return Array.isArray(value) ? value.length === 0 : !value
}
