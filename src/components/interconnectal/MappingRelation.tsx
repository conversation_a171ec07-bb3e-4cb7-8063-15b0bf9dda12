import React, { PureComponent } from 'react'
import { RoleSelected } from '../../ekb-components'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EKBSelect } from '../../ekb-components/index'
// @ts-ignore
import SVG_ADD from './images/add.svg'
// @ts-ignore
import SVG_DELETE from './images/delete.svg'
import { uuid } from '@ekuaibao/helpers'
import { set } from 'lodash'
import styles from './MappingRelation.module.less'
import MappingRelationItem from '../../elements/puppet/MappingRelationItem'

interface IProps {
  field: any
  value: any
  onChange: (value: IItem[]) => void
}

interface IState {
  value: IItem[]
}

interface IItem {
  sourceField: string
  targetField: string
  id?: string
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'mapping:relation'
  },
  validator: () => (level, value, callback) => {
    if (!value || value.find(v => !v.sourceField || !v.targetField)) {
      return callback(i18n.get('请完善信息'))
    }
    callback()
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
})
export default class MappingRelation extends PureComponent<IProps, IState> {
  targetTags: Array<{ field: string }>
  constructor(props: IProps) {
    super(props)
    this.targetTags = props?.field?.targetTags || []
    this.state = {
      value: props.value?.length
        ? props.value.map((item: IItem) => ({
            ...item,
            id: uuid(10)
          }))
        : [{ sourceField: '', targetField: '', id: uuid(10) }]
    }
  }

  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (this.targetTags !== nextProps.field?.targetTags) {
      this.targetTags = nextProps.field?.targetTags
      nextProps.value?.length && this.valueChangeByLatitude(nextProps)
    }
  }

  handleChange = (key: string, id: string, result: any) => {
    const { value } = this.state
    const idx = value.findIndex((line: IItem) => line.id === id)
    set(value[idx], `${key}`, result)
    this.valueChange(value)
  }

  valueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  valueChangeByLatitude = (props: IProps) => {
    const targetTags = this.targetTags.map(v => v.name)
    let { value } = props
    value = value.map(v => {
      return targetTags.includes(v.targetField)
        ? { ...v, id: uuid(10) }
        : { sourceField: '', targetField: '', id: uuid(10) }
    })
    this.setState({ value })
    this.valueChange(value)
  }

  handleAdd = (id: string) => {
    const idx = this.state.value.findIndex((line: IItem) => line.id === id)
    const item = { sourceField: '', targetField: '', id: uuid(10) }
    this.state.value.splice(idx + 1, 0, item)
    this.forceUpdate()
  }

  handleDelete = (id: string) => {
    const idx = this.state.value.findIndex((line: IItem) => line.id === id)
    this.state.value.splice(idx, 1)
    this.forceUpdate()
  }

  formatTargetTags = targetTags => {
    return targetTags.map(v => ({
      id: v.name,
      name: v.label,
      ...v
    }))
  }

  renderMapItem = (line: IItem) => {
    const {
      field: { title, sourceTags, targetTags }
    } = this.props
    const { id, sourceField, targetField } = line
    const maxLength = targetTags?.length || 1
    return (
      <div className="mapping-relation-item">
        {i18n.get('将「title」中的', { title })}
        &nbsp;&nbsp;
        <MappingRelationItem
          id={id}
          sourceField={sourceField}
          sourceTags={sourceTags}
          targetField={targetField}
          targetTags={this.formatTargetTags(targetTags)}
          onChange={this.handleChange}
        />
        {this.state.value?.length < maxLength && (
          <img className="action-img" src={SVG_ADD} onClick={this.handleAdd.bind(this, id)} />
        )}
        {this.state.value?.length > 1 && (
          <img className="action-img" src={SVG_DELETE} onClick={this.handleDelete.bind(this, id)} />
        )}
      </div>
    )
  }

  render() {
    return (
      <div className={styles['mapping-relation-wrapper']}>
        {this.state.value.map((line: IItem) => this.renderMapItem(line))}
      </div>
    )
  }
}
