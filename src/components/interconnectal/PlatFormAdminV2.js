/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17 下午6:33.
 */
import React, {PureComponent, useCallback, useEffect, useMemo, useRef, useState} from 'react'
import { RoleSelected } from '../../ekb-components'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { app as api } from '@ekuaibao/whispered'

@EnhanceField({
  descriptor: {
    type: 'select:admin:v2'
  },
  validator: field => (level, value, callback) => {
    callback(required(field, value))
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
})
export default class PlatFormAdminV2 extends PureComponent {
  render() {
    const { field } = this.props
    return <MemberSelect {...this.props} type={field?.componentType} />
  }

}

const MemberSelectDefaultType = ['department-member', 'department', 'role']
/**
 * 本地化旧版 charge 用的人员选择组件
 * 在原有基础上增加了允许选择角色和部门
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
const MemberSelect = props => {
  const {
    value,
    max,
    type = MemberSelectDefaultType,
    onChange,
  } = props
  const componentTypeRef = useRef(type ?? MemberSelectDefaultType)
  const [loading, setLoading] = useState(false)
  const [useStaff, useDepartment, useRole] = useMemo(() => {
    const componentType = componentTypeRef.current
    const useStaff = componentType.includes('department-member')
    const useDepartment = componentType.includes('department')
    const useRole = componentType.includes('role')
    return [useStaff, useDepartment, useRole]
  }, [])

  // 初始化
  useEffect(() => {
    async function init() {
      try {
        setLoading(true)
        await Promise.all([
          useStaff ? api.dataLoader('@common.staffs').load() : Promise.resolve(),
          useRole ? api.dataLoader('@common.roleList').load() : Promise.resolve(),
          useDepartment ? api.dataLoader('@common.department').load() : Promise.resolve(),
        ])
      } finally {
        setLoading(false)
      }
    }

    init()
  }, [])


  const list = useMemo(() => {
    const { '@common': common } = api.getState()
    const { role = [], department = [], departmentMember = [] } = value ?? {}
    const {
      staffs: staffSource,
      roleList: roleSource,
      department: { data: departmentSource },
    } = common

    let result = []

    if (useDepartment) {
      const flatDepartmentTree = (tree) => {
        let obj = {}
        for (const item of tree) {
          obj[item.id] = item
          if (item.children && item.children.length > 0) {
            obj = Object.assign({}, obj, flatDepartmentTree(item.children))
          }
        }
        return obj
      }
      const departmentMap = flatDepartmentTree(departmentSource)
      const departmentList = department.map((id) => departmentMap[id] ?? { id, name: id, active: false })
      result = result.concat(departmentList)
    }

    if (useStaff) {
      const staffMap = staffSource.reduce(
          (r, staff) => Object.assign(r, { [staff.id]: staff }),
          {},
      )
      const memberList = departmentMember.map((id) => staffMap[id] ?? { id, name: id, active: false })
      result = result.concat(memberList)
    }

    if (useRole) {

      const roleMap = roleSource.reduce(
          (r, role) => Object.assign(r, { [role.id]: role }),
          {},
      )

      const roleList = role.map((id) => roleMap[id] ?? { id, name: id, active: false })
      result = result.concat(roleList)
    }

    return result
  }, [value, loading])

  const handleSelectMember = useCallback(async () => {
    const props = {
      checkedList: [
        { type: 'department-member', multiple: true, checkedKeys: value?.departmentMember ?? [] },
        { type: 'role', multiple: true, checkedKeys: value?.role ?? [] },
        { type: 'department', multiple: true, checkedKeys: value?.department ?? [] },
      ],
      max,
      departmentsIncludeChildren: value?.departmentsIncludeChildren ?? true,
    }

    props.checkedList = props.checkedList.filter((v) => componentTypeRef.current.includes(v.type))
    const result = await api.open('@layout:SelectStaffsModal', props)

    const selectedMember = result.checkedList.reduce((r, item) => {
      switch (item.type) {
        case 'department':
          return Object.assign(r, { department: item.checkedKeys })
        case 'department-member':
          return Object.assign(r, { departmentMember: item.checkedKeys })
        case 'role':
          return Object.assign(r, { role: item.checkedKeys })
        default:
          return r
      }
    }, {})

    onChange?.({
      ...selectedMember,
      departmentsIncludeChildren: result.departmentsIncludeChildren,
    })
  }, [value])


  /**
   * 删除用户
   */
  const handleDeleteMember = useCallback(
    newResult => {
      const { role = [], department = [], departmentMember = [], departmentsIncludeChildren = [] } = value
      const newAllIdMap = newResult.reduce((r, v) => Object.assign(r, { [v.id]: true }), {})
      const deleteIds = list.map(v => v.id).filter(id => !newAllIdMap[id])
      onChange?.({
        role: role.filter(id => !deleteIds.includes(id)),
        department: department.filter(id => !deleteIds.includes(id)),
        departmentMember: departmentMember.filter(id => !deleteIds.includes(id)),
        departmentsIncludeChildren
      })
    },
[list, value])

  return <RoleSelected tags={list} onClick={handleSelectMember} onChange={handleDeleteMember} />
}
