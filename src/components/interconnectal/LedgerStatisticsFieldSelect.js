/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import styles from './InterconSelect.module.less'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { EKBSelect } from '../../ekb-components/index'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EKBInput } from '../../ekb-components/index'
import { Checkbox } from 'antd'
import { isObject } from '@ekuaibao/helpers'

@EnhanceField({
  descriptor: {
    type: 'ledger-statistics-field-select'
  },
  validator: field => (rule, value, callback) => {
    if (field.validator) {
      field.validator(rule, value, callback)
      return
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class LedgerStatisticsFieldSelect extends PureComponent {
  onChange = value => {
    const { tags, hiddenUnit } = this.props.field
    const item = tags?.find(item => item.id === value)
    hiddenUnit ? this.props.onChange(value) : this.valueChange(item)
  }

  valueChange = o => {
    const { value, field } = this.props
    const newValue = { ...value, ...o }
    if (field.isReConfig) {
      newValue.unit = value?.unit || newValue.unit
    }
    this.props?.onChange(newValue)
  }

  unitChange = value => {
    this.valueChange({ unit: value })
  }

  checkChange = e => {
    const { apportionName } = this.props.field
    this.valueChange({ [apportionName]: e.target.checked })
  }

  render() {
    const { value, field, hidden } = this.props
    if (hidden) return null
    const {
      placeholder,
      editable,
      tags,
      describe,
      mode,
      optionFilterProp,
      groupOption,
      isReConfig,
      apportionName,
      apportionLabel,
      hiddenUnit,
      addTitle,
      addHandel
    } = field
    const params = {
      value: isObject(value) ? value?.id : value,
      placeholder,
      disabled: !editable,
      tags,
      mode,
      optionFilterProp,
      groupOption,
      style: { flex: 1 },
      addTitle,
      addHandel
    }
    return (
      <div className={styles['select-wrapper']}>
        <div className="select-content">
          <EKBSelect {...params} onChange={this.onChange} />
          {value?.type?.toLowerCase() === 'number' && !hiddenUnit && (
            <EKBInput
              addonBefore={i18n.get('单位')}
              value={value?.unit}
              disabled={isReConfig}
              onChange={this.unitChange}
              style={{ width: 100, marginLeft: 8 }}
            />
          )}
          {value?.id === 'amount' && (
            <Checkbox onChange={this.checkChange} checked={value[apportionName]}>
              {apportionLabel}
            </Checkbox>
          )}
        </div>
        {describe ? <span className="describe-intercon">{describe}</span> : null}
      </div>
    )
  }
}
