/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2022-06-07 11:03:50
 * @Description  : mc轻共享的下发权限编辑按钮控制 适用于档案下发、企业管理页面
 * 描述功能/使用范围/注意事项
 */

import React, {Component} from 'react'
import { Fetch } from '@ekuaibao/fetch'
import AuthorityEnum from './enum'
import {app} from '@ekuaibao/whispered'
interface IProps{
    corpId:string  // 企业id 
    targetCorpId:string // 目标企业id、租户id
    menuKey:string // 菜单key 权限
    children?: any
}

const AuthorizedCache:any = {}

class MCAuthorizedEdit extends Component<IProps> {
    private  editAuthString = 'edit'
    private static List = [] 
    
    state ={
        authorData:[],
        targetCorpId:'', // 子租户企业
    }
    static async reloadAuth(){
      // 组件实例化到当前list，再根据list 去更新auth 权限
       MCAuthorizedEdit.List.forEach((v,index)=>{
            v.reloadAuth()
       })
    }
    
    componentDidMount() {
        const {corpId , targetCorpId,menuKey } = this.props
        const key = `${targetCorpId || corpId}-${menuKey}`
        MCAuthorizedEdit.List.push(this)
        this.reloadAuth()
    }

    componentWillUnmount(){
        MCAuthorizedEdit.List = MCAuthorizedEdit.List.filter(v=>v !== this)
    }

    getAuth = async ()=>{
        const { corpId } = this.props
        const param = { 
            corpId:corpId,
        }

       const dispatch = Fetch.GET('/api/v1/group/center/rightsGroup/byKey',param)
       return dispatch
    }
    reloadAuth(){
        const {corpId , targetCorpId,menuKey } = this.props
        const key = `${targetCorpId || corpId}-${menuKey}`

        let promise: Promise<any>

        if (AuthorizedCache && AuthorizedCache[key]) {
            promise = AuthorizedCache[key]
        } else {

            AuthorizedCache[key] = this.getAuth()
            promise = AuthorizedCache[key]

            setTimeout(() => {
                // 30秒后清掉获取权限并发请求清楚
                AuthorizedCache[key] = undefined
            }, 10000)
        }
        promise.then((data) => {
            this.setState({ authorData:data.items || [],targetCorpId})
        })
    }
    
    componentWillReceiveProps(nextProps: any) {
        if(nextProps.targetCorpId !== this.props.targetCorpId){
            this.setState({targetCorpId:nextProps.targetCorpId})
        }   
    }

    checkAuthority(){
        const { menuKey } = this.props
        const {authorData,targetCorpId} = this.state
        let auth:boolean = false
        const corporation = app.getState()['@common'].userinfo?.corporation?.id // 当前企业id 

        for (let i = 0; i < authorData.length; i++) {
            const v = authorData[i]
            const tagName = Object.getOwnPropertyNames(v)[0] 
            if(tagName === targetCorpId){
                auth = (v[targetCorpId][menuKey] || []).includes(this.editAuthString)
                break
            }else if(!targetCorpId && tagName.includes(corporation)){
                // 获取主租户，只有主租户有企业权限
                auth = (v[tagName][menuKey] || []).includes(this.editAuthString)
                break
            }
        }
        console.log('当前权限 ===',auth,authorData)
        console.log(menuKey,targetCorpId,'==== 当前权限props =====')
        return auth
    }
    render() {
        return (
            <div className={`${this.checkAuthority() ? 'dis-i': 'dis-none'}`}>{this.props.children}</div>
        )
    }
}

export default MCAuthorizedEdit
