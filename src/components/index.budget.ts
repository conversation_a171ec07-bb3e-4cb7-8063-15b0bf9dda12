/**
 * <AUTHOR>
 * @Date :2020/08/20
 */
import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

const IndexBudget = loadableWithDescriptor(() => import('./budget/BudgetConfig'), {
  type: 'budgetConfig'
})
export const budgetConfig = [IndexBudget]
