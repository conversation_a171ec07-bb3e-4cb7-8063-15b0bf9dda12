import styles from './index.module.less'
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Checkbox, Radio } from '@hose/eui'

export interface ListItemProps {
  type?: 'checkbox' | 'radio'
  rowIndex: string | number
  disabled?: boolean
  checked?: boolean
  children?: React.ReactNode
  onChange?: (e: any) => void
}

export const ListItem: React.FC<ListItemProps> = ({
  type = 'checkbox',
  rowIndex,
  disabled = false,
  checked = false,
  children,
  onChange
}) => {
  if (type === 'radio') {
    return (
      <Radio 
        disabled={disabled}
        onChange={onChange}
        value={rowIndex}
        checked={checked}
      >
        {children}
      </Radio>
    )
  }

  return (
    <Checkbox 
      checked={checked}
      disabled={disabled}
      onChange={onChange}
    >
      {children}
    </Checkbox>
  )
}

export interface ListProps {
  dataList: Array<{
    text?: string
    label?: string
    dataIndex?: string
    value?: any
    title?: string
  }>
  className?: string
  selectedRowKeys?: (string | number)[]
  canSearch?: boolean
  onChange?: (selectedDataIndexes: any[], selectedRowKeys: (string | number)[]) => void
}

const List: React.FC<ListProps> = ({
  dataList = [],
  className = '',
  selectedRowKeys = [],
  canSearch = false,
  onChange
}) => {
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<(string | number)[]>(selectedRowKeys)

  // 同步外部选中状态
  useEffect(() => {
    setInternalSelectedKeys(selectedRowKeys)
  }, [selectedRowKeys])

  const handleItemChange = useCallback((e: any, value: any, index: number, dataIndex: string) => {
    const checked = e.target.checked
    const checkedData = canSearch ? dataIndex : index
    
    let newSelectedKeys = internalSelectedKeys.filter(k => k !== checkedData)
    
    if (checked) {
      newSelectedKeys.push(checkedData)
    }
    
    setInternalSelectedKeys(newSelectedKeys)
    
    // 转换为 dataIndexes
    const selectedDataIndexes = canSearch 
      ? newSelectedKeys 
      : newSelectedKeys.map(idx => dataList[idx as number]?.value)
    
    onChange?.(selectedDataIndexes, newSelectedKeys)
  }, [internalSelectedKeys, canSearch, dataList, onChange])

  const renderItems = useMemo(() => {
    return dataList.map((line, index) => {
      const { text, label, dataIndex, value, title } = line
      const rowIndex = canSearch ? dataIndex : index
      const isChecked = internalSelectedKeys.includes(rowIndex)
      
      return (
        <li key={index} className="ant-dropdown-menu-item">
          <ListItem 
            rowIndex={rowIndex}
            checked={isChecked}
            onChange={(e) => handleItemChange(e, value, index, dataIndex)}
          >
            {text || label || title}
          </ListItem>
        </li>
      )
    })
  }, [dataList, canSearch, internalSelectedKeys, handleItemChange])

  return (
    <ul className={`${styles.wrapper} ${className} ant-dropdown-menu-vertical ant-dropdown-menu-without-submenu ant-dropdown-menu-root`}>
      {renderItems}
    </ul>
  )
}

export default List 