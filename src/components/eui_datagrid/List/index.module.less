/**************************************************
 * List Styles
 **************************************************/

.wrapper {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  
  .listItem {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.selected {
      background-color: #e6f7ff;
      border-color: #91d5ff;
    }
  }
  
  .checkbox {
    width: 100%;
    
    :global {
      .ant-checkbox-wrapper {
        width: 100%;
        margin: 0;
        
        .ant-checkbox + span {
          padding-left: 8px;
          padding-right: 8px;
        }
      }
    }
  }
  
  .empty {
    padding: 24px;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}

.selectAll {
  padding: 8px 12px;
  border-bottom: 2px solid #f0f0f0;
  background-color: #fafafa;
  font-weight: 500;
  
  .selectAllCheckbox {
    :global {
      .ant-checkbox-wrapper {
        font-weight: 500;
      }
    }
  }
} 