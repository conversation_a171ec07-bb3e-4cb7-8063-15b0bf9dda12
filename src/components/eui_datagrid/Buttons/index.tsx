import React, { useCallback } from 'react'
import { Button, Dropdown, Menu, Space } from '@hose/eui'
import { OutlinedDirectionDown } from '@hose/eui-icons'
import styles from './index.module.less'

export interface ButtonsProps {
  selected?: number
  total?: number
  buttons?: any[]
  isButtonsBindMultiSelect?: boolean
  showMoreButtonCount?: number
  onClick?: (button: any) => void
}

const Buttons: React.FC<ButtonsProps> = ({
  selected = 0,
  total = 0,
  buttons = [],
  isButtonsBindMultiSelect = true,
  showMoreButtonCount = 3,
  onClick
}) => {
  const handleMenuClick = useCallback((e: any) => {
    onClick?.(e.key)
  }, [onClick])

  const renderButton = useCallback(({ 
    line, 
    isButtonsBindMultiSelect, 
    total, 
    selected, 
    onClick, 
    index, 
    primary 
  }: any) => {
    const {
      text, 
      className, 
      isActive,
      isBindMultiSelect = isButtonsBindMultiSelect,
      isBindTotal,
      ...others
    } = line

    let disabled = false
    if (isBindMultiSelect) {
      disabled = isActive ? !isActive : selected === 0
    }
    if (isBindTotal && !total) {
      disabled = true
    }
    
    const clsssName = primary === 'primary' ? styles.button_primary : styles.ekb_buttons
    
    return (
      <Button 
        key={index} 
        onClick={() => onClick?.(line)}
        disabled={disabled}
        className={`${clsssName} ${className || ''}`}
        {...others}
      >
        {text}
      </Button>
    )
  }, [])

  const renderPrimaryButton = useCallback(() => {
    const primary = 'primary'
    const primaryButtons = buttons.filter(button => button.type === primary)
    return primaryButtons.map((line, index) => {
      return renderButton({ 
        line, 
        isButtonsBindMultiSelect, 
        total, 
        selected, 
        onClick, 
        index, 
        primary 
      })
    })
  }, [buttons, isButtonsBindMultiSelect, total, selected, onClick, renderButton])

  const renderMoreButton = useCallback(() => {
    const _buttons = buttons.filter(button => button.type !== 'primary')
    const showButtons = _buttons.slice(0, showMoreButtonCount)
    const moreButtons = _buttons.slice(showMoreButtonCount)
    
    const menu = (
      <Menu onClick={handleMenuClick}>
        {moreButtons.map((line, index) => {
          const {
            isActive,
            isBindMultiSelect = isButtonsBindMultiSelect,
            isBindTotal,
          } = line
          
          let disabled = false
          if (isBindMultiSelect) {
            disabled = isActive ? !isActive : selected === 0
          }
          if (isBindTotal && !total) {
            disabled = true
          }
          
          return (
            <Menu.Item key={index} disabled={disabled}>
              <span onClick={() => onClick?.(line)}>{line.text}</span>
            </Menu.Item>
          )
        })}
      </Menu>
    )
    
         return (
       <Space>
         {showButtons.map((line, index) => {
           return renderButton({ 
             line, 
             isButtonsBindMultiSelect, 
             total, 
             selected, 
             onClick, 
             index 
           })
         })}
         <Dropdown overlay={menu}>
           <Button>
             更多 <OutlinedDirectionDown />
           </Button>
         </Dropdown>
       </Space>
     )
  }, [buttons, showMoreButtonCount, handleMenuClick, isButtonsBindMultiSelect, selected, total, onClick, renderButton])

  const renderButtons = useCallback(() => {
    const showMoreButton = buttons.length > showMoreButtonCount
    const _buttons = buttons.filter(button => button.type !== 'primary')
    
    if (showMoreButton) {
      return renderMoreButton()
    } else {
             return (
         <Space>
           {_buttons.map((line, index) => {
             return renderButton({ 
               line, 
               isButtonsBindMultiSelect, 
               total, 
               selected, 
               onClick, 
               index 
             })
           })}
         </Space>
       )
    }
  }, [buttons, showMoreButtonCount, renderMoreButton, isButtonsBindMultiSelect, total, selected, onClick, renderButton])

  return (
    <div className={styles.button_wrapper}>
      {renderPrimaryButton()}
      {renderButtons()}
    </div>
  )
}

export default Buttons 