/**************************************************
 * ListFilter Styles
 **************************************************/

.container {
  min-width: 200px;
  max-width: 300px;
  padding: 8px;
}

.listContainer {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  
  :global {
    .ant-checkbox-wrapper {
      padding: 8px 12px;
      margin: 0;
      width: 100%;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.filterIcon {
  cursor: pointer;
  transition: color 0.3s;
  
  &:hover {
    color: var(--brand-base, #22b2cc);
  }
}

.filtered {
  color: #108ee9;
}

.unfiltered {
  color: #aaa;
} 