import React, { useCallback, useMemo } from 'react'
import { OutlinedEditFilter } from '@hose/eui-icons'
import { encodeWithPrefix, decodeWithPrefix } from '../../utils'
import Footer from '../../Footer'
import List from '../../List'
import styles from './index.module.less'

interface ListFilterProps {
  columnName: string
  filterDataSource: any[]
  bus: any
  state: any
  setState: (state: any) => void
}

export const ListFilter: React.FC<ListFilterProps> = ({
  columnName,
  filterDataSource,
  bus,
  state,
  setState
}) => {
  const filterDropdownVisible = encodeWithPrefix(state, 'filterDropdownVisible', columnName)
  const linkedValue = state.fetchParams.filters[columnName]

  const listDataIndexes = useMemo(() => {
    let indexes = encodeWithPrefix(state, 'LIST_DATA_INDEXES', columnName)
    
    if (linkedValue === undefined) {
      return []
    }

    if (linkedValue && linkedValue.length > 0) {
      return linkedValue.map((o: any) => 
        filterDataSource.findIndex(v => v.value === o)
      )
    }

    return indexes || []
  }, [state, columnName, linkedValue, filterDataSource])

  const filtered = !!(linkedValue && linkedValue.length > 0)

  const onSearch = useCallback(() => {
    const linkedValue = state.fetchParams.filters[columnName]
    bus.emit('column:filter', linkedValue, columnName)
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', false, columnName),
    })
  }, [state, bus, columnName, setState])

  const onChange = useCallback((value: any[], indexes: number[], b?: any, c?: any, fn?: () => void) => {
    let { fetchParams } = state
    let { filters } = fetchParams
    filters = { ...filters, [columnName]: value }
    fetchParams = { ...fetchParams, filters }
    setState({
      ...state,
      fetchParams,
      ...decodeWithPrefix('LIST_DATA_INDEXES', indexes, columnName),
    }, fn)
  }, [state, columnName, setState])

  const onReset = useCallback(() => {
    onChange([], [], undefined, undefined, onSearch)
  }, [onChange, onSearch])

  const onFilterDropdownVisibleChange = useCallback((visible: boolean) => {
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', visible, columnName)
    })
  }, [state, setState, columnName])

  const filterDropdown = (
    <div className={styles.container}>
      <Footer onOk={onSearch} onReset={onReset}>
        <List 
          dataList={filterDataSource}
          selectedRowKeys={listDataIndexes}
          onChange={onChange}
          className={styles.listContainer}
        />
      </Footer>
    </div>
  )

  const filterIcon = (
    <OutlinedEditFilter 
      className={filtered ? styles.filtered : styles.unfiltered}
      style={{ color: filtered ? '#108ee9' : '#aaa' }}
    />
  )

  return {
    filterDropdown,
    filterIcon,
    filterDropdownVisible,
    onFilterDropdownVisibleChange,
  }
}

export default ListFilter 