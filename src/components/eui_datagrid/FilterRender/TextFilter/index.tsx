import React, { useCallback } from 'react'
import { Input } from '@hose/eui'
import { OutlinedEditFilter } from '@hose/eui-icons'
import Footer from '../../Footer'
import { encodeWithPrefix, decodeWithPrefix } from '../../utils'
import styles from './index.module.less'

interface TextFilterProps {
  columnName: string
  bus: any
  state: any
  setState: (state: any) => void
}

export const TextFilter: React.FC<TextFilterProps> = ({
  columnName,
  bus,
  state,
  setState
}) => {
  const filterDropdownVisible = encodeWithPrefix(state, 'filterDropdownVisible', columnName)
  const linkedValue = state.fetchParams.filters[columnName] || ''
  const filtered = !!linkedValue

  const onSearch = useCallback(() => {
    const linkedValue = state.fetchParams.filters[columnName]
    bus.emit('column:filter', linkedValue, columnName)
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', false, columnName),
    })
  }, [state, bus, columnName, setState])

  const onChange = useCallback((e: React.ChangeEvent<HTMLInputElement>, a?: any, b?: any, c?: any, fn?: () => void) => {
    const value = e.target.value
    let { fetchParams } = state
    let { filters } = fetchParams
    filters = { ...filters, [columnName]: value }
    fetchParams = { ...fetchParams, filters }
    setState({ ...state, fetchParams }, fn)
  }, [state, columnName, setState])

  const onReset = useCallback(() => {
    onChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>, undefined, undefined, undefined, onSearch)
  }, [onChange, onSearch])

  const onFilterDropdownVisibleChange = useCallback((visible: boolean) => {
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', visible, columnName)
    })
  }, [state, setState, columnName])

  const filterDropdown = (
    <div className={styles.container}>
      <Footer onOk={onSearch} onReset={onReset}>
        <Input.Search 
          value={linkedValue}
          onChange={onChange}
          onSearch={onSearch}
          onPressEnter={onSearch}
          placeholder="请输入搜索内容"
          className={styles.searchInput}
        />
      </Footer>
    </div>
  )

  const filterIcon = (
    <OutlinedEditFilter 
      className={filtered ? styles.filtered : styles.unfiltered}
      style={{ color: filtered ? '#108ee9' : '#aaa' }}
    />
  )

  return {
    filterDropdown,
    filterIcon,
    filterDropdownVisible,
    onFilterDropdownVisibleChange,
  }
}

export default TextFilter 