/**************************************************
 * NumberRangeFilter Styles
 **************************************************/

.container {
  min-width: 280px;
  padding: 8px;
}

.numberRange {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .inp {
    flex: 1;
  }

  .sep {
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #666;
    font-weight: 500;
  }
}

.filterIcon {
  cursor: pointer;
  transition: color 0.3s;
  
  &:hover {
    color: var(--brand-base, #22b2cc);
  }
}

.filtered {
  color: #108ee9;
}

.unfiltered {
  color: #aaa;
} 