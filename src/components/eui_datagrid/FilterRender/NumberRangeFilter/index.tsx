import React, { useCallback } from 'react'
import { OutlinedEditFilter } from '@hose/eui-icons'
import Footer from '../../Footer'
import {
  encodeWithPrefix,
  decodeWithPrefix,
} from '../../utils'
import NumberRange from '../../NumberRange'
import styles from './index.module.less'

interface NumberRangeFilterProps {
  columnName: string
  bus: any
  state: any
  setState: (state: any) => void
}

export const NumberRangeFilter: React.FC<NumberRangeFilterProps> = ({
  columnName,
  bus,
  state,
  setState
}) => {
  const filterDropdownVisible = encodeWithPrefix(state, 'filterDropdownVisible', columnName)
  const linkedValue = state.fetchParams.filters[columnName]
  const filtered = !!linkedValue

  const onSearch = useCallback(() => {
    const linkedValue = state.fetchParams.filters[columnName]
    bus.emit('column:filter', linkedValue, columnName)
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', false, columnName),
    })
  }, [state, bus, columnName, setState])

  const onChange = useCallback((value: any, a?: any, b?: any, c?: any, fn?: () => void) => {
    let { fetchParams } = state
    let { filters } = fetchParams
    filters = { ...filters, [columnName]: value }
    fetchParams = { ...fetchParams, filters }
    setState({ ...state, fetchParams }, fn)
  }, [state, columnName, setState])

  const onReset = useCallback(() => {
    onChange(undefined, undefined, undefined, undefined, onSearch)
  }, [onChange, onSearch])

  const onFilterDropdownVisibleChange = useCallback((visible: boolean) => {
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', visible, columnName)
    })
  }, [state, setState, columnName])

  const filterDropdown = (
    <div className={styles.container}>
      <Footer onOk={onSearch} onReset={onReset}>
        <NumberRange 
          value={linkedValue}
          onChange={onChange}
          className={styles.numberRange}
        />
      </Footer>
    </div>
  )

  const filterIcon = (
    <OutlinedEditFilter  
      className={filtered ? styles.filtered : styles.unfiltered}
      style={{ color: filtered ? '#108ee9' : '#aaa' }}
    />
  )

  return {
    filterDropdown,
    filterIcon,
    filterDropdownVisible,
    onFilterDropdownVisibleChange,
  }
}

export default NumberRangeFilter 