import React, { useState, useCallback } from 'react'
import { DatePicker } from '@hose/eui'
import { OutlinedEditFilter } from '@hose/eui-icons'
import Footer from '../../Footer'
import {
  encodeWithPrefix,
  decodeWithPrefix,
  toArrayMoment,
  toSELong,
  sE2Array,
  array2SE,
} from '../../utils'
import styles from './index.module.less'

interface DateRangeFilterProps {
  columnName: string
  bus: any
  state: any
  setState: (state: any) => void
}

export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  columnName,
  bus,
  state,
  setState
}) => {
  const filterDropdownVisible = encodeWithPrefix(state, 'filterDropdownVisible', columnName)
  const canVisibleFalse = encodeWithPrefix(state, 'canVisibleFalse', columnName)
  const linkedValue = state.fetchParams.filters[columnName]
  const filtered = !!linkedValue

  const onSearch = useCallback(() => {
    let linkedValue = state.fetchParams.filters[columnName]
    linkedValue = toSELong(linkedValue)
    bus.emit('column:filter', linkedValue, columnName)
    setState({
      ...state,
      ...decodeWithPrefix('filterDropdownVisible', false, columnName),
    })
  }, [state, bus, columnName, setState])

  const onChange = useCallback((range: any, a?: any, b?: any, c?: any, fn?: () => void) => {
    const val = range
    let { fetchParams } = state
    let { filters } = fetchParams
    const oo = toSELong(array2SE(val))
    filters = { ...filters, [columnName]: oo }
    fetchParams = { ...fetchParams, filters }
    setState({ ...state, fetchParams }, fn)
  }, [state, columnName, setState])

  const onReset = useCallback(() => {
    onChange(undefined, undefined, undefined, undefined, onSearch)
  }, [onChange, onSearch])

  const onOpenChange = useCallback((status: boolean) => {
    setState({
      ...state,
      ...decodeWithPrefix('canVisibleFalse', !status, columnName)
    })
  }, [state, setState, columnName])

  const onFilterDropdownVisibleChange = useCallback((visible: boolean) => {
    if (canVisibleFalse === true || canVisibleFalse === undefined) {
      setState({
        ...state,
        ...decodeWithPrefix('filterDropdownVisible', visible, columnName)
      })
    }
  }, [canVisibleFalse, state, setState, columnName])

  const value = toArrayMoment(sE2Array(linkedValue))

  const filterDropdown = (
    <div className={styles.container}>
      <Footer onOk={onSearch} onReset={onReset}>
        <DatePicker.RangePicker 
          value={value}
          onOpenChange={onOpenChange}
          onChange={onChange}
          onSearch={onSearch}
          onPressEnter={onSearch}
        />
      </Footer>
    </div>
  )

  const filterIcon = (
    <OutlinedEditFilter 
      style={{ color: filtered ? '#108ee9' : '#aaa' }}
    />
  )

  return {
    filterDropdown,
    filterIcon,
    filterDropdownVisible,
    onFilterDropdownVisibleChange,
  }
}

export default DateRangeFilter 