.dataGridWrapper {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  a {
    text-decoration: none;
  }

  :global {
    .selection-info {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      margin-right: 16px;
    }

    .eui-pagination-total-text {
      height: 30px;
      line-height: 30px;
    }
  }
}

.scenesBox {
  flex: 1;
  display: block;
  padding-left: 24px;
}

.headerContent {
  min-height: 50px;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;

  .left {
    display: flex;
    flex-direction: row;
    align-items: center;
    float: left;
  }

  .left_header {
    display: flex;
    flex-direction: row;
  }

  .right {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    float: right;
  }

  .search {
    min-width: 220px;
    flex-shrink: 0;
  }

  .pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-shrink: 0;

    :global {
      .eui-pagination-total-text {
        height: 30px;
      }
    }
  }

  .buttons {
    flex: 1;
    display: flex;
    align-items: center;
  }
}

.tableContent {
  display: flex;
  flex: 1;
  overflow: hidden;
  flex-basis: auto;
  flex-shrink: 0;
  transform: translate3d(0, 0, 0);
}

.single_election_table {
  :global {
    .ant-table-fixed-right table td:first-child {
      border-left: none;
    }
  }
}