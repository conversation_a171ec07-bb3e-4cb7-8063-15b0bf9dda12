/**************************************************
 * ColumnsSwitcher Styles
 **************************************************/

.switchWrapper {
  position: absolute;
  width: 45px;
  height: 50px;
  top: 50px;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #F7F7F7;
  border-left: 1px solid #E6E6E6;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background: #EEEEEE;
  }
  
  img {
    width: 24px;
    height: 24px;
  }

  svg {
    width: 24px;
    height: 24px;
    color: #666;
  }
}

.bodyWrapper {
  min-width: 120px;
}

.dropdown {
  .dropdownContent {
    padding: 8px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
} 