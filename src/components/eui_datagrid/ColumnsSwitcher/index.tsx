import styles from './index.module.less'
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Dropdown, Input } from '@hose/eui'
import List from '../List'
import Footer from '../Footer'
import { OutlinedEditFilter } from '@hose/eui-icons'
import { closest } from '../utils'

function keys2Indexes(dataSource: any[], selectedDataIndexes: string[]) {
  const result: string[] = []
  dataSource.forEach(({ dataIndex }) => {
    if (selectedDataIndexes.includes(dataIndex)) {
      result.push(dataIndex)
    }
  })
  return result
}

export interface ColumnsSwitcherProps {
  dataSource?: any[]
  selectedDataIndexes?: string[]
  top?: number
  style?: React.CSSProperties
  canSearch?: boolean
  i18n?: any
  onChange?: (selectedDataIndexes: string[]) => void
  onSceneChanged?: () => void
}

const ColumnsSwitcher: React.FC<ColumnsSwitcherProps> = ({
  dataSource = [],
  selectedDataIndexes = [],
  top = 0,
  style = {},
  canSearch = true,
  i18n,
  onChange,
  onSceneChanged
}) => {
  const [internalSelectedDataIndexes, setInternalSelectedDataIndexes] = useState(selectedDataIndexes)
  const [selectedDataIndexesOrigin, setSelectedDataIndexesOrigin] = useState(selectedDataIndexes)
  const [visible, setVisible] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [filterList, setFilterList] = useState<any[]>([])
  const [needChangeOrigin, setNeedChangeOrigin] = useState(false)

  // 同步外部选中状态
  useEffect(() => {
    setInternalSelectedDataIndexes(selectedDataIndexes)
    if (needChangeOrigin) {
      setNeedChangeOrigin(false)
      setSelectedDataIndexesOrigin(selectedDataIndexes)
    }
  }, [selectedDataIndexes, needChangeOrigin])

  // 场景变化处理
  useEffect(() => {
    if (onSceneChanged) {
      setNeedChangeOrigin(true)
    }
  }, [onSceneChanged])

  // 点击外部隐藏下拉框
  useEffect(() => {
    const handleHide = (e: MouseEvent) => {
      if (closest(e.target as Element, 'div.__columnsSwitch_footer_wrapper')) {
        return false
      }
      setVisible(false)
    }

    document.body.addEventListener('click', handleHide)
    return () => {
      document.body.removeEventListener('click', handleHide)
    }
  }, [])

  const handleListChange = useCallback((selectedDataIndexes: string[]) => {
    setInternalSelectedDataIndexes(selectedDataIndexes)
  }, [])

  const handleOk = useCallback(() => {
    setVisible(false)
    setSearchValue('')
    setFilterList([])
    onChange?.(internalSelectedDataIndexes)
  }, [onChange, internalSelectedDataIndexes])

  const handleReset = useCallback(() => {
    setVisible(false)
    setSearchValue('')
    setFilterList([])
    setInternalSelectedDataIndexes(selectedDataIndexesOrigin)
    onChange?.(selectedDataIndexesOrigin)
  }, [onChange, selectedDataIndexesOrigin])

  const handleShow = useCallback(() => {
    setVisible(true)
  }, [])

  const getFilterList = useCallback((searchValue: string) => {
    if (searchValue.trim().length) {
      return dataSource.filter(v => v.title && v.title.includes(searchValue))
    }
    return []
  }, [dataSource])

  const handleOnChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    const newFilterList = getFilterList(value)
    setSearchValue(value)
    setFilterList(newFilterList)
  }, [getFilterList])

  const handleOnSearch = useCallback((value: string) => {
    const newFilterList = getFilterList(value)
    setFilterList(newFilterList)
  }, [getFilterList])

  const handleOnPressEnter = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    const newFilterList = getFilterList(value)
    setFilterList(newFilterList)
  }, [getFilterList])

  const renderMenus = useCallback(() => {
    const dataList = searchValue.length ? filterList : dataSource
    const selectedRowKeys = keys2Indexes(dataSource, internalSelectedDataIndexes)
    
    return (
      <Footer 
        className="__columnsSwitch_footer_wrapper"
        onOk={handleOk}
        onReset={handleReset}
        i18n={i18n}
      >
        {canSearch && (
          <Input.Search 
            value={searchValue}
            onChange={handleOnChange}
            onSearch={handleOnSearch}
            onPressEnter={handleOnPressEnter}
          />
        )}
        <List 
          dataList={dataList}
          canSearch={canSearch}
          onChange={handleListChange}
          selectedRowKeys={selectedRowKeys}
          className={styles.bodyWrapper}
        />
      </Footer>
    )
  }, [
    searchValue, 
    filterList, 
    dataSource, 
    internalSelectedDataIndexes, 
    canSearch, 
    handleOk, 
    handleReset, 
    i18n, 
    handleOnChange, 
    handleOnSearch, 
    handleOnPressEnter, 
    handleListChange
  ])

  // 如果没有数据源，不渲染
  if (!dataSource || !dataSource.length) {
    return null
  }

  return (
    <Dropdown
      overlay={renderMenus()}
      visible={visible}
    >
      <div 
        className={styles.switchWrapper}
        style={{ top, ...style }}
        onClick={handleShow}
      >
        <OutlinedEditFilter />
      </div>
    </Dropdown>
  )
}

export default ColumnsSwitcher