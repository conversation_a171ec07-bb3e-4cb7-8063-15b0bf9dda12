import styles from './index.module.less'
import React, { useState, useEffect, useCallback } from 'react'
import { Input } from '@hose/eui'

export interface SearchInputProps {
  value?: string
  style?: React.CSSProperties
  className?: string
  size?: 'small' | 'middle' | 'large'
  placeholder?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSearch?: (value: string) => void
  onClear?: () => void
  [key: string]: any
}

const SearchInput: React.FC<SearchInputProps> = ({
  value: propValue = '',
  style,
  className = '',
  size,
  placeholder,
  onChange,
  onSearch,
  onClear,
  ...others
}) => {
  const [value, setValue] = useState(propValue)
  const [focus, setFocus] = useState(false)

  // 同步外部 value 变化
  useEffect(() => {
    setValue(propValue)
  }, [propValue])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    onChange?.(e)
  }, [onChange])

  const handleFocusBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    setFocus(e.target === document.activeElement)
  }, [])

  const handleSearch = useCallback(() => {
    onSearch?.(value)
  }, [onSearch, value])

  const handleClear = useCallback(() => {
    setValue('')
    onClear?.()
  }, [onClear])

  return (
    <div 
      className={`ant-search-input-wrapper ${styles.searchInput} ${className}`}
      style={style}
    >
      <Input.Search 
        {...(others || {})}
        value={value}
        placeholder={placeholder}
        size={size}
        onChange={handleInputChange}
        onFocus={handleFocusBlur}
        onBlur={handleFocusBlur}
        onPressEnter={handleSearch}
        onSearch={handleSearch}
        allowClear
      />
    </div>
  )
}

export default SearchInput 