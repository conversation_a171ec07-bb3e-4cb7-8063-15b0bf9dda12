/**
 *  Created by pan<PERSON> on 2018/5/28 下午3:15.
 */
import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { Button } from '@hose/eui'
import SearchInput from './SearchInput'
import EuiPagination from './EuiPagination'
import Scenes from './Scenes'
import Buttons from './Buttons'
import { useDataGrid } from './context'

export interface HeaderProps {
  // 搜索相关
  isShowSearch?: boolean
  searchPlaceholder?: string
  
  // 分页相关
  pageSize?: number
  paginationSimple?: boolean
  showQuickJumper?: boolean
  showSizeChanger?: boolean
  pageSizeOptions?: string[]
  
  // 场景相关
  scenes?: any[]
  active?: any
  noScene?: boolean
  onSceneChange?: (scene: any) => void
  onEditScenes?: () => void
  
  // 按钮相关
  buttons?: any[]
  menuBarView?: () => React.ReactNode
  
  // 其他配置
  disabledHeader?: boolean
  wrapperHeaderContentStyle?: React.CSSProperties
  
  // 回调函数
  onSearch?: (params: { searchText: string; page?: { currentPage: number; pageSize: number } }) => void
  onPagination?: (currentPage: number, pageSize: number) => void
  onHeaderHeightChanged?: (height: number) => void
  onShowMoreScenes?: (show: boolean) => void
  
  [key: string]: any
}

const Header: React.FC<HeaderProps> = ({
  isShowSearch = true,
  searchPlaceholder = '请输入搜索内容',
  pageSize = 10,
  paginationSimple = false,
  showQuickJumper = false,
  showSizeChanger = true,
  pageSizeOptions,
  scenes = [],
  active,
  noScene = false,
  onSceneChange,
  onEditScenes,
  buttons = [],
  menuBarView,
  disabledHeader = false,
  wrapperHeaderContentStyle = {},
  onSearch,
  onPagination,
  onHeaderHeightChanged,
  onShowMoreScenes,
  ...others
}) => {
  const { state } = useDataGrid()
  const { fetchParams, total, selectedRowKeys } = state
  
  const headerRef = useRef<HTMLDivElement>(null)
  const [searchValue, setSearchValue] = useState(fetchParams.searchText || '')

  // 监听头部高度变化
  useEffect(() => {
    if (headerRef.current && onHeaderHeightChanged) {
      const height = headerRef.current.offsetHeight
      onHeaderHeightChanged(height)
    }
  }, [scenes.length, buttons.length, isShowSearch]) // 移除 onHeaderHeightChanged 依赖

  // 同步搜索值
  useEffect(() => {
    setSearchValue(fetchParams.searchText || '')
  }, [fetchParams.searchText])

  // 搜索处理
  const handleSearch = useCallback((searchText: string) => {
    setSearchValue(searchText)
    onSearch?.({
      searchText
      // page 参数将在 handleSearch 中自动处理
    })
  }, [onSearch])

  // 搜索输入变化
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
  }, [])

  // 分页处理
  const handlePagination = useCallback((currentPage: number, newPageSize: number) => {
    onPagination?.(currentPage, newPageSize)
  }, [onPagination])

  // 分页大小变化
  const handlePageSizeChange = useCallback((current: number, size: number) => {
    onPagination?.(1, size) // 改变页面大小时重置到第一页
  }, [onPagination])

  // 场景变化处理
  const handleSceneChange = useCallback((scene: any) => {
    onSceneChange?.(scene)
  }, [onSceneChange])

  // 显示更多场景
  const handleShowMoreScenes = useCallback((show: boolean) => {
    onShowMoreScenes?.(show)
  }, [onShowMoreScenes])

  // 渲染搜索区域
  const renderSearchArea = useMemo(() => {
    if (!isShowSearch) return null
    
    return (
      <div className="search-area">
        <SearchInput
          value={searchValue}
          placeholder={searchPlaceholder}
          onChange={handleSearchChange}
          onSearch={handleSearch}
          style={{ width: 240 }}
        />
      </div>
    )
  }, [isShowSearch, searchValue, searchPlaceholder, handleSearchChange, handleSearch])

  // 渲染场景区域
  const renderScenesArea = useMemo(() => {
    if (noScene || !scenes.length) return null
    
    return (
      <div className="scenes-area" style={{ marginBottom: 16 }}>
        <Scenes
          scenes={scenes}
          active={active}
          onChange={handleSceneChange}
          onEditScenes={onEditScenes}
          onShowMoreScenes={handleShowMoreScenes}
        />
      </div>
    )
  }, [noScene, scenes, active, handleSceneChange, onEditScenes, handleShowMoreScenes])

  // 渲染按钮区域
  const renderButtonsArea = useMemo(() => {
    if (!buttons.length && !menuBarView) return null
    
    return (
      <div className="buttons-area" style={{ marginBottom: 16 }}>
        {buttons.length > 0 && (
          <Buttons
            buttons={buttons}
            selected={selectedRowKeys.length}
            total={total}
            onClick={() => {}}
          />
        )}
        {menuBarView && menuBarView()}
      </div>
    )
  }, [buttons, menuBarView, selectedRowKeys.length])

  // 渲染分页区域
  const renderPaginationArea = useMemo(() => {
    return (
      <div className="pagination-area" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div className="selection-info">
          {selectedRowKeys.length > 0 && (
            <span>已选择 {selectedRowKeys.length} 项</span>
          )}
        </div>
        <EuiPagination
          total={total}
          pageSize={fetchParams.page.pageSize}
          currentPage={fetchParams.page.currentPage}
          paginationSimple={paginationSimple}
          showQuickJumper={showQuickJumper}
          showSizeChanger={showSizeChanger}
          pageSizeOptions={pageSizeOptions}
          onPageChanged={handlePagination}
          onPageSizeChanged={handlePageSizeChange}
        />
      </div>
    )
  }, [
    selectedRowKeys.length,
    total,
    fetchParams.page,
    paginationSimple,
    showQuickJumper,
    showSizeChanger,
    pageSizeOptions,
    handlePagination,
    handlePageSizeChange
  ])

  if (disabledHeader) {
    return null
  }

  return (
    <div 
      ref={headerRef}
      className="eui-datagrid-header" 
      style={{ 
        paddingBottom: '16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...wrapperHeaderContentStyle 
      }}
    >
      {renderSearchArea}
      {renderScenesArea}
      {renderButtonsArea}
      {renderPaginationArea}
    </div>
  )
}

export default Header
