import React, { createContext, useContext, useReducer, use<PERSON>allback, ReactNode } from 'react'

// 状态类型定义
export interface DataGridState {
  dataSource: any[]
  total: number
  loading: boolean
  selectedRowKeys: string[]
  fetchParams: {
    page: { currentPage: number; pageSize: number }
    searchText: string
    filters: Record<string, any>
    sorters: Record<string, any>
    scene?: any
  }
  columnsSwitcherSelectedDataIndexes?: string[]
  headerHeight: number
  isShowMoreScenes: boolean
}

// Action 类型定义
export type DataGridAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_DATA_SOURCE'; payload: { dataSource: any[]; total: number } }
  | { type: 'SET_SELECTED_ROW_KEYS'; payload: string[] }
  | { type: 'SET_FETCH_PARAMS'; payload: Partial<DataGridState['fetchParams']> }
  | { type: 'SET_COLUMNS_SWITCHER_SELECTED'; payload: string[] }
  | { type: 'SET_HEADER_HEIGHT'; payload: number }
  | { type: 'SET_SHOW_MORE_SCENES'; payload: boolean }
  | { type: 'RESET_STATE'; payload: Partial<DataGridState> }

// Context 类型定义
export interface DataGridContextType {
  state: DataGridState
  dispatch: React.Dispatch<DataGridAction>
  // 操作方法
  updateFetchParams: (params: Partial<DataGridState['fetchParams']>) => void
  updateSelectedRowKeys: (keys: string[]) => void
  updateDataSource: (dataSource: any[], total: number) => void
  setLoading: (loading: boolean) => void
  updateColumnsSwitcher: (indexes: string[]) => void
  updateHeaderHeight: (height: number) => void
  updateShowMoreScenes: (show: boolean) => void
}

// 初始状态
const initialState: DataGridState = {
  dataSource: [],
  total: 0,
  loading: false,
  selectedRowKeys: [],
  fetchParams: {
    page: { currentPage: 1, pageSize: 10 },
    searchText: '',
    filters: {},
    sorters: {},
    scene: undefined
  },
  columnsSwitcherSelectedDataIndexes: undefined,
  headerHeight: 54,
  isShowMoreScenes: false
}

// Reducer
function dataGridReducer(state: DataGridState, action: DataGridAction): DataGridState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_DATA_SOURCE':
      return { 
        ...state, 
        dataSource: action.payload.dataSource, 
        total: action.payload.total 
      }
    
    case 'SET_SELECTED_ROW_KEYS':
      return { ...state, selectedRowKeys: action.payload }
    
    case 'SET_FETCH_PARAMS':
      return { 
        ...state, 
        fetchParams: { ...state.fetchParams, ...action.payload } 
      }
    
    case 'SET_COLUMNS_SWITCHER_SELECTED':
      return { ...state, columnsSwitcherSelectedDataIndexes: action.payload }
    
    case 'SET_HEADER_HEIGHT':
      return { ...state, headerHeight: action.payload }
    
    case 'SET_SHOW_MORE_SCENES':
      return { ...state, isShowMoreScenes: action.payload }
    
    case 'RESET_STATE':
      return { ...state, ...action.payload }
    
    default:
      return state
  }
}

// Context
const DataGridContext = createContext<DataGridContextType | undefined>(undefined)

// Provider 组件
export interface DataGridProviderProps {
  children: ReactNode
  initialState?: Partial<DataGridState>
}

export const DataGridProvider: React.FC<DataGridProviderProps> = ({ 
  children, 
  initialState: customInitialState 
}) => {
  const [state, dispatch] = useReducer(dataGridReducer, {
    ...initialState,
    ...customInitialState
  })

  // 操作方法
  const updateFetchParams = useCallback((params: Partial<DataGridState['fetchParams']>) => {
    dispatch({ type: 'SET_FETCH_PARAMS', payload: params })
  }, [])

  const updateSelectedRowKeys = useCallback((keys: string[]) => {
    dispatch({ type: 'SET_SELECTED_ROW_KEYS', payload: keys })
  }, [])

  const updateDataSource = useCallback((dataSource: any[], total: number) => {
    dispatch({ type: 'SET_DATA_SOURCE', payload: { dataSource, total } })
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading })
  }, [])

  const updateColumnsSwitcher = useCallback((indexes: string[]) => {
    dispatch({ type: 'SET_COLUMNS_SWITCHER_SELECTED', payload: indexes })
  }, [])

  const updateHeaderHeight = useCallback((height: number) => {
    dispatch({ type: 'SET_HEADER_HEIGHT', payload: height })
  }, [])

  const updateShowMoreScenes = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SHOW_MORE_SCENES', payload: show })
  }, [])

  const contextValue: DataGridContextType = {
    state,
    dispatch,
    updateFetchParams,
    updateSelectedRowKeys,
    updateDataSource,
    setLoading,
    updateColumnsSwitcher,
    updateHeaderHeight,
    updateShowMoreScenes
  }

  return (
    <DataGridContext.Provider value={contextValue}>
      {children}
    </DataGridContext.Provider>
  )
}

// Hook
export const useDataGrid = (): DataGridContextType => {
  const context = useContext(DataGridContext)
  if (context === undefined) {
    throw new Error('useDataGrid must be used within a DataGridProvider')
  }
  return context
} 