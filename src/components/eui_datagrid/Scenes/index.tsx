import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Dropdown, Menu } from '@hose/eui'
import styles from './index.module.less'
import { OutlinedDirectionDown } from '@hose/eui-icons'

export interface ScenesProps {
  scenes?: any[]
  onChange?: (scene: any) => void
  onEditScenes?: () => void
  onShowMoreScenes?: (show: boolean) => void
  i18n?: any
  noScene?: boolean
  active?: any
  [key: string]: any
}

const Scenes: React.FC<ScenesProps> = ({
  scenes = [],
  onChange,
  onEditScenes,
  onShowMoreScenes,
  i18n,
  noScene = false,
  active,
  ...others
}) => {
  const [selectItem, setSelectItem] = useState<any>({})

  // 同步 active 变化
  useEffect(() => {
    if (active !== undefined) {
      const selectedScene = scenes.find(item => item.sceneIndex === active)
      if (selectedScene) {
        setSelectItem(selectedScene)
      }
    }
  }, [active, scenes])

  const handleClick = useCallback((sceneIndex: any) => {
    const scene = scenes.find(item => item.sceneIndex === sceneIndex)
    if (!scene || selectItem.sceneIndex === scene.sceneIndex) {
      return
    }

    setSelectItem(scene)
    onChange?.(scene)
  }, [scenes, selectItem.sceneIndex, onChange])

  const handleEditScenes = useCallback(() => {
    onEditScenes?.()
  }, [onEditScenes])

  const isActive = useCallback((scene: any, activeScene?: any) => {
    if (activeScene) {
      return scene.sceneIndex === activeScene.sceneIndex
    }
    return scene.active
  }, [])

  const renderMenu = useMemo(() => {
    if (noScene) {
      return <span />
    }

    const MenuItems = scenes.map(scene => (
      <Menu.Item key={scene.sceneIndex}>
        <a
          className={isActive(scene, selectItem) ? styles.active : ''}
          onClick={() => handleClick(scene.sceneIndex)}
        >
          {scene.name}
        </a>
      </Menu.Item>
    ))

    return <Menu className={styles.menu_wrapper}>{MenuItems}</Menu>
  }, [noScene, scenes, isActive, selectItem, handleClick])

  if (noScene) {
    return <span />
  }

  return (
    <div className={styles.scenesWrapper}>
      <div className={styles.scence_title_wrapper}>
        <Dropdown overlay={renderMenu} trigger={['click']}>
          <span className={styles.scence_title}>
            单据 <OutlinedDirectionDown />
          </span>
        </Dropdown>
      </div>
      <div className={styles.scence_item}>
        {onEditScenes && (
          <span className={styles.scence_item_edit} onClick={handleEditScenes}>
            场景设置
          </span>
        )}
      </div>
    </div>
  )
}

export default Scenes 