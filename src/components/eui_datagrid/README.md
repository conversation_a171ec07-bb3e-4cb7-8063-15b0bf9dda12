# EuiDataGrid 重构说明

## 重构概述

本次重构将 `eui_datagrid` 组件从类组件重构为函数组件，并使用 React Context 替代了 MessageCenter bus 事件系统，提供了更好的数据交互方式。

## 主要改进

### 1. 组件架构
- **函数组件**: 所有组件都改为函数组件，使用 React Hooks
- **Context 管理**: 使用 React Context 进行状态管理，替代 bus 事件系统
- **TypeScript 支持**: 完善的 TypeScript 类型定义

### 2. 数据交互方式
- **Props 回调**: 使用标准的 props 回调函数进行组件间通信
- **Context 状态**: 通过 Context 共享状态，避免 props drilling
- **清晰的数据流**: 单向数据流，状态变化可预测

### 3. 性能优化
- **useCallback**: 优化事件处理函数
- **useMemo**: 优化计算属性
- **减少重渲染**: 通过合理的依赖管理减少不必要的重渲染

## 使用方式

### 基本用法

```tsx
import { EuiDataGrid } from './components/eui_datagrid'

const MyComponent = () => {
  const [dataSource, setDataSource] = useState([])
  const [total, setTotal] = useState(0)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      filterable: true
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      sorter: true
    }
  ]

  const fetchData = async (params) => {
    // 调用 API 获取数据
    const response = await api.getData(params)
    return {
      dataSource: response.data,
      total: response.total
    }
  }

  const handleSelectChange = (selectedRowKeys, selectedRowData) => {
    setSelectedRowKeys(selectedRowKeys)
    console.log('选中的数据:', selectedRowData)
  }

  const handleSceneChange = (scene) => {
    console.log('场景变化:', scene)
  }

  return (
    <EuiDataGrid
      columns={columns}
      fetch={fetchData}
      selectedRowKeys={selectedRowKeys}
      onSelectChange={handleSelectChange}
      onSceneChange={handleSceneChange}
      isShowSearch={true}
      searchPlaceholder="请输入搜索内容"
      scenes={[
        { sceneIndex: 1, name: '全部', active: true },
        { sceneIndex: 2, name: '待审核' }
      ]}
      buttons={[
        { text: '新增', type: 'primary' },
        { text: '删除', isBindMultiSelect: true }
      ]}
    />
  )
}
```

### 高级用法

```tsx
const AdvancedExample = () => {
  const handleColumnsSwitcherChange = (indexes) => {
    console.log('列显示变化:', indexes)
  }

  const handleEditScenes = () => {
    // 打开场景编辑弹窗
  }

  const menuBarView = () => (
    <div>
      <Button>自定义按钮</Button>
    </div>
  )

  return (
    <EuiDataGrid
      // ... 其他 props
      columnsSwitcherSelectedDataIndexes={['name', 'age']}
      onColumnsSwitcherChange={handleColumnsSwitcherChange}
      onEditScenes={handleEditScenes}
      menuBarView={menuBarView}
      disabledSwitcher={false}
      tableWidth={1200}
      columnMinWidth={150}
      scroll={{ y: 400 }}
    />
  )
}
```

## API 变化

### 移除的 Props
- `bus`: 不再需要传递 MessageCenter 实例
- 所有与 bus 相关的事件监听

### 新增的 Props
- `onSelectChange`: 选择变化回调
- `onSceneChange`: 场景变化回调
- `onSearch`: 搜索回调
- `onPagination`: 分页回调

### 修改的 Props
- `onChange` → `onSceneChange` (Scenes 组件)
- `onClick` → `onChange` (各子组件)

## 组件结构

```
EuiDataGrid (主组件 + Context Provider)
├── DataGridProvider (Context 提供者)
├── Header (头部组件)
│   ├── SearchInput (搜索输入)
│   ├── Scenes (场景选择)
│   ├── Buttons (操作按钮)
│   └── EKBPagination (分页)
├── ColumnsSwitcher (列切换)
├── TableWrapper (表格包装)
└── Context (状态管理)
```

## 迁移指南

### 1. 移除 bus 相关代码
```tsx
// 旧代码
const bus = new MessageCenter()
bus.on('select:change', handleSelectChange)

// 新代码
const handleSelectChange = (selectedRowKeys, selectedRowData) => {
  // 处理选择变化
}
<EuiDataGrid onSelectChange={handleSelectChange} />
```

### 2. 使用新的回调 Props
```tsx
// 旧代码
bus.on('dataGrid:fetch:data', handleSearch)
bus.on('pagination:changed', handlePagination)

// 新代码
<EuiDataGrid 
  onSearch={handleSearch}
  onPagination={handlePagination}
/>
```

### 3. 场景变化处理
```tsx
// 旧代码
bus.on('scene:changed', handleSceneChange)

// 新代码
<EuiDataGrid onSceneChange={handleSceneChange} />
```

## 注意事项

1. **向后兼容**: 大部分 API 保持兼容，但建议逐步迁移到新的回调方式
2. **性能**: 新架构下性能更好，减少了不必要的事件监听和触发
3. **调试**: 使用 React DevTools 可以更好地调试组件状态
4. **类型安全**: 完整的 TypeScript 支持，编译时类型检查

## 常见问题

### Q: 如何获取选中的数据？
A: 使用 `onSelectChange` 回调函数，第二个参数包含完整的选中行数据。

### Q: 如何自定义搜索逻辑？
A: 使用 `onSearch` 回调函数，接收搜索参数并调用 `fetch` 函数。

### Q: 如何处理表格排序和筛选？
A: 在 `fetch` 函数中处理 `params.sorters` 和 `params.filters` 参数。 