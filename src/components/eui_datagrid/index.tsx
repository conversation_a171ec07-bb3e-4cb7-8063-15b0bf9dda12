import React, { useEffect, useCallback, useMemo, useRef } from 'react'
import styles from './index.module.less'
import TableWrapper from './TableWrapper'
import Header from './Header'
import { mergeOthers2Columns } from './utils'
import ColumnsSwitcher from './ColumnsSwitcher'
import { DataGridProvider, useDataGrid } from './context'

// 类型定义
interface FetchParams {
  page: { currentPage: number; pageSize: number }
  searchText: string
  filters: Record<string, any>
  sorters: Record<string, any>
  scene?: any
}

interface EuiDataGridProps {
  loading?: boolean
  dataSource?: any[]
  columns?: any[]
  total?: number
  pageSize?: number
  selectedRowKeys?: string[]
  highlightWords?: string[]
  isMultiSelect?: boolean
  isSingleSelect?: boolean
  radioSelect?: boolean
  isButtonsBindMultiSelect?: boolean
  fetchParams?: FetchParams
  columnMinWidth?: number
  wrapperStyle?: React.CSSProperties
  wrapperHeaderContentStyle?: React.CSSProperties
  wrapperColumnsSwitcherStyle?: React.CSSProperties
  isShowSearch?: boolean
  columnsSwitcherSelectedDataIndexes?: string[]
  rowKey?: string | ((record: any) => string)
  fetch?: (params: any) => Promise<{ dataSource: any[]; total: number }>
  onColumnsSwitcherChange?: (indexes: string[]) => void
  getCheckboxProps?: (record: any) => any
  disabledSwitcher?: boolean
  scroll?: any
  scenes?: any[]
  buttons?: any[]
  disabledHeader?: boolean
  tableWidth?: number
  searchPlaceholder?: string
  noScene?: boolean
  menuBarView?: () => React.ReactNode
  onEditScenes?: () => void
  active?: any
  onSceneChange?: (scene: any) => void
  onSelectChange?: (selectedRowKeys: string[], selectedRowData: Record<string, any>) => void
  onSearch?: (params: { searchText: string; page?: { currentPage: number; pageSize: number } }) => void
  onPagination?: (currentPage: number, pageSize: number) => void
  onFiltersChange?: (filters: Record<string, any>) => void
  
  // 分页配置
  paginationSimple?: boolean
  showQuickJumper?: boolean
  showSizeChanger?: boolean
  pageSizeOptions?: string[]
  
  // 国际化配置
  locale?: {
    filterReset?: string
    filterConfirm?: string
    emptyText?: React.ReactNode | string
    [key: string]: any
  }
  
  [key: string]: any
}

function getRowKeyFn(rowKey: string | ((record: any) => string)) {
  if (typeof rowKey === 'string') {
    return (record: any) => record[rowKey]
  }
  return rowKey
}

// 内部组件，使用 Context
const EuiDataGridInner: React.FC<EuiDataGridProps> = (props) => {
  const {
    columns = [],
    isMultiSelect = true,
    isSingleSelect = false,
    radioSelect = false,
    getCheckboxProps,
    wrapperStyle = {},
    disabledSwitcher = false,
    wrapperColumnsSwitcherStyle = {},
    scroll,
    scenes = [],
    buttons = [],
    isShowSearch = true,
    disabledHeader = false,
    columnMinWidth = 180,
    tableWidth,
    onColumnsSwitcherChange,
    searchPlaceholder,
    noScene = false,
    menuBarView,
    onEditScenes,
    active,
    onSceneChange,
    onSelectChange,
    onSearch,
    onPagination,
    onFiltersChange,
    fetch,
    rowKey = 'id',
    ...others
  } = props

  const { 
    state, 
    updateFetchParams, 
    updateSelectedRowKeys, 
    updateDataSource, 
    setLoading, 
    updateColumnsSwitcher, 
    updateHeaderHeight, 
    updateShowMoreScenes 
  } = useDataGrid()

  // 选中行数据缓存
  const selectedRowDataRef = useRef<Record<string, any>>({})
  
  // 使用 ref 来避免 fetchData 的循环依赖
  const fetchParamsRef = useRef(state.fetchParams)
  fetchParamsRef.current = state.fetchParams
  
  // 获取行键函数
  const rowKeyFn = useMemo(() => getRowKeyFn(rowKey), [rowKey])

  // 数据获取
  const fetchData = useCallback(async (params: Partial<FetchParams> = {}) => {
    if (!fetch) return Promise.resolve({ dataSource: [], total: 0 })
    
    setLoading(true)
    
    try {
      // 合并当前状态的 fetchParams 和新参数
      const mergedParams = { ...fetchParamsRef.current, ...params }
      updateFetchParams(mergedParams)
      
      const data = await fetch(mergedParams)
      const { dataSource, total } = data
      
      // 标记这是内部更新
      isInternalUpdateRef.current = true
      updateDataSource(dataSource, total)
      setLoading(false)
      
      return data
    } catch (error) {
      setLoading(false)
      throw error
    }
  }, [fetch, updateFetchParams, updateDataSource, setLoading])

  // 选择变化处理
  const handleSelectChange = useCallback((selectedRowKeys: string[], selectedRowData: any[]) => {
    // 单选模式下的处理逻辑
    if (radioSelect && !isMultiSelect) {
      // 单选模式：直接使用当前选择的行，不累积之前的选择
      const finalSelectedKeys = selectedRowKeys
      
      // 清空之前的缓存，只保留当前选中的行
      selectedRowDataRef.current = {}
      selectedRowData.forEach(line => {
        const key = rowKeyFn(line)
        selectedRowDataRef.current[key] = line
      })
      
      updateSelectedRowKeys(finalSelectedKeys)
      onSelectChange?.(finalSelectedKeys, selectedRowDataRef.current)
      return
    }
    
    // 多选模式下的处理逻辑（原逻辑保持不变）
    // 获取当前页的所有行Key，用于判断取消选择
    const currentPageRowKeys = new Set(state.dataSource.map(rowKeyFn))
    
    // 获取当前所有已选中的keys（跨页累积）
    const allSelectedKeys = new Set(state.selectedRowKeys)
    
    // 处理当前页的选择变化
    currentPageRowKeys.forEach(pageKey => {
      if (selectedRowKeys.includes(pageKey)) {
        // 当前页选中的行：添加到全局选中
        allSelectedKeys.add(pageKey)
      } else {
        // 当前页未选中的行：从全局选中中移除
        allSelectedKeys.delete(pageKey)
        // 同时从缓存中移除
        delete selectedRowDataRef.current[pageKey]
      }
    })
    
    // 更新选中行数据缓存（只更新当前页选中的）
    selectedRowData.forEach(line => {
      const key = rowKeyFn(line)
      selectedRowDataRef.current[key] = line
    })
    
    // 将Set转回数组并更新状态
    const finalSelectedKeys = Array.from(allSelectedKeys)
    updateSelectedRowKeys(finalSelectedKeys)
    
    // 通知外部（传递完整的跨页选择结果）
    onSelectChange?.(finalSelectedKeys, selectedRowDataRef.current)
  }, [rowKeyFn, updateSelectedRowKeys, onSelectChange, state.selectedRowKeys, state.dataSource, radioSelect, isMultiSelect])

  // 搜索处理
  const handleSearch = useCallback((params: { searchText: string; page?: { currentPage: number; pageSize: number } }) => {
    // 如果没有提供 page 参数，使用当前的 pageSize 并重置到第一页
    const searchParams = {
      ...params,
      page: params.page || { currentPage: 1, pageSize: fetchParamsRef.current.page.pageSize }
    }
    fetchData(searchParams)
    // 通知外部搜索参数变化
    onSearch?.(searchParams)
  }, [fetchData, onSearch])

  // 分页处理
  const handlePagination = useCallback((currentPage: number, pageSize: number) => {
    const pageParams = {
      page: { currentPage, pageSize }
    }
    fetchData(pageParams)
    // 通知外部分页参数变化
    onPagination?.(currentPage, pageSize)
  }, [fetchData, onPagination])

  // 表格变化处理（排序、筛选）
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    const params: any = {}
    
    // 处理筛选
    const hasFilters = filters && Object.keys(filters).length > 0
    const processedFilters: Record<string, any> = {}
    
    if (hasFilters) {
      Object.keys(filters).forEach(key => {
        const filterValue = filters[key]
        if (filterValue && (Array.isArray(filterValue) ? filterValue.length > 0 : filterValue)) {
          // 根据筛选器类型处理数据格式
          if (Array.isArray(filterValue)) {
            // 数组类型的筛选器（金额范围、日期范围、文本等）
            if (filterValue.length === 2 && (filterValue[0] || filterValue[1])) {
              // 金额/数字范围筛选器：需要特殊处理为范围对象
              // 检查是否为数字范围筛选器（通过检查值是否为数字字符串）
              const isNumericRange = filterValue.some(val => 
                val && typeof val === 'string' && /^-?\d*\.?\d*$/.test(val.trim()) && val.trim() !== ''
              )
              
              // 检查是否为日期范围筛选器（通过检查值是否为moment对象或日期对象）
              const isDateRange = filterValue.some(val => 
                val && (val._isAMomentObject || val instanceof Date || (val.format && typeof val.format === 'function'))
              )
              
              if (isNumericRange) {
                // 为数字/金额范围筛选器创建后端期望的格式：{start: value, end: value}
                const rangeFilter: any = {}
                if (filterValue[0] && filterValue[0].trim() !== '') {
                  rangeFilter.start = parseFloat(filterValue[0])
                }
                if (filterValue[1] && filterValue[1].trim() !== '') {
                  rangeFilter.end = parseFloat(filterValue[1])
                }
                // 只有当至少有一个有效值时才添加筛选器
                if (rangeFilter.start !== undefined || rangeFilter.end !== undefined) {
                  processedFilters[key] = rangeFilter
                }
              } else if (isDateRange) {
                // 为日期范围筛选器创建后端期望的格式：{start: timestamp, end: timestamp}
                const dateRangeFilter: any = {}
                if (filterValue[0]) {
                  // 转换为时间戳格式（毫秒）
                  dateRangeFilter.start = filterValue[0].valueOf ? filterValue[0].valueOf() : new Date(filterValue[0]).getTime()
                }
                if (filterValue[1]) {
                  dateRangeFilter.end = filterValue[1].valueOf ? filterValue[1].valueOf() : new Date(filterValue[1]).getTime()
                }
                // 只有当至少有一个有效值时才添加筛选器
                if (dateRangeFilter.start || dateRangeFilter.end) {
                  processedFilters[key] = dateRangeFilter
                }
              } else {
                // 其他类型的双值筛选器
                processedFilters[key] = filterValue
              }
            } else if (filterValue.length > 0) {
              // 多选筛选器：[value1, value2, ...] 
              // 这种格式会在后端生成 field.in(value1,value2,...) 查询

              if (filterValue.length === 1  && filterValue[0].includes(' ')) {
                // 将空格分隔的文本转换为关键词数组，用于多关键词模糊匹配
                const keywords = filterValue[0].split(/\s+/).filter(keyword => keyword.trim() !== '')
                if (keywords.length > 1) {
                  // 多关键词：发送数组格式，后端需要实现多关键词模糊匹配（AND逻辑）
                  processedFilters[key] = keywords
                } else {
                  // 单关键词
                  processedFilters[key] = filterValue
                }
              } else {
                processedFilters[key] = filterValue
              }
            }
          } else {     
            // 非数组类型的筛选器
            processedFilters[key] = filterValue
          }
        }
      })
    }
    
    // 无论筛选条件是否为空，都更新筛选状态
    params.filters = processedFilters
    
    // 如果筛选条件发生变化（新筛选或清空筛选），重置到第一页
    // 比较当前筛选条件和新筛选条件是否不同
    const currentFilters = fetchParamsRef.current.filters || {}
    const filtersChanged = JSON.stringify(currentFilters) !== JSON.stringify(processedFilters)
    
    if (filtersChanged) {
      params.page = { currentPage: 1, pageSize: fetchParamsRef.current.page.pageSize }
      // 筛选变化时不清空选中状态，保持跨页选择能力
    }
    
    // 处理排序
    if (sorter && sorter.order) {
      const { columnKey, order, field, key } = sorter
      
      // 尝试获取正确的字段名
      let sortField = field || columnKey || key
      
      // 如果是数组，转换为字符串
      if (Array.isArray(sortField)) {
        sortField = sortField.join('.')
      }
      
      // 如果字段名有效，处理排序
      if (sortField && sortField !== 'undefined') {
        params.sorters = { [sortField]: order }
      }
    } else {
      // 清空排序
      params.sorters = {}
    }
    
    fetchData(params)
    
    // 通知外部筛选参数变化
    if (filtersChanged) {
      onFiltersChange?.(processedFilters)
    }
  }, [fetchData, onFiltersChange])

  // 行点击处理
  const handleRowClick = useCallback((record: any, index: number, event: React.MouseEvent) => {
    // 可以在这里处理行点击逻辑
  }, [])

  // 列切换处理
  const handleColumnsSwitch = useCallback((columnsSwitcherSelectedDataIndexes: string[]) => {
    updateColumnsSwitcher(columnsSwitcherSelectedDataIndexes)
    onColumnsSwitcherChange?.(columnsSwitcherSelectedDataIndexes)
  }, [updateColumnsSwitcher, onColumnsSwitcherChange])

  // 场景变化处理
  const handleSceneChange = useCallback((scene: any) => {
    const sceneParams = {
      scene,
      page: { currentPage: 1, pageSize: fetchParamsRef.current.page.pageSize },
      searchText: fetchParamsRef.current.searchText, // 保留搜索文本
      sorters: {},
      filters: {}
    }
    
    // 清空选中状态
    updateSelectedRowKeys([])
    selectedRowDataRef.current = {}
    
    fetchData(sceneParams)
    onSceneChange?.(scene)
  }, [updateSelectedRowKeys, fetchData, onSceneChange])

  // 显示更多场景
  const handleShowMoreScenes = useCallback((show: boolean) => {
    updateShowMoreScenes(show)
  }, [updateShowMoreScenes])

  // 头部高度变化
  const handleHeaderHeightChanged = useCallback((headerHeight: number) => {
    updateHeaderHeight(headerHeight)
  }, [updateHeaderHeight])

  // 修正滚动X
  const handleFixScrollX = useCallback((columns: any[]) => {
    const minWidth = columnMinWidth || 200
    return columns
      .map(line => {
        line.width = line.width || minWidth
        return line.width
      })
      .reduce((a, b) => a + b, 0)
  }, [columnMinWidth])

  // 固定列处理
  const handleFixedColumn = useCallback((columns: any[] = []) => {
    const { dataSource } = state
    const element = document.getElementById('ekbc-table')
    const fixedColumns = columns.filter(c => c.fixed)
    
    if (dataSource && !dataSource.length) {
      // 特殊处理没有数据的情况，eui在没有数据的时候固定在右边的列显示有问题
      fixedColumns.forEach(c => delete c.fixed)
    }
    
    if (!fixedColumns.length) {
      return columns
    }
    
    if (element) {
      if (fixedColumns.length === columns.length) {
        columns.forEach(c => delete c.fixed)
      } else {
        const offsetWidth = tableWidth || element.clientWidth
        const totalWidth = columns.reduce((sum, c) => {
          const width = c.fixedWidth || c.width
          return width + sum
        }, 0)
        
        let width = columnMinWidth || 180
        if (totalWidth < offsetWidth) {
          width = offsetWidth / columns.length
        }
        
        columns.forEach(c => {
          if (c.fixedWidth) {
            c.width = c.fixedWidth
          } else {
            c.width = width
          }
        })
      }
    }
    return columns
  }, [state.dataSource, tableWidth, columnMinWidth])

  // 同步外部传入的 dataSource 和 total 到内部状态
  // 使用 ref 来跟踪是否是内部更新，避免循环更新
  const isInternalUpdateRef = useRef(false)
  
  useEffect(() => {
    if (props.dataSource !== undefined && props.total !== undefined && !isInternalUpdateRef.current) {
      // 如果外部传入了 dataSource 和 total，且不是内部更新触发的，同步到内部状态
      if (JSON.stringify(props.dataSource) !== JSON.stringify(state.dataSource) || props.total !== state.total) {
        updateDataSource(props.dataSource, props.total)
      }
    }
    isInternalUpdateRef.current = false
  }, [props.dataSource, props.total, state.dataSource, state.total, updateDataSource])

  // 同步外部传入的 selectedRowKeys 到内部状态
  useEffect(() => {
    if (props.selectedRowKeys !== undefined) {
      // 如果外部传入了 selectedRowKeys，同步到内部状态
      if (JSON.stringify(props.selectedRowKeys) !== JSON.stringify(state.selectedRowKeys)) {
        updateSelectedRowKeys(props.selectedRowKeys)
      }
    }
  }, [props.selectedRowKeys, state.selectedRowKeys, updateSelectedRowKeys])

  // 初始数据加载
  useEffect(() => {
    if (fetch && !props.dataSource?.length) {
      fetchData({})
    }
  }, []) // 只在组件挂载时执行一次

  // 计算派生数据
  const { sorters = {} } = state.fetchParams
  const columns2 = useMemo(() => {
    try{
      return mergeOthers2Columns(columns, sorters) || []
    }catch(e){
      console.log(e)
      return []
    }
  }, [columns, sorters])

  const columnsSwitcherTop = useMemo(() => {
    return disabledHeader ? 2 : scenes && buttons ? (isShowSearch ? 91 : 116) : 50
  }, [disabledHeader, scenes, buttons, isShowSearch])

  const fixScroll = useMemo(() => {
    return state.isShowMoreScenes 
      ? { ...scroll, y: scroll?.y - state.headerHeight + 49 } 
      : scroll
  }, [state.isShowMoreScenes, scroll, state.headerHeight])

  // 行选择配置
  const rowSelection = useMemo(() => {
    if (isMultiSelect) {
      const config: any = { 
        columnWidth: 40, 
        fixed: 'left', 
        selectedRowKeys: state.selectedRowKeys, 
        onChange: handleSelectChange
      }
      if (getCheckboxProps) {
        config.getCheckboxProps = getCheckboxProps
      }
      return config
    }
    
    if (!isMultiSelect && radioSelect) {
      const config: any = { 
        columnWidth: 40, 
        fixed: 'left', 
        type: 'radio', 
        selectedRowKeys: state.selectedRowKeys, 
        onChange: handleSelectChange
      }
      if (getCheckboxProps) {
        config.getCheckboxProps = getCheckboxProps
      }
      return config
    }
    
    return null
  }, [isMultiSelect, radioSelect, state.selectedRowKeys, handleSelectChange, getCheckboxProps])

  // 样式类名
  const className = useMemo(() => {
    let cls = styles.dataGridWrapper
    if (isSingleSelect) {
      cls += ' ' + styles.single_election_table
    }
    return cls
  }, [isSingleSelect])

  return (
    <div className={className} style={wrapperStyle}>
      {!disabledSwitcher && (
        <ColumnsSwitcher
          style={wrapperColumnsSwitcherStyle}
          top={columnsSwitcherTop}
          dataSource={columns2}
          selectedDataIndexes={state.columnsSwitcherSelectedDataIndexes}
          onChange={handleColumnsSwitch}
        />
      )}
      
      <Header
        isShowSearch={isShowSearch}
        searchPlaceholder={searchPlaceholder}
        scenes={scenes}
        active={active}
        noScene={noScene}
        onSceneChange={handleSceneChange}
        onEditScenes={onEditScenes}
        buttons={buttons}
        menuBarView={menuBarView}
        disabledHeader={disabledHeader}
        wrapperHeaderContentStyle={props.wrapperHeaderContentStyle}
        onSearch={handleSearch}
        onPagination={handlePagination}
        onHeaderHeightChanged={handleHeaderHeightChanged}
        onShowMoreScenes={handleShowMoreScenes}
        {...others}
      />

      <div className={styles.tableContent} id="eui-datagrid-table">
        <TableWrapper
          {...others}
          rowKey={rowKey}
          scroll={fixScroll}
          fixScrollX={handleFixScrollX}
          fixedColumn={handleFixedColumn}
          loading={state.loading}
          dataSource={state.dataSource}
          columns={columns2}
          showColumnsDataIndexes={state.columnsSwitcherSelectedDataIndexes}
          rowSelection={rowSelection}
          onTableChange={handleTableChange}
          onRowClick={handleRowClick}
        />
      </div>
    </div>
  )
}

// 主组件，提供 Context
export const EuiDataGrid: React.FC<EuiDataGridProps> = (props) => {
  const {
    dataSource = [],
    total = 0,
    selectedRowKeys = [],
    loading = false,
    fetchParams = {
      page: { currentPage: 1, pageSize: 10 },
      searchText: '',
      filters: {},
      sorters: {},
      scene: undefined
    },
    columnsSwitcherSelectedDataIndexes,
    ...restProps
  } = props

  const initialState = {
    dataSource,
    total,
    selectedRowKeys,
    loading,
    fetchParams,
    columnsSwitcherSelectedDataIndexes,
    headerHeight: 54,
    isShowMoreScenes: false
  }

  return (
    <DataGridProvider initialState={initialState}>
      <EuiDataGridInner {...props} />
    </DataGridProvider>
  )
}

export default EuiDataGrid
