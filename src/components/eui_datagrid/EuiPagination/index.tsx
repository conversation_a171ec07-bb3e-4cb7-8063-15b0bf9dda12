import React, { useCallback } from 'react'
import { Pagination } from '@hose/eui'
import styles from './index.module.less'

const defaultSizeOptions = ['10', '20', '50', '100']

export interface EuiPaginationProps {
  total?: number
  pageSize?: number
  currentPage?: number
  showQuickJumper?: boolean
  showSizeChanger?: boolean
  pageSizeOptions?: string[]
  paginationSimple?: boolean
  onPageChanged?: (page: number, pageSize: number) => void
  onPageSizeChanged?: (current: number, size: number) => void
}

const EuiPagination: React.FC<EuiPaginationProps> = ({
  total = 1,
  pageSize = 10,
  currentPage = 1,
  showQuickJumper = false,
  showSizeChanger = true,
  pageSizeOptions,
  paginationSimple = false,
  onPageChanged,
  onPageSizeChanged
}) => {
  const handlePaginationChanged = useCallback((page: number, pageSize: number) => {
    onPageChanged?.(page, pageSize)
  }, [onPageChanged])

  const handleOnShowSizeChange = useCallback((current: number, size: number) => {
    onPageSizeChanged?.(current, size)
  }, [onPageSizeChanged])

  const _pageSizeOptions = showSizeChanger 
    ? (pageSizeOptions || defaultSizeOptions) 
    : []
  
  const simple = !!paginationSimple

  return (
    <div className={styles.pagination}>
      <Pagination
        total={total}
        showTotal={(total, range) => `共${total}条`}
        defaultPageSize={pageSize}
        current={currentPage}
        showQuickJumper={showQuickJumper}
        showSizeChanger={showSizeChanger}
        pageSizeOptions={_pageSizeOptions}
        size="small"
        // simple={simple}
        onChange={handlePaginationChanged}
        onShowSizeChange={handleOnShowSizeChange}
      />
    </div>
  )
}

export default EuiPagination 