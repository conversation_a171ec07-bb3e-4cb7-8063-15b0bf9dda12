/**************************************************
 * EKBPagination Styles
 **************************************************/

.paginationWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 16px 0;
  gap: 16px;
  
  .selectedInfo {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    
    .selectedCount {
      color: var(--brand-base, #22b2cc);
      font-weight: 500;
    }
  }
  
  .totalInfo {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    
    .totalCount {
      color: #333;
      font-weight: 500;
    }
  }
  
  .pagination {
    flex-shrink: 0;
    
    :global {
      .ant-pagination {
        margin: 0;
        
        .ant-pagination-item {
          border-radius: 4px;
        }
        
        .ant-pagination-item-active {
          background-color: var(--brand-base, #22b2cc);
          border-color: var(--brand-base, #22b2cc);
          
          a {
            color: white;
          }
        }
        
        .ant-pagination-prev,
        .ant-pagination-next {
          border-radius: 4px;
        }
        
        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
          border-radius: 4px;
        }
      }
    }
  }
  
  .pageSizeSelector {
    flex-shrink: 0;
    
    :global {
      .ant-select {
        min-width: 80px;
      }
    }
  }
} 