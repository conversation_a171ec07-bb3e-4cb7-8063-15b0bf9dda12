import styles from './index.module.less'
import React, { useCallback, useMemo } from 'react'
import { Table } from '@hose/eui'

export interface TableWrapperProps {
  dataSource: any[]
  columns: any[]
  loading?: boolean
  bordered?: boolean
  scroll?: any
  rowSelection?: any
  showColumnsDataIndexes?: string[]
  fixScrollX?: (columns: any[]) => number
  fixedColumn?: (columns: any[]) => any[]
  onTableChange?: (pagination: any, filters: any, sorter: any) => void
  onRowClick?: (record: any, index: number, event: React.MouseEvent) => void
  locale?: {
    filterReset?: string
    filterConfirm?: string
    emptyText?: React.ReactNode | string
    [key: string]: any
  }
  children?: React.ReactNode
  [key: string]: any
}

const TableWrapper: React.FC<TableWrapperProps> = ({
  scroll,
  fixScrollX,
  fixedColumn,
  bordered = false,
  dataSource = [],
  columns = [],
  rowSelection,
  showColumnsDataIndexes,
  loading = false,
  children,
  onTableChange,
  onRowClick,
  locale,
  ...others
}) => {
  const handleChange = useCallback((pagination: any, filters: any, sorter: any) => {
    // 确保传递完整的排序信息，包括原始的 key
    const enhancedSorter = {
      ...sorter,
      // 如果没有 field，使用 columnKey，如果没有 columnKey，使用 key
      field: sorter.field || sorter.columnKey || sorter.key
    }
    
    onTableChange?.(pagination, filters, enhancedSorter)
  }, [onTableChange])

  const handleTableRowClick = useCallback((record: any, index: number, event: React.MouseEvent) => {
    onRowClick?.(record, index, event)
  }, [onRowClick])

  // 处理列显示/隐藏
  const processedColumns = useMemo(() => {
    let processedCols = columns.slice()
    
    // 如果此值不为 undefined，就说明有隐藏的列
    if (Array.isArray(showColumnsDataIndexes)) {
      processedCols = processedCols.filter(
        ({ dataIndex }) => showColumnsDataIndexes.includes(dataIndex)
      )
    }

    // 应用固定列处理
    processedCols = fixedColumn ? fixedColumn(processedCols) : processedCols

    // eui中把 antd3 中 dataIndex 从支持路径嵌套如 user.age 改成了数组路径如 ['user', 'age']
    processedCols = processedCols.map(column => {
      const originalDataIndex = column.dataIndex
      return {
        ...column,
        dataIndex: typeof column.dataIndex === 'string' 
          ? column.dataIndex.split('.') 
          : column.dataIndex,
        // 保留原始 dataIndex 用于排序
        key: column.key || originalDataIndex
      }
    })

    return processedCols
  }, [columns, showColumnsDataIndexes, fixedColumn])

  // 计算滚动宽度
  const scrollConfig = useMemo(() => {
    const x = fixScrollX
      ? fixScrollX(processedColumns)
      : processedColumns
          .filter(line => !line.fixed)
          .map(line => {
            line.width = line.width || 200
            return line.width
          })
          .reduce((a, b) => a + b, 0) + 99

    return { ...scroll, x }
  }, [scroll, fixScrollX, processedColumns])

  // 合并默认的locale和外部传入的locale
  const mergedLocale = useMemo(() => {
    const defaultLocale = {
      filterReset: '重置',
      filterConfirm: '确认',
      emptyText: '暂无数据'
    }
    return locale ? { ...defaultLocale, ...locale } : defaultLocale
  }, [locale])

  return (
    <div className={styles.tableWrapper}>
      <Table
        {...others}
        locale={mergedLocale}
        onRow={(record, index) => ({
          onClick(event) {
            handleTableRowClick(record, index, event)
          }
        })}
        loading={loading}
        pagination={false}
        rowSelection={rowSelection}
        columns={processedColumns}
        dataSource={dataSource}
        bordered={bordered}
        onChange={handleChange}
        scroll={scrollConfig}
      >
        {children}
      </Table>
    </div>
  )
}

export default TableWrapper 