import React from 'react'
import { OutlinedEditFilter } from '@hose/eui-icons'
import { Button, Input, Checkbox } from '@hose/eui'
import { DepartmentFilterDropdown } from '../../elements/HoseTable/filter/getColumnDepartment'
import { StaffFilterDropdown } from '../../elements/HoseTable/filter/getColumnStaff'
import { DatePicker } from 'antd'

const ValueRangeDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => {
  let moneyArr: string[] = selectedKeys || ['', '']
  const reg = /^-?\d*(\.\d*)?$/

  const onMoneyStartChange = (value: string, setSelectedKeys: any) => {
    if (reg.test(value) || value === '' || value === '-') {
      moneyArr = [value, moneyArr[1]]
      setSelectedKeys(moneyArr)
    }
  }

  const onMoneyEndChange = (value: string, setSelectedKeys: any) => {
    if (reg.test(value) || value === '' || value === '-') {
      moneyArr = [moneyArr[0], value]
      setSelectedKeys(moneyArr)
    }
  }

  const handleReset = () => {
    setSelectedKeys([])
    clearFilters && clearFilters({confirm: true, closeDropdown: true})
  }

  return (
    <div style={{ padding: 4, width: '264px' }} onKeyDown={e => e.stopPropagation()}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px'}}>
        <Input 
          style={{ width: '117px' }}
          value={selectedKeys?.[0] || ''}
          onChange={e => onMoneyStartChange(e.target.value, setSelectedKeys)}
          placeholder="最小值"
        />
        ～
        <Input 
          style={{ width: '117px' }}
          value={selectedKeys?.[1] || ''}
          onChange={e => onMoneyEndChange(e.target.value, setSelectedKeys)}
          placeholder="最大值"
        />
      </div>
      <div style={{width: '100%', display: 'flex', justifyContent: 'end'}}>
        <Button
          category="secondary"
          onClick={handleReset}
          size="small"
          style={{ width: 40, marginRight: '4px' }}
        >
          重置
        </Button>
        <Button
          category="primary"
          onClick={() => confirm()}
          size="small"
          style={{ width: 40 }}
        >
          确认
        </Button>
      </div>
    </div>
  )
}

// 日期范围过滤器组件
const DateRangeDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => {
  const { RangePicker } = DatePicker

  const handleDateChange = (dates: any, dateStrings: string[]) => {
    if (dates && dates.length === 2) {
      // 设置时间范围：开始时间为当天00:00:00，结束时间为当天23:59:59
      const startDate = dates[0].clone().startOf('day')
      const endDate = dates[1].clone().endOf('day')
      setSelectedKeys([startDate, endDate])
    } else {
      setSelectedKeys([])
    }
  }

  const handleReset = () => {
    setSelectedKeys([])
    clearFilters && clearFilters({confirm: true, closeDropdown: true})
  }

  return (
    <div style={{ padding: 4, width: '264px' }} onKeyDown={e => e.stopPropagation()}>
      <RangePicker
        style={{ marginBottom: '8px', width: '100%' }}
        value={selectedKeys}
        onChange={handleDateChange}
        format="YYYY-MM-DD"
      />
      <div style={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
        <Button
          category="secondary"
          onClick={handleReset}
          size="small"
          style={{ width: 40, marginRight: '4px' }}
        >
          重置
        </Button>
        <Button
          category="primary"
          onClick={() => confirm()}
          size="small"
          style={{ width: 40 }}
        >
          确认
        </Button>
      </div>
    </div>
  )
}

// 人员搜索过滤器组件 - 基于模糊搜索
const StaffSearchDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => {
  const [searchValue, setSearchValue] = React.useState('')
  const [loading, setLoading] = React.useState(false)
  
  // 组件挂载时，如果有筛选值但没有显示文本，保持输入框为空让用户重新输入
  React.useEffect(() => {
    if (!selectedKeys || selectedKeys.length === 0) {
      setSearchValue('')
    }
    // 不从ID反推显示文本，让用户重新输入
  }, [selectedKeys])

  // 处理输入变化
  const handleInputChange = (e: any) => {
    const value = e.target.value
    setSearchValue(value) // UI 显示用户输入的文本
    // 注意：这里不直接设置 selectedKeys，等确认时再转换为ID
  }

    // 确认筛选（UI显示文本，底层转换为ID）
  const handleConfirm = async () => {
    const keyword = searchValue.trim()
    
    if (keyword === '') {
      setSelectedKeys([])
      confirm()
      return
    }
    
    setLoading(true)
    try {
      // 使用项目现有的人员模糊搜索方案
      const { app: api } = require('@ekuaibao/whispered')
      
      // 获取所有人员数据
      const staffs = await api.invokeService('@common:get:staffs:external').then(resp => resp[0].concat(resp[1]))
      
      // 根据关键词进行模糊匹配，支持姓名和工号
      const matchedStaffs = staffs.filter(staff => 
        staff.name.indexOf(keyword) !== -1 || 
        (staff.code && staff.code.indexOf(keyword) !== -1)
      )
      
      if (matchedStaffs.length > 200) {
        // 匹配人员过多时的处理
        const { Modal } = require('antd')
        Modal.error({
          title: i18n.get('匹配到的人员过多'),
          content: i18n.get('匹配到的人员过多，请尽量输入完整人员姓名，或工号')
        })
        setSelectedKeys([])
      } else if (matchedStaffs.length > 0) {
        // 转换为ID数组，UI继续显示用户输入的文本
        const staffIds = matchedStaffs.map(staff => staff.id)
        setSelectedKeys(staffIds) // 直接传递ID数组
      } else {
        // 没有匹配到人员
        setSelectedKeys([])
      }
      
      setLoading(false)
      confirm()
    } catch (error) {
      console.error('人员模糊搜索失败:', error)
      setLoading(false)
      setSelectedKeys([])
      confirm()
    }
  }

  // 重置
  const handleReset = () => {
    setSearchValue('')
    setSelectedKeys([])
    clearFilters && clearFilters({confirm: true, closeDropdown: true})
  }

      return (
      <div style={{ padding: 8, width: '264px' }} onKeyDown={e => e.stopPropagation()}>
        <Input
          placeholder="输入筛选条件"
          value={searchValue}
          onChange={handleInputChange}
          onPressEnter={handleConfirm}
          style={{ marginBottom: 8, display: 'block' }}
        />
        
        <div style={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
          <Button
            category="secondary"
            onClick={handleReset}
            size="small"
            style={{ width: 40, marginRight: '4px' }}
          >
            重置
          </Button>
          <Button
            category="primary"
            onClick={handleConfirm}
            size="small"
            style={{ width: 40 }}
          >
            确认
          </Button>
        </div>
      </div>
    )
}

// 列表筛选器组件
const ListFilterDropdown = ({ dataSource, setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => {
  const [checkedValues, setCheckedValues] = React.useState(selectedKeys || [])

  React.useEffect(() => {
    setCheckedValues(selectedKeys || [])
  }, [selectedKeys])

  const handleChange = (value: any, checked: boolean) => {
    let newValues = checkedValues.slice()
    if (checked) {
      if (!newValues.includes(value)) {
        newValues.push(value)
      }
    } else {
      newValues = newValues.filter(v => v !== value)
    }
    setCheckedValues(newValues)
  }

  const handleConfirm = () => {
    setSelectedKeys(checkedValues)
    confirm()
  }

  const handleReset = () => {
    setCheckedValues([])
    setSelectedKeys([])
    clearFilters && clearFilters({confirm: true, closeDropdown: true})
  }

  return (
    <div style={{ padding: 8, maxHeight: '300px', overflow: 'auto' }} onKeyDown={e => e.stopPropagation()}>
      <div style={{ marginBottom: 8 }}>
        {dataSource?.map((item: any) => (
          <div key={item.value} style={{ marginBottom: 4 }}>
            <Checkbox
              checked={checkedValues.includes(item.value)}
              onChange={(e) => handleChange(item.value, e.target.checked)}
            >
              {item.label}
            </Checkbox>
          </div>
        ))}
      </div>
      <div style={{ width: '100%', display: 'flex', justifyContent: 'end', borderTop: '1px solid #f0f0f0', paddingTop: 8 }}>
        <Button
          category="secondary"
          onClick={handleReset}
          size="small"
          style={{ width: 40, marginRight: '4px' }}
        >
          重置
        </Button>
        <Button
          category="primary"
          onClick={handleConfirm}
          size="small"
          style={{ width: 40 }}
        >
          确认
        </Button>
      </div>
    </div>
  )
}

// 默认过滤器组件
const DefaultFilterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
  <div style={{ padding: 8, width: '264px' }} onKeyDown={e => e.stopPropagation()}>
    <Input
      placeholder="搜索筛选"
      value={selectedKeys[0]}
      onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
      onPressEnter={() => confirm()}
      style={{ marginBottom: 8, display: 'block' }}
    />
    <div style={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
      <Button
        category="secondary"
        onClick={() => clearFilters && clearFilters({confirm: true, closeDropdown: true})}
        size="small"
        style={{ width: 40, marginRight: '4px' }}>
        重置
      </Button>
      <Button
        category="primary"
        onClick={() => confirm()}
        size="small"
        style={{ width: 40 }}>
        确认
      </Button>
    </div>
  </div>
)

// 默认过滤器图标
const DefaultFilterIcon = () => (
  <OutlinedEditFilter fontSize={16} style={{ color: 'var(--eui-icon-n2)' }} />
)

// 解析过滤器配置
export function parseFilter(column: any) {
  const { filterable, filterType, filterIcon, filter, renderFilter, filterDataSource } = column
  // 如果明确设置 filterType 为 false，则不显示筛选
  if (filterType === false) {
    return null
  }

  // 检查是否启用筛选功能
  const hasFilter = filterable ||
    filterType === true ||
    filterType === 'text' ||
    filterType === 'list' ||
    filterType === 'money' ||
    filterType === 'number' ||
    filterType === 'date' ||
    filterType === 'shortdate' ||
    filterType === 'dateRange' ||
    filterType === 'staffFZ' ||
    filterType === 'custom' ||
    filterType === 'ref' ||
    filter === true ||
    renderFilter ||
    filterDataSource

  if (!hasFilter) {
    return null
  }

  // 参与人不支持
  if(column?.dataType === "staffCYR") {
    return null
  }

  // 业务对象多选不支持
  if (filterType === "list" && column?.property?.elemType?.entity?.includes('datalink.DataLinkEntity')) {
    return null
  }

  const res: any = {
    filterIcon: filterIcon || DefaultFilterIcon
  }

  // 金额数字类型
  if (['number', 'money'].includes(filterType)) {
    return {
      ...res,
      filterDropdown: ValueRangeDropdown
    }
  }

  // 日期类型
  if (['date', 'shortdate', 'dateRange'].includes(filterType)) {
    return {
      ...res,
      filterDropdown: DateRangeDropdown
    }
  }

  // 部门
  if (filterType === 'custom' && column?.property?.entity === 'organization.Department') {
    return {
      ...res,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <DepartmentFilterDropdown
          clearFilters={clearFilters}
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          selectedKeys={selectedKeys}
        />
      )
    }
  }

  // 人员
  if (column?.dataType === "staffFZ" || (filterType === "custom" && column?.property?.entity === 'organization.Staff')) {
    return {
      ...res,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <StaffFilterDropdown
          clearFilters={clearFilters}
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          selectedKeys={selectedKeys}
        />
      ),
    }
  }

  // 人员多选
  if (filterType === "ref"  && column?.property?.elemType?.entity === 'organization.Staff') {
    return {
      ...res,
      filterDropdown: StaffSearchDropdown
    }
  }

  if (renderFilter) {
    return {
      filterDropdown: renderFilter,
      filterIcon: DefaultFilterIcon
    }
  }

  if (filterDataSource) {
    return {
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <ListFilterDropdown
          dataSource={filterDataSource}
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters}
        />
      ),
      filterIcon: filterIcon || DefaultFilterIcon
    }
  }

  if (filterType === 'ref') {
    return {
      filterDropdown: DefaultFilterDropdown,
      filterIcon: DefaultFilterIcon
    }
  }

  // 根据 filterType 生成默认的过滤器
  if (hasFilter) {
    return {
      filterDropdown: DefaultFilterDropdown,
      filterIcon: DefaultFilterIcon
    }
  }

  return null
}

// 解析渲染函数
export function parseRender(column: any) {
  // 如果已经有 render 函数，直接返回
  if (column.render) {
    return column.render
  }

  // 默认渲染函数
  return (text: any, record: any, index: number) => {
    if (text === null || text === undefined) {
      return '-'
    }
    return text
  }
}

// 合并其他配置到列
export function mergeOthers2Columns(columns: any[], orders: Record<string, any> = {}) {
  return columns.map(line => {
    let { render, dataIndex, key, sorter, filterable, ...rest } = line
    const ff = parseFilter(line)
    render = render ? render : parseRender(line)

    // 使用多种方式匹配排序状态
    const sortKey = key || dataIndex
    let sortOrder: string | undefined = undefined

    // 尝试多种匹配方式
    if (sortKey) {
      sortOrder = orders[sortKey] || orders[String(sortKey)]

      // 如果 sortKey 是数组形式的 dataIndex，尝试字符串形式
      if (!sortOrder && Array.isArray(sortKey)) {
        const stringKey = sortKey.join('.')
        sortOrder = orders[stringKey]
      }

      // 如果 sortKey 是字符串形式，尝试原始 dataIndex
      if (!sortOrder && typeof dataIndex === 'string') {
        sortOrder = orders[dataIndex]
      }
    }

    // 转换排序状态：ASC/DESC 转换为 antd 表格期望的 ascend/descend
    if (sortOrder) {
      if (sortOrder === 'ASC') {
        sortOrder = 'ascend'
      } else if (sortOrder === 'DESC') {
        sortOrder = 'descend'
      }
      // 保持原有的 ascend/descend 值不变
    }

    const result = {
      ...rest,
      render,
      sortOrder,
      dataIndex: typeof dataIndex === 'string' ? dataIndex.split('.') : dataIndex,
      key: sortKey
    }

    // 添加排序功能
    if (sorter !== false) {
      result.sorter = true
      result.sortDirections = ['ascend', 'descend']
    }

    // 添加过滤功能
    if (ff && typeof ff === 'object') {
      Object.assign(result, ff)
    }

    return result
  })
}

// 获取最近的父元素
export function closest(element: Element, selector: string): Element | null {
  if (!element || element.nodeType !== 1) return null

  if (element.matches && element.matches(selector)) {
    return element
  }

  return closest(element.parentElement!, selector)
} 