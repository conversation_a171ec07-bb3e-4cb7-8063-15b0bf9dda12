import loadable, { LoadableComponent } from '@loadable/component'

import { ENUM_TYPES } from './consts'
import { includes } from 'lodash'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

const Attachment = loadableWithDescriptor(() => import('./dynamic/Attachment.readonly'), {
  type: 'attachments'
})
const AIAttachment = loadableWithDescriptor(() => import('./dynamic/AIAttachment.readonly'), {
  type: 'aiAttachments'
})
const Money = loadableWithDescriptor(() => import('./dynamic/Money.readonly'), {
  type: 'money'
})
const PayeeInfo = loadableWithDescriptor(() => import('./dynamic/PayeeInfo.readonly'), {
  type: 'payeeInfo'
})
const Ref = loadableWithDescriptor(() => import('./dynamic/Ref.readonly'), {
  test({ type }) {
    return type.startsWith('ref') && type !== 'ref:organization.Staff' && !includes(ENUM_TYPES, type) && type !=='ref:basedata.Enum.currency' 
  }
})
const RefStaff = loadableWithDescriptor(() => import('./dynamic/RefStaff.readonly'), {
  type: 'ref:organization.Staff'
})
const Date = loadableWithDescriptor(() => import('./dynamic/Date.readonly'), {
  type: 'date'
})
const Number = loadableWithDescriptor(() => import('./dynamic/Number.readonly'), {
  type: 'number'
})
const DateRange = loadableWithDescriptor(() => import('./dynamic/DateRange.readonly'), {
  type: 'dateRange'
})
const Switcher = loadableWithDescriptor(() => import('./dynamic/Switcher.readonly'), {
  type: 'switcher'
})
const Label = loadableWithDescriptor(() => import('./dynamic/Label.readonly'), {
  test({ type }) {
    return !!~['specification', 'text', 'textarea', 'list'].indexOf(type)
  }
})
const Separator = loadableWithDescriptor(() => import('./dynamic/Separator.readonly'), {
  type: 'separator'
})
const Annotation = loadableWithDescriptor(() => import('./dynamic/Annotation'), {
  type: 'annotation'
})
const City = loadableWithDescriptor(() => import('./dynamic/City.readonly'), {
  type: 'city'
})
const RefEnum = loadableWithDescriptor(() => import('./dynamic/RefEnum.readonly'), {
  test({ type = '' }) {
    return includes(ENUM_TYPES, type)
  }
})
const SelectSearch = loadableWithDescriptor(() => import('./dynamic/SelectSearch.readonly'), {
  type: 'select_search'
})
const DataLink = loadableWithDescriptor(() => import('./dynamic/DataLink.readonly'), {
  type: 'dataLink'
})
const DataLinkEdits = loadableWithDescriptor(() => import('./dynamic/dataLinkEdit/DataLinkEdit.readonly'), {
  test(field) {
    const { type, referenceData, name } = field
    return (type === 'dataLinkEdits' && referenceData.type !== 'TRIP') || name === 'budgetAdjustDetails'
  }
})
const DataLinkList = loadableWithDescriptor(() => import('./dynamic/DataLinkList.readonly'), {
  type: 'dataLinks'
})
const MutilStaff = loadableWithDescriptor(() => import('./dynamic/MutilStaff.readonly'), {
  type: 'list:ref:organization.Staff'
})
const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })
const Currency = loadableWithDescriptor(() => import('./dynamic/Currency.readonly'), {
  type:'ref:basedata.Enum.currency'
})
const DataLinkStaff = loadableWithDescriptor(() => import('./dynamic/DataLinkStaff'), {
  type: 'list:ref:dataLink.Staff'
})
const MutilDimensionList = loadableWithDescriptor(() => import('./dynamic/MutilDimensionList.readonly'), {
  test(field) {
    const { type} = field
    return type?.startsWith('list:ref:basedata.Dimension') && type?.endsWith(':select')
  }
})

export const readonlyTrips = [
  Currency,
  Attachment,
  Date,
  Ref,
  RefStaff,
  Money,
  PayeeInfo,
  Label,
  Separator,
  Annotation,
  Number,
  DateRange,
  City,
  RefEnum,
  Switcher,
  SelectSearch,
  DataLink,
  DataLinkEdits,
  DataLinkList,
  MutilStaff,
  Unknown,
  DataLinkStaff,
  MutilDimensionList,
  AIAttachment
]
