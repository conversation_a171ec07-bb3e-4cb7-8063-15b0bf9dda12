import React from 'react'
import { Select } from 'antd'
import { T } from '@ekuaibao/i18n'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceField({
  descriptor: {
    type: 'split:rule'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    if (!value?.index || !value?.entityField) {
      return callback(i18n.get('拆分规则填写不完整'))
    }
    callback()
  },
  wrapper: wrapper()
} as IComponentDef)
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data
}))
export default class SelectSupplier extends React.PureComponent {
  fieldSource = [
    { value: 'flow', label: <T name="单据" /> },
    { value: 'detail', label: <T name="明细" /> }
  ]

  state = { fields: [] }

  componentDidMount() {
    const fields = this.getFields()
    this.setState({ fields })
  }

  getFields = () => {
    const { baseDataProperties } = this.props
    return baseDataProperties.filter(this.isLegalEntity)
  }

  isLegalEntity = item => {
    return item?.dataType?.entity === 'basedata.Dimension.法人实体'
  }

  sourceChange = value => {
    this.onChange({ index: value })
  }

  fieldChange = value => {
    this.onChange({ entityField: value })
  }

  onChange = val => {
    const { value, onChange } = this.props
    const v = { ...value, ...val }
    onChange?.(v)
  }

  render() {
    const { fields } = this.state
    const { value } = this.props
    return (
      <div>
        <div>
          <T name="按照" />
          <Select onChange={this.sourceChange} allowClear value={value?.index} style={{ width: 90, padding: '0 8px' }}>
            {this.fieldSource.map(item => {
              return <Select.Option key={item.value}>{item.label}</Select.Option>
            })}
          </Select>
          <T name="中的" />
          <Select
            onChange={this.fieldChange}
            allowClear
            value={value?.entityField}
            style={{ width: 200, padding: '0 8px' }}
          >
            {fields.map(item => {
              return <Select.Option key={item.name}>{item.label}</Select.Option>
            })}
          </Select>
          <T name="进行拆分" />
        </div>
        <div>
          <T name="拆分后按照下方开票信息及适用范围进行开票" />
        </div>
      </div>
    )
  }
}
