import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Radio, Modal } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { DefaultValue } from '../utils/DefaultValue'
const RadioGroup = Radio.Group

@EnhanceField({
  descriptor: {
    type: 'radio:group'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
} as IComponentDef)
@DefaultValue
export default class RadioGroupWrapper extends PureComponent {
  handleChange = e => {
    const { field } = this.props
    const val = e.target.value
    if (field?.changedConfirm && val === field?.confirmKey) {
      Modal.confirm({
        title: i18n.get('提示'),
        content: field.confirmContent,
        onOk: () => {
          this.props.onChange?.(val)
        }
      })
    } else {
      this.props.onChange?.(val)
    }
  }

  render() {
    const {
      value,
      field: { defaultValue, tags = [] }
    } = this.props
    return (
      <RadioGroup onChange={this.handleChange} value={value ?? defaultValue}>
        {tags.map(item => {
          return (
            <Radio key={item.value} value={item.value} disabled={item?.disabled}>
              {item.label}
              {!!item.suffix && item.suffix}
            </Radio>
          )
        })}
      </RadioGroup>
    )
  }
}
