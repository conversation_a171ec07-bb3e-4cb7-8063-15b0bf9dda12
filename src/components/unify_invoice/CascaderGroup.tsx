import React from 'react'
import { <PERSON>r } from '@hose/eui'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { Fetch } from '@ekuaibao/fetch'
import type { DefaultOptionType } from '@hose/eui/es/components/cascader'
import './OrderMapping.less'

@EnhanceField({
  descriptor: {
    type: 'cascader:group'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
} as IComponentDef)
export default class CascaderGroup extends React.PureComponent {
  state = { options:[], CascaderArr: [],invoiceAutoMatch: {} }
  originCascaderArr = [
    {
      label: '机票票号',
      code: 'ticketNo',
    },
    {
      label: '机票乘机人姓名',
      code: 'aircraftTraveler'
    },
    {
      label: '机票金额',
      code: 'aircraftPrice'
    },
    {
      label: '机票航班号',
      code: 'flightNumber'
    },
    {
      label: '乘机时间',
      code: 'aircraftStartTime'
    },
    {
      label: '火车票车次',
      code: 'trainNumber'
    },
    {
      label: '火车票票面价',
      code: 'trainPrice'
    },
    {
      label: '火车票乘车人',
      code: 'trainTravelers'
    },
    {
      label: '火车票发车时间',
      code: 'trainStartTime'
    },
  ]
  componentWillMount() {   
    this.init()
  }

  init = () => {
    const {
      field: { supplerAccountId,invoiceAutoMatchData }
    } = this.props
    console.log('invoiceAutoMatchData: ', invoiceAutoMatchData);
    Fetch.GET(`/api/supplier/v3/account/categoryFields?supplierAccountId=${encodeURIComponent(supplerAccountId)}`, {}, {}).then(res => {

    this.originCascaderArr.forEach((item) => {
      for (let key in invoiceAutoMatchData) {
        if(item.code === key) {
          if(invoiceAutoMatchData[key] && invoiceAutoMatchData[key].length !== 0) {
            const data = invoiceAutoMatchData[key].reduce((pre,item) => {
              pre.push([item.code, item.field])
              return pre
            },[])
            item['data'] = data
          } else {
            item['data'] = []
          }
        }
      }
    })

      this.setState({
        CascaderArr: this.originCascaderArr,
        options: res?.items,
        invoiceAutoMatch: invoiceAutoMatchData
      })
    })
  }

  onChange = (code: any,selectedOptions) => {
    const { invoiceAutoMatch = {} } = this.state
    const data = selectedOptions.map((item) => {
      return {
        code: item[0].code,
        field: item[1].code,
        fieldName: item[1].name
      }
    })
    invoiceAutoMatch[code] = data

    const { value, onChange } = this.props
    const v = { ...value, ...invoiceAutoMatch }
    onChange?.(v )
  }

  displayRender = (labels: string[], selectedOptions: DefaultOptionType[]) => {
    const { field: {supplierName = ''} }  = this.props
    return labels.map((label,index) => {
      if (index === 0){
        label = label.replace(`${supplierName}-`,'')
      }
      return label
    }).join('-')
  }

  render() {
    const {CascaderArr,options } = this.state
    return (
      <>
        {CascaderArr.length !== 0 && CascaderArr.map((item) => (
          <div className="order-mapping-wrapper">
            <div className="order-desc">将订单字段</div>
            <Cascader
              className={'order-mapping'}
              fieldNames={{ label: 'name', value: 'code', children: 'fields' }}
              defaultValue={item.data}
              options={options}
              showCheckedStrategy={Cascader.SHOW_CHILD}
              displayRender={(labels,selectedOptions) => this.displayRender(labels,selectedOptions)}
              onChange={(value,selectedOptions) => this.onChange(item.code,selectedOptions)}
              multiple
              maxTagCount="responsive"
            />
          <div className="order-right-desc">{`识别为${item.label}`}</div>
          </div>
      ))}
      </>
    )
  }
}

