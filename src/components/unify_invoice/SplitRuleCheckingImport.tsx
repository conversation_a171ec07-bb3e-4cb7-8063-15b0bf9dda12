/**
 *  Created by pw on 2021/6/25 下午8:59.
 */
import React, { Component } from 'react'
import { app, app as api } from '@ekuaibao/whispered'
import { <PERSON>hanceField } from '@ekuaibao/template'
import  './SplitRuleCheckingImport.less'
import { Table } from 'antd'
import { Checkbox, Tooltip, Select, Button, Popover, Popconfirm } from '@hose/eui'
import {
  OutlinedTipsInfo,
  OutlinedTipsAdd,
  IllustrationSmallNoContent,
} from '@hose/eui-icons'
import { EnhanceConnect } from '@ekuaibao/store'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { toJS } from 'mobx'

interface Props {
  value?: any
  feeTypeMap: any
  supplerAccountName?: string
  showUseFeeTypeSplitTip: boolean
}

interface BillTypeModalResIF {
  feeTypeId: string
  invoiceCategorys: string[]
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'split:rule:checkingImport'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    const rulesCheckbox = value?.radioDefaultValue
    const rulesSelect = value?.selectDefaultValue
    if (rulesCheckbox && rulesCheckbox?.length === 0) {
      return callback(i18n.get('拆分规则不能为空'))
    }
    if (
      rulesCheckbox?.includes('useLegalEntitySplit') &&
      !rulesSelect?.length &&
      value?.versionType !== 'hoseTripOnlineInvoicing'
    ) {
      return callback(i18n.get('请选择至少一个法人实体'))
    }
    if (
      rulesCheckbox?.includes('useFeeTypeSplit') &&
      !value?.invoiceTypes?.length &&
      value?.versionType !== 'hoseTripOnlineInvoicing'
    ) {
      return callback(i18n.get('请至少维护1个费用类型，否则无法拆分发票批次'))
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
} as IComponentDef)
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  feeTypeMap: state['@common'].feetypes.map,
}))
export default class SplitRuleCheckingImport extends Component<Props> {
  state = { legalEntitySelect: [], splitRuleOptions: [] }
  isHoseOnline = this.props.value?.versionType === 'hoseTripOnlineInvoicing'

  getFeeTypeModeValue = ({ feeTypeId, invoiceCategorys }) => {
    const { feeTypeMap } = this.props
    const feeType = feeTypeMap[feeTypeId]
    if (!feeType) return null
    return {
      id: feeType.id,
      code: feeType.code,
      name: feeType.name,
      feeTypeId,
      invoiceCategorys,
    }
  }

  handleFeeTypeEdit = (record, index) => {
    api
      .open("@bills:BillTypeModal", {
        title: i18n.get('编辑费用类型'),
        feeTypeId: record?.id,
        invoiceCategorys: record?.invoiceCategorys
      })
      .then((res: BillTypeModalResIF) => {
        console.log("====== res", res)
        const invoiceTypes = this.props.value?.invoiceTypes || []
        const invoiceType = this.getFeeTypeModeValue(res)
        if (!invoiceType) return
        invoiceTypes[index] = invoiceType
        this.onChange({ invoiceTypes })
      })
  }
  handleFeeTypeDelete = (record, index) => {
    const value = this.props.value
    const invoiceTypes = value?.invoiceTypes || []
    invoiceTypes.splice(index, 1)
    this.onChange({ invoiceTypes })
  }

  fnShowUseFeeTypeSplitTip = () => {
    const { showUseFeeTypeSplitTip } = this.props
    if (!showUseFeeTypeSplitTip) return
    setTimeout(()=>{
      const useFeeTypeSplitCheckbox = document.getElementById('splitRuleCheckingImport-checkbox')
      if (useFeeTypeSplitCheckbox) {
        useFeeTypeSplitCheckbox.click()
      }
    },300)
  }

  componentDidMount() {
    this.fnShowUseFeeTypeSplitTip()
    app.dataLoader('@common.feetypes').load()
    console.log('this.props.value: ', this.props.value)
    const content = <div style={{ width: '210px' }}>
      {i18n.get('删除该费用类型后，发票批次拆分时不会将符合该费用类型的明细打包在同一批次。是否删除？')}
    </div>
    const invoiceFeeTypeColumns = invoiceTypeColumns
      .concat({
        title: "操作",
        key: "action",
        width: 160,
        render: (text, record, index) => (
          <div className="action_view">
            <span className="edit"
                  onClick={() => this.handleFeeTypeEdit(record, index)}>
              {i18n.get("编辑")}
            </span>
            <Popconfirm
              arrowPointAtCenter
              placement="topRight"
              title={i18n.get("删除该费用类型？")}
              content={content}
              okText={i18n.get("删除")}
              okButtonProps={{ theme: "danger" }}
              onConfirm={() => this.handleFeeTypeDelete(record, index)}
              getPopupContainer={() => document.getElementById('splitRuleCheckingImport-wrapper')}>
              <span className="delete">{i18n.get("删除")}</span>
            </Popconfirm>
          </div>
        )
      })
    const splitRuleOptions = [
      {
        label: '按法人实体拆分',
        value: 'useLegalEntitySplit',
        isTable: this.isHoseOnline,
        colmuns: columsLegalEntityHoseTripOnlineInvoicing,
        tableData: this.props.value?.invoices,
        disabled: this.isHoseOnline,
        suffix: (
          <Tooltip
            title={
              this.isHoseOnline
                ? '按「供应商账户」中配置的抬头对应的法人实体值拆分'
                : '如需按照法人实体拆分，可填写对应法人实体字段。字段先后顺序为优先级顺序，将会在费用按照最先找到的法人实体字段拆分批次。'
            }
          >
            <OutlinedTipsInfo />
          </Tooltip>
        )
      },
      {
        label: this.isHoseOnline ? '按合思商城业务线拆分' : '按费用类型拆分',
        value: 'useFeeTypeSplit',
        showCreateBtn: !this.isHoseOnline,
        isTable: true,
        colmuns: this.isHoseOnline ? columnsBusinessNameHoseTripOnlineInvoicing : invoiceFeeTypeColumns,
        disabled: true,
        suffix: (
          <Tooltip
            title={
              this.isHoseOnline
                ? '业务线划分仅供参考，不可修改'
                : '按照「供应商账户-详情」中配置的费用类型进行拆分，如需修改，可在「供应商账户-详情」编辑'
            }
          >
            <OutlinedTipsInfo />
          </Tooltip>
        )
      }
    ]
    const legalEntitySelect = this.getFields()
    this.setState({ legalEntitySelect, splitRuleOptions })
  }

  getFields = () => {
    const { baseDataProperties } = this.props
    return baseDataProperties.filter(this.isLegalEntity)
  }

  isLegalEntity = item => {
    return item?.dataType?.entity === 'basedata.Dimension.法人实体'
  }

  fieldChange = selectValue => {
    const { value } = this.props
    if (!value?.radioDefaultValue.includes('useLegalEntitySplit')) {
      value?.radioDefaultValue.push('useLegalEntitySplit')
    }
    this.onChange({ selectDefaultValue: selectValue })
  }

  checkGroupChange = checkedValue => {
    const { value } = this.props
    if (!checkedValue.includes('useLegalEntitySplit')) {
      value.selectDefaultValue = []
    }
    this.onChange({ radioDefaultValue: checkedValue })
  }

  onChange = val => {
    const { value, onChange } = this.props
    const v = { ...value, ...val }
    onChange?.(v)
  }

  handleCreateFeeTypeMode = () => {
    const value = toJS(this.props.value)
    const invoiceTypes = value?.invoiceTypes || []

    api
      .open('@bills:BillTypeModal', {
        title: i18n.get('新增费用类型'),
        oldData: invoiceTypes,
      })
      .then((res: BillTypeModalResIF) => {
        const invoiceTypes = this.props.value?.invoiceTypes || []
        const invoiceType = this.getFeeTypeModeValue(res)
        if (!invoiceType) return
        invoiceTypes.push(invoiceType)
        this.onChange({ invoiceTypes })
      })
  }

  renderCheckbox = (item) => {
    const { showUseFeeTypeSplitTip } = this.props
    if (item.value === 'useFeeTypeSplit' && showUseFeeTypeSplitTip) {
      const content = <div style={{ width: 170 }}>
        {i18n.get('已为您默认勾选「按费用类型拆分」，请维护费用类型后点击「确定」')}
      </div>
      return <Popover content={content}
                      getPopupContainer={() => document.getElementById('splitRuleCheckingImport-wrapper')}
                      placement="bottomLeft"
                      trigger="click">
        <div id="splitRuleCheckingImport-checkbox">
          <Checkbox key={item.value} value={item.value} disabled={item.disabled}>
            {item.label} {item.suffix}
          </Checkbox>
        </div>
      </Popover>
    }
    return <Checkbox key={item.value} value={item.value} disabled={item.disabled}>
      {item.label} {item.suffix}
    </Checkbox>
  }

  render() {
    const { value, supplerAccountName } = this.props
    const data = value?.invoiceTypes
    const { legalEntitySelect, splitRuleOptions } = this.state
    return (
      <div className="splitRuleCheckingImport-wrapper" id="splitRuleCheckingImport-wrapper">
        <Checkbox.Group style={{ width: '100%' }} onChange={this.checkGroupChange} value={value?.radioDefaultValue}>
          {splitRuleOptions.map((item: any) => {
            return (
              <>
                <div className="splitRuleCheckingImport-header">
                  {this.renderCheckbox(item)}
                  {item.showCreateBtn && <Button
                    size="small"
                    category="secondary"
                    theme="highlight"
                    onClick={() => this.handleCreateFeeTypeMode()}>
                    <OutlinedTipsAdd />
                    {i18n.get('新增费用类型')}
                  </Button>}
                </div>
                <div>
                  {item.isTable ? (
                    <TableComponent columns={item?.colmuns}
                                    data={data}
                                    supplerAccountName={supplerAccountName}
                    />
                  ) : (
                    <Select
                      onChange={this.fieldChange}
                      allowClear
                      mode="multiple"
                      value={value?.selectDefaultValue}
                      style={{ width: '100%', margin: '8px 0px 16px' }}
                    >
                      {legalEntitySelect.map((item: any) => {
                        return <Select.Option key={item.name}>{item.label}</Select.Option>
                      })}
                    </Select>
                  )}
                </div>
              </>
            )
          })}
        </Checkbox.Group>
      </div>
    )
  }
}

interface TableComponentProps<T> {
  rowKey?: string
  columns: T[]
  data: T[]
  supplerAccountName?: string
}

const locale = (supplerAccountName: string) => ({
  emptyText: (<div className="splitRuleCheckingImport-table-null">
    <IllustrationSmallNoContent/>
    <span>{i18n.get(`${supplerAccountName}还没有维护费用类型，账单无法按照指定费用类型拆分批次`)}</span>
  </div>)
})

const TableComponent: React.FC<TableComponentProps<any>> = props => {
  const { data, columns, rowKey = 'id', supplerAccountName } = props
  return (
    <Table
      style={{ margin: '16px 0px' }}
      rowKey={rowKey}
      showHeader={true}
      columns={columns}
      dataSource={data}
      pagination={false}
      locale={locale(supplerAccountName)}
    />
  )
}

interface TitleIF {
  title: string
}

interface FeeTypeTitleIF {
  title: string
  code?: string
}


const ColumnCP: React.FC<TitleIF> = props => {
  return <div className="supplier-account-setting-value">{props.title || '-'}</div>
}

const FeeTypeColumnCP: React.FC<FeeTypeTitleIF> = props => {
  let feeTypeName = props.title
  if (!feeTypeName) {
    feeTypeName = '-'
  } else if (props.code) {
    feeTypeName = `${props.title}（${props.code}）`
  }
  return <div className="supplier-account-setting-feetype-value">
    {feeTypeName.length > 33
      ? <Tooltip title={feeTypeName}>{feeTypeName}</Tooltip>
      : feeTypeName
    }
  </div>
}

const InvoiceTypeMapping = () => ({
  INVOICE_TAXI: i18n.get('出租车发票'),
  VAT_INVOICE: i18n.get('VAT_INVOICE'),
  INVOICE_OTHER: i18n.get('其他'),
  INVOICE_TRAIN: i18n.get('铁路客票'),
  INVOICE_PASSENGER_CAR: i18n.get('客运汽车发票'),
  INVOICE_AIRCRAFT: i18n.get('航空运输电子客票行程单'),
  INVOICE_ROAD_TOLL: i18n.get('过路费发票'),
  INVOICE_QUOTA: i18n.get('定额发票'),
  INVOICE_MACHINE_PRINT: i18n.get('机打发票'),
  DIGITAL_NORMAL: i18n.get('增值税电子普通发票'),
  PAPER_SPECIAL: i18n.get('增值税专用发票'),
  DIGITAL_SPECIALINVOICE_TAXI: i18n.get('增值税电子专用发票'),
  INVOICE: i18n.get('增值税发票')
})

const invoiceColumns = [
  {
    title: '抬头名称',
    dataIndex: 'corpPayerInfoId.name',
    key: 'corpPayerInfoId.name',
    render: value => <ColumnCP title={value} />
  },
  {
    title: '税号',
    dataIndex: 'corpPayerInfoId.payerNo',
    key: 'corpPayerInfoId.payerNo',
    render: value => <ColumnCP title={value} />
  },
  {
    title: '法人实体',
    dataIndex: 'entityField',
    key: 'entityField',
    render: value => <ColumnCP title={value} />
  }
]

const invoiceTypeColumns = [
  {
    key: 'name',
    title: '费用类型',
    dataIndex: 'name',
    render: (value, record) => <FeeTypeColumnCP title={value} code={record?.code}/>
  },
  {
    key: 'invoiceCategorys',
    title: '发票类型',
    dataIndex: 'invoiceCategorys',
    render: (text: string[]) => {
      const title = text ? text.map(value => InvoiceTypeMapping()[value]).join(',') : '-'
      return <ColumnCP title={title} />
    }
  }
]

const columnsBusinessNameHoseTripOnlineInvoicing = [
  {
    title: '业务类型',
    dataIndex: 'businessName',
    key: 'businessName',
    width: '30%',
    render: text => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceCategorys',
    width: '30%',
    key: 'invoiceCategorys',
    render: (text, record, index) => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  },
  {
    title: '开票内容',
    dataIndex: 'invoicingContent',
    width: '30%',
    key: 'invoicingContent',
    render: (text, record, index) => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  }
]

const columsLegalEntityHoseTripOnlineInvoicing = [
  {
    title: '抬头名称',
    dataIndex: 'name',
    key: 'name',
    render: text => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  },
  {
    title: '抬头税号',
    dataIndex: 'payerNo',
    key: 'payerNo',
    render: text => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  },
  {
    title: '法人实体字段',
    key: 'entityFieldLabel',
    dataIndex: 'entityFieldLabel',
    render: text => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  },
  {
    title: '法人实体',
    key: 'legalEntityName',
    dataIndex: 'legalEntityName',
    render: text => <BaseTableColumnText>{text ? text : i18n.get('-')}</BaseTableColumnText>
  }
]

const BaseTableColumnText = props => {
  return <div style={{ color: 'var(--eui-text-caption)' }}>{props.children}</div>
}
