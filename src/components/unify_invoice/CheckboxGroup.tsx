import React from 'react'
import { Checkbox } from 'antd'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
const Group = Checkbox.Group

@EnhanceField({
  descriptor: {
    type: 'checkbox:group'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
} as IComponentDef)
export default class SelectSupplier extends React.PureComponent {
  onChange = (value: any) => {
    const { onChange } = this.props
    onChange?.(value)
  }

  render() {
    const {
      value,
      field: { tags = [] }
    } = this.props
    return <Group value={value} options={tags} onChange={this.onChange} />
  }
}
