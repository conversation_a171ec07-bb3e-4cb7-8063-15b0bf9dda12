import React from 'react'
import { Checkbox } from 'antd'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'

@EnhanceField({
  descriptor: {
    type: 'autoSplitCheckbox'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback()
  },
  wrapper: wrapper()
} as IComponentDef)
export default class SelectSupplier extends React.PureComponent {
  onChange = e => {
    const { onChange } = this.props
    onChange?.(e.target.checked)
  }

  render() {
    const {
      value,
      field: { text }
    } = this.props
    return (
      <Checkbox checked={value} onChange={this.onChange}>
        {text}
      </Checkbox>
    )
  }
}
