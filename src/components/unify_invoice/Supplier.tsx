import React from 'react'
// import { Select } from 'antd'
import { Select } from '@ekuaibao/eui-web'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'

@EnhanceField({
  descriptor: {
    type: 'select:supplier'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
} as IComponentDef)
export default class SelectSupplier extends React.PureComponent {
  onChange = (value: any) => {
    const { onChange } = this.props
    onChange?.(value)
  }

  render() {
    const {
      value,
      field: { label, placeholder = i18n.get('请选择{__k0}', { __k0: label }), tags = [], disabled }
    } = this.props
    return (
      <Select
        value={value}
        allowClear
        showSearch
        disabled={disabled}
        data={tags}
        style={{ width: '100%' }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder={placeholder}
        onChange={this.onChange}
      />
    )
  }
}
