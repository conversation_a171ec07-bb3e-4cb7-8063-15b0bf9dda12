import React from 'react'
import { But<PERSON> } from 'antd'
import { T } from '@ekuaibao/i18n'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { EnhanceConnect } from '@ekuaibao/store'
import { app } from '@ekuaibao/whispered'
import EKBIcon from '../../elements/ekbIcon'
import PayerInfo from '../../elements/payerInfo'

@EnhanceField({
  descriptor: {
    type: 'corp:payer:info'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
} as IComponentDef)
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data
}))
export default class CorpPayerInfo extends React.PureComponent {
  state = { payerInfos: [] }

  handleAdd = async () => {
    const { fnCheckPayerInfo } = app.require('@components/utils/fnCheckPayerInfo')
    const user = await app.dataLoader('@common.userinfo').load()
    const payerInfo = await app.dataLoader('@common.payerInfo').load()
    const checkUnable = fnCheckPayerInfo(user.data, payerInfo, false, {
      title: i18n.get('无法添加'),
      content: i18n.get('没有开票信息，请先设置一个开票信息')
    })
    if (checkUnable) {
      return
    }
    const { value, onChange } = this.props
    const result = await app.open('@invoice-manage:SelectInvoicePayer', { selectedKeys: value?.map(item => item.id) })
    onChange?.(result)
  }

  handleDelete = item => {
    const { value, onChange } = this.props
    const data = value?.filter(ele => item?.id !== ele.id)
    onChange?.(data)
  }

  render() {
    const { value } = this.props
    return (
      <div style={{ padding: '0 16px' }}>
        <PayerInfo data={value} handleDelete={this.handleDelete} />
        <Button
          type="dashed"
          style={{ width: '100%', color: 'var(--brand-base)', borderColor: 'var(--brand-base)' }}
          onClick={this.handleAdd}
        >
          <EKBIcon
            name="#EDico-plus-default"
            style={{ color: 'var(--brand-base)', width: 14, height: 14, marginRight: 4 }}
          />
          <T name="添加" />
        </Button>
      </div>
    )
  }
}
