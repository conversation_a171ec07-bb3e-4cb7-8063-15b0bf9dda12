import React, { PureComponent } from 'react'
import { IExtendBus } from '@ekuaibao/template'

type IEnhanceFieldProps = {
  initialValue: any
  field: any
  form: any
  bus: IExtendBus
  wrapper: any
  [key: string]: any
}

export const DefaultValue = <T extends IEnhanceFieldProps, P>(Component: React.ReactNode) => {
  return class extends PureComponent<T, P> {
    componentDidMount() {
      const { value, field, onChange } = this.props
      if (
        value === undefined &&
        (!!field?.defaultValue || field?.defaultValue === 0 || field?.defaultValue === false)
      ) {
        onChange?.(field.defaultValue)
      }
    }

    render() {
      return <Component {...this.props} />
    }
  }
}
