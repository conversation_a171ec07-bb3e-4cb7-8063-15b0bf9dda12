import { getV } from '@ekuaibao/lib/lib/help'

export default (field, autoCalFields = {}) => {
  const { onFields = [] } = autoCalFields
  return onFields && !!~onFields.indexOf(field)
}

export function changeAutoComputerDisplayValue(value, props) {
  const defatultValue = getV(props, 'field.defaultValue.value')
  const type = getV(props, 'field.defaultValue.type')
  const editable = getV(props, 'field.editable')
  const flowState = getV(props, 'billState')
  if (defatultValue && flowState === 'new' && type === 'formula' && !editable) {
    const regexp = /\$单号\`/g // @i18n-ignore
    const regexpRequisition = /\`\$关联申请\`\.\`\$单号\`/g // @i18n-ignore
    if (regexp.test(defatultValue) && !regexpRequisition.test(defatultValue)) {
      value = value ? value + `(${i18n.get('待生成单号')})` : i18n.get('待生成单号')
    }
  }
  return value
}
