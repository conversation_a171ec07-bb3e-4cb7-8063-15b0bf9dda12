import { GlobalFieldIF, StaffIF } from '@ekuaibao/ekuaibao_types'
export const checkStaffDataRange = (
  field: GlobalFieldIF & { allowInteriorStaff?: boolean; allowExternalStaff?: boolean; defaultValue: { type: string } },
  staff: StaffIF | StaffIF[]
): StaffIF | StaffIF[] => {
  // allowInteriorStaff为undefined是为了兼容历史数据
  if (
    !field ||
    field.name === 'submitterId' ||
    !staff ||
    (Array.isArray(staff) && !staff.length) ||
    field.allowInteriorStaff === undefined
  ) {
    return staff
  }
  if (field.allowInteriorStaff && field.allowExternalStaff) {
    return staff
  }
  if (field.allowInteriorStaff) {
    if (Array.isArray(staff) && field?.defaultValue?.type !== 'formula') {
      return staff.filter(item => !item.external)
    }
    return staff.external ? undefined : staff
  }

  if (field.allowExternalStaff) {
    if (Array.isArray(staff)) {
      return staff.filter(item => item.external)
    }
    return staff.external ? staff : undefined
  }
  return staff
}
