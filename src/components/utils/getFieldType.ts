/***************************************************
 * Created by nanyuantingfeng on 2020/3/24 17:04. *
 ***************************************************/

export const enum FieldType {
  Text = 'text',
  AutoNumber = 'autoNumber',
  Date = 'date',
  DateRange = 'dateRange',
  Number = 'number',
  Switch = 'switcher',
  Money = 'money',
  RefPayee = 'ref:pay.PayeeInfo',
  RefEntity = 'ref:datalink.DataLinkEntity',
  RefEntityList = 'list:datalink.DataLinkEntity',
  RefDimension = 'ref:basedata.Dimension',
  RefDimensions = 'list:ref:basedata.Dimension',
  RefEnum = 'ref:basedata.Enum',
  RefDepartment = 'ref:organization.Department',
  AutoComputed = 'autoComputed',
  RefCity = 'ref:basedata.city',
  RefStaff = 'ref:organization.Staff',
  ListStaff = 'list:organization.Staff',
  Default = '',
  Ref = 'ref',
  List = 'list',
  Attachment = 'attachment'
}

export const selectFields = [
  {
    value: FieldType.Text,
    formatPriority: 0,
    formatField: input => input.type === FieldType.Text
  },
  {
    value: FieldType.AutoNumber,
    formatPriority: 0,
    formatField: input => input.type === FieldType.AutoNumber
  },
  {
    value: FieldType.Date,
    formatPriority: 0,
    formatField: input => input.type === FieldType.Date
  },
  {
    value: FieldType.DateRange,
    formatPriority: 0,
    formatField: input => input.type === FieldType.DateRange
  },
  {
    value: FieldType.Number,
    formatPriority: 0,
    formatField: input => input.type === FieldType.Money
  },
  {
    value: FieldType.Money,
    formatPriority: 0,
    formatField: input => input.type === FieldType.Money
  },
  {
    value: FieldType.Switch,
    formatPriority: 0,
    formatField: input => input.type === FieldType.Switch
  },
  {
    value: FieldType.RefPayee,
    formatPriority: 1,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity === 'pay.PayeeInfo' && {
        ...input,
        type: FieldType.RefPayee
      }
  },
  {
    value: FieldType.RefEntity,
    formatPriority: 1,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity.startsWith('datalink.DataLinkEntity.') && {
        ...input,
        type: FieldType.RefEntity,
        entity: input.entity.substr('datalink.DataLinkEntity.'.length)
      }
  },
  {
    value: FieldType.RefEntityList,
    formatPriority: 0,
    formatField: input =>
      input.type === FieldType.List &&
      input.elemType &&
      input.elemType.type === FieldType.Ref &&
      input.elemType.entity.startsWith('datalink.DataLinkEntity.') && {
        ...input,
        entity: input.elemType.entity.substr('datalink.DataLinkEntity.'.length),
        type: FieldType.RefEntityList
      }
  },
  {
    value: FieldType.RefDimension,
    formatPriority: 1,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity.startsWith('basedata.Dimension.') && {
        ...input,
        type: FieldType.RefDimension,
        entity: input.entity.substr('basedata.Dimension.'.length)
      }
  },
  {
    value: FieldType.RefDimensions,
    formatPriority: 1,
    formatField: input =>

      input.type === FieldType.List &&
      input.elemType &&
      input.elemType.type === FieldType.Ref &&
      input.elemType.entity.startsWith('basedata.Dimension.') && {
        ...input,
        type: FieldType.RefDimensions,
      }
  },
  {
    value: FieldType.RefEnum,
    formatPriority: 1,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity.startsWith('basedata.Enum.') && {
        ...input,
        type: FieldType.RefEnum,
        entity: input.entity.substr('basedata.RefEnum.'.length)
      }
  },
  {
    value: FieldType.RefDepartment,
    formatPriority: 1,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity.startsWith('organization.Department') && {
        ...input,
        type: FieldType.RefDepartment,
      }
  },
  {
    value: FieldType.RefCity,
    formatPriority: 0,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity === 'basedata.city' && {
        ...input,
        type: FieldType.RefCity
      }
  },
  {
    value: FieldType.RefStaff,
    formatPriority: 0,
    formatField: input =>
      input.type === FieldType.Ref &&
      input.entity === 'organization.Staff' && {
        ...input,
        type: FieldType.RefStaff
      }
  },
  {
    value: FieldType.ListStaff,
    formatPriority: 0,
    formatField: input =>
      input.type === FieldType.List &&
      input.elemType &&
      input.elemType.type === FieldType.Ref &&
      input.elemType.entity === 'organization.Staff' && {
        ...input,
        type: FieldType.ListStaff
      }
  },
  {
    value: FieldType.AutoComputed,
    formatPriority: 2,
    formatField: input =>
      (input.type === FieldType.Money || input.type === FieldType.Number) &&
      !!input.formula && {
        ...input,
        type: FieldType.AutoComputed,
        _returnType: input.type
      }
  },
  {
    value: FieldType.Attachment,
    formatPriority: 0,
    formatField: input =>
      input.type === FieldType.List &&
      input.elemType &&
      input.elemType.type === FieldType.Attachment && {
        ...input,
        type: FieldType.Attachment
      }
  }
]

const formatFields = selectFields.slice().sort((a, b) => b.formatPriority - a.formatPriority)

export function getFieldType(line) {
  for (const item of formatFields) {
    const output = item.formatField(line)
    if (output) {
      return output === true ? line.type : item.value
    }
  }
  return line.type
}
