/**
 *  Created by pw on 2019-09-18 16:51.
 */
import React, { PureComponent } from 'react'
import { isArray, isString } from '@ekuaibao/helpers'
import { app as api } from '@ekuaibao/whispered'

interface ElemTypeIF {
  type: string
  entity: string
}

interface DataTypeIF {
  type: string
  elemType: ElemTypeIF
}

interface FieldIF {
  dataType: DataTypeIF
}

interface Props {
  value: string | any[]
  onChange: (value: string | any[]) => void
  field: FieldIF
}

export const FetchRefList = <P extends Props>(WrappedComponent: React.ComponentType<P>) => {
  return class extends PureComponent<P> {
    constructor(props: P) {
      super(props)
      const value = props.value
      this.state = {
        value: value ? (isArray(value) ? value : isString(value) ? (value as string).split(',') : [value]) : undefined
      }
    }

    async componentDidMount() {
      const { value, field, onChange } = this.props
      if (needFetchData(this.props)) {
        const func = fetchMap[field.dataType.elemType.entity]
        if (func) {
          const values = await func(value)
          this.setState({ value: values }, () => onChange && onChange(values))
        }
      }
    }

    async componentWillReceiveProps(nextProps) {
      const { value, field, onChange } = nextProps
      if (needFetchData(nextProps)) {
        const func = fetchMap[field.dataType.elemType.entity]
        if (func) {
          const values = await func(value)
          const list = value.map(id => values.find(item => item.id === id)) //重排顺序，要不影响自动计算准确性
          this.setState({ value: list }, () => onChange && onChange(list))
        }
      }
    }

    render(): React.ReactNode {
      let newProps = {}
      if (needFetchData(this.props)) {
        newProps = { ...this.state }
      }
      return <WrappedComponent {...this.props} {...newProps} />
    }
  }
}

const fetchMap: any = {
  'organization.Staff': fetchStaff
}

async function fetchStaff(value: string) {
  if (!value) {
    return value
  }
  let ids: string[] = []
  if (isString(value)) {
    ids = value.split(',')
  }
  if (isArray(value)) {
    ids = (value as unknown) as string[]
  }
  if (ids.length) {
    const res = await api.invokeService('@common:get:staff:by:ids', { ids })
    return res.items
  }
  return Promise.resolve(value)
}

function needFetchData(props: Props): boolean {
  const { field, value } = props
  if (!value || !field) {
    return false
  }
  if (field.dataType.type !== 'list' && field.dataType.elemType.type !== 'ref') {
    return false
  }
  if (isString(value)) {
    return true
  }
  if (isArray(value)) {
    const id = (value as string[]).find((line: string) => isString(line))
    return id && !!id.length
  }
  return false
}
