import { app as api } from '@ekuaibao/whispered'
import { convertConsume } from '../../plugins/bills/parts/right-part/billInfo/BillImport/importUtils'
import { get, uniq, cloneDeep, reduce, set } from 'lodash'
import { message } from 'antd'
import { related } from '../../elements/feeDetailViewList/Related'
import { MoneyMath } from '@ekuaibao/money-math'
import { getDetailContainApportion, moveDetailApportion, hasCopy } from '../../plugins/bills/layers/import-bill/util'
import { showModal } from '@ekuaibao/show-util'

const fnFDealWidthMoney = (money) => {
  if (!new MoneyMath(money).gte(0)) {
    set(money, 'standard', '0')
  }
  return money
}

/**
 * 费用明细价格计算数据格式化
 * @param {*} dataSource 
 * @param {*} _tempConsumId 
 * @returns 
 */
export function formatImportApplyDetail(dataSource, _tempConsumId) {
  const formatList = dataSource.map(item => {
    const dataList = item?.dataList.map(line => {
      const detailId = get(line, 'id')
      const detailsMoneys = related.getDetialsMoneyByConsumeId(_tempConsumId)
      const consumeUseMoney = detailsMoneys && detailsMoneys.find(line => line.relateId === detailId)
      let useMoney = related.relatedMap[detailId] || 0
      useMoney = consumeUseMoney ? new MoneyMath(useMoney).minus(consumeUseMoney.consumeAmount).value : useMoney
      const notWrittenOffAmount = new MoneyMath(line.unwrittenOffAmount).minus(useMoney).value
      const noWrittenOffAmount = fnFDealWidthMoney(notWrittenOffAmount)
      line._tempConsumId = _tempConsumId
      line.modifyValue = line.modifyMoney = noWrittenOffAmount
      return line
    })
    return { ...item, dataList }
  })
  return formatList
}

/**
 * 导入明细
 * @param {*} props 
 * @param {*} isModal 
 */
export function handleImportApplyDetail(props) {
  const {
    currentVisibleFeeTypes,
    applicationListDetails = [],
    bus,
    submitterId,
    dataFromOrder,
  } = props
  api
    .open('@bills:FeeDetailViewApplyList', {
      from: 'IMPORT',
      currentVisibleFeeTypes,
      dataSource: applicationListDetails,
      dataFromOrder,
    })
    .then(async res => {
      const selectedList = await formatNoFeeTypeList(res)
      const importDetails = await autoImportApplyDetail(selectedList, submitterId, dataFromOrder);
      if (importDetails?.length) {
        bus.emit('import:expenseLink', importDetails)
      }
    })
}

/**
 * 费用明细调用自动导入费用明细系统计算后数据格式化返回
 * @param {*} selectedList 
 * @param {*} submitterId
 * @param {*} dataFromOrder 成本归属单传入的数据
 * @returns
 */
export async function autoImportApplyDetail(selectedList, submitterId, dataFromOrder) {

  // 成本归属单传入的订单企业已付金额
  const companyRealPayFromOrder = dataFromOrder?.companyRealPayFromOrder

  if (selectedList.length > 5) {
    setTimeout(() => {
      message.loading(i18n.get('计算中...'), 120)
    }, 600)
  }

  const autoFeeDetails = await Promise.all(
    selectedList.map(item => {
      const { feeTypeId, noWrittenOffAmount, linkDetailEntities } = item
      const specificationId = feeTypeId.expenseSpecificationId
      feeTypeId.expenseSpecificationId = specificationId.id
      const consume = {
        feeTypeId,
        specificationId
      }
      const expenseComponents = specificationId.components
      // 成本归属单传入订单的企业已付金额时，不处理费用明细的金额赋值
      const feeTypeForm = companyRealPayFromOrder
        ? { ...item, invoiceForm: undefined, amount: companyRealPayFromOrder }
        : { ...item, invoiceForm: undefined, amount: noWrittenOffAmount || item.amount }
      item.noWrittenOffAmount && delete item.noWrittenOffAmount
      consume.feeTypeForm = convertConsume(feeTypeForm, expenseComponents, 'detail', submitterId)
      return api
        .invoke('generate:DetailByAutoCalculate', { detailFormValue: consume, components: expenseComponents, isMeta: true })
        .then(newForm => {
          // 如果发现 specificationId 的 open 发生了变化是因为 generate:DetailByAutoCalculate 修改的
          consume.feeTypeForm = newForm
          consume.feeTypeForm.linkDetailEntities = linkDetailEntities
          return consume
        })
    })
  )
  return autoFeeDetails
}

/**
 * 明细未包含费类选择弹框提示
 * @param {*} list 
 * @returns 
 */
export async function formatNoFeeTypeList(list) {
  const noFeeTypeList = list.filter(v => v.noFeeType)
  if (noFeeTypeList.length) {
    const feeTypeId = await api.open('@bills:SelectFeeTypeModal', {
      importFeeList: list,
      text: i18n.get('您选择的部分申请事项，未包含消费明细，请选择这些申请事项要导入的费用类型')
    })
    const specifications = await api.invokeService('@bills:get:Specifications', [feeTypeId.expenseSpecificationId])
    const feeTypeCopy = cloneDeep(feeTypeId)
    feeTypeCopy.expenseSpecificationId = specifications.items[0]
    delete feeTypeCopy.feeType
    list.forEach(v => {
      if (v.noFeeType) {
        v.feeTypeId = cloneDeep(feeTypeCopy)
      }
    })
    return list
  }
  return list
}


function selectHasApportion() {
  return new Promise((resolve) => {
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get('导入申请单明细时，是否需要包含分摊明细？'),
      onOk: () => {
        resolve(true)
      },
      onCancel: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 关联明细费用
 */
export function fnAddUseMoney(data) {
  const moneys = data?.dataList.map((item) => item.modifyValue || item.amount)
  data.useTotalMoney = reduce(
    moneys,
    function (sum, n) {
      return new MoneyMath(sum).add(n).value
    },
    0
  )
  return data
}

/**
   * 导入费用明细所需要的操作
   * @param {*} selectedList 
   * isAbort 是否中断不可导入的数据，给出提示,默认是不导出该数据
   * false 则一路走下去(数据无则不导入)，true 的话中间有阻断则弹款提示
   * @returns 
   */
export async function copyExpenseLinkFeeDetail(selectedList, submitterId, isAbort = false) {
  if (!selectedList.length) return
  let expenseSpecIds = uniq(selectedList.map(line => line.feeTypeId.expenseSpecificationId))
  let specificationMap = {}

  let result = await api.invokeService('@bills:get:Specifications', expenseSpecIds)
  let items = result?.items || [], isNeedApportion = false;
  const { containApportion } = getDetailContainApportion(selectedList)

  items.forEach(line => (specificationMap[line.id] = line))

  // 申请可分摊之后首先校验每个费用明细的费用类别中申请明细所用的分摊方式是否在当前费用类型中报销模版中存在
  if (containApportion) {
    // 查看 申请分摊的分摊模版 在 报销分摊中有没有
    isNeedApportion = isAbort ? await selectHasApportion() : true
    if (isNeedApportion) {
      const { copy, spc } = hasCopy(selectedList, items)
      const feeName = get(spc, 'feeTypeId.name')
      if (!copy && isAbort) {
        return showModal.error({
          title: i18n.get('提示'),
          content: i18n.get('申请明细中的分摊方式在报销明细中不存在，无法导入', { feeName })
        })
      } else if (!copy && !isAbort) {
        return {
          autoFeeDetails: [],
          isApportionAbortCopy: true
        }
      }
    } else {
      selectedList = moveDetailApportion(selectedList) // 清除分摊数据
    }
  }

  const autoFeeDetails = await Promise.all(
    selectedList.map(line => {
      let expenseSpecificationId = line.feeTypeId.expenseSpecificationId
      let consume = {
        feeTypeId: line.feeTypeId,
        specificationId: specificationMap[expenseSpecificationId]
      }
      let expenseComponents = (specificationMap[expenseSpecificationId]).components
      consume.feeTypeForm = convertConsume(line.feeTypeForm, expenseComponents, 'detail', submitterId, ['invoiceForm'])
      return api
        .invoke('generate:DetailByAutoCalculate', { detailFormValue: consume, components: expenseComponents })
        .then(newForm => {
          // 如果发现 specificationId 的 open 发生了变化是因为 generate:DetailByAutoCalculate 修改的
          consume.feeTypeForm = newForm
          return consume
        })
    })
  )
  return {
    autoFeeDetails, //  自动计算后的明细数据
    isApportionAbortCopy: false // 明细分摊是否阻断导入
  }
}
