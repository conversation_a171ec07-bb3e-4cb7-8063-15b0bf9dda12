import { header, cellValue } from '../dynamic/SplitCalculation'
import Money from '../../elements//puppet/Money'
import React from 'react'



function renderText(value, line, index) {
  return value
}

function renderMoney(value, line, index) {
  return (
    <Money value={value} />
  )
}


export function formatSplitDetail(headers: header[], result: cellValue[], total): any {
  const columns = []
  const secondCols = []
  const dataSource = []
  const secondDatas = {}
  headers.forEach(el => {
    const { name, field, order, show } = el
    const renderFn = !!~['money', 'singleMoney'].indexOf(field) ? renderMoney : null
    if (el.type === 'MASTER') {
      columns.push({ title: name, dataIndex: field, key: field, order, render: renderFn })
    } else {
      secondCols.push({ title: name, dataIndex: field, key: field, order, render: renderFn })
    }
  })
  columns.sort((colPre, colNext) => colPre.order - colNext.order)
  secondCols.sort((colPre, colNext) => colPre.order - colNext.order)
  const col1Key = columns[0].key
  const col2Key = columns[1].key
  const travelersData = result[col1Key][0]
  const moneyData = result[col2Key][0]
  if (travelersData) {
    Object.keys(travelersData).forEach((id, index) => {
      dataSource.push({ id, [col1Key]: travelersData[id], [col2Key]: moneyData[id]})
    })
  }
  dataSource.sort((colPre, colNext) => colPre[col1Key].localeCompare(colNext[col1Key]))
  dataSource.forEach(item => {
    const colDatas = {}
    const tableData = []
    const colKeys = secondCols.map(el => el.key)
    secondCols.forEach(el => {
      const colData = result[el.dataIndex].find(el => el[item.id])
      if (colData) {
        colDatas[el.dataIndex] = colData[item.id]
      }
    })
    const cols = Object.values(colDatas)[0].length
    if (cols) {
      for (let i = 0; i < cols; i++) {
        const rowData = { id: `${item.id}_${i}`}
        colKeys.forEach(key => {
          colDatas[key] && (rowData[key] = colDatas[key][i])
        })
        tableData.push(rowData)
      }
    }
    item['secondTableData'] = tableData
  })
  
  
  return { dataSource, columns, secondCols }
}
