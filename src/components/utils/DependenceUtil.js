import { isObject } from '@ekuaibao/helpers'
import { app } from '@ekuaibao/whispered'

export function shouldUpdateValue(prevProps, nextProps, checkValue) {
  const { bus, field } = nextProps
  let nextValue = isObject(nextProps.value) ? nextProps.value.id : nextProps.value
  let prevValue = isObject(prevProps.value) ? prevProps.value.id : prevProps.value
  nextValue = nextValue || ''
  prevValue = prevValue || ''
  if (nextValue !== prevValue) {
    if (bus) {
      bus.emit('on:dependence:change', { key: field.name, id: nextValue })
    }
    if (checkValue && this.state.isDependence && nextProps.value && nextProps.value.needCheckValue) {
      const { dependenceList } = this.state
      this.filterLastChoice && this.filterLastChoice(dependenceList, prevProps.value, nextProps.value)
    }
  }
}

export async function handleDependence(params = {}) {
  const { key, dependence, id } = params
  if (id && dependence && dependence.length) {
    let isNeedFetch = dependence?.find(v => v.dependenceId === key)
    if (isNeedFetch) {
      return await app.invokeService('@bills:get:datalink:dependence:list', getDepedenceParam(params))
    }
  }
}

export async function handleAutoDependence(params = {}) {
  const { id } = params
  if (id) {
    return await app.invokeService('@bills:get:datalink:dependence:list', getDepedenceParam(params))
  }
}

export function getDepedenceParam(params = {}) {
  const { dependenceFeeType, dataType, recordSearch, dependenceCondition } = params
  return {
    recordSearch: recordSearch,
    entity: dataType?.entity,
    dependenceFeeType,
    type: 'TABLE',
    dependenceCondition
  }
}

export function checkIsEqualDependenceAndAutoDependence(dependence, autoDependence) {
  return (
    autoDependence?.length === dependence?.length &&
    autoDependence[0].dependenceId === dependence[0].dependenceId &&
    autoDependence[0].roleDefId === dependence[0].roleDefId &&
    autoDependence[0].direction === dependence[0].direction
  )
}
