/**
 * Created by <PERSON><PERSON> on 2018/1/24.
 */
import { MoneyMath } from '@ekuaibao/money-math'

export function fnUpdateApportionMoney(amount, apportions, totalApportionMoney) {
  let totalMoney = 0
  apportions.forEach((line, index) => {
    let { apportionForm } = line
    let { apportionMoney } = apportionForm
    let aPercent = 0
    let aMoney = 0
    if (index !== apportions.length - 1) {
      aMoney = new MoneyMath(apportionMoney).div(totalApportionMoney).times(amount).value
      aPercent = new MoneyMath(aMoney).div(amount).percent
      totalMoney = new MoneyMath(totalMoney).add(aMoney).value
    } else {
      aMoney = new MoneyMath(amount).minus(totalMoney).value
      aPercent = new MoneyMath(aMoney).div(amount).percent
    }
    apportionForm.apportionPercent = aPercent
    apportionForm.apportionMoney = aMoney
  })
  return apportions
}

export function fnUpdateApportionPercent(amount, apportions) {
  let totalPercent = 0
  apportions.forEach((line, index) => {
    let { apportionForm } = line
    let { apportionMoney } = apportionForm
    let apportionPercent = 0
    if (index !== apportions.length - 1) {
      apportionPercent = new MoneyMath(apportionMoney).div(amount).percent
      totalPercent = new MoneyMath(totalPercent).add(apportionPercent).value
    } else {
      apportionPercent = new MoneyMath(100).minus(totalPercent).value
    }
    apportionForm.apportionPercent = apportionPercent
  })
  return apportions
}
