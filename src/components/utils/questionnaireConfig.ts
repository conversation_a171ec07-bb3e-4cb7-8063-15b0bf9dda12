// @ts-ignore
import { QuestionnaireProps } from '@hose/eui/es/components/questionnaire'

type questionnaireType = {
  home: QuestionnaireProps
  submit: QuestionnaireProps
  approve: QuestionnaireProps
}

// 正式环境
let questionnaireConfig: questionnaireType = {
  home: {
    sid: '5086446804274176',
    channelId: '5096702956471296',
    width: '400px'
  },
  submit: {
    sid: '5098396723428352',
    channelId: '5132412918841344',
    width: '400px'
  },
  approve: {
    sid: '5086424377012224',
    channelId: '5132586402658304',
    width: '400px'
  }
}

// 开发环境不弹出 所以 channelId 设置一个永不弹出的问卷
if(process.env.NODE_ENV === 'development') {
  questionnaireConfig = {
    home: {
      sid: '5384969199904768',
      channelId: '5384973761210368',
      width: '400px'
    },
    submit: {
      sid: '5384969199904768',
      channelId: '5384973761210368',
      width: '400px'
    },
    approve: {
      sid: '5384969199904768',
      channelId: '5384973761210368',
      width: '400px'
    }
  }
}


export { questionnaireConfig }
