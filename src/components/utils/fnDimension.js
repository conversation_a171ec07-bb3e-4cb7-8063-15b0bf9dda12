/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/19 下午2:34.
 */
import { includes } from 'lodash'
import { app as api } from '@ekuaibao/whispered'

export function fnDimension() {
  let { value, submitterId, onChange, isModify } = this.props
  if (value && typeof value === 'object' && !isModify) {
    //如果是审批中修改单据，后台已经返回制单人档案值的可见性
    const visible = dimensionValueVisible(value, submitterId)

    if (!visible) {
      value = undefined
      onChange && onChange(value)
    }
  }
}

export function dimensionValueVisible(value = {}, submitterId) {
  if (!value) {
    return true
  }
  let visible = true
  const userInfo = api.getState()['@common'].userinfo
  if (!userInfo) {
    return value
  }
  const {
    staff: { departments: userDepts, roles: userRole, id }
  } = userInfo

  let { visibility = {} } = value
  let { staffs, roles, departments, fullVisible } = visibility

  if (submitterId && fullVisible === false) {
    staffs = staffs || []
    roles = roles || []
    departments = departments || []

    //现在档案可见性是按照制单人的权限判断的
    const userRoles = userRole ? userRole.values : []
    const inRoles = userRoles.filter(r => includes(roles, r.roleDefId)).length > 0
    const userDept = userDepts || []

    let departFullPath = userDepartmentPath(userDept)

    let inDepartment = departFullPath.filter(d => includes(departments, d.id)).length > 0

    let inStaffs = !!~staffs.indexOf(id)

    if (!inStaffs && !inDepartment && !inRoles) {
      visible = false
    }
  }
  return visible
}

function userDepartmentPath(userDepts) {
  let mapData = api.getState()['@common'].department.mapData
  let departFullPath = []
  userDepts.forEach(department => {
    const fn = parentId => {
      const d = mapData[parentId]
      if (d) {
        departFullPath.push(d)
      }
      if (d.parentId) {
        fn(d.parentId)
      }
    }
    departFullPath.push(department)
    department.parentId && fn(department.parentId)
  })
  return departFullPath
}
