/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/15 下午1:40.
 */
import { cloneDeep, get } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
import { uuid } from '@ekuaibao/helpers'
import Big from 'big.js'
import { reportNegativeApportionMoney } from '../../lib/flowPerformanceStatistics.ts'
/**
 * @description 日期转为时间戳
 * @param {分摊字段值} apportions
 * @returns
 */
export const formatApportionDateTime = apportions => {
  return apportions.map(item => {
    const apportionFormCopy = {}
    const {
      apportionForm,
      specificationId: { components }
    } = item
    Object.keys(apportionForm).forEach(cc => {
      const val = apportionForm[cc]
      const type = components?.find(oo => oo.field === cc)?.type
      if (type === 'date') {
        if (typeof val === 'number') {
          apportionFormCopy[cc] = val
        } else {
          apportionFormCopy[cc] = Date.parse(val)
        }
      } else {
        apportionFormCopy[cc] = val
      }
    })
    return {
      ...item,
      apportionForm: apportionFormCopy
    }
  })
}

export const recordApportionMoneyData = (apportions, props) => {
  let isNegativeAmount = false
  let type = 'apportionMoney'
  for (const item of apportions) {
    const { apportionMoney, apportionPercent } = item.apportionForm
    const moneyValue = parseFloat(apportionMoney?.standard)
    const percentValue = parseFloat(apportionPercent)

    if (moneyValue < 0 || parseFloat(percentValue) < 0) {
      if (parseFloat(percentValue) < 0) {
        type = 'percentValue'
      }
      isNegativeAmount = true
      break
    }
  }
  if (isNegativeAmount) {
    reportNegativeApportionMoney({
      type: type,
      feeTypeId: props?.feeType?.id,
      feeTypeName: props?.feeType?.name,
      feeDetailId: props?.feeDetailId,
      billSpecification: props?.billSpecification?.name,
    })
  }
}

/**
 * @description 日期转为时间戳
 * @param {分摊字段值} amortizes
 * @returns
 */
export const formatAmortizesDateTime = amortizes => {
  return amortizes.map(item => {
    const apportionFormCopy = {}
    const {
      amortizeForm,
      specificationId: { components }
    } = item
    Object.keys(amortizeForm).forEach(cc => {
      const val = amortizeForm[cc]
      const type = components?.find(oo => oo.field === cc)?.type
      if (type === 'date') {
        if (typeof val === 'number') {
          apportionFormCopy[cc] = val
        } else {
          apportionFormCopy[cc] = Date.parse(val)
        }
      } else if (type === 'dataRange' && val) {
        if (typeof val?.start === 'number') {
          apportionFormCopy[cc] = val
        } else {
          apportionFormCopy[cc] = { start: Date.parse(val?.start), end: Date.parse(val?.end) }
        }
      } else {
        apportionFormCopy[cc] = val
      }
    })
    return {
      ...item,
      amortizeForm: apportionFormCopy
    }
  })
}

function getOtherMoneyFelid (feeTypeForm){
  const other = 'otherApportionMoney_'; 
  const felids = []
  const otherApportionMoney = []

  Object.keys(detail.feeTypeForm).forEach(v=>{
    if(v.includes(other)){
      felids.push(v.split(other)[1])
      otherApportionMoney.push(v)
    }
  })
  return {
    felids:felids,
    otherApportionMoney:otherApportionMoney
  }
}

export function fnDetailsApportion(checkedDetails = [], data = []) {
  const other = 'otherApportionMoney_';  // 其他金额字段
  const {apportionMoneyField,otherApportionMoneyFields = []} = getApportionSpecification(data)
  checkedDetails.forEach(detail => {
    let amount = detail.feeTypeForm[apportionMoneyField]
    let apportionData = cloneDeep(formatApportionDateTime(data))
    let totalPercent = 0
    let totalAmount = 0
    let remainForeignMoney = amount?.foreign || 0 // 结余外币
    apportionData.forEach((line, index) => {
      let {
        apportionForm: { apportionPercent }
      } = line

      // 重新生成分摊id
      line.apportionForm['apportionId'] = 'ID_' + uuid(11)
      
      if (index === apportionData.length - 1) {
        let percent = new MoneyMath(100).minus(totalPercent).fixedValue
        let apportionMoney = new MoneyMath(amount).minus(totalAmount).fixedValue
        if(amount?.foreignStrCode){
          apportionMoney['foreign'] = new Big(remainForeignMoney).toFixed(Number(amount?.foreignScale)) 
        }
        line.apportionForm.apportionMoney = apportionMoney
        line.apportionForm.apportionPercent = percent
        otherApportionMoneyFields.forEach(item=>{
          const money = detail.feeTypeForm[item] //  其他金额字段用的是百分比计算
          line.apportionForm[other+item] = new MoneyMath(money).times(apportionPercent).div(100).fixedValue
        })
      } else {
        line.apportionForm.apportionMoney = new MoneyMath(amount).times(apportionPercent).div(100).fixedValue
        totalPercent = new MoneyMath(totalPercent).add(apportionPercent).value
        totalAmount = new MoneyMath(totalAmount).add(line.apportionForm.apportionMoney).value
        otherApportionMoneyFields.forEach(item=>{
          const money = detail.feeTypeForm[item]
          line.apportionForm[other+item] = new MoneyMath(money).times(apportionPercent).div(100).fixedValue
        })
        if(amount?.foreignStrCode){
          remainForeignMoney = new Big(remainForeignMoney).minus(Number(line.apportionForm.apportionMoney?.foreign))
        }
      }
    })

    detail.feeTypeForm.apportions = apportionData
  })
}
function getApportionSpecification(data = []) {
  const spcConfig = get(data[0], 'specificationId.configs', [])
  const { apportionMoneyField, otherApportionMoneyFields } = spcConfig.find(v => v.ability === 'apportion') || {}
  return {
    apportionMoneyField,
    otherApportionMoneyFields:otherApportionMoneyFields || []
  }
}
