import React from 'react'
import { Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
const datalink = new Resource('/api/v1/datalink')
const Money = api.require<any>('@elements/puppet/Money')

export const getBudgetAdjustDetails = param => {
  return datalink.GET('/byDataLinkId/[ids]', param)
}

export const getBudgetAdjustColumns = entityId => {
  return [
    {
      title: i18n.get('预算节点'),
      dataIndex: `form.E_${entityId}_name`,
      key: `form.E_${entityId}_name`,
      width: 200,
      render: text => {
        return text ? (
          <div className="overflow-ellipsis-line-3">
            <Tooltip title={text}>{text}</Tooltip>
          </div>
        ) : (
          '-'
        )
      }
    },
    {
      title: i18n.get('预算周期'),
      dataIndex: `form.E_${entityId}_periodTimeName`,
      key: `form.E_${entityId}_periodTimeName`,
      width: 130
    },
    {
      title: i18n.get('调整前金额'),
      dataIndex: `form.E_${entityId}_beforeBudgetMoney`,
      key: `form.E_${entityId}_beforeBudgetMoney`,
      width: 120,
      render(text) {
        return text?.standard ? <Money value={text} /> : '-'
      }
    },
    {
      title: i18n.get('调整后金额'),
      dataIndex: `form.E_${entityId}_afterBudgetMoney`,
      key: `form.E_${entityId}_afterBudgetMoney`,
      width: 120,
      render(text) {
        return text?.standard ? <Money value={text} /> : '-'
      }
    },
    {
      title: i18n.get('差额'),
      dataIndex: `form.E_${entityId}_difference`,
      key: `form.E_${entityId}_difference`,
      width: 120,
      render(text) {
        return text?.standard ? <Money value={text} /> : '-'
      }
    },
    {
      title: i18n.get('预算余额'),
      dataIndex: `form.E_${entityId}_balance`,
      key: `form.E_${entityId}_balance`,
      width: 120,
      render(text) {
        return text?.standard ? <Money value={text} /> : '-'
      }
    }
  ]
}
