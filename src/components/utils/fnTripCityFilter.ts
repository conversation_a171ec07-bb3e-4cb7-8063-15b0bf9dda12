// @ts-ignore
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'

interface OtherProps extends StringAnyProps {
  airportCode?: string[]
  trainCode?: string[]
  cityExtendCode?: string
}

interface CityProps extends StringAnyProps {
  extendInfo?: OtherProps
}

type FunctionType = (city: CityProps) => boolean

export function filterCity(tripTypeId: StringAnyProps): FunctionType {
  const name = getNodeValueByPath(tripTypeId, 'name')
  return (city: CityProps): boolean => {
    const isYeego = api.getState('@common').powers.YEEGO
    if (!isJJH(name) || !isYeego) {
      return true
    }
    if (!city) {
      return false
    }
    const extendInfo = getNodeValueByPath(city, 'extendInfo', {})
    const { airportCode = [], trainCode = [], cityExtendCode = '' } = extendInfo
    if (name === '飞机') { // @i18n-ignore
      return !!airportCode && !!airportCode.length
    }

    if (name === '酒店') {// @i18n-ignore
      return !!cityExtendCode
    }

    if (name === '火车') {// @i18n-ignore
      return !!trainCode && !!trainCode.length
    }
    return true
  }
}

export function isJJH(name = '') {
  return !!~[i18n.get('飞机'), i18n.get('酒店'), i18n.get('火车')].indexOf(name)
}
