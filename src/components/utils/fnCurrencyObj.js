/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/12/6 下午3:07.
 */
import { standardValueMoney, initializeCurrencyData } from '../../lib/misc'
import Big from 'big.js'
import { app as api } from '@ekuaibao/whispered'
import { get, isEqual as _isEqual, cloneDeep, isObject } from 'lodash'
import { getBillTemplateAddFeeReceivingAmount } from '../../lib/fee-util'
import { resetForeignInvoiceForEntity } from '../../lib/InvoiceUtil'
const MaxAccuracy = 8

export function initValue(value, currency) {
  if (typeof value === 'object' && isNaN(value)) {
    if (isFixValue(value)) return standardValueMoney(value.standard)
    return value
  }
  let result = standardValueMoney(value)
  if (currency) {
    const constantForeignMoney = fnShouldForeignCurrencyUnchangedWhenSwitch()
    result = updateValueCurrency(result, currency, constantForeignMoney)
  }
  return result
}

export function initDimentionCurrencyValue(value, dimentionCurrency) {
  if (typeof value === 'object' && isNaN(value)) {
    if (isFixValue(value)) return standardValueMoney(value.standard, dimentionCurrency)
    return value
  }
  let result = standardValueMoney(result, dimentionCurrency)
  return result
}
export function updateValueCurrency(value, currency, constantForeignMoney) {
  // currency as foreignCurrency
  let result = { ...value }
  const { strCode, numCode, symbol, unit, scale, rate } = currency

  if (value?.standardStrCode === strCode) {
    if (constantForeignMoney) result.standard = result.foreign
    delete result.foreign
    delete result.foreignNumCode
    delete result.foreignScale
    delete result.foreignStrCode
    delete result.foreignSymbol
    delete result.foreignUnit
    delete result.rate
    delete result.sysRate
  } else if (hasForeign(result) || result?.foreignStrCode !== strCode) {
    result.foreignStrCode = strCode
    result.foreignNumCode = numCode
    result.foreignSymbol = symbol
    result.foreignUnit = unit
    result.foreignScale = scale
    result.rate = rate
    result.sysRate = rate
    !value?.foreign && (result.foreign = '')
    if (constantForeignMoney && calculateAble([result?.standard, rate, result?.standardScale])) {
      // 原金额没有外币时，将本位币赋值给外币；
      // 原金额有外币时，外币金额不变；
      if (!value?.foreignStrCode) result.foreign = Number(result.standard).toFixed(Number(scale))
      result.standard = new Big(result?.foreign).times(rate).toFixed(Number(result?.standardScale))
    } else if (calculateAble([result?.standard, rate, scale])) {
      result.foreign = new Big(result?.standard).div(rate).toFixed(Number(scale))
    }
  }
  if (
    constantForeignMoney &&
    result?.budget &&
    calculateAble([result?.budget, result?.budgetRate, result?.budgetScale])
  ) {
    result.budget = new Big(result.standard).div(result.budgetRate).toFixed(result.budgetScale)
  }

  return result
}

export function updateValueMoney({
  value,
  money,
  prefix,
  isSelectedAutoRate = false,
  isSelectedAutoBudgetRate = false,
  currency
}) {
  let result = { ...value }
  const { rate, standardScale, foreignScale, foreign, budgetRate, budgetScale } = result
  if (prefix === 'foreign') {
    result.foreign = money
    if (!isSelectedAutoRate && calculateAble([money, rate, standardScale])) {
      result.standard = new Big(money).times(rate).toFixed(Number(standardScale))
      if (hasBudget(result)) {
        if (calculateAble([result.standard, budgetRate, budgetScale])) {
          result.budget = new Big(result.standard).div(budgetRate).toFixed(Number(budgetScale))
        }
      }
    } else if (money === '') {
      result.standard = ''
      if (hasBudget(result)) {
        result.budget = ''
      }
    }
    if (isSelectedAutoRate && calculateAble([result.standard, result.foreign])) {
      result.rate = new Big(result.standard).div(result.foreign).toFixed(MaxAccuracy || 8)
    }
    if (isSelectedAutoBudgetRate && calculateAble([result.standard, result.budget])) {
      result.budgetRate = new Big(result.standard).div(result.budget).toFixed(MaxAccuracy || 8)
    }
    return result
  }
  if (prefix === 'budget') {
    result.budget = money
    if (!isSelectedAutoBudgetRate && calculateAble([money, budgetRate, standardScale])) {
      result.standard = new Big(money).times(budgetRate).toFixed(Number(standardScale))
      if (hasForeign(result)) {
        result.foreign = foreign
        if (!isSelectedAutoRate && calculateAble([result.standard, rate, foreignScale]) && Number(rate) !== 0) {
          result.foreign = new Big(result.standard).div(rate).toFixed(Number(foreignScale))
        }
      }
    } else if (money === '') {
      result.standard = ''
      if (hasForeign(result)) {
        result.foreign = ''
      }
    }
    if (isSelectedAutoBudgetRate && calculateAble([result.standard, result.budget])) {
      result.budgetRate = new Big(result.standard).div(result.budget).toFixed(MaxAccuracy || 8)
    }
    if (isSelectedAutoRate && calculateAble([result.standard, result.foreign])) {
      result.rate = new Big(result.standard).div(result.foreign).toFixed(MaxAccuracy || 8)
    }
    return result
  }
  result.standard = money
  if (hasBudget(result)) {
    if (!isSelectedAutoBudgetRate) {
      if (calculateAble([result.standard, budgetRate, budgetScale])) {
        result.budget = new Big(result.standard).div(budgetRate).toFixed(Number(budgetScale))
      } else if (money === '') {
        result.budget = ''
      }
    } else if (isSelectedAutoBudgetRate && calculateAble([result.standard, result.budget])) {
      result.budgetRate = new Big(result.standard).div(result.budget).toFixed(MaxAccuracy || 8)
    }
  }
  if (hasForeign(result)) {
    result.foreign = foreign
    if (!isSelectedAutoRate) {
      if (calculateAble([money, rate, foreignScale])) {
        result.foreign = new Big(money).div(rate).toFixed(Number(foreignScale))
      } else if (money === '') {
        result.foreign = ''
      }
    } else if (isSelectedAutoRate && calculateAble([result.standard, result.foreign])) {
      result.rate = new Big(result.standard).div(result.foreign).toFixed(MaxAccuracy || 8)
    }
  }
  if (isFixValue(result)) {
    result = standardValueMoney(result.standard, currency)
  }
  return result
}

export function updateReceivingAmountMoney({ prefix, nextValue, preValue, currency, receivingCurrency }) {
  let moneyValue = null
  let formatAmount = {} // 重置后的金额字段
  const allCurrencyRates = api.getState()['@bills']?.billsRateList || []

  if (!currency) {
    return standardValueMoney(0)
  }

  if (nextValue?.standardNumCode === currency.numCode) {
    // 如果下一个值的本位币代码与当前币种代码相同，直接赋值
    moneyValue = nextValue
  } else if (prefix === 'amount') {
    const { standardCurrency } = getBillAllCurrencyRatesInfo(currency.numCode)
    // 如果前缀是'amount'，计算收款金额
    moneyValue = computerReceivingAmount(nextValue, currency?.numCode)

    if (moneyValue?.foreignNumCode === moneyValue?.standardNumCode) {
      // 如果外币代码与本位币代码相同，标准化金额
      moneyValue = standardValueMoney(moneyValue.standard, currency)
    } else if (!moneyValue?.foreignNumCode && !nextValue?.foreignNumCode) {
      // 没有外币时，初始化货币数据
      moneyValue = initializeCurrencyData(standardCurrency, currency, moneyValue) // 拿外币信息数据
      moneyValue = { ...moneyValue, foreign: nextValue.standard } //  外币直接赋值
    }
  } else if (prefix === 'receivingAmount') {
    const receivingObj = allCurrencyRates.find(v => v.numCode === (nextValue?.foreignNumCode || receivingCurrency))
    const amountFixValue = { rate: receivingObj.rate, sysRate: receivingObj.rate }
    // 为空的时候给默认值
    if (!nextValue) {
      nextValue = initializeCurrencyData({}, receivingObj, preValue)
    }
    if (nextValue?.foreignNumCode && nextValue.standardNumCode) {
      // 如果有外币且本位币代码存在
      if (nextValue?.foreignNumCode == currency?.numCode) {
        // 外币代码与当前币种代码相同且之前没有外币代码
        moneyValue = standardValueMoney(nextValue?.foreign, currency, amountFixValue)
      } else {
        // 没有外币时，初始化外币数据
        if (nextValue?.foreignNumCode !== currency?.numCode && preValue?.foreignNumCode !== nextValue?.foreignNumCode) {
          const foreign = allCurrencyRates.find(v => v.numCode === nextValue?.foreignNumCode)
          formatAmount = initializeCurrencyData(foreign, currency, amountFixValue) // 收款金额外币转换成原币
        } else if (!preValue?.foreignNumCode) {
          formatAmount = initializeCurrencyData({}, receivingObj, amountFixValue)
        }
        // 更新外币金额
        moneyValue = updateValueMoney({
          value: {
            ...preValue,
            ...formatAmount
          },
          money: nextValue?.foreign,
          prefix: 'foreign'
        })
      }
    } else if (!nextValue?.foreignNumCode) {
      // 如果没有外币代码
      const foreignObj = allCurrencyRates.find(v => v.numCode === nextValue.standardNumCode)
      moneyValue = initializeCurrencyData(foreignObj, currency, amountFixValue) // 初始化外币数据
      if (nextValue.standard) {
        // 更新外币金额
        moneyValue = updateValueMoney({
          value: moneyValue,
          money: nextValue.standard,
          prefix: 'foreign'
        })
      }
    } else if (nextValue?.foreignNumCode === currency?.numCode) {
      // 如果前缀是'receiving'且外币代码与当前币种代码相同
      moneyValue = standardValueMoney(moneyValue?.foreign ?? nextValue.foreign, currency)
    }
  }

  return moneyValue
}

function isFixValue(value) {
  const keys = Object.keys(value)

  if (!keys.includes('foreign')) {
    if (keys.length === 1) {
      return true
    }
    keys
      .filter(key => !!key)
      .forEach(key => {
        if (value[key] === 'undefined') {
          return true
        }
      })
  }
  return false
}

export function updateValueRate(value, rate, unchangeCurrency, rateType, isUpdateSysRate) {
  let result = { ...value }
  const { standard, standardScale, foreign, foreignScale, budgetRate, budgetScale } = result
  if (unchangeCurrency === 'standard') {
    if (rateType === 'foreign') {
      result.rate = rate
      if (calculateAble([standard, result.rate, foreignScale]) && Number(rate) !== 0) {
        result.foreign = new Big(standard).div(result.rate).toFixed(Number(foreignScale))
      }
    } else if (rateType === 'budget') {
      result.budgetRate = rate
      if (calculateAble([result.standard, result.budgetRate, budgetScale]) && Number(result.budgetRate) !== 0) {
        result.budget = new Big(result.standard).div(result.budgetRate).toFixed(Number(budgetScale))
      }
    }
  }
  if (unchangeCurrency === 'foreign') {
    if (rateType === 'foreign') {
      result.rate = rate
      if (calculateAble([foreign, result.rate, standardScale]) && Number(rate) !== 0) {
        result.standard = new Big(foreign).times(result.rate).toFixed(Number(standardScale))
        if (calculateAble([result.standard, result.budgetRate, budgetScale]) && Number(result.budgetRate) !== 0) {
          result.budget = new Big(result.standard).div(result.budgetRate).toFixed(Number(budgetScale))
        }
      }
    } else if (rateType === 'budget') {
      result.budgetRate = rate
      if (calculateAble([result.standard, result.budgetRate, budgetScale]) && Number(result.budgetRate) !== 0) {
        result.budget = new Big(result.standard).div(result.budgetRate).toFixed(Number(budgetScale))
      }
    }
  }
  if (unchangeCurrency === 'budget') {
    if (rateType === 'foreign') {
      result.rate = rate
      if (calculateAble([result.standard, result.rate, foreignScale]) && Number(result.rate) !== 0) {
        result.foreign = new Big(result.standard).div(result.rate).toFixed(Number(foreignScale))
      }
    } else if (rateType === 'budget') {
      result.budgetRate = rate
      if (calculateAble([result.budget, result.budgetRate, standardScale])) {
        result.standard = new Big(result.budget).times(result.budgetRate).toFixed(Number(standardScale))
        if (calculateAble([result.standard, result.rate, foreignScale]) && Number(result.rate) !== 0) {
          result.foreign = new Big(result.standard).div(result.rate).toFixed(Number(foreignScale))
        }
      }
    }
  }
  if (isUpdateSysRate && result?.sysRate) {
    result.sysRate = rate
  }
  return result
}

export function formartMoney(value) {
  const result = { ...value }
  const { standard, foreign, standardScale, foreignScale } = result
  if (calculateAble([standard, standardScale])) {
    result.standard = new Big(standard).toFixed(Number(standardScale))
  }
  if (hasForeign(result) && calculateAble([foreign, foreignScale])) {
    result.foreign = new Big(foreign).toFixed(Number(foreignScale))
  }
  return result
}

export function calculateAble(iterms = []) {
  let result = true
  iterms.forEach(e => {
    if (isNaN(e) || e === '' || e === null || e === undefined) result = false
  })
  return result
}

export function format0MoneyFrom(amount) {
  if (!amount) return standardValueMoney(amount)
  const result = { ...amount }
  result.standard = '0.00'
  if (hasForeign(result)) {
    result.foreign = '0.00'
  }
  return result
}

export function isMoneyObject(obj) {
  return obj && typeof obj === 'object'
}

export function hasForeign(value) {
  return !!value && !!value.foreignStrCode
}

export function hasBudget(value) {
  return !!value && !!value.budgetStrCode
}

export const BudgetStrCode = 'CNY'

export function updateCurrencyValueByDimention(value, currency, rates = [], isChangeDimension, others = {}) {
  if (!value) {
    return value
  }
  const { strCode, numCode, symbol, unit, scale } = currency
  let result = { ...value }
  result.standardNumCode = numCode
  result.standardScale = scale
  result.standardStrCode = strCode
  result.standardSymbol = symbol
  result.standardUnit = unit
  const useOriginForeign = others?.useOriginForeign
  const useSystemRate = others?.useSystemRate
  const constantForeignMoney = fnShouldForeignCurrencyUnchangedWhenSwitch()
  if (hasForeign(value)) {
    // 判断法人实体多币种切换本位币后，外币列表中是否包含原金额中的外币币种
    const rate = rates.find(rate => rate.strCode === value.foreignStrCode)
    if (rate) {
      let computeRate = value?.rate || rate.rate
      if (useSystemRate) {
        computeRate = rate.rate
        result.rate = rate.rate
      }
      if (value.standard !== undefined) {
        if (constantForeignMoney) {
          result.rate = result.rate || rate.rate
          result.standard = new Big(value.foreign).times(result.rate).toFixed(rate.scale)
        } else {
          const originForeign = value?.foreign
          let nowForeign = new Big(value.standard).div(computeRate).toFixed(rate.scale)
          if (useOriginForeign && originForeign !== undefined && nowForeign !== originForeign) {
            nowForeign = originForeign
          }
          result.foreign = nowForeign
        }
      }
      result.foreignNumCode = rate.numCode
      result.foreignScale = rate.scale
      result.foreignStrCode = rate.strCode
      result.foreignSymbol = rate.symbol
      result.foreignUnit = rate.unit
    } else {
      if (constantForeignMoney) result.standard = value.foreign
      delete result.foreign
      delete result.foreignNumCode
      delete result.foreignScale
      delete result.foreignStrCode
      delete result.foreignSymbol
      delete result.foreignUnit
    }
  }
  if (result.standardStrCode !== BudgetStrCode) {
    const budgetCurrencyValue = initBudgetCurrentInfo(value, rates)
    result = { ...result, ...budgetCurrencyValue }
  } else if (hasBudget(value) && result.standardStrCode === BudgetStrCode) {
    delete result.budget
    delete result.budgetNumCode
    delete result.budgetScale
    delete result.budgetStrCode
    delete result.budgetSymbol
    delete result.budgetUnit
    delete result.budgetRate
  }
  return result
}

export const initBudgetCurrentInfo = (value, rates = []) => {
  const result = {}
  const budgetCurrency = rates.find(rate => rate.strCode === BudgetStrCode)
  if (budgetCurrency && budgetCurrency.budgetRate && Number(budgetCurrency.budgetRate)) {
    result.budgetRate = budgetCurrency.budgetRate
    result.budgetNumCode = budgetCurrency.numCode
    result.budgetScale = budgetCurrency.scale
    result.budgetStrCode = budgetCurrency.strCode
    result.budgetSymbol = budgetCurrency.symbol
    result.budgetUnit = budgetCurrency.unit
    if (value && value.standard !== undefined && result.budgetRate) {
      result.budget = new Big(value.standard).div(result.budgetRate).toFixed(budgetCurrency.scale)
    } else {
      delete result.budget
    }
  } else {
    delete result.budget
    delete result.budgetNumCode
    delete result.budgetScale
    delete result.budgetStrCode
    delete result.budgetSymbol
    delete result.budgetUnit
    delete result.budgetRate
  }
  return result
}

export function updateDetailsMoneyValue(
  details = [],
  currency,
  rates = [],
  others = {},
  allowSelectionReceivingCurrency
) {
  return details.map(d => {
    const { specificationId } = d
    let { feeTypeForm } = d
    specificationId.components = getBillTemplateAddFeeReceivingAmount(
      specificationId.components,
      allowSelectionReceivingCurrency
    )
    const moneyComponents = specificationId.components.filter(cp => cp.type === 'money')
    moneyComponents.forEach(cp => {
      const value = feeTypeForm[cp.field]
      const amountField = feeTypeForm['amount']
      if (!!value) {
        let newValue = updateCurrencyValueByDimention(value, currency, rates, undefined, others)
        if (cp.field === 'receivingAmount') {
          const allRates = [...rates, { ...currency, rate: 1 }]
          const standardObj = allRates?.find(el => el.numCode === value?.standardNumCode)
          const foreigObj = allRates?.find(el => el.numCode === value?.foreignNumCode)
          // 法人实体以本位币为准，计算
          if (amountField.standardNumCode === value.standardNumCode || !standardObj || !foreigObj) {
            newValue = amountField
          } else if (value?.foreignNumCode) {
            // 如果收款币种存在外币，那就以本位币再算一次外币
            const rate = getExchangeRateDifferential(currency.numCode, value.standardNumCode, allRates)
            if (rate) {
              newValue = standardMoneyForReceivingAmount(value, currency.numCode, rate)
            }
          }
        }
        feeTypeForm[cp.field] = newValue
      }
    })
    feeTypeForm = resetForeignInvoiceForEntity(feeTypeForm, currency, rates)
    if (feeTypeForm?.apportions?.length) {
      feeTypeForm.apportions.forEach(apportion => {
        apportion.apportionForm.apportionMoney = updateCurrencyValueByDimention(
          apportion.apportionForm.apportionMoney,
          currency,
          rates,
          others
        )
      })
      feeTypeForm.apportions = feeTypeForm.apportions.slice()
    }
    return d
  })
}

/**
 * 获取boolean值：币种和汇率切换时，外币金额是否需要重算
 * 取币种设置中的字段currencyUnchangedWhenSwitch
 * OCCUPY_MONEY: 切换币种时，本位币金额保持不变
 * ORIGINAL_AMOUNT: 切换币种时，原币金额保持不变
 * @returns {boolean}
 */
const fnShouldForeignCurrencyUnchangedWhenSwitch = () => {
  const currencyConfig = api.getState()['@common']?.currencyConfig || {}
  return get(currencyConfig, 'currencyUnchangedWhenSwitch', 'OCCUPY_MONEY') === 'ORIGINAL_AMOUNT'
}

export function foreignTimesRate(value, numCode, rates) {
  let newValue = {}
  const { standardCurrency } = getBillAllCurrencyRatesInfo()
  rates = [...rates, standardCurrency]
  const rateValue = getExchangeRateDifferential(numCode, value?.foreignNumCode, rates)
  if (calculateAble([value?.foreign, rateValue, value?.foreignScale])) {
    newValue['standard'] = new Big(value?.foreign).times(rateValue).toFixed(value?.standardScale)
  }

  return {
    ...value,
    rate: rateValue,
    sysRate: rateValue,
    ...newValue
  }
}

/**
 * 计算收款金额
 * @param {*} value 被计算的金额
 * @param {*} numCode 收款币种
 * @param {*} rateList 汇率列表
 * @returns 收款币种金额
 */
export function computerReceivingAmount(value, numCode) {
  let receivingAmount = null
  let newValue = {}
  if (!value) return value
  const billsRateList = api.getState()['@bills']?.billsRateList
  const rateList = billsRateList ?? dimentionCurrencyInfo?.rates ?? []

  const currentRate = rateList.find(v => v.numCode === value?.foreignNumCode)
  const targetRate = rateList.find(v => v.numCode === numCode)
  if (value?.standardNumCode !== numCode && targetRate?.rate) {
    let rateValue = targetRate.rate
    // 本位币，无外币情况
    if (!value?.foreignNumCode) {
      const rate = getExchangeRateDifferential(numCode, value?.standardNumCode)
      if (calculateAble([value.standard, rate])) {
        receivingAmount = new Big(value.standard).times(rate).toFixed(targetRate.scale)
      }
      newValue = standardValueMoney(receivingAmount, targetRate)
      newValue['rate'] = rate
      newValue['sysRate'] = rate
      // 有外币的情况
    } else if (currentRate && targetRate) {
      rateValue = getExchangeRateDifferential(numCode, value?.foreignNumCode) // 命名需优化
      if (calculateAble([value?.foreign, rateValue, value?.foreignScale])) {
        receivingAmount = new Big(value?.foreign).times(rateValue).toFixed(targetRate?.scale)
      }
      newValue = standardValueMoney(receivingAmount, targetRate)
      newValue['rate'] = rateValue // 无外币信息,则是本位币
      newValue['sysRate'] = rateValue
    }
  }

  return {
    ...value,
    ...newValue
  }
}

// numCode 当前汇率
// targetNumCode 目标汇率 targetNumCode / numCode
export function getExchangeRateDifferential(numCode, targetNumCode, ratesList) {
  // 找到当前原币的汇率与本位币之间的汇率算出本位币等于多少
  // 计算的币种与本位币之间的汇率算出本位币等于多少
  const billsRateList = api.getState()['@bills']?.billsRateList
  const rateList = ratesList ?? billsRateList ?? []
  const currencyConfig = api.getState()['@common']?.currencyConfig || {} // 参考汇率

  if (numCode === targetNumCode) {
    return 1
  }

  // 参考汇率
  if (!currencyConfig?.allowReferenceRate) {
    return null
  }

  const currentRate = rateList.find(rate => rate.numCode === numCode)
  const targetRate = rateList.find(rate => rate.numCode === targetNumCode)
  if (!currentRate?.rate || !targetRate?.rate) {
    return null
  }
  // 目标汇率/当前汇率 = 汇率差额
  const result = targetRate.rate / currentRate.rate
  return parseFloat(new Big(result || 1).toFixed(32))
}

/**
 * 收款金额数据
 * feeAmount，收款币种, 汇率rate
 * @returns
 */
export const standardMoneyForReceivingAmount = (feeAmount, receivingCurrencyNum, rate) => {
  const allCurrencyRates = api.getState()['@common']?.allCurrencyRates
  const billsRateList = api.getState()['@bills']?.billsRateList // 汇率列表
  const receivingCurrency = (billsRateList || allCurrencyRates)?.find(el => el.numCode === receivingCurrencyNum)
  // 费用金额有原币
  if (feeAmount?.foreignNumCode) {
    let standard = undefined // 收款币种
    let rateCopy = rate || receivingCurrency?.rate

    if (calculateAble([rateCopy, feeAmount?.foreign])) {
      standard = new Big(feeAmount.foreign).times(rateCopy).toFixed(receivingCurrency?.scale)
    }
    return {
      ...feeAmount,
      standard,
      standardStrCode: receivingCurrency?.strCode,
      standardNumCode: receivingCurrency?.numCode,
      standardSymbol: receivingCurrency?.symbol,
      standardUnit: receivingCurrency?.unit,
      standardScale: receivingCurrency?.scale,
      rate: rateCopy,
      sysRate: rateCopy
    }
  } else {
    return standardValueMoney(feeAmount?.standard, receivingCurrency)
  }
}

export const getBillAllCurrencyRatesInfo = receivingCurrencyNum => {
  const dimentionCurrencyInfo = api.getState()['@bills']?.dimentionCurrencyInfo
  const standardCurrency = api.getState()['@common']?.standardCurrency
  const billsRateList = api.getState()['@bills']?.billsRateList // 汇率列表
  const receivingCurrency = billsRateList?.find(el => el.numCode === receivingCurrencyNum)
  const currency = dimentionCurrencyInfo?.currency ?? standardCurrency // 当前单据的本位币
  const rates = billsRateList.length ? billsRateList : dimentionCurrencyInfo?.rates

  return {
    standardCurrency: { ...currency, rate: currency?.rate || 1 },
    receivingCurrency,
    rates: rates
  }
}

// 币种切换更新明细数据
export function updateDetailsReceivingAmountValue(details = [], currency, isAddReceivingTemplate) {
  return details.map(d => {
    const { specificationId, feeTypeForm } = d
    let error = ''
    if (isAddReceivingTemplate) {
      specificationId.components = getBillTemplateAddFeeReceivingAmount(specificationId.components, true)
    }
    const moneyComponents = specificationId.components.filter(cp => cp.type === 'money')
    moneyComponents.map(cp => {
      let value = feeTypeForm[cp.field]
      const amountField = feeTypeForm['amount']
      const { standardCurrency } = getBillAllCurrencyRatesInfo(currency?.numCode)

      if (cp.field === 'receivingAmount' && amountField?.standard && currency) {
        let newReceivingValue = null
        if (amountField.standardNumCode == currency.numCode) {
          newReceivingValue = amountField
        } else if (value?.foreign) {
          // 切换的code 与当前的币种一致
          if (value?.foreignNumCode === currency.numCode) {
            newReceivingValue = standardValueMoney(value.foreign, currency)
          } else {
            // 外币计算逻辑
            const rate = getExchangeRateDifferential(currency.numCode, value.foreignNumCode)
            if (rate) {
              newReceivingValue = standardMoneyForReceivingAmount(value, currency.numCode, rate)
            } else {
              error = i18n.get('收款金额汇率为空，请检查')
              newReceivingValue = initializeCurrencyData(currency, {}, { ...value, foreign: undefined, rate: null })
            }
          }
        } else {
          // 无外币，但费用金额有外币，外币金额算收款金额
          if (amountField?.foreignNumCode) {
            const rate = getExchangeRateDifferential(currency.numCode, amountField.foreignNumCode)
            if (rate) {
              newReceivingValue = standardMoneyForReceivingAmount(amountField, currency.numCode, rate)
            } else {
              error = i18n.get('收款金额汇率为空，请检查')
              newReceivingValue = initializeCurrencyData({}, currency, {
                ...amountField,
                standard: undefined,
                rate: null
              })
            }
          } else {
            // 费用金额本位币变成外币，汇率计算（场景：两币为本位币切换外币收款）
            newReceivingValue = initializeCurrencyData(standardCurrency, currency, {
              foreign: value?.standard || amountField?.standard
            })
            const rate = getExchangeRateDifferential(currency.numCode, amountField.standardNumCode)
            if (rate) {
              newReceivingValue = standardMoneyForReceivingAmount(newReceivingValue, currency.numCode, rate)
            } else {
              error = i18n.get('收款金额汇率为空，请检查')
              newReceivingValue = initializeCurrencyData(standardCurrency, currency, {
                foreign: amountField.standard,
                rate: null
              })
            }
          }
        }

        if (newReceivingValue) {
          feeTypeForm[cp.field] = newReceivingValue
        }
      }
    })
    if (error) {
      d['errorMsg'] = {
        completed: d?.errorMsg?.completed || error,
        isCheckCalAttr: true
      }
    }
    return d
  })
}

// 费用明细金额展示
export function moneyReceivingAmountDisplay(valueA, receivingAmount) {
  let isShowStandard = true
  let isShowForeign = false
  let isShowReceivingAmount = false
  const standardEqual = _isEqual(valueA?.standardNumCode, receivingAmount?.standardNumCode)
  const foreignEqual = _isEqual(valueA?.standardNumCode, receivingAmount?.foreignNumCode)
  const receivingAmountStandardEqual = _isEqual(valueA?.foreignNumCode, receivingAmount?.standardNumCode)
  if (!receivingAmount) {
    isShowStandard = false
  }
  // 收款币有外币就展示外币并且当前本位币不等于本位币
  if (
    (isShowStandard && receivingAmount?.foreign && valueA?.foreign) ||
    (valueA?.foreign && receivingAmountStandardEqual)
  ) {
    isShowForeign = true
  }
  // 折合本位币展示 三币不相等 或者 valueA 和收款原币相等
  if (
    (isShowStandard && receivingAmount?.foreign && !standardEqual && !foreignEqual) ||
    (receivingAmount?.foreign && foreignEqual)
  ) {
    isShowReceivingAmount = true
  }
  return {
    isShowStandard,
    isShowForeign,
    isShowReceivingAmount
  }
}

export function getAllReceivingCurrency(currentStandardCurrency = {}, rates = [], currencyRange = []) {
  let allReceivingCurrency = rates.filter(v => v.originalId === currentStandardCurrency?.numCode) || []

  const hasStandardCurrency = allReceivingCurrency.some(v => v.numCode === currentStandardCurrency.numCode)
  if (!hasStandardCurrency) {
    allReceivingCurrency.unshift(currentStandardCurrency)
  }

  if (currencyRange?.length > 0) {
    allReceivingCurrency = allReceivingCurrency.filter(currency => currencyRange.includes(currency.numCode))
  }
  return allReceivingCurrency
}

// 本位币和外币转换
export function convertForeignToStandard(target) {
  if (target?.foreignStrCode && target?.standardStrCode) {
    return {
      ...target,
      standardStrCode: target?.foreignStrCode,
      standardNumCode: target?.foreignNumCode,
      standardSymbol: target?.foreignSymbol,
      standardUnit: target?.foreignUnit,
      standard: target?.foreign,
      foreign: target?.standard,
      foreignNumCode: target?.standardNumCode,
      foreignStrCode: target?.standardStrCode,
      foreignSymbol: target?.standardSymbol,
      foreignUnit: target?.standardUnit
    }
  }
  return target
}

// 以外币驱动算本位币
// 被计算的金额 money，需要计算的币种 currentCurrency
export function convertForeignToBaseCurrency(money, currentCurrency, rateList) {
  let newValue = {
    ...money
  }
  // 本位币等于外币时
  if (currentCurrency.numCode == money?.foreignNumCode) {
    newValue = standardValueMoney(new Big(newValue.foreign || 0).toFixed(currentCurrency.scale), currentCurrency)
  } else if (![money?.standardNumCode, money?.foreignNumCode].includes(currentCurrency.numCode)) {
    // 本位币和外币都不相等的情况下（1、只有本位币 2、本位币和外币都有的情况下）
    const foreignNum = money?.foreignNumCode ? money?.foreign : money?.standard
    const targetRate = rateList?.find(v => v.numCode === (money?.foreignNumCode || money?.standardNumCode))
    if (calculateAble([money?.foreign, targetRate?.rate, targetRate?.scale])) {
      const value = new Big(foreignNum || 0).times(targetRate.rate).toFixed(targetRate?.scale)
      newValue = {
        ...money,
        rate: targetRate.rate,
        sysRate: targetRate.rate,
        ...standardValueMoney(value, currentCurrency)
      }
      if (!money?.foreignNumCode && money?.standard) {
        //  没有外币本位币并且本位币不等于当前单据币种
        newValue = initializeCurrencyData(
          targetRate,
          { ...currentCurrency, rate: targetRate.rate },
          { standard: value, foreign: money?.standard }
        )
      }
    } else {
      newValue = standardValueMoney(new Big(foreignNum).toFixed(currentCurrency.scale), currentCurrency)
    }
  } else {
    // 剩下最后一种情况，只有本位币, 汇率可能和时间口径有变化，这里重新设置计算
    const recalculationTargetRate = rateList.find(v => v.numCode === money?.foreignNumCode)
    if (
      recalculationTargetRate &&
      newValue.rate &&
      recalculationTargetRate?.rate !== newValue?.rate &&
      calculateAble([money.foreign, recalculationTargetRate.rate, money?.standardScale])
    ) {
      const value = new Big(money.foreign || 0).times(recalculationTargetRate.rate).toFixed(money?.standardScale)
      newValue = {
        ...newValue,
        rate: recalculationTargetRate.rate,
        sysRate: recalculationTargetRate.rate,
        standard: value
      }
    }
  }
  // 剩下最后一种情况，只有本位币 直接return
  return newValue
}

// ... existing code ...
// 货币参考示例
// {
//   "numCode": "156",
//   "strCode": "CNY",
//   "scale": 2,
//   "name": "人民币",
//   "symbol": "￥",
//   "unit": "元",
//   "icon": "https://images.ekuaibao.com/currency/cny.png",
//   "rate": "1",
//   "startTime": 1512544620000,
//   "endTime": 4638916800000,
//   "id": "U5M6uhTVjQ6c00",
//   "version": 1,
//   "active": true,
//   "createTime": 1512544669795,
//   "updateTime": 1512544669795,
//   "corporationId": "VMU6hlsYeg0000"
// }
// 标准金额数据
// {
//   "standard": "2.00",
//   "standardStrCode": "THB",
//   "standardNumCode": "764",
//   "standardSymbol": "$",
//   "standardUnit": "美元",
//   "standardScale": 2,
//   "foreignStrCode": "VND",
//   "foreignNumCode": "704",
//   "foreignSymbol": "₫",
//   "foreignUnit": "越南盾",
//   "foreignScale": 0,
//   "rate": "1",
//   "sysRate": "1",
//   "foreign": "2"
// }
