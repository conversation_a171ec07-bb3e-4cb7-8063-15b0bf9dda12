import { app as api } from '@ekuaibao/whispered'
import { isObject } from '@ekuaibao/helpers'
import { get } from 'lodash'
import { getBoolVariation } from '../../lib/featbit'
import {
  handleDependence,
  handleAutoDependence,
  getDepedenceParam,
  checkIsEqualDependenceAndAutoDependence
} from './DependenceUtil'
import {
  formatTreeToArray,
  isAllowCancelDependenceClearValue,
  addFullPath,
  checkBelongDepartment
} from './fnInitalValue'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import { checkStaffDataRange } from './fnCheckStaffDataRange'
import { logEvent } from '../../lib/logs'

export const useNewAutomaticAssignment = () => {
  return api.getState('@common')?.powers?.DepRangeRuleAutomaticRecord
}

const isInitLoadNotAutoDependence = isInitLoad => {
  return getBoolVariation('cyxq-73091-modify-init-not-auto-dependence') ? isInitLoad : false
}

const fnIsInitLoad = (billState, isDetail, detailId, isInit) => {
  // 非单据新建及费类新建（费类有detailId 则为编辑）
  return ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && isInit
}

const getAutoDependenceId = async ({ autoDependence, key, id, isNeedFetch, bus }) => {
  let dependenceId = id
  if (isNeedFetch && key !== autoDependence[0].dependenceId) {
    const formValue = await bus.getFieldsValue()
    dependenceId = formValue[autoDependence[0].dependenceId]?.id
  }
  return dependenceId
}

export const dependenceLogger = (key, newValue, props) => {
  let { billData, billSpecification, feeType, dataSource, field, value } = props
  let newBillData = billData
  let message = '单据上的档案关系赋值'
  if (feeType) {
    message = '明细上的档案关系赋值'
  } else {
    newBillData = dataSource
  }
  api?.logger?.info(message, {
    specificationId: billSpecification?.id,
    specificationName: billSpecification?.name,
    flowId: newBillData?.flowId || newBillData?.id,
    code: newBillData?.code || newBillData?.form?.code,
    sceneName: '档案关系',
    feeTypeId: feeType?.id,
    feeTypeName: feeType?.name,
    field: field?.field,
    dependField: key,
    oldValue: value,
    newValue
  })
}

// 标注最后执行的promise id
const fieldRequestIdMap = {}
const setFieldRequestIdMap = (field, dependenceField, dependenceId, isDetail) => {
  const key = `${isDetail ? 'detail' : 'bill'}_${field.field}`
  fieldRequestIdMap[key] = `${dependenceField}__${dependenceId}`
}
const getFieldRequestId = (field, dependenceField, isDetail) => {
  const key = `${isDetail ? 'detail' : 'bill'}_${field.field}`
  const [_dependenceField, dependenceId] = fieldRequestIdMap[key].split('__')
  return dependenceField === _dependenceField ? (dependenceId === 'undefined' ? undefined : dependenceId) : null
}

/**
 * datalink
 */

const checkIdSelectable = async (id, props) => {
  const {
    field: { referenceData, filterId, field },
    submitterId,
    bus
  } = props
  if (!filterId) return true
  const form = await bus.getFieldsValue()
  const params = {
    entityId: referenceData.id,
    type: 'TABLE',
    query: { limit: { start: 0, count: 1 }, filterBy: `(active==true && id==\"${id}\")` },
    submitterId: submitterId?.id,
    filterId,
    field,
    form
  }
  const res = await api.invokeService('@third-party-manage:search:datalink:by:entityId', params)
  return res?.items?.data?.length === 1
}

const datalinkAutoDependence = async (
  { key, id, dependenceParams },
  isNeedFetch,
  dependenceItems,
  getDataLinkTempById,
  state,
  props
) => {
  const { field, onChange, bus } = props
  const { dependence, autoDependence } = field
  const { dependenceData } = state
  if (!autoDependence?.length) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]

  const result = await handleAutoDependence({ ...dependenceParams, recordSearch })

  if (result && result.data?.length === 1) {
    let newValueId = result.data[0]?.dataLink?.id
    const isSelectable = await checkIdSelectable(newValueId, props)
    if (!isSelectable) return

    bus.setValidateLevel(1)
    if (isNeedFetch) {
      if (dependenceItems?.findIndex(v => v?.dataLink?.id === newValueId) !== -1) {
        getDataLinkTempById(newValueId, undefined, key)
      } else {
        onChange()
      }
    } else {
      if (!dependence?.length) {
        getDataLinkTempById(newValueId, undefined, key)
      } else if (
        dependenceData?.data?.length &&
        dependenceData.data.findIndex(v => v?.dataLink?.id === newValueId) !== -1
      ) {
        getDataLinkTempById(newValueId, undefined, key)
      }
    }
  }
}

export const datalinkHandleDenpence = async ({ id, key }, options = {}, getDataLinkTempById, state, props) => {
  const { onChange, field, bus, value, billState, isDetail, detailId, isModify } = props
  const {
    dependence,
    dependenceCondition,
    allowCancelDependence,
    autoDependence,
    dataType,
    rangeOnlyOneAutomaticAssignment
  } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const dependenceParams = {
    key,
    dataType,
    id,
    dependenceFeeType: false,
    dependence,
    onChange,
    dependenceCondition
  }
  let isNeedFetch = dependence?.find(v => v.dependenceId === key)

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    datalinkAutoDependence({ key, id, dependenceParams }, false, {}, getDataLinkTempById, state, props)
  }

  if (isNeedFetch && id && dependence && dependence.length) {
    const recordSearch = state.dependenceParams?.recordSearch?.map((v, i) => {
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })
    dependenceParams.recordSearch = recordSearch

    setFieldRequestIdMap(field, key, id, isDetail)
    const result = await handleDependence(dependenceParams)
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    const { matchDefaultValue, leafItems, ...others } = result
    const { data = [] } = others
    const id = value && value.id
    const item = data.find(v => v?.dataLink?.id === id)
    const res = data && data.length > 1 ? {} : data[0]
    const selectItem = item ? item : res
    bus.setValidateLevel(1)
    // 初始化加载出来的数据，并且已经有值了，就不触发联动赋值
    if (isInitLoad && value && value.id && !others?.data?.length && !isModify) {
      return
    }
    if (rangeOnlyOneAutomaticAssignment && selectItem?.dataLink?.id && !isInitLoadNotAutoDependence(isInitLoad)) {
      let newValueId = selectItem.dataLink.id
      const isSelectable = await checkIdSelectable(newValueId, props)
      if (isSelectable) {
        getDataLinkTempById(newValueId, undefined, key)
      }
    } else if (autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        datalinkAutoDependence(
          { key, id, dependenceParams },
          true,
          others.data || [],
          getDataLinkTempById,
          state,
          props
        )
      } else if (!item && selectItem?.dataLink?.id) {
        // 要不要重新赋值
        //只有当前选择的值改变的时候才调用联动赋值
        let newValueId = selectItem?.dataLink?.id
        const isSelectable = await checkIdSelectable(newValueId, props)
        if (!isSelectable) newValueId = null
        getDataLinkTempById(newValueId, undefined, key)
      }
    } else if (!matchDefaultValue) {
      onChange()
    }
    if (!allowCancelDependence && !item) {
      // 要不要清空已经存在的值：返回值中没有已经存在的值且不允许取消依赖关系
      onChange()
    }
    dependenceLogger(key, result?.data, props)
    return {
      dependenceData: others,
      dependenceParams: getDepedenceParam(dependenceParams)
    }
  }
}

/**
 * mutilDimensionList
 */

const mutilDimensionListAutoDenpence = async ({ key, id, dependenceParams }, isNeedFetch, itemsMap, state, props) => {
  const { onChange, bus, field, getRecordLink } = props
  const { autoDependence } = field
  const { isDependence, dependenceListMap } = state
  if (!autoDependence?.length) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]

  const result = await getRecordLink({ ...dependenceParams, recordSearch })
  const data = result.payload
  const { items, leafItems } = data
  if (!items?.length || !leafItems) return
  bus.setValidateLevel(1)
  if (isNeedFetch) {
    if (itemsMap[leafItems.id]) {
      onChange([leafItems.id])
    } else {
      onChange(undefined)
    }
  } else {
    if (isDependence && dependenceListMap[leafItems.id]?.selectable) {
      onChange([leafItems.id])
    } else if (!isDependence) {
      onChange([leafItems.id])
    }
  }
}

export const mutilDimensionListHandleDenpence = async (
  { key, id, dependenceFeeType = false },
  options = {},
  state,
  props
) => {
  const {
    onChange,
    getRecordLink,
    field,
    value = [],
    bus,
    flowId,
    isModify,
    billState,
    form,
    detailId,
    isDetail
  } = props
  const {
    dependence,
    dataType,
    selectRange,
    dependenceCondition,
    allowCancelDependence,
    autoDependence,
    rangeOnlyOneAutomaticAssignment
  } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const isNeedFetch = dependence?.find(v => v.dependenceId === key)

  const dependenceParams = {
    entity: get(dataType, 'elemType.entity'),
    defaultValue: isObject(value) ? value.id : value,
    range: selectRange,
    dependenceFeeType,
    flowId: isModify ? flowId : '',
    dependenceCondition
  }

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    mutilDimensionListAutoDenpence({ key, id, dependenceParams }, false, undefined, state, props)
  }

  if (!!isNeedFetch) {
    const { dependenceMap } = state
    const recordSearch = dependenceMap.map((v, i) => {
      if (dependence[i]?.dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })

    setFieldRequestIdMap(field, key, id, isDetail)
    const action = await getRecordLink({ ...dependenceParams, recordSearch })
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    const { matchDefaultValue, items, leafItems } = action.payload
    const spreadArray = formatTreeToArray(items)
    const itemMap = spreadArray?.map(v => v?.id)
    let newValue = undefined

    const map = treeDataToMap(items)
    bus.setValidateLevel(1)
    if (isInitLoad) {
      const value = form.getFieldsValue()
      newValue = value[field?.field] ?? undefined
      onChange(newValue)
    } else if (
      rangeOnlyOneAutomaticAssignment &&
      items.length &&
      leafItems &&
      !matchDefaultValue &&
      !isInitLoadNotAutoDependence(isInitLoad)
    ) {
      newValue = leafItems
      onChange(newValue)
    } else if (autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        mutilDimensionListAutoDenpence({ key, id, dependenceParams }, true, map, state, props)
      } else {
        if (!matchDefaultValue) {
          if (leafItems) {
            newValue = [leafItems.id]
            onChange(newValue)
          } else if (value.length) {
            newValue = value.filter(v => itemMap.includes(v))
            if (isAllowCancelDependenceClearValue(allowCancelDependence, newValue?.length, value)) {
              onChange(newValue)
            }
          } else if (isAllowCancelDependenceClearValue(allowCancelDependence, true, value)) {
            onChange(undefined)
          }
        }
      }
    } else if (!matchDefaultValue) {
      if (isAllowCancelDependenceClearValue(allowCancelDependence, true, value)) {
        onChange(undefined)
      }
    }
    dependenceLogger(key, newValue, props)
    return {
      dependenceList: items,
      dependenceListMap: map
    }
  }
}

/**
 * mutilStaff
 */

const mutilStaffAutoDenpence = async ({ key, id, dependenceParams }, isNeedFetch, dependenceItems, state, props) => {
  const { field, getRecordLink, onChange, bus } = props
  const { autoDependence } = field
  const { isDependence, dependenceList } = state
  if (!autoDependence?.length) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]
  const result = await getRecordLink({ ...dependenceParams, recordSearch })
  const data = result.payload
  const { items } = data
  if (items?.length !== 1) return
  const newValue = checkStaffDataRange(field, items[0])
  if (!newValue.id) return
  bus.setValidateLevel(1)
  if (isNeedFetch) {
    if (dependenceItems && dependenceItems.findIndex(v => v.id === newValue.id) !== -1) {
      onChange(newValue)
    } else {
      onChange(undefined)
    }
  } else {
    if (isDependence && dependenceList.findIndex(v => v.id === newValue.id) !== -1) {
      onChange(newValue)
    } else if (!isDependence) {
      onChange(newValue)
    }
  }
}

export const mutilStaffHandleDenpence = async ({ key, id, dependenceFeeType = false }, options = {}, state, props) => {
  let { getRecordLink, field, onChange, bus, billState, form, detailId, isDetail, value } = props
  let { dependence, dataType, dependenceCondition, autoDependence, rangeOnlyOneAutomaticAssignment } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const isNeedFetch = dependence?.find(v => v.dependenceId === key)

  const dependenceParams = {
    entity: dataType.elemType.entity,
    dependenceFeeType,
    dependenceCondition
  }

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    mutilStaffAutoDenpence({ key, id, dependenceParams }, false, undefined, state, props)
  }

  if (!!isNeedFetch) {
    const { dependenceMap } = state
    let recordSearch = dependenceMap.map((v, i) => {
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })

    setFieldRequestIdMap(field, key, id, isDetail)
    const action = await getRecordLink({ ...dependenceParams, recordSearch })
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    let { items } = action.payload
    let newValue = undefined
    bus.setValidateLevel(1)
    if (isInitLoad) {
      const fieldsValue = form.getFieldsValue()
      newValue = fieldsValue[field?.field] ?? undefined
      newValue = checkStaffDataRange(field, newValue)
      onChange(newValue)
    } else if (rangeOnlyOneAutomaticAssignment && items.length === 1 && !isInitLoadNotAutoDependence(isInitLoad)) {
      newValue = checkStaffDataRange(field, items)
      onChange(newValue)
    } else if (autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        mutilStaffAutoDenpence({ key, id, dependenceParams }, true, items, state, props)
      } else if (items.length === 1) {
        newValue = checkStaffDataRange(field, items)
        onChange(newValue)
      }
    } else if (Array.isArray(value)) {
      let isValid = true
      value.forEach(staff => {
        if (items.findIndex(item => item.id === staff.id) === -1) {
          isValid = false
          return
        }
      })
      if (!isValid) {
        onChange(undefined)
      }
    }
    dependenceLogger(key, newValue, props)
    return {
      dependenceList: items
    }
  }
}

/**
 * ref
 */

const refAutoDenpence = async ({ key, id, dependenceParams }, isNeedFetch, itemsMap, state, props) => {
  const { onChange, bus, field, getRecordLink, value } = props
  const { autoDependence } = field
  const { isDependence, dependenceListMap } = state
  if (!autoDependence?.length) return
  if (isDependence && !itemsMap && !dependenceListMap) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]
  const result = await getRecordLink({ ...dependenceParams, recordSearch })
  const data = result.payload
  const { items, leafItems } = data
  bus.setValidateLevel(1)
  if (getBoolVariation('cyxq-73091-modify-init-not-auto-dependence')) {
    if (isNeedFetch) {
      if (itemsMap[leafItems?.id]?.active) {
        onChange(leafItems)
      } else if (!itemsMap[value?.id || value]) {
        onChange(undefined)
      }
    } else {
      if (!isDependence || dependenceListMap[leafItems?.id]?.active) {
        onChange(leafItems)
      }
    }
  } else {
    if (!items?.length || !leafItems) return
    const newValue = leafItems
    if (isNeedFetch) {
      if (itemsMap && itemsMap[newValue.id]?.active) {
        onChange(newValue)
      } else {
        onChange(undefined)
      }
    } else {
      if (isDependence && dependenceListMap[newValue.id]?.active) {
        onChange(newValue)
      } else if (!isDependence) {
        onChange(newValue)
      }
    }
  }
}

export const refHandleDenpence = async ({ key, id, dependenceFeeType = false, __value }, options, state, props) => {
  const { onChange, getRecordLink, field, value, bus, flowId, isModify, billState, form, detailId, isDetail } = props
  const {
    dependence,
    dataType,
    selectRange,
    dependenceCondition,
    isShowFullPath,
    allowCancelDependence,
    autoDependence,
    rangeOnlyOneAutomaticAssignment
  } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const isNeedFetch = dependence?.find(v => v.dependenceId === key)

  const dependenceParams = {
    entity: dataType.entity,
    defaultValue: isObject(value) ? value.id : value,
    range: selectRange,
    dependenceFeeType,
    flowId: isModify ? flowId : '',
    dependenceCondition
  }

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    await refAutoDenpence({ key, id, dependenceParams }, false, undefined, state, props)
  }
  if (!!isNeedFetch) {
    const { dependenceMap } = state
    const recordSearch = dependenceMap.map((v, i) => {
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })

    setFieldRequestIdMap(field, key, id, isDetail)
    const action = await getRecordLink({ ...dependenceParams, recordSearch })
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    const { matchDefaultValue, items, leafItems } = action.payload
    let newValue = value
    let newLeafItems = leafItems
    if (!items?.length) newLeafItems = null
    const map = treeDataToMap(items)
    bus.setValidateLevel(1)

    const fnOnChange = (updateCurrentFieldValue, updateInitLoad) => {
      if (onChange) {
        try {
          onChange(updateCurrentFieldValue, updateInitLoad)
        } catch (error) {
          console.error('onChange 执行出错:', error)
          logEvent('执行onChange报错', { field, value })
        }
      }
    }

    if (isInitLoad && !isModify) {
      const fieldsValue = form.getFieldsValue()
      let updateValue = fieldsValue[field?.field] ?? undefined
      if (!!updateValue && items?.length) {
        const id = isObject(updateValue) ? updateValue.id : updateValue
        updateValue = map[id] ?? newLeafItems
      }
      fnOnChange(updateValue, isInitLoad)
    } else if (
      rangeOnlyOneAutomaticAssignment &&
      items.length &&
      newLeafItems &&
      !matchDefaultValue &&
      !isInitLoadNotAutoDependence(isInitLoad)
    ) {
      newValue = newLeafItems
      fnOnChange(newValue)
    } else if (autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        await refAutoDenpence({ key, id, dependenceParams }, true, map, state, props)
      } else {
        if (items.length && !newLeafItems && __value) {
          const id = isObject(__value) ? __value.id : __value
          const map = treeDataToMap(items)
          const vv = map[id]
          newValue = vv
          fnOnChange(vv)
        } else {
          newValue = newLeafItems ? newLeafItems : undefined
          if (isAllowCancelDependenceClearValue(allowCancelDependence, newValue, value)) {
            fnOnChange(newValue)
          }
        }
      }
    } else if (!matchDefaultValue) {
      newValue = undefined
      if (isAllowCancelDependenceClearValue(allowCancelDependence, newValue, value)) {
        fnOnChange(newValue)
      }
    }
    if (field?.name === 'legalEntityMultiCurrency' && !!items?.length) {
      if (items.length === 1) {
        const item0 = items[0]
        item0?.id !== value?.id && bus.emit('dimention:multi:currency:change')
      } else if (value?.id !== newValue?.id) {
        bus.emit('dimention:multi:currency:change')
      }
    }
    isShowFullPath && addFullPath(items, '')
    dependenceLogger(key, newValue, props)
    return {
      dependenceList: items,
      dependenceListMap: map
    }
  }
}

/**
 * refDepartment
 */

const refDepartmentAutoDenpence = async (
  { key, id, dependenceParams },
  isNeedFetch,
  itemsMap,
  onChange,
  state,
  props
) => {
  const { bus, field, getRecordLink, departmentsMap, submitterId } = props
  const { autoDependence } = field
  const { isDependence, dependenceListMap } = state
  if (!autoDependence?.length) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]
  const result = await getRecordLink({ ...dependenceParams, recordSearch })
  const data = result.payload
  const { items, leafItems } = data
  if (!items?.length || !leafItems) return
  const newValue = checkBelongDepartment(field, leafItems, submitterId)
  if (!newValue?.id) return
  bus.setValidateLevel(1)
  if (isNeedFetch) {
    if (itemsMap && itemsMap[newValue.id]) {
      onChange(newValue)
    } else {
      onChange(undefined)
    }
  } else {
    if (isDependence && dependenceListMap[newValue.id]?.selectable) {
      onChange(newValue)
    } else if (!isDependence && departmentsMap[newValue.id]) {
      onChange(newValue)
    }
  }
}

export const refDepartmentHandleDenpence = async (
  { key, id, dependenceFeeType = false },
  options = {},
  onChange,
  fnFilterDeptForOnlyBelongDepartment,
  state,
  props
) => {
  const { getRecordLink, field, value, bus, submitterId = {}, form, billState, detailId, isDetail } = props
  const {
    dependence,
    dataType,
    selectRange,
    dependenceCondition,
    allowUseDeactivatedData,
    autoDependence,
    rangeOnlyOneAutomaticAssignment
  } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const isNeedFetch = dependence?.find(v => v.dependenceId === key)

  const dependenceParams = {
    entity: dataType.entity,
    range: selectRange,
    dependenceFeeType,
    defaultValue: value && value.id,
    dependenceCondition,
    findDeactivated: allowUseDeactivatedData ? 'all' : 'no'
  }

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    refDepartmentAutoDenpence({ key, id, dependenceParams }, false, undefined, onChange, state, props)
  }

  if (!!isNeedFetch) {
    const { dependenceMap } = state
    const recordSearch = dependenceMap.map((v, i) => {
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })

    setFieldRequestIdMap(field, key, id, isDetail)
    const action = await getRecordLink({ ...dependenceParams, recordSearch })
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    const data = action.payload
    let { matchDefaultValue, items, leafItems } = data
    let newValue = leafItems
    const map = treeDataToMap(items)
    bus.setValidateLevel(1)
    if (isInitLoad && field.allowCancelDependence) {
      const fieldsValue = form.getFieldsValue()
      newValue = fieldsValue[field?.field] ?? undefined
      onChange(newValue)
    } else if (
      rangeOnlyOneAutomaticAssignment &&
      items?.length &&
      leafItems &&
      !matchDefaultValue &&
      !isInitLoadNotAutoDependence(isInitLoad)
    ) {
      newValue = checkBelongDepartment(field, leafItems, submitterId)
      onChange(newValue)
    } else if (autoDependence?.length && autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        refDepartmentAutoDenpence({ key, id, dependenceParams }, true, map, onChange, state, props)
      } else {
        if (!leafItems) {
          const fieldsValue = form.getFieldsValue()
          newValue = fieldsValue[field?.field] ?? undefined
          const id = isObject(newValue) ? newValue.id : newValue
          const map = treeDataToMap(items)
          newValue = map[id]
        }
        newValue = checkBelongDepartment(field, newValue, submitterId)
        onChange(newValue)
      }
    } else if (!matchDefaultValue) {
      onChange(undefined)
    }
    dependenceLogger(key, newValue, props)
    const dependenceList = fnFilterDeptForOnlyBelongDepartment(field, items, submitterId)
    return {
      dependenceList,
      originDependenceList: items,
      dependenceListMap: map
    }
  }
}

/**
 * refStaff
 */

const refStaffAutoDenpence = async ({ key, id, dependenceParams }, isNeedFetch, dependenceItems, state, props) => {
  const { onChange, bus, field, getRecordLink } = props
  const { autoDependence } = field
  const { isDependence, dependenceList } = state
  if (!autoDependence?.length) return

  const dependenceId = await getAutoDependenceId({ autoDependence, key, id, isNeedFetch, bus })
  const recordSearch = [{ ...autoDependence[0], dependenceId }]
  const result = await getRecordLink({ ...dependenceParams, recordSearch })

  const data = result.payload
  const { items } = data
  if (items?.length !== 1) return
  const newValue = checkStaffDataRange(field, items[0])
  bus.setValidateLevel(1)
  if (isNeedFetch) {
    if (dependenceItems && dependenceItems.findIndex(v => v.id === newValue?.id) !== -1) {
      onChange(newValue)
    } else {
      onChange(undefined)
    }
  } else {
    if (isDependence && dependenceList.findIndex(v => v.id === newValue?.id) !== -1) {
      onChange(newValue)
    } else if (!isDependence) {
      onChange(newValue)
    }
  }
}

export const refStaffHandleDenpence = async ({ key, id, dependenceFeeType = false }, options = {}, state, props) => {
  let { onChange, getRecordLink, field, bus, billState, form, detailId, isDetail } = props
  let {
    dependence,
    dataType,
    dependenceCondition,
    allowCancelDependence,
    autoDependence,
    rangeOnlyOneAutomaticAssignment
  } = field

  const isInitLoad = fnIsInitLoad(billState, isDetail, detailId, options?.isInit)
  const isNeedFetch = dependence?.find(v => v.dependenceId === key)

  const dependenceParams = {
    entity: dataType.entity,
    dependenceFeeType,
    dependenceCondition
  }

  if (
    !isNeedFetch &&
    autoDependence?.length &&
    autoDependence[0].dependenceId === key &&
    !isInitLoadNotAutoDependence(isInitLoad)
  ) {
    refStaffAutoDenpence({ key, id, dependenceParams }, false, undefined, state, props)
  }

  if (!!isNeedFetch) {
    const { dependenceMap } = state
    let recordSearch = dependenceMap.map((v, i) => {
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })

    setFieldRequestIdMap(field, key, id, isDetail)
    const action = await getRecordLink({ ...dependenceParams, recordSearch })
    if (getFieldRequestId(field, key, isDetail) !== id) return undefined

    let data = action.payload
    const { items, matchDefaultValue } = data
    let newValue = undefined
    const value = props.value
    let staffId = isObject(value) ? value.id : value
    let index = items.findIndex(v => v.id === staffId)

    bus.setValidateLevel(1)
    if (isInitLoad) {
      const fieldsValue = form.getFieldsValue()
      newValue = fieldsValue[field?.field] ?? undefined
      newValue = checkStaffDataRange(field, newValue)
      onChange(newValue)
    } else if (
      rangeOnlyOneAutomaticAssignment &&
      items?.length === 1 &&
      !matchDefaultValue &&
      !isInitLoadNotAutoDependence(isInitLoad)
    ) {
      newValue = checkStaffDataRange(field, items[0])
      newValue && onChange(newValue)
    } else if (autoDependence?.length && !isInitLoadNotAutoDependence(isInitLoad)) {
      if (!checkIsEqualDependenceAndAutoDependence(dependence, autoDependence)) {
        await refStaffAutoDenpence({ key, id, dependenceParams }, true, items, state, props)
      } else if (index === -1) {
        let result = items.length === 1 ? items[0] : undefined
        result = checkStaffDataRange(field, result)
        newValue = result
        if (isAllowCancelDependenceClearValue(allowCancelDependence, result, value)) {
          onChange(result)
        }
      }
    } else if (index === -1) {
      let result = undefined
      newValue = result
      if (isAllowCancelDependenceClearValue(allowCancelDependence, result, value)) {
        onChange(result)
      }
    }
    dependenceLogger(key, newValue, props)
    return {
      dependenceList: items
    }
  }
}
