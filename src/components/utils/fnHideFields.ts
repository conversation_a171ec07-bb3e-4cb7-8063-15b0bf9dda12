import { app as api } from '@ekuaibao/whispered'
import { getAllAutoCalResultForBillDiff } from '../../plugins/bills/util/autoCalculate'

interface VisibilityProps {
  departments: string[]
  departmentsIncludeChildren: boolean
  fullVisible: boolean
  roles: string[]
  staffs: string[]
}

export function fnHideFields(hide: boolean, hideVisibility: VisibilityProps) {
  if (hide) {
    const { departments, roles, staffs } = hideVisibility || { departments: [], roles: [], staffs: [] }
    if (departments?.length || roles?.length || staffs?.length) {
      const userInfo = api.getState('@common.userinfo').staff || {}
      const departmentIds = userInfo?.departments?.map(line => line.id) || []
      const rolesIds = userInfo?.roles?.values?.map(line => line.roleDefId) || []
      return staffs.indexOf(userInfo.id) >= 0 || isIncludes(roles, rolesIds) || isIncludes(departments, departmentIds)
    }
    return false
  } else {
    return !hide
  }
}

export function fnHideFieldsNote(hideVisibility: VisibilityProps) {
  const { departments, roles, staffs } = hideVisibility || { departments: [], roles: [], staffs: [] }
  if (departments?.length || roles?.length || staffs?.length) {
    const userInfo = api.getState('@common.userinfo').staff || {}
    const departmentIds = userInfo?.departments?.map(line => line.id) || []
    const rolesIds = userInfo?.roles?.values?.map(line => line.roleDefId) || []
    return staffs.indexOf(userInfo.id) >= 0 || isIncludes(roles, rolesIds) || isIncludes(departments, departmentIds)
  }
  return false
}
export function isHiddenFieldsInclude(hiddenFields: string[], field) {
  if (!hiddenFields?.length || !field) {
    return false
  }
  return hiddenFields.includes(field.field)
}

function isIncludes(departments, ids) {
  let flag = false
  for (let i = 0; i < ids.length; i++) {
    if (departments.indexOf(ids[i]) >= 0) {
      flag = true
      break
    }
  }
  return flag
}

export function fnFlowHideFields(plan) {
  let nodes = plan?.nodes ?? []
  let curNode =
    nodes?.length &&
    nodes.find(v => {
      return v?.id === plan?.taskId
    })
  return curNode?.hiddenFileds ?? []
}

export function getApportionHideDetailCount(billData, selectedData) {
  const fncalResult = async () => {
    try {
      if (!selectedData || !selectedData.length) return false
      // 复用小组件getAllAutoCalResultForBillDiff方法，查询整张单据的自动计算结果
      const data = await getAllAutoCalResultForBillDiff({ ...billData, details: selectedData })
      const apportionItem = data?.items?.filter(
        item => !!item.detailId && item.onField === 'apportions' && item.attribute
      )

      const result = selectedData.map(detail => {
        // 找到隐藏分摊的明细
        const apportionComponet = detail?.specificationId?.components?.find(comp => comp.field === 'apportions') || {}

        if (!apportionComponet.hide) return //没有配置隐藏

        const hideAttr = apportionItem.find(
          item => item.detailId === detail?.feeTypeForm?.detailId && item.attribute === 'hide'
        )
        if (hideAttr && hideAttr.result === 'false') {
          //配置隐藏，但是隐藏公式 false时
          return
        }
        if (apportionComponet.hideVisibility && fnHideFieldsNote(apportionComponet.hideVisibility)) {
          //配置隐藏，但是白名单
          return
        }
        if (apportionComponet.open) {
          //始终开启分摊时 不隐藏
          const openItem = apportionItem.find(
            item => item.detailId === detail?.feeTypeForm?.detailId && item.attribute === 'open'
          )
          if (!openItem || openItem.result === 'true') {
            return
          }
        }
        return detail
      })
      return result.filter(i => i)?.length
    } catch (error) {}
  }
  return fncalResult()
}

export const fnHiddenFieldByConfig = (
  field: any,
  hiddenFields: string[] = [],
  validateError: string[] = []
): boolean => {
  const { hide, name, configs, attributeHide = false } = field
  const hasAuto = configs === null ? false : configs?.find(r => r?.property === 'hide') !== undefined
  if (
    !hide ||
    (hide && !hasAuto) ||
    (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
    (hide && isHiddenFieldsInclude(hiddenFields, field) && !hasAuto)
  ) {
    const isShow = (hide && !isHiddenFieldsInclude(hiddenFields, field)) || !hide || validateError.includes(name)
    if (!isShow) {
      return true
    }
  } else if (hide && hasAuto && isHiddenFieldsInclude(hiddenFields, field)) {
    const isShow = (hide && !attributeHide) || validateError.includes(name)
    if (!isShow) {
      return true
    }
  }
  return false
}
