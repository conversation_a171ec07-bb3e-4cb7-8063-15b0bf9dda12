/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/9 下午1:45.
 */
export function fnCompareObj(objA, objB) {
  if (!objA || !objB) return
  if (!isObj(objA) || !isObj(objB)) return
  if (getLength(objA) !== getLength(objB)) return
  return CompareObj(objA, objB, true)
}

function isObj(object) {
  return object && typeof object === 'object'
}

function getLength(object) {
  let count = 0
  for (let key in object) count++
  return count
}

function isArray(o) {
  return Object.prototype.toString.call(o) === '[object Array]'
}

function CompareObj(objA, objB, flag) {
  for (let key in objA) {
    if (!flag)
      //跳出整个循环
      break
    if (!objB.hasOwnProperty(key)) {
      flag = false
      break
    }

    if (!isArray(objA[key])) {
      //子级不是数组时,比较属性值
      if (isObj(objB[key]) && isObj(objA[key])) {
        if (!flag) break
        flag = CompareObj(objB[key], objA[key], flag)
      } else {
        if (objB[key] !== objA[key]) {
          flag = false
          break
        }
      }
    } else {
      if (!isArray(objB[key])) {
        flag = false
        break
      }

      let oA = objA[key],
        oB = objB[key]
      if (oA.length !== oB.length) {
        flag = false
        break
      }

      for (let k in oA) {
        if (!flag) break
        flag = CompareObj(oA[k], oB[k], flag)
      }
    }
  }

  return flag
}
