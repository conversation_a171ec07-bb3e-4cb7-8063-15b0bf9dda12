/*
 * @Author: Teacher Tang
 * @Date:   2021-11-18 17:17:29
 * @Module: BlockUI 工具函数
 */
import { Fetch } from '@ekuaibao/fetch'
import { get } from 'lodash'

/**
 * 互联卡片编辑器相关配置
 *
 * @export
 * @param {Object} field 配置字段信息
 * @param {Function} cb 回调函数
 */
export async function getLoanStatisticsWidgetCard(props, defaultShow = false) {
  const { field, flowId, detailId } = props
  // 获取填单是否展示的配置
  const isShow = get(field, 'blockUIConfig.loanStatisticsWidgetCard', 'show')
  if (!defaultShow && isShow !== 'show') {
    return ''
  }
  const referenceData = field?.dataType?.entity?.split('.').pop()
  // 获取卡片详情

  const res = await getWidgetCardDSL(referenceData, {
    flowId,
    detailId
  })
  const dsl = res.value
  if (dsl) {
    try {
      const dslObj = JSON.parse(dsl)
      return dslObj || ''
    } catch (error) {
      return null
    }
  }
  return null
}

/** 数据和dsl替换 */
export const setDataToDsl = (dsl, data) => {
  if (data) {
    let str = dsl.replace(/[\n]/g, '\\n')
    for (const k of Object.keys(data)) {
      str = str.replaceAll(`{${k}}`, data[k])
    }
    if (str.indexOf('{reserved+remain}') >= 0) {
      str = str.replace('{reserved+remain}', data.reserved + data.remain - 0)
    }
    return str
  }
  return dsl
}

/**
 * 获取互联卡片详情
 *
 * @export
 * @param {string} id 互联卡片id
 * @returns Promise Fn
 */
export async function getWidgetCardDSL(id, params) {
  return Fetch.POST(`/api/engine/widgetCard/renderDSL/$${encodeURIComponent(id)}`, {}, { body: params })
    .then(res => {
      return res
    })
    .catch(err => {
      return err
    })
}

/**
 * 获取FaaS数据
 *
 * @export
 * @param {object} params 参数
 * @returns Promise Fn
 */
export async function getWidgetCardData(id, params) {
  return Fetch.POST(`/api/engine/widgetCard/renderData/$${encodeURIComponent(id)}`, {}, { body: params })
}

/**
 * 获取互联卡片DSL及填充数据
 *
 * @export
 * @param {Object} props 数据参数
 * @param {Function} cb 回调函数
 */
export async function getBlockUIReadonlyDSL(props) {
  const { field, flowId, submitterId, ownerId } = props
  const referenceData = field?.dataType?.entity?.split('.').pop()
  let staffId = ''
  if (referenceData === 'LOAN_STATISTICS') {
    const config = get(field, 'blockUIConfig.loanStatistics', 'fromBorrower')
    if (!submitterId || !ownerId) {
      return ''
    }
    staffId = config === 'fromBorrower' ? submitterId?.id : ownerId?.id //fromBorrower 借款人
  } else if (field?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0) {
    // 互联卡片编辑器相关
    const res = await getLoanStatisticsWidgetCard(props, true)
    return res
  } else if (referenceData === 'ENTERPRISE_PURCHASE_ORDER') {
    // 集采需求
    const res = await fetchDSL('COMPANYORDERN8N:BLOCK_UI_RENDER', { data: { flowId } })
    return res?.value || ''
  } else if (referenceData === 'ADJUSTMENTNODE' || referenceData.indexOf('_THIRD') >= 0) {
    // ADJUSTMENTNODE KA预算调整
    // 统一规则定为包含_THIRD的情况都是和第三方对接的情况
    const body = { data: { flowId, staffId, propertyId: field?.field } }
    const res = await fetchDSL(`${referenceData}:BLOCK_UI_RENDER`, body)
    return res?.value || ''
  }

  const res = await fetchDSL(referenceData, { flowId, propertyId: field?.field, staffId })
  return res?.value || ''
}

/**
 * 获取DSL模板
 *
 * @export
 * @param {String} referenceData DSL模板名称
 * @param {Object} body 请求参数
 * @returns
 */
export function fetchDSL(referenceData, body) {
  return Fetch.POST(`/api/engine/blockUI/$${referenceData}`, {}, { body })
}
