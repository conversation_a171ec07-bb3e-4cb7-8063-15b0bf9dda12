/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/12 下午12:14.
 */

import { get, find, intersection, isString } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { Fetch } from '@ekuaibao/fetch'

export function lastSelectValue(field, lastChoice) {
  let { defaultValue = {} } = field
  let type = get(field, 'defaultValue.type', '')
  if (type === 'predefine' && defaultValue.value === 'lastselect' && lastChoice) {
    let obj = lastChoice.find(line => line.fieldName === field.name)
    if (obj) {
      return obj.lastChoiceValue
    }
  }
  return null
}
export function constantValue(field = {}, changeFeetype = false) {
  let { defaultValue = {} } = field
  let type = get(field, 'defaultValue.type', '')
  if (type === 'constant' && defaultValue.value) {
    if (changeFeetype && field?.type === 'ref:organization.Department' && defaultValue.value.id) {
      const departmentsMap = api?.getState()['@common']?.departmentVisibility?.mapData || {}
      return departmentsMap[defaultValue.value.id] ?? defaultValue.value.id
    } else {
      return defaultValue?.value?.ids ?? defaultValue?.value?.id
    }
  }
  return null
}

export function predefineValue(field, submitterId) {
  const { name, defaultValue = {}, dataType = {} } = field
  if (!submitterId) return null
  if (defaultValue?.type === 'predefine' && defaultValue?.value === 'submitter') {
    if (name === 'travelers' || dataType?.type === 'list') {
      return [submitterId?.id || submitterId]
    } else {
      return submitterId
    }
  }
}

export function getRefDefaultValue(props) {
  let { field, lastChoice } = props
  let constVal = constantValue(field)
  if (constVal) return constVal

  let lastVal = lastSelectValue(field, lastChoice)
  if (lastVal) return lastVal

  return null
}

export function refPropertyById(props) {
  let { field, lastChoice } = props
  let constVal = constantValue(field)
  if (constVal) {
    return handleGetDataById(field, constVal)
  }
  let lastVal = lastSelectValue(field, lastChoice)
  if (lastVal) {
    return handleGetDataById(field, lastVal)
  }
}
export function handleGetDataById(field, id) {
  const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
  if (entity.startsWith('basedata.Dimension')) {
    return handleGetDimensionById(id)
  }
  if (entity.startsWith('basedata.Settlement')) {
    return api.invokeService('@common:get:staff:dimension', { name: 'basedata.Settlement' }).then(data => {
      let result = data.items.find(item => item.id === id)
      return result
    })
  }
  if (entity === 'organization.Staff') {
    return field.dataType.type === 'list'
      ? api.invokeService('@common:get:staff:by:ids', { ids: isString(id) ? id.split(',') : id }).then(data => {
          return data.items
        })
      : api.invokeService('@bills:get:StaffById', id)
  }
  if (entity.startsWith('basedata.Enum.')) {
    const code = entity.match(/basedata.Enum.(\S*)/)[1]
    return api.invokeService('@common:get:enumitems', code).then(data => {
      return find(data.items, { code: id })
    })
  }

  return Promise.resolve()
}

export function handleGetDimensionById(id) {
  return api.invokeService('@bills:get:dimension', id).then(result => {
    if (result && result.items.length > 0) {
      let me = api.getState()['@common'].userinfo.staff
      let item = result.items[0]
      if (!item.visibility.fullVisible && !checkDimensionVisible(me, item.visibility)) {
        return undefined
      }
      return item
    }
  })
}
export function checkDimensionVisible(user, visibility) {
  const { departments, roles, staffs } = visibility

  if (staffs?.includes(user.id)) {
    return true
  }

  //部门可见性无法判断,需要优化这个逻辑
  // let deptIds = user.departments && user.departments.map(v => v.id)
  if (departments && departments.length) {
    return true
  }
  let roleIds = user.roles && user.roles.values.map(v => v.roleDefId)
  if (roleIds && intersection(roleIds, roles).length) {
    return true
  }
  return false
}

export function canSelectParent(field = {}) {
  const { selectRange } = field
  return 'leaf' !== selectRange
}

export function checkDefaultDeptValue(dept, field, submitterId = {}) {
  if (!dept) return dept
  const { id } = dept
  //没有部门数据时返回空值
  const deptMap = api.getState('@common.department').mapData
  if (!deptMap || !Object.keys(deptMap).length) return {}
  let value = deptMap[id]
  const whiteList = ['ID01iLmzUrRPVd', '8focGZAIaZKI00']
  if (!value && dept && whiteList.includes(Fetch.ekbCorpId)) {
    // 部门企业数据比较多时，数据还没有请求回来，进入单据就会导致状态树上面的数据为空，但是提交人有默认部门，目前先使用提交人的默认部门
    value = dept
  }
  //确认是否只可选择末级部门
  const children = getNodeValueByPath(value, 'children', [])
  if (children.length && !canSelectParent(field)) return {}
  //如果在配置中限制了只可选择用户的所在部门, 需要确认部门是否在默认部门中
  return checkBelongDepartment(field, value, submitterId)
}

//检查模版中是否配置了选项，让提交人只能选择其所在部门
export function checkBelongDepartment(field = {}, value, submitterId = {}) {
  if (field && field.onlyBelongDepartment) {
    const submitterDepartments = get(submitterId, 'departments', [])
    if (value && submitterDepartments.length) {
      const submitterDepartmentsArr = submitterDepartments.map(dep => {
        if (typeof dep === 'string') return dep
        return dep.id
      })
      if (!submitterDepartmentsArr.includes(value.id)) return {}
    }
  }
  return value
}

/**
 * @param {Array | Object} tree
 */
export function formatTreeToArray(tree) {
  if (!tree) return []

  let queue = Array.isArray(tree) ? [...tree] : [tree]
  let data = []

  while (queue.length !== 0) {
    let item = queue.shift()

    data.push({
      id: item?.id,
      parentId: item?.parentId,
      name: item?.name
    })

    let children = item?.children

    if (children) {
      for (let i = 0; i < children.length; i++) {
        const element = children[i]
        queue.push(element)
      }
    }
  }
  return data
}

/**
 * 档案添加完整路径
 * @param {array} list
 * @param {string} fullPathName
 */
export function addFullPath(list = [], fullPathName = '') {
  list.forEach(el => {
    el.fullPath = fullPathName ? `${fullPathName}/${el.name}` : el.name
    if (el.children) {
      el.children = addFullPath(el.children, el.fullPath)
    }
    return el
  })
  return list
}

/**
 * 根据取消依赖配置是否请需要清空值
 * @param {*} allowCancelDependence
 * @param {*} value
 */
export function isAllowCancelDependenceClearValue(allowCancelDependence, dependValue, fieldValue) {
  if (allowCancelDependence && !dependValue && fieldValue) {
    return false
  }
  return true
}

export const fieldDefaultValue = (field, defaultValue) => {
  if (defaultValue !== undefined) {
    return defaultValue
  }
  if (field?.type === 'number' && field?.defaultValue?.type !== 'none') {
    return 0
  }
  return defaultValue
}
