/**
 *  Created by pw on 2019-02-28 23:04.
 */
import { MoneyMath } from '@ekuaibao/money-math'
import { getInvoiceAmount } from '../../lib/InvoiceUtil'
import { get, cloneDeep } from 'lodash'
import { standardValueMoney, getAcountKey } from '../../lib/misc'

function add({ targetFormValue, formFormValue, taxs = [] }) {
  const formValue = cloneDeep(targetFormValue)

  // 新增自动计算 值可为undefined，防止计算出错
  if (!get(formFormValue, 'amount.standard')) {
    formFormValue.amount = standardValueMoney(0)
  }

  if (!get(formFormValue, 'taxAmount.standard')) {
    formFormValue.taxAmount = standardValueMoney(0)
  }

  if (!get(formFormValue, 'noTaxAmount.standard')) {
    formFormValue.noTaxAmount = standardValueMoney(0)
  }

  if (targetFormValue.amount) {
    targetFormValue.amount = new MoneyMath(targetFormValue.amount).add(formFormValue.amount).value
  }
  if (targetFormValue.taxAmount) {
    //税额
    targetFormValue.taxAmount = new MoneyMath(targetFormValue.taxAmount).add(formFormValue.taxAmount).value
  }
  if (targetFormValue.noTaxAmount) {
    //不计税金额
    targetFormValue.noTaxAmount = new MoneyMath(targetFormValue.noTaxAmount).add(formFormValue.noTaxAmount).value
  }
  if (targetFormValue.taxRate) {
    //税率
    targetFormValue.taxRate =
      targetFormValue.taxRate === formFormValue.taxRate ? formFormValue.taxRate : targetFormValue.taxRate
  }
  // 计算税额、不计税金额、可抵扣税额、价税合计
  taxs.forEach(({ defaultValue, name }) => {
    const v = get(defaultValue, 'value')
    let value = getAcountKey(v)
    if (v === 'taxTotal') {
      targetFormValue[name] = new MoneyMath(formFormValue[name])
        .add(formValue.taxAmount)
        .add(formValue.noTaxAmount).value
    } else {
      targetFormValue[name] = new MoneyMath(formFormValue[name]).add(formValue[value]).value
    }
  })

  return targetFormValue
}

function minus({ targetFormValue, invoice, residueInvoice, taxs }) {
  if (invoice) {
    const invoiceAmount = getInvoiceAmount(invoice)
    const formValue = cloneDeep(targetFormValue)
    const { amount, taxAmount, noTaxAmount } = invoiceAmount
    if (targetFormValue.amount) {
      targetFormValue.amount = new MoneyMath(targetFormValue.amount).minus(amount).value
      // 金额的值不小于0
      if (targetFormValue.amount.standard < 0) {
        targetFormValue.amount = standardValueMoney(0)
      }
    }
    if (targetFormValue.taxAmount) {
      //税额
      targetFormValue.taxAmount = new MoneyMath(targetFormValue.taxAmount).minus(taxAmount).value
    }
    if (targetFormValue.noTaxAmount) {
      //不计税金额
      targetFormValue.noTaxAmount = new MoneyMath(targetFormValue.noTaxAmount).minus(noTaxAmount).value
    }
    if (Object.keys(targetFormValue).indexOf('taxRate') >= 0) {
      //税率
      if (residueInvoice && !residueInvoice.length) {
        targetFormValue.taxRate = ''
      }
      if (residueInvoice && residueInvoice.length === 1) {
        const { taxRate } = getInvoiceAmount(residueInvoice[0])
        targetFormValue.taxRate = taxRate
      }
    }
    const noWrite = residueInvoice && residueInvoice.length > 0
    // 计算税额、不计税金额、可抵扣税额、价税合计
    taxs.forEach(({ defaultValue, name }) => {
      const v = get(defaultValue, 'value')
      let value = getAcountKey(v)
      if (noWrite) {
        if (v === 'taxTotal') {
          targetFormValue[name] = new MoneyMath(formValue[name])
            .minus(invoiceAmount.taxAmount)
            .minus(invoiceAmount.noTaxAmount).value
        } else {
          targetFormValue[name] = new MoneyMath(formValue[name]).minus(invoiceAmount[value]).value
        }
      } else {
        targetFormValue[name] = undefined
      }
    })

    return targetFormValue
  }
}

const funcMap = {
  add: add,
  minus: minus
}

export default function updateMoney({ targetFormValue, invoice, action, formFormValue, residueInvoice, taxs }) {
  const func = funcMap[action]
  return func({ targetFormValue, invoice, formFormValue, residueInvoice, taxs })
}

const invoiceDateMap = {
  system_发票主体: 'E_system_发票主体_发票日期', //@i18n-ignore
  system_出租车票: 'E_system_出租车票_上车时间', //@i18n-ignore
  system_火车票: 'E_system_火车票_乘车时间', //@i18n-ignore
  system_过路费发票: 'E_system_过路费发票_时间', //@i18n-ignore
  system_客运汽车发票: 'E_system_客运汽车发票_时间', //@i18n-ignore
  system_航空运输电子客票行程单: 'E_system_航空运输电子客票行程单_乘机时间', //@i18n-ignore
  system_消费小票: 'E_system_消费小票_时间', //@i18n-ignore
  system_机打发票: 'E_system_机打发票_时间', //@i18n-ignore
  system_医疗发票:'E_system_医疗发票_开票日期', //@i18n-ignore
  system_非税收入类票据:'E_system_非税收入类票据_开票日期', //@i18n-ignore
  system_其他: 'E_system_其他_日期' //@i18n-ignore
}

const invoicePriceMap = {
  system_定额发票: 'E_system_定额发票_金额', //@i18n-ignore
  system_出租车票: 'E_system_出租车票_金额', //@i18n-ignore
  system_发票主体: 'E_system_发票主体_价税合计', //@i18n-ignore
  system_发票明细: 'E_system_发票明细_金额', //@i18n-ignore
  system_火车票: 'E_system_火车票_金额', //@i18n-ignore
  system_过路费发票: 'E_system_过路费发票_金额', //@i18n-ignore
  system_客运汽车发票: 'E_system_客运汽车发票_金额', //@i18n-ignore
  system_航空运输电子客票行程单: 'E_system_航空运输电子客票行程单_金额', //@i18n-ignore
  system_消费小票: 'E_system_消费小票_金额', //@i18n-ignore
  system_机打发票: 'E_system_机打发票_金额', //@i18n-ignore
  system_医疗发票:'E_system_医疗发票_金额合计', //@i18n-ignore
  system_非税收入类票据:'E_system_非税收入类票据_金额合计', //@i18n-ignore
  system_其他: 'E_system_其他_金额' //@i18n-ignore
}

export function fnSortInvoice(invoices = [], isSort = true, sortType = '2', riskData) {
  // 默认按照发票时间降序
  switch (sortType) {
    // 按金额合计
    case '1':
      const invoiceArrByPrice = invoices.sort((a, b) => {
        const entityId1 = a.master.entityId
        const key1 = invoicePriceMap[entityId1]
        const price1 = Number(a.master.form[key1]?.standard)
        const entityId2 = b.master.entityId
        const key2 = invoicePriceMap[entityId2]
        const price2 = Number(b.master.form[key2]?.standard)
        return isSort ? price2 - price1 : price1 - price2
      })
      return invoiceArrByPrice
    // 按发票时间
    case '2':
      const quotaInvoice = invoices.filter(oo => oo.master.entityId === i18n.get('system_定额发票'))
      const invoiceArr = invoices
        .filter(oo => oo.master.entityId !== i18n.get('system_定额发票'))
        .sort((a, b) => {
          const entityId = a.master.entityId
          const date1 = invoiceDateMap[entityId]
          const entityId2 = b.master.entityId
          const date2 = invoiceDateMap[entityId2]
          return isSort ? b.master.form[date2] - a.master.form[date1] : a.master.form[date1] - b.master.form[date2]
        })
      return quotaInvoice.concat(invoiceArr)
    // 按风险数量
    case '3':
      const riskDataCount = {}
      riskData?.singleInvoiceRiskWarning?.map(v => {
        riskDataCount[v.invoiceId] = v.riskWarning.length
      })
      const invoiceArrByRiskCount = invoices.sort((a, b) => {
        const count1 = riskDataCount[a.invoiceId] || 0
        const count2 = riskDataCount[b.invoiceId] || 0
        return isSort ? count2 - count1 : count1 - count2
      })
      return invoiceArrByRiskCount
  }
}

export const fnSortInvoiceByMoney = (invoices = [], isSortMoney = true) => {
  return invoices.sort((cur, next) => {
    const cur_EntityId = cur.master.entityId
    const next_EntityId = next.master.entityId
    const cur_key = cur_EntityId !== 'system_发票主体' ? `E_${cur_EntityId}_金额` : `E_${cur_EntityId}_价税合计`
    const next_key = next_EntityId !== 'system_发票主体' ? `E_${next_EntityId}_金额` : `E_${next_EntityId}_价税合计`
    const cur_money = cur?.master?.form[cur_key]?.standard ?? 0
    const next_money = next?.master?.form[next_key]?.standard ?? 0
    return isSortMoney ? cur_money - next_money : next_money - cur_money
  })
}
