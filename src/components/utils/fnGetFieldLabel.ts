/**
 *  Created by pw on 2020/9/22 12:29 下午.
 */
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import { Fetch } from '@ekuaibao/fetch'

const languageMap = {
  'en-US': 'en',
  'zh-CN': 'cn'
}

export default function fnGetFieldLabel(field: GlobalFieldIF = {} as GlobalFieldIF): string {
  const currentLanguagePrefix = languageMap[Fetch.lang || Fetch.defaultLanguage]
  return field[`${currentLanguagePrefix}Label`] || i18n.get(field.label)
}

export function getPlaceholder(
  field: GlobalFieldIF & { placeholder: string } = {} as GlobalFieldIF & { placeholder: string }
): string {
  const currentLanguagePrefix = languageMap[Fetch.lang || Fetch.defaultLanguage]
  return field[`${currentLanguagePrefix}Placeholder`] || field.placeholder || ''
}
