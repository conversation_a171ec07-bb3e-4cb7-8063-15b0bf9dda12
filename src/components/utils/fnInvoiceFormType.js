import { invoiceOptions } from '../../lib/InvoiceUtil'
import { get } from 'lodash'

/**
 *  Created by gym on 2018/8/24 下午7:39.
 */
export function fnInvoiceFormType(detail) {
  if (get(detail, 'feeTypeForm.invoiceForm')) return
  const {
    specificationId: { components }
  } = detail
  let invoiceType = components.find(v => v.field === 'invoiceForm')
  if (invoiceType) {
    if (!invoiceType.editable) {
      detail.feeTypeForm.invoiceForm = { type: 'noWrite' }
    } else {
      let arr = invoiceOptions(invoiceType.invoiceType) || []
      const defaultInvoiceType = get(invoiceType, 'invoiceType.defaultInvoiceType', '')
      let type = defaultInvoiceType || (arr.length && arr[0].type)
      if (type) {
        detail.feeTypeForm.invoiceForm = type === 'unify' ? { type: type, invoiceCorporationId: 'defaults' } : { type }
      }
    }
  }
}
