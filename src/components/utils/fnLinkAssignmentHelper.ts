import { dimensionValueVisible } from './fnDimension'
import { cloneDeep, get } from 'lodash'
import { checkStaffDataRange } from './fnCheckStaffDataRange'
import { app as api } from '@ekuaibao/whispered'
import { getAssignmentRuleById } from '../../plugins/bills/bills.action'
import { GlobalFieldIF, DatalinkIF, ComponentIF } from '@ekuaibao/ekuaibao_types'

interface IAssignmentRuleField {
  sourceField: string
  targetField: string
}

interface IAssignmentRule {
  fields: IAssignmentRuleField[]
}

interface IField extends GlobalFieldIF {
  assignmentRule: IAssignmentRule
  referenceData: DatalinkIF
}

interface IParams {
  updateDisableField?: boolean
  updateValueForNullField?: boolean
}

export const linkAssignmentFieldsToValue = async (
  linkAssignmentField: IField,
  data: Record<string, any>,
  templateFields: ComponentIF[],
  formValue: Record<string, any>,
  params: IParams = {}
) => {
  const { assignmentRule, referenceData } = linkAssignmentField
  if (!data) {
    return {}
  }

  const { fields = [] } = assignmentRule
  const valueMap: Record<string, any> = {}
  // 是否只更新禁用字段
  const { updateDisableField = false, updateValueForNullField = false } = params

  const templateFieldMap = templateFields.reduce((map, item) => {
    map[item.field] = item
    return map
  }, {})

  for (const item of fields) {
    const fieldItem = templateFieldMap[item.targetField]
    if (!fieldItem) {
      continue
    }

    const fnUpdateValue = async () => {
      const value = data.dataLink[item.sourceField]
      if (value !== undefined && value !== null) {
        if (fieldItem.type === 'number') {
          valueMap[fieldItem.name] = `${value * 1}`
        } else if (fieldItem.type.startsWith('ref:basedata.Dimension')) {
          if (value) {
            value.needCheckValue = true
          }
          valueMap[fieldItem.name] = dimensionValueVisible(value, formValue.submitterId) ? value : undefined
        } else if (fieldItem.type === 'dataLink') {
          const id = get(value, 'data.dataLink.id')
          if (id && fieldItem.isLinkageAssignment) {
            await handleDataLink(fieldItem, id, referenceData, templateFields, valueMap, formValue)
          }
          valueMap[fieldItem.name] = { ...value, id }
        } else if (fieldItem.type === 'payeeInfo') {
          const payeeValue = formValue['payeeId']
          valueMap[fieldItem.name] = payeeValue && payeeValue.multiplePayeesMode ? payeeValue : value
        } else if (fieldItem.type === 'text' && value?.location) {
          valueMap[fieldItem.name] = value?.name || value?.address
        } else if (fieldItem.type === 'ref:organization.Staff' || fieldItem.type === 'list:ref:organization.Staff') {
          const rangeValue = checkStaffDataRange(fieldItem, value)
          if (Array.isArray(rangeValue)) {
            if (rangeValue.length) {
              valueMap[fieldItem.name] = rangeValue
            }
          } else if (rangeValue) {
            valueMap[fieldItem.name] = rangeValue
          }
        } else {
          valueMap[fieldItem.name] = cloneDeep(value)
        }
      }

      if (
        value === undefined &&
        fieldItem.editable === false &&
        fieldItem.defaultValue &&
        fieldItem.defaultValue.type === 'none'
      ) {
        valueMap[fieldItem.name] = value
      }
    }

    const currentFormItemValue = get(formValue, fieldItem.name)
    // ，不能够编辑的字段直接赋值
    if (updateDisableField && !fieldItem.editable) {
      await fnUpdateValue()
      continue
    }
    // 能够编辑的字段如果为空也需要重新赋值
    if (updateValueForNullField && (currentFormItemValue === undefined || currentFormItemValue === null)) {
      await fnUpdateValue()
      continue
    }
    await fnUpdateValue()
  }

  return valueMap
}

const handleDataLink = async (
  fieldItem: IField,
  id: string,
  referenceData: DatalinkIF,
  templateFields: ComponentIF[],
  valueMap: Record<string, any>,
  formValue: Record<string, any>
) => {
  try {
    const res = await api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' })
    const resData = res.value.data
    if (referenceData.id !== resData.dataLink.entityId) {
      const rule = fieldItem.assignmentRule
      if (rule) {
        const nestedValueMap = await linkAssignmentFieldsToValue(
          { assignmentRule: rule, referenceData } as IField,
          resData,
          templateFields,
          formValue
        )
        Object.assign(valueMap, nestedValueMap)
      } else {
        const result = await api.dispatch(getAssignmentRuleById([resData.dataLink.entityId]))
        const ruleList = result.items
        if (ruleList && ruleList.length > 0) {
          const nestedValueMap = await linkAssignmentFieldsToValue(
            { assignmentRule: ruleList[0], referenceData } as IField,
            resData,
            templateFields,
            formValue
          )
          Object.assign(valueMap, nestedValueMap)
        }
      }
    }
  } catch (error) {
    console.error('Error in handleDataLink:', error)
  }
}
