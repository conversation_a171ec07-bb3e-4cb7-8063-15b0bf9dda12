/**
 *  Created by pw on 2022/9/23 10:26 PM.
 */
import { app } from '@ekuaibao/whispered'
export function fnFlowShowFields(plan) {
  const nodes = plan?.nodes ?? []
  const curNode =
    nodes?.length &&
    nodes.find(v => {
      return v?.id === plan?.taskId
    })
  const userId = app.getState()['@common'].userinfo?.data?.staff?.id
  if (curNode?.counterSigners?.length) {
    const isApporver = curNode?.counterSigners?.find(counterSigner => counterSigner?.signerId?.id === userId)
    if (!isApporver) {
      return { currentNodeShowFieldMap: {} }
    }
  }
  if (curNode?.approverId?.id !== userId) {
    const hasCountSinger = curNode?.counterSigners?.length && curNode?.counterSigners?.find(signer => signer?.signerId?.id === userId)
    if (!hasCountSinger){
      return { currentNodeShowFieldMap: {} }
    }
  }
  const showFileds = curNode?.config?.showFileds ?? []
  const currentNodeShowFieldMap =
    showFileds?.reduce((result, field) => {
      result[field] = field
      return result
    }, {}) || {}
  return { currentNodeShowFieldMap, isShowFileds: curNode?.config?.isShowFileds }
}
