import moment from 'moment'
import fnGetFieldLabel from './fnGetFieldLabel'
function fnFormatLinkFrom(obj) {
  const copyObj = { ...obj },
    newObj = {}
  for (const key in copyObj) {
    if (Object.hasOwnProperty.call(copyObj, key)) {
      let element = copyObj[key]
      typeof element === 'string' && element.startsWith('[') && element.endsWith(']') && (element = JSON.parse(element))
      newObj[key.split('_').pop()] = element
    }
  }
  return newObj
}

function fnFormatTimestamp(timestamp) {
  const dateFormat = 'YYYY-MM-DD'
  return moment(timestamp).format(dateFormat)
}

export function buildDataLinkEditsStr(field, value, allTemplate, isTable) {
  let entityName = '',
    val = '无'
  if (!(value && value[0])) return ''
  const { dataLinkTemplateId } = value && value[0]
  entityName =
    allTemplate && allTemplate.length
      ? allTemplate.find(item => item.templateId === dataLinkTemplateId).entity.name
      : ''
  let dataLinkForm = {}
  value && value[0] && (dataLinkForm = value[0].dataLinkForm)
  const formatDataLinkForm = fnFormatLinkFrom(dataLinkForm)
  if (formatDataLinkForm['出发地']?.length && formatDataLinkForm['目的地']?.length) {
    val = `${entityName} ${formatDataLinkForm['出发地'][0]?.label}-${
      formatDataLinkForm['目的地'][0]?.label
    } ${fnFormatTimestamp(formatDataLinkForm['行程日期'])}`
  } else if (formatDataLinkForm['住宿地']?.length) {
    val = `${entityName} ${formatDataLinkForm['住宿地'][0]?.label} ${fnFormatTimestamp(
      formatDataLinkForm['入住日期']
    )}-${fnFormatTimestamp(formatDataLinkForm['离店日期'])}`
  }
  return !isTable ? i18n.get(`{__k0}：{__k1}`, { __k0: fnGetFieldLabel(field), __k1: val }) : val
}

export default buildDataLinkEditsStr
