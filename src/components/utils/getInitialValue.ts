import get from 'lodash/get'
import { isObject, isArray } from '@ekuaibao/helpers'
import { invoiceOptions } from '../../lib/InvoiceUtil'
import { standardValueMoney } from '../../lib/misc'
import { checkDefaultDeptValue, constantValue, handleGetDataById, lastSelectValue, predefineValue } from './fnInitalValue'
import fnPredefine4Date from './fnPredefine4Date'
import fnPredefine4DateRange from './fnPredefine4DateRange'
import fnPredefine4Number from './fnPredefine4Number'
import { app } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'


function getDepartmentValue(component, field, lastChoice, submitterId) {
  const constVal = constantValue(component)
  if (constVal) {
    return checkDefaultDeptValue({ id: constVal }, component)
  }
  const lastVal = lastSelectValue({ ...component, ...field }, lastChoice)
  if (lastVal) {
    return checkDefaultDeptValue({ id: lastVal }, component)
  }

  const { defaultValue = {} } = component
  const type = get(component, 'defaultValue.type', '')
  if (type === 'predefine' && !['submit.requisition', 'lastselect'].includes(defaultValue.value)) {
    const defaultDepartment = submitterId.defaultDepartment
    if (defaultDepartment) {
      const { id, name, code } = defaultDepartment
      return checkDefaultDeptValue({ id, name, code }, component)
    }
    return checkDefaultDeptValue(defaultDepartment, component)
  }
}

function getRefDimensionValue(component, field, lastChoice, submitterId) {
  return new Promise(resolve => {
    const constValue = constantValue(component)
    if (constValue) {
      return handleGetDataById(field, constValue).then(data => {
        return resolve(data)
      })
    }
    const lastVal = lastSelectValue({ ...component, ...field }, lastChoice)
    if (lastVal) {
      return handleGetDataById(field, lastVal).then(data => {
        resolve(data)
      })
    }
    const predefineVal = predefineValue({...component, ...field}, submitterId)
    if (predefineVal) {
      return resolve(predefineVal)
    }
    resolve()
  })
}

function getRefValue(component, field, lastChoice, submitterId) {
  if (!isHongShanTestingEnterprise(Fetch.ekbCorpId) && Fetch.ekbCorpId !== 'wIW3Izfc6F0d20' && Fetch.ekbCorpId !== 'ID01nNhizKrbZR') {
    if (component && component.dependence && component.dependence.length) {
      return Promise.resolve()
    }
  }
  // if (component && component.dependence && component.dependence.length) {
  //   return Promise.resolve()
  // }
  const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
  return new Promise(resolve => {
    if (entity === 'organization.Department') {
      resolve(getDepartmentValue(component, field, lastChoice, submitterId))
    } else if (
      entity.startsWith('basedata.Dimension.') ||
      entity === 'organization.Staff' ||
      entity.startsWith('basedata.Enum.') ||
      entity === 'basedata.Settlement'
    ) {
      getRefDimensionValue(component, field, lastChoice, submitterId).then(data => {
        resolve(data)
      })
    } else {
      resolve()
    }
  })
}

async function getInvoiceDefaultValue(component) {
  if (!component.editable) {
    return { type: 'noWrite' }
  } else {
    const arr = invoiceOptions(component.invoiceType) || []
    const defaultInvoiceType = get(component, 'invoiceType.defaultInvoiceType', '')
    const type =defaultInvoiceType || arr.length && arr[0].type
    if (type) {
      if (type === 'unify') {
        const ids = component?.invoiceType?.unify?.limit ? component?.invoiceType?.unify?.invoiceCorporation : undefined
        if ((ids && ids.length) || !component?.invoiceType?.unify?.limit) {
          const res = await app.invokeService('@bills:get:invoice:corporation', ids)
          if (res?.items?.length && res?.items?.length === 1) {
            const invoiceCorporation = res.items[0]
            return { type: type, invoiceCorporationId: invoiceCorporation.id, invoiceCorporation }
          }
        }
      }
      return type === 'unify' ? { type: type, invoiceCorporationId: 'defaults' } : { type }
    }
    return undefined
  }
}

function getSwitcherInitialValue(component) {
  let { defaultValue } = component
  if (defaultValue && defaultValue.type === 'constant') {
    return defaultValue.value
  }
  return undefined
}

export default function getDefaultValue(component, field, lastChoice, submitterId) {
  const type = get(field, 'dataType.type')
  return new Promise(async resolve => {
    if (type === 'number') {
      return resolve(fnPredefine4Number({ ...component, ...field }))
    }
    if (type === 'date') {
      return resolve(fnPredefine4Date(component))
    }

    if (type === 'dateRange') {
      return resolve(fnPredefine4DateRange(component))
    }

    if (type === 'ref' || (type === 'list' && get(field, 'dataType.elemType.type') === 'ref')) {
      return getRefValue(component, field, lastChoice, submitterId).then(data => {
        const entity = get(field, 'dataType.elemType.entity', '')
        if (type === 'list' && entity.startsWith('basedata.Dimension')) {
          resolve(isObject(data) ? [data.id] : data)
        } else {
          resolve(data)
        }
      })
    }

    if (type === 'invoice') {
      const invoiceDefaultValue = await getInvoiceDefaultValue(component)
      return resolve(invoiceDefaultValue)
    }

    if (type === 'boolean') {
      return resolve(getSwitcherInitialValue(component))
    }

    resolve()
  })
}
