/**
 *  Created by pw on 2023/4/6 16:19.
 */
export function fnCheckLimitTripOrder(billSpecification) {
  let limitTripOrders = []
  const applyConfigs = billSpecification.configs.filter(config => config.ability === 'apply')
  if (applyConfigs?.length) {
    const applyConfig = applyConfigs.find(applyConfig => !!applyConfig.limitTripOrder?.length)
    if (applyConfig) {
      const { hasPersonPay, hasEnterprisePay, hasHybridPay } = judgeLimitOrder(applyConfig?.limitTripOrder)
      if (hasPersonPay && hasEnterprisePay && hasHybridPay) {
        // 三种方式都限制了,没有订单导入的入口
        limitTripOrders = ['ALL']
      } else {
        limitTripOrders = applyConfig?.limitTripOrder
      }
    }
    return limitTripOrders
  }
  return []
}

export function limitTripOrderFilter(limitTripOrders: string[] = [], entityInfo: any = {}) {
  if (!limitTripOrders.length) {
    return []
  }
  const entityStr = `form.E_${entityInfo.id}`
  const { hasPersonPay, hasEnterprisePay, hasHybridPay } = judgeLimitOrder(limitTripOrders)
  const filters = []
  if (hasPersonPay && hasEnterprisePay) {
    // 禁止个人,禁止企业,允许混合
    filters.push(`${entityStr}_个人支付.standard>0`)
    filters.push(`${entityStr}_企业支付.standard>0`)
  } else if (hasPersonPay && hasHybridPay) {
    // 禁止个人,允许企业,禁止混合
    filters.push(`${entityStr}_个人支付.standard==0`)
    filters.push(`${entityStr}_企业支付.standard>0`)
  } else if (hasEnterprisePay && hasHybridPay) {
    // 允许个人,禁止企业,禁止混合
    filters.push(`${entityStr}_个人支付.standard>0`)
    filters.push(`${entityStr}_企业支付.standard==0`)
  } else if (hasPersonPay) {
    // 禁止个人,允许企业,允许混合
    filters.push(
      `(${entityStr}_个人支付.standard==0&&${entityStr}_企业支付.standard>0) || (${entityStr}_个人支付.standard>0&&${entityStr}_企业支付.standard>0)`
    )
  } else if (hasEnterprisePay) {
    // 允许个人,禁止企业,允许混合
    filters.push(
      `(${entityStr}_个人支付.standard>0 && ${entityStr}_企业支付.standard==0) || (${entityStr}_个人支付.standard>0 && ${entityStr}_企业支付.standard>0)`
    )
  } else if (hasHybridPay) {
    // 允许个人,允许企业,禁止混合
    filters.push(
      `(${entityStr}_个人支付.standard>0 && ${entityStr}_企业支付.standard==0) || (${entityStr}_个人支付.standard==0 && ${entityStr}_企业支付.standard>0)`
    )
  }
  return filters
}

function judgeLimitOrder(limitTripOrders: string[] = []) {
  let hasPersonPay = false
  let hasEnterprisePay = false
  let hasHybridPay = false
  limitTripOrders.forEach(limit => {
    if (limit === 'PERSON_PAY') {
      hasPersonPay = true
    }
    if (limit === 'ENTERPRISE_PAY') {
      hasEnterprisePay = true
    }
    if (limit === 'HYBRID_PAY') {
      hasHybridPay = true
    }
  })
  return { hasPersonPay, hasEnterprisePay, hasHybridPay }
}

/**
 * https://hose2019.feishu.cn/wiki/wikcnZoF0mLO4ID8iD0q5a7GzNe
 * 请求参数filterBy的值为：
 * - 情况1：不展示【导入差旅订单】按钮，【导入】订单列表不发起/searchDataLinks请求
 * 过滤结果：无
 * - 情况2：(form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0)&&(active==true)
 * 过滤结果：订单3
 * - 情况3：(form.E_de0f4858891f002f5fc0_个人支付.standard==0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0)&&(active==true)
 * 过滤结果：订单1
 * - 情况4：(form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard==0)&&(active==true)
 * 过滤结果：订单2
 * - 情况5：((form.E_de0f4858891f002f5fc0_个人支付.standard==0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0)||(form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0))&&(active==true)
 * 过滤结果：订单1、订单3
 * - 情况6：((form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard==0)||(form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0))&&(active==true)
 * 过滤结果：订单2、订单3
 * - 情况7：((form.E_de0f4858891f002f5fc0_个人支付.standard>0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard==0)||(form.E_de0f4858891f002f5fc0_个人支付.standard==0)&&(form.E_de0f4858891f002f5fc0_企业支付.standard>0))&&(active==true)
 * 过滤结果：订单1、订单2
 */
