import loadable, { LoadableComponent } from '@loadable/component'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

const Text = loadableWithDescriptor(() => import('./dynamic/Text'), {
  type: 'text'
})
const Select = loadableWithDescriptor(() => import('./dynamic/Select'), {
  type: 'select'
})
const CheckBoxGroup = loadableWithDescriptor(() => import('./internal/CheckBoxGroup'), {
  type: 'checkbox-group'
})
const AccountPeriod = loadableWithDescriptor(() => import('./supplier/AccountPeriod'), {
  type: 'accountPeriod'
})

const RuleList = loadableWithDescriptor(() => import('./supplier/RuleList'), {
  type: 'ruleList'
})
const AccountPaymentSetting = loadableWithDescriptor(() => import('./supplier/AccountPaymentSetting'), {
  type: 'accountPaymentSetting'
})

const BalanceAdjustSetting = loadableWithDescriptor(() => import('./supplier/BalanceAdjustSetting'), {
  type: 'balanceAdjustSetting'
})
export const supplierConfigEditable = [
  Text,
  Select,
  CheckBoxGroup,
  AccountPeriod,
  RuleList,
  AccountPaymentSetting,
  BalanceAdjustSetting
]
