import { driver } from 'driver.js'
import 'driver.js/dist/driver.css'
import { Config, DriveStep } from 'driver.js/dist/driver.js.d'
import './index.less'

interface Props {
  driverSteps: DriveStep[]
  optionsConfig?: Config
  customsClass?: string
  isHeaderDark?: boolean // 为后续首页迁移留的口子，先兼容下
}
// 此组件作为初始化配置使用，调用地方可重写该所有配置
export default function useLayoutDriver({ driverSteps, optionsConfig = {}, isHeaderDark = false }: Props) {
  const options: Config = {
    allowClose: false,
    allowKeyboardControl: false,
    stagePadding: driverSteps?.length || 0,
    popoverClass: 'hose-guid-popover',
    showProgress: true,
    progressText: '{{current}} / {{total}}',
    onPopoverRender: (popover, { config, state }) => {
      // 当前产品中设计无上一步的步骤说明，所以这一块暂时隐藏disabled 状态
      popover.previousButton.removeAttribute('disabled')
      popover.previousButton.classList.remove('driver-popover-btn-disabled')
    },
    nextBtnText: i18n.get('下一步'),
    prevBtnText: i18n.get('稍后再说'),
    doneBtnText: i18n.get('知道了'),
    ...optionsConfig
  }
  if (isHeaderDark) {
    options.overlayColor = 'var(--eui-decorative-sub-500)'
  }

  const driverObj = driver({ ...options, steps: driverSteps })
  driverObj.drive()

  return driverObj
}
