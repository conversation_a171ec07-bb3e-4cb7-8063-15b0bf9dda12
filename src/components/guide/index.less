.hose-guid-popover{
    padding: 12px 16px;
    .driver-popover-title{
        color: var(--eui-primary-pri-500);
        margin-top: 20px;
        font-size: 18px;
        line-height: 26px;
        font-weight: 500;
    }
    .driver-popover-progress-text{
        position: absolute;
        top: 14px;
        font-size: 12px;
        color: var(--eui-text-placeholder);
    }
    .driver-popover-description{
        font-size: 14px;
        color: var(--eui-text-title);
    }
    .driver-popover-footer button{
        all:initial;
        text-shadow:none;
        font: var(--eui-font-head-r1);
        padding: 4px 8px;
        border-radius: var(--eui-radius-s);
        border:1px solid var(--eui-line-border-component);
        cursor: pointer;
        height: 16px;
        line-height: 12px;
        text-align: center;
        font-size: 12px;
    }
    .driver-popover-footer .driver-popover-prev-btn{
        display: block;
        color: var(--eui-text-title);
        border-color: var(--eui-line-border-component);
        border-radius: var(--eui-radius-s);
    }
    .driver-popover-footer .driver-popover-next-btn{
        background: var(--eui-primary-pri-500);
        color: var(--eui-static-white);
        border: 1px solid var(--eui-primary-pri-500);
        &:hover,&.focus{
            background-color:var(--eui-primary-pri-400);
        }
    }
    .driver-popover-navigation-btns button+button{
        margin-left: 8px;
    }
    .driver-popover-btn-disabled{
        opacity:initial;
        pointer-events: none;
    }
}

#BillMoreInfoGuide{
    overflow:visible !important;
}
.driver-active-element{
    .warning-item.isHover .eui-button{
        display: none;
    }
}

