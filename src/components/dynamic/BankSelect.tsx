/**************************************************
 * Created by zhaohuabing on 2020/6/09
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { Select } from 'antd'
const { Option } = Select
import { debounce, cloneDeep } from 'lodash'
import './BankSelect.less'

interface IProps {
  value: any
  field: any
  bankList?: any[]
  mode?: string
  allowClear?: boolean
  onChange(params: any): void
  getBankList?(params: any): any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'bankSelect'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class BankSelect extends PureComponent<IProps, any> {
  allList = []
  constructor(props) {
    super(props)
    this.state = {
      bankList: this.props.bankList || []
    }
    this.allList=cloneDeep(this.props.bankList || [])
  }

  componentDidMount(): void {
    if (!this.props.bankList || !this.props.bankList.length) {
      this.fnGetBankList('')
    }
  }

  fnGetBankList = searchKey => {
    const { getBankList } = this.props
    getBankList &&
      getBankList({ searchKey }).then(res => {
        if (res && res.items) {
          this.setState({ bankList: res.items })
          this.allList = cloneDeep(res.items)
        }
      })
  }

  handleSelect = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  handleSearch = debounce(searchKey => {
    this.getFilter(searchKey)
  }, 100)

  getFilter = searchKey => {
    if (this.allList && this.allList.length > 0) {
      const newSearch = this.allList.filter(i => {
        return i?.accountName?.includes(searchKey) || i?.name?.includes(searchKey) || i?.accountNo?.includes(searchKey)
      })
      this.setState({
        bankList: newSearch || []
      })
    }
  }

  handleBlur = () => {
    this.setState({
      bankList: this.allList || []
    })
  }

  getChildren = () => {
    const { bankList } = this.state
    return bankList.map(line => {
      const { accountName, id, name } = line

      return (
        <Option value={id} label={name || accountName} key={id}>
          {this.getOption(line)}
        </Option>
      )
    })
  }

  getOption = line => {
    const { icon, accountName, accountNo, name } = line
    return (
      <div className="bank-detail-item">
        <img className="bank-img" src={icon} alt="" />
        <div className="title-number">
          <div className="bank-title">{name || accountName}</div>
          <div className="bank-number">{accountNo}</div>
        </div>
      </div>
    )
  }

  render() {
    const {
      value,
      mode,
      allowClear,
      field: { placeholder },
      ...others
    } = this.props
    return (
      <div className="bank-select-wrapper">
        <Select
          value={value}
          allowClear={allowClear || true}
          showSearch={mode !== 'multiple'}
          onSelect={this.handleSelect}
          onSearch={this.handleSearch}
          style={{ width: '100%' }}
          placeholder={placeholder}
          dropdownClassName="bank-detail-wrapper"
          optionLabelProp="label"
          filterOption={false}
          {...others}
          onBlur={this.handleBlur}
          dropdownStyle={{ maxHeight: 260, overflowY: 'auto' }}
          getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        >
          {this.getChildren()}
        </Select>
      </div>
    )
  }
}
