@import '~@ekuaibao/eui-styles/less/token.less';
.split-detail {
  max-width: 732px;
  padding-bottom: 24px;
  :global {
    .split-group-label {
      .font-size-2;
      .font-weight-2;
      color: rgba(29,43,61,.75);
      margin-bottom: 4px;
      .split-label-optional {
        margin-right: 4px;
        font-size: 12px;
        color: #ff7c7c;
      }
    }
    .split-group-value {
      .font-size-2;
      .font-weight-2;
      color: rgba(29,43,61,.75);
      margin-bottom: 8px;
    }
    .split-group-title {
      .font-size-2;
      .font-weight-3;
      margin-bottom: @space-4;
    }
    .desc {
      color: @color-black-3;
      margin-bottom: @space-4;
      .font-size-2;
    }
    .error {
      white-space: pre-wrap;
      text-align: left;
      word-break: break-all;
      color: #ff7c7c;
    }
    .split-header {
      display: flex;
      align-items: center;
      .split-group-select {
        flex: 1;
        margin-right: 8px;
      }
    }
    
    .detals-count {
      display: flex;
      justify-content: space-between;
      .font-size-2;
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      background: @color-bg-3;
      .detals-total {
        color: rgba(29,43,61,0.5);
        padding-right: 8px;
      }
    }
    .table-c {
      margin-top: 8px;
      border-top: 1px solid #ddd;
      max-height: 400px;
      height: auto;
      overflow-y: auto;
      .tableWrapper {
        td .header-caption {
          white-space: normal;
        }
        &:before {
          content: ' ';
          position: absolute;
          border-top: 1px solid #ddd;
          width: auto;
          right: 30px;
          left: 30px;
          z-index: 100;
        }
      }
    }
  }
}
