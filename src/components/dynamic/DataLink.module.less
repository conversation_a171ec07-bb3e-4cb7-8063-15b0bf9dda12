@import '~@ekuaibao/eui-styles/less/token.less';

.dataLink-input-wrapper {
  :global {
    .card-bottom {
      .font-size-2;
      height: 22px;
      margin-top: @space-4;
      font-weight: 400;
      color: @color-brand-2;
      span {
        cursor: pointer;
        &:nth-child(1) {
          margin-right: @space-6;
        }
      }
    }
    .card-list{
      > div{margin-bottom:8px;}
      .card-bottom{
        margin-block: 0px;
      }
      &-simple{
        white-space: normal;
        word-break: break-all;
      }
    }
    .datalink-select {
      // height: 60px;
      .datalink-placeholder {
        height: 30px;
        padding-top: 8px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.3);
        line-height: 20px;
        max-width: 600px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .ant-input {
      cursor: pointer;
    }
  }
}
