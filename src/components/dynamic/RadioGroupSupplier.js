import React, { PureComponent, Fragment } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RadioGroupSupplier.module.less'
import { required } from '../validator/validator'
const RadioGroup = Radio.Group

@EnhanceField({
  descriptor: {
    type: 'radio-group-supplier'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class RadioGroupSupplier extends PureComponent {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    const { value, field, onChange, bus } = this.props
    const { defaultValue } = field
    if (value === undefined) {
      const defVal = defaultValue && defaultValue.value
      bus.emit('openReconciliation:changed', { openReconciliation: true, sourceType: defVal === 'true' })
      onChange && onChange(defVal)
    }
  }

  handleChange = e => {
    let val = e.target.value
    let { onChange, bus } = this.props
    bus.emit('openReconciliation:changed', { openReconciliation: true, sourceType: val === 'true' })
    onChange && onChange(val)
  }

  render() {
    let { field, value, bus, form, isEdit } = this.props
    let { defaultValue, name, style = {}, showForm, size, tags, editable } = field
    let defVal = defaultValue && defaultValue.value
    value = value !== undefined ? value : defVal
    return (
      <div data-cy={`ekb-radio-group-${field.name}`}>
        <RadioGroup
          className={styles['radio-group-wrapper']}
          disabled={!editable}
          value={value}
          onChange={this.handleChange}
          style={style}
        >
          {tags.map((el, key, arr) => {
            const radioStyle =
              showForm === 'horizontal'
                ? {}
                : {
                    display: 'block',
                    marginBottom: key === arr.length - 1 ? '0' : '10px'
                  }
            return (
              <Radio key={key} style={radioStyle} value={el.value} className={size}>
                {el.label}
              </Radio>
            )
          })}
        </RadioGroup>
        {!isEdit && <div style={{ color: 'rgba(250,150,42,1)' }}>{i18n.get('创建后不可修改')}</div>}
      </div>
    )
  }
}
