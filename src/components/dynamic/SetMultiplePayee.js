import './SetMultiplePayee.less'
import React from 'react'
import { Checkbox, Radio, Tooltip } from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import classnames from 'classnames'

export const defaultMode = {
  detail: 'detailsOnly',
  money: 'moneyOnly',
  payee: 'payeeOnly',
}

export default class SetMultiplePayee extends React.PureComponent {
  constructor(props) {
    super(props)
    const { multiplePayeesMode, payeePayPlan, payPlanMode } = props.value || {}
    this.state = {
      checked: !!multiplePayeesMode,
      radio: payeePayPlan ? defaultMode.payee : payPlanMode ? defaultMode.money : defaultMode.detail,
    }
  }

  onChange = e => {
    const checked = e.target.checked
    this.setState({ checked }, () => {
      const radios = this.getRadios()
      this.onRadioChange({ target: { value: radios[0].value }})
    })
  }

  onRadioChange = e => {
    const radio = e.target.value
    this.setState({ radio })
    this.props.onChange({
      multiplePayeesMode: this.state.checked,
      payPlanMode: radio === defaultMode.money,
      payeePayPlan: radio === defaultMode.payee,
    })
  }

  getRadios = () => {
    const { payConfig } = this.props
    const radios = this.payees.filter(item => payConfig?.[item.value])
    return radios.length ? radios : [this.payees[0]]
  }

  payees = [
    {
      value: defaultMode.detail,
      label: i18n.get('按明细'),
      tooltip: i18n.get('请在“费用明细”中填写收款信息。'),
    },
    {
      value: defaultMode.money,
      label: i18n.get('按金额'),
      tooltip: i18n.get('请在“支付计划”中填写收款信息。'),
    },
    {
      value: defaultMode.payee,
      label: i18n.get('按收款信息汇总明细金额'),
      tooltip: i18n.get('选择“按收款信息汇总明细金额”后，费用明细中收款人相同的将被合并为一条支付计划列示。'),
    }
  ]

  render() {
    const { checked, radio } = this.state
    const { disabled } = this.props
    const radios = this.getRadios()

    return (
      <div className="setMultiplePayee">
        <Checkbox
          className={classnames('setMultipleCheck', { checked })}
          checked={checked}
          disabled={disabled}
          onChange={this.onChange}
        >
          {i18n.get('是否为多收款人')}
        </Checkbox>
        {checked && radios.length > 1 && (
          <Radio.Group
            buttonStyle="solid"
            className="setMultipleRadio"
            value={radio}
            disabled={disabled}
            onChange={this.onRadioChange}
          >
            {radios.map((item) => (
              <Radio key={item.value} value={item.value}>
                {item.label}
                <Tooltip title={item.tooltip} >
                  <OutlinedTipsInfo className="help-icon" />
                </Tooltip>
              </Radio>
            ))}
          </Radio.Group>
        )}
      </div>
    )
  }
}
