/**************************************************
 * Created by nanyuanting<PERSON> on 12/07/2017 16:53.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { includes } from 'lodash'
import { ENUM_TYPES } from '../consts'
import { formatLang } from '../../lib/lib-util'
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return includes(ENUM_TYPES, type)
    }
  },
  wrapper: wrapper(true)
})
export default class RefEnum extends PureComponent {
  render() {
    let { value } = this.props
    return <span>{value && value[formatLang()] ? `${value[formatLang()]}（${value.code}）` : i18n.get('无')}</span>
  }
}
