import React, { memo, useState, useEffect, useMemo, lazy, Suspense } from 'react'
import { AsyncComponentProps, HABAuth } from './type'
import { Spin, ErrorBlock } from '@hose/eui'
import { Fetch } from '@ekuaibao/fetch'
import { app } from '@ekuaibao/whispered'

// Loading
const Loading = () => (
  <div className="hab-widget-loading">
    <Spin text="加载中" size="large" direction="vertical" />
  </div>
)

export const ErrorBoundaries = () => (
  <div className="hab-widget-error">
    <ErrorBlock status="loadfailed" title="组件加载失败，请稍后再试" />
  </div>
)

// 异步组件加载器
export const HABAsyncComponent = memo(({ field, ...otherProps }: AsyncComponentProps) => {
  const [loading, setLoading] = useState(true)
  const [reference, setReference] = useState<{
    url: string
    bundleId: string
    platformId: string
  } | null>(null)
  const [habAuth, setHabAuth] = useState<HABAuth | null>(null)

  useEffect(() => {
    if (field?.reference) {
      setReference(field?.reference)
      setLoading(false)
    } else {
      Fetch.GET(`/api/v1/datalink/formExtend/$${field.referenceData}`).then(res => {
        setReference(res.value)
        setLoading(false)
      })
    }
  }, [field])

  // 获取 HAB 权限标识
  useEffect(() => {
    if (!reference || habAuth) {
      return
    }

    Fetch.GET('/api/engine/hab/permission/runtime', { appId: reference?.platformId })
      .then(res => {
        setHabAuth(res?.data)
      })
      .catch(err => {
        console.log('HAB权限标识获取失败', err)
      })
  }, [reference?.platformId])

  const HABComponent = useMemo(() => {
    return lazy(() => {
      return loadComponent(reference.url, reference.bundleId, otherProps)
    })
  }, [reference])

  if (loading) {
    return <Loading />
  }

  if (reference?.bundleId && reference?.url) {
    return (
      <Suspense fallback={<Loading />}>
        <HABComponent {...otherProps} habAuth={habAuth} isRuntime />
      </Suspense>
    )
  } else {
    return null
  }

})

// 异步加载组件
async function loadComponent(url: string, bundleId: string, otherProps?: any) {
  if (!url || !bundleId) {
    throw new Error(`url:${url}, bundleId:${bundleId}, 其中一项为空`)
  }
  const staff = app.getState()['@common'].userinfo?.staff
  window.__HAB_BaseURL = '/api/engine'
  window.__HAB_CorpId = Fetch.ekbCorpId
  window.__HAB_AcessToken = Fetch.accessToken
  window.__HAB_StaffId = staff.userId
  window.__HAB_DepartmentId = staff.defaultDepartment
  window.__HAB_DataType = otherProps?.formType
  window.__HAB_FlowId = otherProps.flowId
  window.__HAB_FlowCode = otherProps.flowCode

  // 将异步加载的模块挂载到 window 的 __HAB_Components__ 上
  // __HAB_Components__ 是个 Map，key 为 url，value 为模块
  // 这样可以避免重复加载
  // 使用 url 作为 key，避免不同的发布有相同的 bundleId
  if (!window.__HAB_Components__) {
    window.__HAB_Components__ = new Map()
  }

  // 如果已经加载过，直接返回
  if (window.__HAB_Components__.has(url)) {
    return window.__HAB_Components__.get(url)
  }

  // 动态加载脚本
  await new Promise<void>((resolve, reject) => {
    const script = document.createElement('script')
    script.src = url // CDN上模块的URL
    script.onload = () => {
      resolve()
    }
    script.onerror = () => {
      reject(new Error(`Dynamic script loading failed: ${url}`))
    }
    document.head.appendChild(script)
  })

  // 使用全局变量获取模块
  if (!window[bundleId]) {
    throw new Error(`Module bundleId not available`)
  }

  // 缓存模块
  window.__HAB_Components__.set(url, window[bundleId])

  return window[bundleId]
}

export default HABAsyncComponent
