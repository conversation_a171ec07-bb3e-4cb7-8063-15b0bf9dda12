import React, { PureComponent, Fragment } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import styles from './RadioGroupSupplier.module.less'

@EnhanceField({
  descriptor: {
    type: 'radio-group-supplier'
  },
  wrapper: wrapper(true)
})
export default class RadioGroupSupplier extends PureComponent {
  constructor(props) {
    super(props)
  }

  render() {
    let { field, value, bus, form } = this.props
    let { defaultValue, name, style = {}, showForm, size, tags } = field
    let defVal = defaultValue && defaultValue.value
    value = value !== undefined ? value : defVal
    return (
      <div className={styles['radio-group-wrapper']}>
        <div className="label">{tags.find(el => el.value === value)?.label}</div>
      </div>
    )
  }
}
