/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Details } from '../../elements/puppet/details/index'
import { fnCheckPayerInfo } from '../../lib/fee-util'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { toJS } from 'mobx'
import { fnFlowHideFields, isHiddenFieldsInclude } from "../utils/fnHideFields";
import { provider, inject } from '@ekuaibao/react-ioc'
import { DetailTableVm } from '../../elements/puppet/details/TableMVC/DetailTableVm'
import TableManage from '../../elements/puppet/details/TableMVC/TableManage'
import { observer } from 'mobx-react'
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return type === 'requisitionDetails' || type === 'details'
    }
  }
})
@provider([DetailTableVm.NAME, DetailTableVm])
@observer
export default class DetailsReadonly extends PureComponent {
  state = { value: [], unvisibleCount: 0 }
  @inject(DetailTableVm.NAME) tableVm
  TM = undefined
  fnSetValueAfterFilter = (props, refresh) => {
    let { value, feeTypeVisibleList = [], showAllFeeType } = props
    if (!value) return
    //验证发票的PayerInfo
    fnCheckPayerInfo(value)
    let unvisibleCount = value.length
    if (showAllFeeType) {
      unvisibleCount = 0
    } else {
      if (feeTypeVisibleList.length > 0) {
        value = value.filter(el => feeTypeVisibleList.includes(el.feeTypeForm.detailId))
        unvisibleCount -= value.length
      } else {
        value = []
      }
    }
    if (refresh) {
      this.refreshTable(value)
    }
    this.setState({ value, unvisibleCount })
  }

  componentWillMount() {
    const staffId = getV(this.props, 'ownerId.id')
    api.dataLoader('@common.payerInfo').reload({ staffId })
  }

  componentDidMount() {
    const { bus } = this.props
    this.fnSetValueAfterFilter(this.props, false)
    api
      .dataLoader('@common.feetypeTableEdit')
      .load()
      .then(res => {
        if (res) {
          this.tableVm.loading = true
          this.tableVm.updateDetails = this.onDetailsChange
          this.TM = new TableManage(this.tableVm, bus, this.props)
          this.tableVm.TM = this.TM
        }
      })
    api.on('bill:detail:change', this.fnBillDetailChange)
  }

  componentWillUnmount() {
    api.un('bill:detail:change', this.fnBillDetailChange)
  }

  fnBillDetailChange = () => {
    if (this?.tableVm?.showType !== 'TABLE_NO_GROUP') return
    const { bus, flowId } = this.props
    bus.invoke('bills:update:for:detail:changed', { id: flowId }).then(res => {
      const details = res?.form?.details
      if (details) {
        this.refreshTable(details)
      }
    })
  }

  componentWillReceiveProps(np) {
    this.tableVm?.TM?.updateProps(np)
    if (
      this.props.value !== np.value ||
      this.props.feeTypeVisibleList !== np.feeTypeVisibleList ||
      this.props.showAllFeeType !== np.showAllFeeType
    ) {
      this.fnSetValueAfterFilter(np, true)
    }
  }
  refreshTable = details => {
    if (this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      console.log('🚀 ~ file: Details.js:474 ~ DetailsEditable ~ details:', details)
      this?.tableVm?.TM?.handleDataSource(details, true)
    }
  }
  handleLineClick = line => {
    let { bus, flowRulePerformLogs, external, isForbid, showAllFeeType, apportionVisibleList = [] } = this.props
    const { value } = this.state
    const idx = showAllFeeType ? line.idx : undefined
    bus.invoke(
      'element:details:line:click',
      { ...toJS(line), idx, showAllFeeType, apportionVisibleList },
      value,
      flowRulePerformLogs,
      external,
      isForbid
    )
  }

  handleUpdateBills = () => {
    const { flowId, bus } = this.props
    bus.invoke('bills:update:for:detail:changed', { id: flowId })
  }

  getReceivingConfig = () => {
    const { billSpecification = {} } = this.props
    const allowSelectionReceivingCurrency = billSpecification?.configs?.find(v => v.ability === 'pay')?.allowSelectionReceivingCurrency
    return allowSelectionReceivingCurrency
  }

  render() {
    let {
      hiddenFields,
      field,
      flowRulePerformLogs,
      baseDataProperties,
      external,
      isForbid,
      riskInfo,
      isInHistory,
      template,
      showAllFeeType,
      apportionVisibleList = [],
      billSpecification,
      inModal,
      dataSource,
      flowId,
      currentNode,
      riskData,
      bus
    } = this.props

    let { value, unvisibleCount } = this.state
    const { type, hide, name } = field
    if (hide && isHiddenFieldsInclude(hiddenFields, field)) {
      return <></>
    }
    //审批流是否设置了字段隐藏
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    if (flowHiddenFields.includes(name)) {
      return <></>
    }
    const t = type === 'details' ? (billSpecification.type === 'receipt' ? 'receipt' : 'expense') : 'requisition'
    let submitterId = this.props.form.getFieldValue('submitterId')
    //验证发票的PayerInfo
    fnCheckPayerInfo(value)
    return (
      <Details
        isEdit={false}
        submitterId={submitterId}
        type={t}
        bus={bus}
        tableVm={this.tableVm}
        TM={this.TM}
        currentNode={currentNode}
        template={template}
        field={field}
        flowRulePerformLogs={flowRulePerformLogs}
        onLineClick={this.handleLineClick}
        dataSource={value}
        baseDataProperties={baseDataProperties}
        external={external}
        isForbid={isForbid}
        riskInfo={riskInfo}
        isInHistory={isInHistory}
        unvisibleCount={unvisibleCount}
        showAllFeeType={showAllFeeType}
        apportionVisibleList={apportionVisibleList}
        fnUpdateBill={this.handleUpdateBills}
        billType={billSpecification.type}
        specificationId={billSpecification.id}
        inModal={inModal}
        flowId={flowId}
        riskData={riskData}
        allowSelectionReceivingCurrency={this.getReceivingConfig()}
      />
    )
  }
}
