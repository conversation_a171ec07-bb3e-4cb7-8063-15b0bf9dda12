/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import PayeeAccountCard from '../../elements/payee-account/payee-account-card'

@EnhanceField({
  descriptor: {
    type: 'payeeInfo'
  },
  wrapper: wrapper(true)
})
export default class PayeeInfo extends PureComponent {
  render() {
    let { value, emptyPlaceholder = i18n.get('无'), dataSource } = this.props
    if (!value) return emptyPlaceholder
    return (
      <PayeeAccountCard
        hiddenActions
        {...value}
        dynamicCard="readonly"
        style={{ marginBottom: 0 }}
        className="mw-560 clear_borderhover flex-1"
        receivingCurrencyNumCode={dataSource?.form?.receivingCurrency}
      />
    )
  }
}
