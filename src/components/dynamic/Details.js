/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { message, Modal } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
import { Details } from '../../elements/puppet/details/index'
import { showModal } from '@ekuaibao/show-util'
import { uuid } from '@ekuaibao/helpers'
import { fnCheckCompleted } from '../validator/validator'
import { cloneDeep, remove, difference, get, debounce } from 'lodash'
import { fnDetailsApportion } from '../utils/fnDetailsApportion'
import { fnCheckPayerInfo as checkPayerInfo } from '../utils/fnCheckPayerInfo'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnFormatThirdData2Details, formatDIDIDetail } from '../../lib/third-data'
import { detailIsDisable } from '../utils/fnDisableComponent'
import { fnInvoiceFormType } from '../utils/fnInvoiceFormType'
import { standardValueMoney } from '../../lib/misc'
import {
  fnCheckPayerInfo,
  fnGetVisibleIds,
  getFeeTypeById,
  getFeeTypePath,
  lineSelectCurrencyDisable,
  isSelectCurrencyDisable,
  getForeignNumCode,
  getBillReceivingCurrency
} from '../../lib/fee-util'
const corporationBinding = api.invokeServiceAsLazyValue('@tpp-v2:import:corporationBinding')
import { getV } from '@ekuaibao/lib/lib/help'
import { checkPriority } from '../utils/fnFormartDatalinkData'
import { getAssignmentRuleById } from '../../plugins/bills/bills.action'
import { parseFormValueAsParam } from '../../plugins/bills/util/parse'
import { fnInvoicepathTrack } from '../../elements/invoice-form/utils/invoiceTrack'
import { related } from '../../elements/feeDetailViewList/Related'
import { handleImportApplyDetail as _handleImportApplyDetail } from '../utils/fnExpenseLinkUtil'
import {
  updateDetailsMoneyValue,
  updateDetailsReceivingAmountValue,
  getBillAllCurrencyRatesInfo
} from '../utils/fnCurrencyObj'
import { fnFlowHideFields, fnHideFieldsNote, isHiddenFieldsInclude } from '../utils/fnHideFields'
import {
  fnApportionDetails,
  detailsTemplateAddReceivingAmount,
  sortFeeTypeForm
} from '../../plugins/bills/util/billUtils'
import { deleteFeeDetailLog, deleteFeeDetail2RecordNote } from '../../plugins/bills/util/trackLogs'
import { provider, inject } from '@ekuaibao/react-ioc'
import { DetailTableVm } from '../../elements/puppet/details/TableMVC/DetailTableVm'
import TableManage from '../../elements/puppet/details/TableMVC/TableManage'
import { getBoolVariation } from '../../lib/featbit'

const FLAG = { cancelLimit: false }
import { observer } from 'mobx-react'

@EnhanceConnect(state => {
  return {
    payerInfo: state['@common'].payerInfo,
    userInfo: state['@common'].userinfo.data,
    feeTypes: state['@common'].feetypes.data,
    registrationList: state['@tpp-v2'].registrationList,
    mappingRelation: state['@tpp-v2'].mappingRelation,
    corporationList: state['@bills'].corporationList,
    multiplePayeesMode: state['@bills'].multiplePayeesMode,
    payPlanMode: state['@bills'].payPlanMode,
    baseDataProperties: state['@common'].globalFields.data,
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
    currentVisibleFeeTypes: state['@bills'].currentVisibleFeeTypes,
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    remunerationBatchField: state['@remuneration'].remunerationBatchField,
    customizeQueryPower: state['@common'].powers.customizeQuery,
    civilServiceCard: state['@common'].powers.CivilServiceCard,
    travelBlackListV3: state['@itinerary-manage'].travelBlackListV3,
    travelBlackList: state['@bills'].travelBlackList
  }
})
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return type === 'requisitionDetails' || type === 'details'
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0 && (!value || !value.length)) {
      return callback()
    }
    const error = fnCheckCompleted({ ...props, value })
    // 保存草稿时，有明细填写不完整提示也跳过校验
    if (error && rule.level <= 0) return callback(error)
    return callback()
  }
})
@provider([DetailTableVm.NAME, DetailTableVm])
@observer
export default class DetailsEditable extends PureComponent {
  @inject(DetailTableVm.NAME) tableVm
  TM = undefined
  state = {
    didiAuth: {},
    visibleFeeTypes: [],
    value: [],
    upgradeOrderMicro: false
  }

  visibleFeeTypes = []

  selectedDataIdx = []

  RecordExpendsDetails = []

  isEdit = true

  componentWillMount() {
    let { bus, billSpecification, tag } = this.props
    corporationBinding().then(res => {
      if (res && res.items && res.items[0] && res.items[0].platformId == 'DIDI') {
        this.setState({ didiAuth: res.items[0] })
      }
    })

    api.dataLoader('@common.payerInfo').reload()
    bus.on('check:details:validator', this.handleCheckDetails)
    bus.on('set:delegator', this.handleDelegatorChanged)
    bus.on('import:expenseLink', this.handleImportExpenseLink)
    bus.on('on:dependence:change', this.handleDependenceChange)
    bus.on('dynamic:detail:change', this.handleDetails)
    bus.on('open:trip:order:datalink:modal', this.handleSelectDataLink)
    bus.watch('get:moveTo:RecordExpendsDetails', this.getMoveToRecordExpendsDetails)
    bus.on('dimention:currency:change', this.handleDimentionCurrencyChange)
    bus.on('switch:payee:currency:change', this.handlePayeeCurrencyChange)
    bus.on('select:data:change', this.handleSelectChange)
    this.initOrders = this.setCacheOrders(tag.initValue)
  }

  componentDidMount() {
    const { billSpecification, bus } = this.props
    this.fnSetValueAfterFilter(this.props)
    api.invokeService('@bills:get:invoice:corporation')
    this.loadVisibleFeeTypes(billSpecification)
    api
      .dataLoader('@common.feetypeTableEdit')
      .load()
      .then(res => {
        if (res) {
          this.tableVm.loading = true
          this.tableVm.updateDetails = this.onDetailsChange
          this.TM = new TableManage(this.tableVm, bus, this.props)
          this.tableVm.TM = this.TM
        }
      })
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('check:details:validator', this.handleCheckDetails)
    bus.un('set:delegator', this.handleDelegatorChanged)
    bus.un('import:expenseLink', this.handleImportExpenseLink)
    bus.un('on:dependence:change', this.handleDependenceChange)
    bus.un('dynamic:detail:change', this.handleDetails)
    bus.un('get:moveTo:RecordExpendsDetails', this.getMoveToRecordExpendsDetails)
    bus.un('open:trip:order:datalink:modal', this.handleSelectDataLink)
    bus.un('dimention:currency:change', this.handleDimentionCurrencyChange)
    bus.un('select:data:change', this.handleSelectChange)
    bus.un('switch:payee:currency:change', this.handlePayeeCurrencyChange)
  }

  componentWillReceiveProps(np) {
    this.tableVm?.TM?.updateProps({ ...np, selectAble: true })
    if (this.props.value !== np.value) {
      this.fnSetValueAfterFilter(np)
    }
  }

  getMoveToRecordExpendsDetails = () => {
    return this.RecordExpendsDetails
  }

  fnSetValueAfterFilter = props => {
    let {
      feeTypeVisibleObjForModify = {},
      showAllFeeType,
      value = [],
      isModify,
      feeTypes,
      billSpecification,
      field
    } = props
    if (value && value.length > 1) {
      value = sortFeeTypeForm(value, field?.orderByType)
    }
    value.forEach(v => {
      let feeType = v.feeTypeId || v.feeType
      feeType.fullname = feeType?.fullname || getFeeTypePath(feeTypes, feeType)
      return v
    })
    //验证发票的PayerInfo
    fnCheckPayerInfo(value)
    this.handleCheckDetails(billSpecification)
    const { feeTypeVisibleList = [] } = feeTypeVisibleObjForModify
    let unvisibleCount = value.length
    if (!isModify || showAllFeeType) {
      unvisibleCount = 0
    } else {
      value = value.filter(el => !el.feeTypeForm.detailId || feeTypeVisibleList.includes(el.feeTypeForm.detailId))
      unvisibleCount -= value.length
    }
    this.setState({ value, unvisibleCount })
  }

  handleDependenceChange = ({ key, id }) => {
    const { field, billSpecification, value } = this.props
    const { dependence } = field
    if (dependence && dependence.length && !!dependence?.find(v => v.dependenceId === key)) {
      FLAG.cancelLimit = false
      setTimeout(() => this.handleCheckDetails(billSpecification))
    }
  }

  checkDetailValue = billSpecification => {
    const { value = [] } = this.props
    const { name } = billSpecification

    const allowCancelDependence = billSpecification.components?.some(
      item => item.type === 'details' && item.allowCancelDependence
    )

    fnCheckCompleted({ ...this.props, value })
    if (value && value.length) {
      let visibleIdsWithChild = fnGetVisibleIds(this.visibleFeeTypes)
      value.forEach(v => {
        v.errorMsg = v.errorMsg || {}
        let feeTypeId = v.feeTypeId || (v.feeTypeId && v.feeTypeId.id)
        if (
          !allowCancelDependence &&
          !~visibleIdsWithChild.indexOf(feeTypeId.id) &&
          v.feeTypeId &&
          v.feeTypeId.active
        ) {
          v.errorMsg['visible'] = i18n.get('not-in-name', { name })
        } else {
          v.errorMsg['visible'] = ''
        }
      })
    }
  }

  loadVisibleFeeTypes = billSpecification => {
    this.getVisibleFeeTypes().then(() => {
      this.checkDetailValue(billSpecification)
    })
  }

  getVisibleFeeTypes = () => {
    const { bus } = this.props
    return bus.invoke('update:visibleFeeTypes', { isCancelLimit: FLAG.cancelLimit }).then(({ items }) => {
      this.visibleFeeTypes = items
      this.setState({ visibleFeeTypes: this.visibleFeeTypes })
    })
  }

  handleCheckDetails = billSpecification => {
    const { bus } = this.props
    bus.invoke('update:visibleFeeTypes', { isCancelLimit: FLAG.cancelLimit }).then(({ items }) => {
      this.visibleFeeTypes = items
      this.checkDetailValue(billSpecification)
      this.setState({ visibleFeeTypes: this.visibleFeeTypes })
    })
  }

  handleDelegatorChanged = staff => {
    let { value = [], submitterId, civilServiceCard } = this.props
    value.forEach(val => {
      let components = (val && val.specificationId && val.specificationId.components) || []
      components.forEach(cp => {
        if (
          !cp.editable &&
          cp.defaultValue &&
          cp.defaultValue.type === 'predefine' &&
          cp.defaultValue.value === 'submit.department'
        ) {
          //判断field的类型为Department
          val.feeTypeForm[cp.field] = staff.defaultDepartment
        }
      })
    })
    // 公务卡委托报销，切换提交人时清除前一个提交人的公务卡数据
    if (civilServiceCard) {
      let filterDetails = []
      const prevStaff = get(submitterId, 'id')
      const currentStaff = get(staff, 'id')
      if (prevStaff && currentStaff && prevStaff !== currentStaff && value.length) {
        value.forEach(item => {
          const ordersData = get(item, 'feeTypeForm.ordersData', [])
          if (ordersData.every(el => el.platform !== 'transact')) {
            filterDetails.push(item)
          }
        })
        this.formatDetailsIdx(filterDetails)
        this.handleDetails(filterDetails)
      }
    }
  }

  handleDelDetailClick = line => {
    if (line.tripEdit === false) {
      return
    }
    if (this?.tableVm?.isEdit && this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    let { bus, onChange, customizeQueryPower, value = [] } = this.props
    let details = value.slice(0)
    let idx = details.findIndex(v => v.idx === line.idx)
    this.handleRemoveRelatedApplication([line])
    details.splice(idx, 1)
    this.formatDetailsIdx(details)
    if (customizeQueryPower) {
      const changeDetails = cloneDeep(details)
      changeDetails.currentEditField = [{ type: 'detail_', values: [], operate: 'deleteFeeType' }]
      bus.emit('details:change', changeDetails)
    } else {
      bus.emit('details:change', details)
    }
    this.emitToDelDetailsExternal([line.feeTypeForm.detailId])
    onChange && onChange(details)
    this.refreshTable(details)
    this.logDeleteDetail({ data: [line] })
    api.invokeService('@bills:delete:detail:dataLink:instance', { details: [line] })
  }

  handleRemoveRelatedApplication(details) {
    details.forEach(line => {
      const detailId1 = get(line, 'feeTypeForm.detailId')
      const linkDetailEntities = get(line, 'feeTypeForm.linkDetailEntities', []) || []
      const dataList = linkDetailEntities.length > 0 ? linkDetailEntities[0].dataList : []
      const detailId2 = dataList && dataList.length > 0 && dataList[0]._tempConsumId
      const removeId = detailId2 || detailId1
      related.removeByConsumeId(removeId)
    })
  }

  handleAddDetailClick = feetype => {
    if (this.handleCheckFeeType(this.handleAddDetailClick, feetype)) return
    if (this?.tableVm?.isEdit && this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    if (this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      const idx = this?.tableVm?.tableDataSource?.length ?? 0
      if (idx === 0) {
        this.tableVm.tableDataSource = [{ feeTypeForm: {}, idx: this?.tableVm?.tableDataSource?.length ?? 0 }]
      } else {
        this?.tableVm?.tableDataSource?.push({ feeTypeForm: {}, idx: this?.tableVm?.tableDataSource?.length ?? 0 })
      }
      this.tableVm.editingKey = idx
      this.tableVm.TM.billBus.$isTableEdit = true
      return
    }
    let line = undefined
    if (feetype.id) {
      line = { feeTypeId: feetype, feeTypeForm: {}, add: true }
    }
    const { bus, value, field } = this.props
    this.fnUpdateDimensionCurrencyValue()
    const selectCurrencyDisable = isSelectCurrencyDisable(field, value)
    const foreignNumCode = getForeignNumCode(value)
    bus
      .invoke('element:details:add:click', {
        line,
        details: value,
        visibleFeeTypes: this.visibleFeeTypes,
        onDetailsChange: this.handleDetails,
        update: false,
        selectCurrencyDisable,
        foreignNumCode,
        field
      })
      .then(datas => {
        bus
          .invoke('update:flow:risk:info', datas)
          .then(newDetails => {
            this.handleDetails(newDetails)
          })
          .catch(err => {
            this.handleDetails(datas)
            console.log(err)
          })
      })
  }

  fnUpdateDimensionCurrencyValue = () => {
    if (!getBoolVariation('cyxq-73419')) {
      return
    }
    const { bus, form, dimentionCurrencyInfo } = this.props
    const formValues = form?.getFieldsValue(['legalEntityMultiCurrency'])
    if (!!formValues?.legalEntityMultiCurrency && !dimentionCurrencyInfo) {
      bus.emit('update:dimention:currency:by:dimension:value', formValues.legalEntityMultiCurrency, false)
    }
  }

  fnIsNeedDel = component => {
    const { type, defaultValue, editable } = component
    const value = get(defaultValue, 'value', '')
    const isOfficialCardSettlement = defaultValue?.type === 'officialCardSettlement'
    return (
      type === 'dataLink' ||
      type === 'dataLinkEdits' ||
      (value === 'submit.requisition' && !editable) ||
      isOfficialCardSettlement
    )
  }

  handleDetailCopy = data => {
    if (this.tableVm?.isEdit && this.tableVm?.showType === 'TABLE_NO_GROUP') {
      message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
      return
    }
    data = JSON.parse(JSON.stringify(data))
    if (this.handleCheckFeeType()) return
    const components = get(data, 'specificationId.components')
    let feeTypeForm = cloneDeep(data.feeTypeForm)
    // 复制时将发票、订单、数据互联、关联明细置空
    delete feeTypeForm.detailId
    delete feeTypeForm.linkDetailEntities
    delete feeTypeForm.detailNo
    const delKeys = ['code']
    components.forEach(component => {
      if (this.fnIsNeedDel(component)) {
        delKeys.push(component.field)
      }
    })
    delKeys.forEach(key => {
      delete feeTypeForm[key]
    })

    if (
      feeTypeForm.thirdPartyOrders || //通过导入中航易购生成的明细
      feeTypeForm.ordersData //通过导入易快报自营业务生成的明细（企业消费，公务卡等）
    ) {
      delete feeTypeForm.thirdPartyOrders
      delete feeTypeForm.ordersData
      delete feeTypeForm.orders
    }
    if (feeTypeForm.travelPlanning?.length) {
      feeTypeForm.travelPlanning.forEach(item => {
        if (item.travelId) {
          item.travelId = null
        }
      })
    }

    // 复制时，删除分摊id
    const apportions = getV(feeTypeForm, 'apportions', [])
    if (apportions.length) {
      apportions.forEach(el => {
        if (el.apportionForm?.apportionId) {
          delete el.apportionForm.apportionId
        }
      })
    }
    // let type = data?.feeTypeForm?.invoiceForm?.type
    // if (!type) {
    const invoiceFormField = components.find(el => el.field === 'invoiceForm')
    let type = 'noWrite'
    if (invoiceFormField) {
      const defaultInvoiceType = get(invoiceFormField, 'invoiceType.defaultInvoiceType', '')
      if (defaultInvoiceType && get(invoiceFormField, `invoiceType.${defaultInvoiceType}`, false)) {
        type = defaultInvoiceType
      }
    }
    // }
    feeTypeForm = { ...feeTypeForm, invoiceForm: { type }, feeDate: Date.now() }
    let { bus, value, field } = this.props
    const selectCurrencyDisable = isSelectCurrencyDisable(field, value)
    const foreignNumCode = getForeignNumCode(value)
    // 删除自定义actionValue
    let feeTypeId = cloneDeep(data.feeTypeId)
    if (feeTypeId?.actionValue === i18n.get('版本变更')) {
      delete feeTypeId?.actionValue
    }
    if (this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      const idx = this?.tableVm?.tableDataSource?.length
      this.tableVm.editingKey = idx
      this.tableVm.TM.billBus.$isTableEdit = true
      feeTypeForm.detailId = uuid(14)
      this.tableVm?.TM?.copyRow({
        ...data,
        feeTypeId,
        feeTypeForm,
        idx
      })
    } else {
      bus
        .invoke('element:details:add:click', {
          line: { ...data, feeTypeId, feeTypeForm, add: true },
          details: value,
          visibleFeeTypes: this.visibleFeeTypes,
          onDetailsChange: this.handleDetails,
          update: false,
          selectCurrencyDisable,
          foreignNumCode,
          field
        })
        .then(datas => {
          this.handleDetails(datas)
        })
    }
  }

  handleDetails = details => {
    const { bus, onChange } = this.props
    bus.emit('details:change', details)
    onChange && onChange(details)
    this.refreshTable(details)
  }

  refreshTable = details => {
    if (this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      console.log('🚀 ~ file: Details.js:474 ~ DetailsEditable ~ details:', details)
      this?.tableVm?.TM?.handleDataSource(details, true)
    }
  }

  handleCheckFeeType = (cb, params) => {
    let { billSpecification } = this.props
    const allowCancelDependence = billSpecification.components?.some(
      item => item.type === 'details' && item.allowCancelDependence
    )
    // let allowCancelDependence = true
    let { name } = billSpecification
    name = name ? i18n.get(`「{__k0}」`, { __k0: name }) : i18n.get('此模板')
    if (!(this.visibleFeeTypes && this.visibleFeeTypes.length)) {
      if (!FLAG.cancelLimit && allowCancelDependence) {
        showModal.confirm({
          title: i18n.get('无法添加费用明细'),
          content: i18n.get('please-contact-admin', { name }),
          okText: i18n.get('取消限制'),
          onOk: () => {
            FLAG.cancelLimit = true
            this.getVisibleFeeTypes().then(() => {
              cb && cb(params)
            })
          }
        })
      } else {
        showModal.warning({
          title: i18n.get('无法添加费用明细'),
          content: i18n.get('please-contact-admin', { name })
        })
      }
      return true
    }
    return false
  }

  fnCheckPayerInfo = () => {
    let { userInfo, payerInfo } = this.props
    return checkPayerInfo(userInfo, payerInfo, true)
  }
  //单据模板关联申请字段是否配置了：费用金额发生变更时同步本次报销金额
  fncheckIskeepEqual = billTemplate => {
    const expenseLinksField = billTemplate?.components?.find(el => el.field === 'expenseLinks')
    return get(expenseLinksField, 'keepEqualIsChecked')
  }
  fnupdateLinkDetailAmount = (feeTypeForm, linkDetailEntities) => {
    const result = cloneDeep(linkDetailEntities[0])
    const form = cloneDeep(feeTypeForm) //解决费用金额变更的时候LinkDetailAmount也变更的问题，只有金额是自动计算的情况下才会出现这个问题
    const dataList = result && result.dataList
    if (dataList && dataList.length === 1) {
      const amount = get(form, 'amount.standard')
      const money = get(dataList[0], 'unwrittenOffAmount.standard')
      if (amount && money && amount * 1 <= money * 1) {
        result.dataList[0] = { ...result.dataList[0], modifyValue: form.amount }
        result.useTotalMoney = form.amount
      }
    }
    return [result]
  }
  async handleImport(data, isImportConsume, isCal) {
    let { bus, value = [], onChange, dimentionCurrencyInfo, billSpecification = {}, customizeQueryPower } = this.props
    const allowSelectionReceivingCurrency = this.getReceivingConfig()
    const billValue = await bus.getFieldsValue()
    const receivingNumCode = getBillReceivingCurrency(billValue)
    const { receivingCurrency } = getBillAllCurrencyRatesInfo(receivingNumCode)
    let details = value.slice(0)
    let perLen = details.length
    const keepEqualIsChecked = this.fncheckIskeepEqual(billSpecification)
    for (let index = 0; index < data.length; index++) {
      let v = data[index]
      // 复制明细特殊处理
      if (isImportConsume && v?.feeTypeForm?.invoiceForm?.type) {
        v.feeTypeForm.invoiceForm = { type: v?.feeTypeForm?.invoiceForm?.type }
      }
      v = isCal
        ? v
        : await api.invoke(
            'generate:DetailByAutoCalculate',
            { detailFormValue: v, components: v?.specificationId?.components, needCalculateRecordLink: false },
            true
          )
      v.idx = perLen + index

      //导入消费明细时如果关联申请配置了联动,需根据报销金额修改申请金额
      try {
        if (keepEqualIsChecked && v?.feeTypeForm?.linkDetailEntities?.length === 1) {
          v.feeTypeForm.linkDetailEntities = this.fnupdateLinkDetailAmount(
            v.feeTypeForm,
            v.feeTypeForm.linkDetailEntities
          )
        }
      } catch (err) {}
    }

    const { configs = [], type } = billSpecification
    const isLockFeeTypeVersion = getV(
      configs.find(config => config.ability === type),
      'lockFeeTypeVersion',
      false
    )
    if (value.length && isLockFeeTypeVersion) {
      Array.isArray(data) &&
        data.forEach(res => {
          const target = value.find(originDetail => {
            const oldOriginalId = getV(originDetail, 'specificationId.originalId')
            const oldId = getV(originDetail, 'specificationId.id')
            const newOriginalId = getV(res, 'specificationId.originalId')
            const newId = getV(res, 'specificationId.id')
            return oldOriginalId === newOriginalId && oldId !== newId
          })
          if (target) {
            res.specificationId = target.specificationId
          }
        })
    }
    details = details.concat(data)
    if (dimentionCurrencyInfo) {
      details = updateDetailsMoneyValue(details, dimentionCurrencyInfo.currency, dimentionCurrencyInfo.rates)
    }

    if (allowSelectionReceivingCurrency && receivingCurrency) {
      details = updateDetailsReceivingAmountValue(details, receivingCurrency, true)
    }

    if (customizeQueryPower) {
      const changeDetails = cloneDeep(details)
      let changedFields = []
      data.forEach(v => changedFields.push(v?.idx))
      changeDetails.currentEditField = [{ type: 'detail_', values: changedFields, operate: 'exportFeeType' }]
      bus.emit('details:change', changeDetails)
    } else {
      bus.emit('details:change', details)
    }
    this.refreshTable(details)
    onChange && onChange(details)
  }

  handleImportExpenseLink = (value, isCal) => {
    this.handleImport(value, value?.isImportConsume, isCal)
  }

  //神策埋点
  fnInvoiceTrack = importWay => {
    const {
      userInfo: { staff }
    } = this.props
    fnInvoicepathTrack(staff, 'import', importWay)
  }

  handleImportInvoiceClick = () => {
    let { bus } = this.props
    this.fnInvoiceTrack('upload')
    if (this.handleCheckFeeType() || this.fnCheckPayerInfo()) return
    this.fnUpdateDimensionCurrencyValue()
    bus
      .invoke('element:details:import:click', { visibilityFeeTypes: this.visibleFeeTypes })
      .then(data => {
        return this.setupEnumValue(data)
      })
      .then(result => {
        this.handleImport(result)
      })
  }

  handleImportAliPayInvoiceClick = async () => {
    let { bus } = this.props
    this.fnInvoiceTrack('upload')
    if (this.handleCheckFeeType() || this.fnCheckPayerInfo()) return
    const isAuth = await api.invokeService('@bills:check:aliPayCard:auth')
    if (!isAuth) return

    bus
      .invoke('element:details:aliPay:import:click', { feeTypes: this.visibleFeeTypes })
      .then(data => {
        return this.setupEnumValue(data)
      })
      .then(result => {
        this.handleImport(result)
      })
  }

  handleImportAifaPiaoInvoiceClick = async () => {
    const isAuth = await api.invokeService('@bills:check:aifapiao:auth', this.importAiFaPiaoH5Callback)
    if (!isAuth) return null
    this.importAiFaPiaoH5Callback()
  }

  importAiFaPiaoH5Callback = async () => {
    const url = await api.invokeService('@bills:get:aifapiao:public:url')
    const res = await api.open('@bills:IframeModal', {
      src: url,
      handleListener: debounce(this.handleImportAifapiao, 300)
    })
  }

  handleImportAifapiao = async data => {
    const { ids, type } = data
    let { bus, submitterId } = this.props
    if (type === 'importInvoice') {
      const data = await api.invokeService('@bills:import:invoice:from:aifapiao', { ids, staffId: submitterId?.id })
      bus
        .invoke('element:details:import:afp:click', { feeTypes: this.visibleFeeTypes, data: data?.items })
        .then(data => {
          return this.setupEnumValue(data)
        })
        .then(result => {
          this.handleImport(result)
        })
    }
  }

  handleImportOCRMedicalClick = () => {
    this.handleImportInvoiceOCRClick({ isMedical: true })
  }

  handleImportOverseasInvoiceClick = () => {
    this.handleImportInvoiceOCRClick({ isOverseas: true })
  }

  handleImportInvoiceOCRClick = ({ isMedical, isOverseas }) => {
    let { bus } = this.props
    this.fnInvoiceTrack('ocr')
    if (this.handleCheckFeeType() || this.fnCheckPayerInfo()) return
    bus
      .invoke('element:details:import:ocr:click', { visibilityFeeTypes: this.visibleFeeTypes, isMedical, isOverseas })
      .then(data => {
        return this.setupEnumValue(data)
      })
      .then(data => {
        this.handleImport(data)
      })
  }

  setupEnumValue(result) {
    if (result && result[0] && result[0].feeTypeForm.invoiceType) {
      return api
        .invokeService('@common:get:staff:dimension', { name: 'basedata.Enum.InvoiceType' })
        .then(({ items }) => {
          return result.map(item => {
            item.feeTypeForm.invoiceType = items.find(
              e =>
                e.id ===
                (typeof item.feeTypeForm.invoiceType === 'string'
                  ? item.feeTypeForm.invoiceType
                  : item.feeTypeForm.invoiceType?.id)
            )
            return item
          })
        })
    }
    return result
  }

  handleImportInputInvoiceClick = () => {
    let { bus } = this.props
    this.fnInvoiceTrack('query')
    if (this.handleCheckFeeType() || this.fnCheckPayerInfo()) return
    bus
      .invoke('element:details:input:import:click', { visibilityFeeTypes: this.visibleFeeTypes })
      .then(data => {
        return this.setupEnumValue(data)
      })
      .then(data => {
        this.handleImport(data)
      })
  }

  handleRecordExpendsClick = () => {
    const { bus, billSpecification } = this.props
    if (this.handleCheckFeeType()) {
      return
    }
    bus.invoke('element:details:record:expends:click', this.visibleFeeTypes).then(value => {
      fnApportionDetails(value, billSpecification).then(details => {
        this.handleImport(details)
      })
    })
  }

  handleQuickExpendsClick = () => {
    const { bus } = this.props
    if (this.handleCheckFeeType()) {
      return
    }
    bus.invoke('element:details:quick:expends:click', this.visibleFeeTypes).then(value => {
      this.handleImport(value)
    })
  }

  handleImportCSCClick = () => {
    const { bus } = this.props
    if (this.handleCheckFeeType()) {
      return
    }
    bus.invoke('element:details:importCSC:click', this.visibleFeeTypes).then(data => {
      this.handleImport(data)
    })
  }

  fnGetValidExternal = (external, line) => {
    //获取到对应该条分摊明细的超标提醒
    if (!external) return void 0
    let validExternal = void 0
    for (let key in external) {
      key === line.feeTypeForm.detailId && (validExternal = external[key])
    }
    return validExternal
  }

  handleLineClick = (line, index) => {
    let {
      bus,
      onChange,
      flowRulePerformLogs,
      external,
      field,
      feeTypeVisibleObjForModify = {},
      isModify,
      value,
      showAllFeeType
    } = this.props
    let details = value
    let id = (line.feeTypeId && line.feeTypeId.id) || line.feeType
    let feeType = getFeeTypeById(this.visibleFeeTypes, id)
    let visibleFeeTypes = cloneDeep(this.visibleFeeTypes)
    const results = (flowRulePerformLogs && flowRulePerformLogs.results) || []
    const detailLog = results.filter(
      element => element.dataFrom === 'details' && element.loc === line.idx && element.type !== 'calculate'
    )
    let temFeeType
    if (!feeType) {
      temFeeType = line.feeTypeId || line.feeType
      visibleFeeTypes.unshift(temFeeType)
    }
    let isEdit = this.isEdit

    //审批中修改时，校验明细权限是否允许修改
    let apportions = line.feeTypeForm.apportions || []
    let cannotEditAmountField = false
    const { apportionVisibleList = [] } = feeTypeVisibleObjForModify
    if (isModify && apportions.length > 0) {
      apportions = apportions.filter(el => {
        const apportionId = get(el, 'apportionForm.apportionId', '')
        return !apportionVisibleList.includes(apportionId)
      })
      cannotEditAmountField = apportions.length > 0
    }
    this.fnUpdateDimensionCurrencyValue()
    const ValidExternal = this.fnGetValidExternal(external, line)
    const selectCurrencyDisable = lineSelectCurrencyDisable(field, value, index)
    const foreignNumCode = getForeignNumCode(value)
    bus
      .invoke('element:details:line:click', {
        details,
        line: { ...line, showAllFeeType, apportionVisibleList, cannotEditAmountField },
        visibleFeeTypes,
        temFeeType,
        ruleLog: detailLog,
        isEdit,
        external: ValidExternal,
        index,
        selectCurrencyDisable,
        foreignNumCode,
        field
      })
      .then(datas => {
        bus.emit('details:change', datas)
        bus.invoke('update:flow:risk:info', datas)
        onChange && onChange(datas)
        this.refreshTable(datas)
      })
  }

  handleSelectChange = selectedData => {
    this.selectedDataIdx = selectedData.map(item => item.idx)
    this.tableVm?.TM?.updateSelectData(this.selectedDataIdx)
  }

  handleBatchApportionClick = apportionSpecificationIds => {
    let { value = [], bus, onChange } = this.props
    let details = value.slice(0)
    // let checkedDetails = details.filter(line => line.checked) || []
    const selectValue = value.filter((item, idx) => this.selectedDataIdx.includes(idx)) || []

    if (this.selectedDataIdx.length > 0) {
      bus.invoke('element:details:batch:apportion', apportionSpecificationIds, selectValue).then(data => {
        fnDetailsApportion(this.filterSelectedData(), data)
        bus.emit('details:change', details)
        onChange && onChange(details)
        this.refreshTable(details)
      })
    } else {
      message.warning(i18n.get('请选择要分摊的费用明细！'))
    }
  }

  filterSelectedData() {
    const { value } = this.props
    const result = []
    value &&
      value.forEach((item, index) => {
        if (!!~this.selectedDataIdx.indexOf(item.idx)) {
          result.push(item)
        }
      })
    return result
  }

  emitToDelDetailsExternal = ids => {
    let { external } = this.props
    const delDetailIds = []
    ids.forEach(v => {
      const allowEmit = external && external[v] && Object.keys(v).length
      allowEmit && delDetailIds.push(v)
    })
    delDetailIds.length && api.emit('external:details:delete', delDetailIds)
  }

  logDeleteDetail = ({ isRemoveRecord = false, data = [] }) => {
    try {
      const { billSpecification, dataSource = {} } = this.props
      const userInfo = api.getState()['@common'].userinfo?.data
      const corpId = userInfo?.staff?.corporationId?.id
        ? userInfo?.staff?.corporationId.id
        : userInfo?.staff?.corporationId
      const logObj = {
        corpId,
        staffId: userInfo?.staff?.id,
        logTime: Date.now(),
        billSpecificationId: billSpecification?.id,
        flowId: dataSource?.id,
        billCode: dataSource?.form?.code,
        details: data.map(item => {
          const obj = {
            detailId: item.feeTypeForm?.detailId,
            specificationId: item.specificationId?.id
          }
          if (!!item.feeTypeForm?.invoiceForm?.invoices?.length) {
            obj['invoiceIds'] = item.feeTypeForm.invoiceForm.invoices.map(
              el => el?.master?.id || el?.originalData?.master?.id
            )
          }
          return obj
        })
      }
      isRemoveRecord ? deleteFeeDetail2RecordNote(logObj) : deleteFeeDetailLog(logObj)
    } catch (error) {
      console.log(error)
    }
  }

  handleOnBatchRemoveClick = args => {
    if (this?.tableVm?.isEdit && this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    let { bus, onChange, customizeQueryPower, value = [] } = this.props
    let details = value.slice(0)
    let removePre = details.length
    let delDetailIds = []
    const data = this.filterSelectedData()
    this.handleRemoveRelatedApplication(data)
    data.forEach(v => delDetailIds.push(v.feeTypeForm.detailId))
    remove(details, function(line) {
      return !!~data.indexOf(line)
    })
    let removeAfter = details.length
    if (removePre === removeAfter) {
      message.warning(i18n.get('请选择要删除的费用明细！'))
    } else {
      this.formatDetailsIdx(details)
      if (this.selectedDataIdx.includes(this.tableVm.editingKey)) {
        //如果删除的数据里面包含了正在编辑的数据，则把编辑状态清空
        this.tableVm.editingKey = ''
        this.tableVm.TM.billBus.$isTableEdit = false
      }
      this.selectedDataIdx = []
      this.tableVm?.TM?.updateSelectData(this.selectedDataIdx)
      if (customizeQueryPower) {
        const changeDetails = cloneDeep(details)
        changeDetails.currentEditField = [{ type: 'detail_', values: [], operate: 'deleteFeeType' }]
        bus.emit('details:change', changeDetails)
      } else {
        bus.emit('details:change', details)
      }
      this.emitToDelDetailsExternal(delDetailIds)
      onChange && onChange(details)
      this.refreshTable(details)
      this.logDeleteDetail({ isRemoveRecord: args?.isRemoveRecord, data })
    }
  }

  formatDetailsIdx = details => {
    details.forEach((v, idx) => {
      v.idx = idx
    })
  }

  handleBatchImport = e => {
    let { bus, value = [], onChange, dimentionCurrencyInfo, customizeQueryPower } = this.props
    api
      .dataLoader('@common.feetypes')
      .load()
      .then(({ data }) => {
        bus
          .invoke('import:detail:excel', this.visibleFeeTypes, data, { isCancelLimit: FLAG.cancelLimit })
          .then(result => {
            let details = value.concat(result).map((v, idx) => {
              v.idx = idx
              fnInvoiceFormType(v)
              return v
            })
            if (dimentionCurrencyInfo) {
              details = updateDetailsMoneyValue(details, dimentionCurrencyInfo.currency, dimentionCurrencyInfo.rates)
            }
            if (customizeQueryPower) {
              const changeDetails = cloneDeep(details)
              let changedFields = []
              value.forEach(v => v.idx >= value.length && changedFields.push(v.idx))
              changeDetails.currentEditField = [{ type: 'detail_', values: changedFields, operate: 'exportFeeType' }]
              bus.emit('details:change', changeDetails)
            } else {
              bus.emit('details:change', details)
            }
            this.refreshTable(details)
            onChange && onChange(details)
          })
      })
  }

  handleSelectDataLink = (entityInfo, expenseCodeList, importItem, otherParams) => {
    const { bus, flowId, billSpecification, baseDataProperties, customizeQueryPower } = this.props
    const { linkId } = entityInfo
    let { type } = billSpecification
    let MultiSelectDataLinkModal = '@bills:MultiSelectDataLinkModal'

    // if (upgradeOrderMicro) { MultiSelectDataLinkModal = '@bills:NewOrderMultiSelectDataLinkModal' }
    const modalKey = linkId ? '@bills:SelectAssociatedDataLinkModal' : MultiSelectDataLinkModal
    bus.getValue().then(form => {
      const billData = parseFormValueAsParam(form, billSpecification, undefined, baseDataProperties)
      api
        .open(modalKey, {
          entityInfo,
          type: 'detail',
          flowId,
          expenseCodeList,
          formType: type,
          visibleFeeTypes: this.visibleFeeTypes,
          specificationId: billSpecification.originalId.id || billSpecification.originalId,
          billData: billData.form,
          importItem,
          otherParams
        })
        .then(resp => {
          const { data, selectFeeType, specification } = resp
          this.renderDatalinkToBill(entityInfo, data, selectFeeType, specification).then(async result => {
            let feeTypes = form.details || []
            if (billSpecification.type === 'expense') {
              result.forEach(el => {
                if (el?.feeTypeId && !el?.feeTypeId?.expenseSpecification) {
                  el.feeTypeId.expenseSpecification = { components: el?.specificationId?.components }
                }
              })
              result = await fnApportionDetails(result, billSpecification)
            }
            feeTypes = feeTypes.concat(result)
            feeTypes.map((item, index) => (item.idx = index))

            if (customizeQueryPower) {
              const changeDetails = cloneDeep(feeTypes)
              let changedFields = []
              result.forEach(v => changedFields.push(v.idx))
              changeDetails.currentEditField = [{ type: 'detail_', values: changedFields, operate: 'exportFeeType' }]
              this.handleDetails(changeDetails)
            } else {
              this.handleDetails(feeTypes)
            }
          })
        })
    })
  }

  handleDimentionCurrencyChange = ({ currency, rates }) => {
    const { value = [], onChange, bus } = this.props
    const allowSelectionReceivingCurrency = this.getReceivingConfig()
    const details = updateDetailsMoneyValue(
      value,
      currency,
      rates,
      { useSystemRate: true },
      allowSelectionReceivingCurrency
    )
    onChange && onChange(details)
    this.refreshTable(details)
    this.tableVm.editingKey = ''
    bus.emit('details:change', details)
  }

  handlePayeeCurrencyChange = ({ currency }) => {
    const { value = [], onChange, bus } = this.props
    const receivingConfig = this.getReceivingConfig()
    if (!value?.length) return
    const details = updateDetailsReceivingAmountValue(value, currency, receivingConfig)
    onChange && onChange(details)
    this.refreshTable(details)
    this.tableVm.editingKey = ''
    bus.emit('details:change', details)
  }

  onDetailsChange = details => {
    const { onChange, bus } = this.props
    bus.emit('details:change', details)
    onChange && onChange(details)
  }

  fnGetLinkDetailDataAndUpdateLinkedData = (entityInfo, dataLink, feeTypeForm, components, assignmentRuleMap) => {
    // linkedDataLink: 业务对象关联中，被关联的业务对象
    if (entityInfo.linkDataLinkEntity) {
      let linkedData = this.getformData(entityInfo.linkDataLinkEntity, dataLink.linkedDataLink)
      const linkedDatalinkComponent = components.find(
        item => item.type === 'dataLink' && item.referenceData.id === entityInfo.linkDataLinkEntity.id
      )
      const { field: linkedDataLinkField, assignmentRule, isLinkageAssignment } = linkedDatalinkComponent || {}
      const linkedObj = dataLink.linkedDataLink
      linkedObj.form = linkedData
      feeTypeForm[linkedDataLinkField] = linkedObj
      if (isLinkageAssignment === true) {
        return this.getformDetailData(assignmentRuleMap[linkedObj.entityId] || assignmentRule, linkedObj)
      }
    }
    return null
  }

  fnUpdateData = (entityInfo, assignmentRuleMap, data, selectFeeType, specification) => {
    const { multiplePayeesMode, payPlanMode, billSpecification } = this.props
    const { type = 'expense' } = billSpecification
    return Promise.all(
      data
        .map((item, index) => {
          var dObj = item
          const { feeType } = item
          const copySpecification = feeType
            ? type === 'expense'
              ? feeType.expenseSpecification
              : feeType.requisitionSpecification
            : specification
          const datalinkComponent = copySpecification.components.find(
            item => item.type === 'dataLink' && item.referenceData.id === entityInfo.id
          )
          const { field, assignmentRule, isLinkageAssignment } = datalinkComponent || {}
          //明细form表单要赋值的数据关联字段的值dObj
          if (field) {
            const components = copySpecification.components
            let feeTypeForm = {}
            const linkedDetailData = this.fnGetLinkDetailDataAndUpdateLinkedData(
              entityInfo,
              item,
              feeTypeForm,
              components,
              assignmentRuleMap
            )
            let fdata = this.getformData(entityInfo, dObj)
            let detail
            dObj.form = fdata
            feeTypeForm[field] = dObj
            if (isLinkageAssignment === true || linkedDetailData) {
              if (isLinkageAssignment === true) {
                detail = this.getformDetailData(assignmentRuleMap[item.entityId] || assignmentRule, dObj, components)
              }
              if (linkedDetailData) {
                detail = { ...detail, ...linkedDetailData }
              }
              for (let key in detail) {
                copySpecification.components.forEach(scitem => {
                  if (
                    scitem.field === key &&
                    checkPriority(scitem) &&
                    (!scitem.dependence || (scitem.dependence && !scitem.dependence.length))
                  ) {
                    feeTypeForm[key] = detail[key]
                  }
                })
              }
              if (multiplePayeesMode && !payPlanMode) {
                feeTypeForm.feeDetailPayeeId = detail.feeDetailPayeeId
              }
            }

            const fee = {
              idx: index,
              feeTypeId: feeType ? feeType : selectFeeType,
              feeTypeForm: feeTypeForm,
              specificationId: copySpecification
            }
            let moneyTemplate = components?.find(t => t.field === 'amount')
            return api.invoke('generate:DetailByAutoCalculate', { detailFormValue: fee, components }).then(newForm => {
              if (
                moneyTemplate?.optional === true &&
                moneyTemplate?.hide === true &&
                (!newForm?.amount || !newForm?.amount?.standard)
              ) {
                newForm.amount = standardValueMoney(0)
              }
              // 如果发现 specificationId 的 open 发生了变化是因为 generate:DetailByAutoCalculate 修改的
              fee.feeTypeForm = newForm
              return fee
            })
          }
        })
        .filter(item => item)
    )
  }
  renderDatalinkToBill = (entityInfo, data, selectFeeType, specification) => {
    //数据互联赋值的时候如果选择的数据是业务对象子类的数据，需要用子类的赋值规则赋值
    //entityInfo数据业务对象对象, data数据业务对象对应的数据, selectFeeType费用类型, specification选择的费用类型模板
    let assignmentRuleMap = {}
    const ids = data.filter(item => item.entityId !== entityInfo.id).map(o => o.entityId) //获取业务对象子类的id
    if (ids.length > 0) {
      return api.dispatch(getAssignmentRuleById([...new Set(ids)])).then(result => {
        const ruleList = result.items
        ruleList.forEach(item => {
          assignmentRuleMap[item.sourceEntityId] = item
        })
        return this.fnUpdateData(entityInfo, assignmentRuleMap, data, selectFeeType, specification)
      })
    } else {
      return this.fnUpdateData(entityInfo, assignmentRuleMap, data, selectFeeType, specification)
    }
    //idLinkageAssignment如果为false,assignmentRule赋值规则不用管。
  }

  //获取映射表单中由业务对象对象数据映射的其他字段
  getformDetailData(assignmentRule, data, components) {
    let componentMap = {}
    if (components?.length) {
      componentMap = components.reduce((result, component) => {
        result[component.field] = component
        return result
      }, {})
    }
    let formData = {},
      fData = data.form
    assignmentRule.fields.forEach(item => {
      let tf = item.targetField,
        sf = item.sourceField
      const targetValue = fData[sf]
      if (targetValue?.location) {
        formData[tf] = targetValue?.name || targetValue.address
      } else {
        formData[tf] = fData[sf]
      }
      const component = componentMap[tf]
      if (
        component &&
        component.type === 'dataLink' &&
        component.isLinkageAssignment &&
        component.assignmentRule?.fields?.length
      ) {
        const form = formData[tf]?.data?.dataLink
        if (form) {
          const dataLinkAssignment = this.getformDetailData(component.assignmentRule, { form })
          if (!!dataLinkAssignment) {
            formData = { ...formData, ...dataLinkAssignment }
          }
        }
      }
    })
    return formData
  }

  //获取映射表单中某字段指向的业务对象对象数据
  getformData(datalinkEntity, data) {
    let formData = {},
      fData = data
    datalinkEntity.fields.forEach(item => {
      let fkey = item.name
      formData[fkey] = fData[fkey]
    })
    return formData
  }

  setCacheOrders(value = []) {
    return value
      .map(item => {
        let { feeTypeForm } = item
        if (feeTypeForm.thirdPartyOrders && feeTypeForm.thirdPartyOrders.length) {
          let order = feeTypeForm.thirdPartyOrders[0]
          return order.id
        }
      })
      .filter(o => o)
  }

  handleThirdPartyImportClick = item => {
    let { onChange, value = [], bus, mappingRelation, submitterId, corporationList, customizeQueryPower } = this.props
    let authScope = this.state.didiAuth.setting && this.state.didiAuth.setting.authScope == '1|2|3'

    let ordersList = this.setCacheOrders(value)
    let orderImported = difference(ordersList, this.initOrders)
    let deleteOrders = difference(this.initOrders, ordersList)
    api.invokeService('@tpp-v2:check:thirdParty:auth', item).then(_ => {
      return api
        .open(`@bills:ImportThirdPartyModal`, {
          item,
          orderImported,
          deleteOrders,
          orderTypeList: item.orderTypeList
        })
        .then(orderDatas => {
          api
            .open('@bills:SearchFeetypeTreeModal', {
              canSelectLast: true,
              visibilityFeeTypes: this.visibleFeeTypes
            })
            .then(({ selectFeeType: selectItem, specification }) => {
              let promiseArr = []
              const obj = this._fnGetSettlementComponent(specification)
              if (obj) {
                promiseArr.push(obj)
              }
              Promise.all(promiseArr).then(res => {
                let others
                if (res && res.length > 0) {
                  const settlementItems = res[0].items
                  others = { settlementItems }
                }
                return fnFormatThirdData2Details(
                  selectItem,
                  specification,
                  orderDatas,
                  value,
                  mappingRelation,
                  others
                ).then(data => {
                  return formatDIDIDetail({
                    details: data,
                    specification,
                    item,
                    ordersData: orderDatas,
                    authScope
                  }).then(details => {
                    let newDetails = value.concat(details)
                    const fapiao = specification.components.find(v => v.field === 'invoiceForm')
                    if (fapiao) {
                      const { invoiceType } = fapiao
                      if (invoiceType && invoiceType.unify && invoiceType.unify.invoiceCorporation.length === 1) {
                        const corporationId = invoiceType.unify.invoiceCorporation[0]
                        const corporation = corporationList && corporationList.items.find(v => v.id === corporationId)
                        newDetails = newDetails.map(item => {
                          if (
                            item.feeTypeForm.thirdPartyOrders &&
                            item.feeTypeForm.thirdPartyOrders.length > 0 &&
                            item.feeTypeForm.thirdPartyOrders[0].platform != 'DIDI'
                          ) {
                            item.feeTypeForm.invoiceForm.invoiceCorporation = corporation
                          }
                          return item
                        })
                      }
                    }
                    if (customizeQueryPower) {
                      const changeDetails = cloneDeep(newDetails)
                      let changedFields = []
                      details.forEach(v => changedFields.push(v.idx))
                      changeDetails.currentEditField = [
                        { type: 'detail_', values: changedFields, operate: 'exportFeeType' }
                      ]
                      bus.emit('details:change', changeDetails)
                    } else {
                      bus.emit('details:change', newDetails)
                    }
                    onChange && onChange(newDetails)
                    this.refreshTable(details)
                  })
                })
              })
            })
        })
    })
  }

  // 导入关联明细
  handleImportApplyDetail = async () => {
    const { bus, form, submitterId, dataFromOrder, currentVisibleFeeTypes } = this.props
    const dataSource = await form.getFieldsValue()
    const applicationListDetails = (await bus.invoke('update:select:expenselink', { ...dataSource })) || []
    _handleImportApplyDetail({ currentVisibleFeeTypes, applicationListDetails, bus, submitterId, dataFromOrder })
  }

  _fnGetSettlementComponent(specification) {
    const { components } = specification
    const component = components.filter(line => line.field === 'settlement')
    if (component && component.length > 0) {
      return api.invokeService('@common:get:staff:dimension', { name: 'basedata.Settlement' })
    }
  }

  handleRemoveToRecordExpendsClick = () => {
    if (this?.tableVm?.isEdit && this?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    const data = this.filterSelectedData()
    if (data?.length && JSON.stringify(data)?.includes('transact')) {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get('移入后，消费明细中的公务卡订单信息、取公务卡金额字段和结算方式将被清除'),
        cancelText: i18n.get('取消'),
        okText: i18n.get('我知道了'),
        onOk: () => this.handleDeleteCSC(data)
      })
    } else {
      this.handleRemoveToRecordExpendsClickAction(data)
    }
  }

  handleRemoveToRecordExpendsClickAction = data => {
    this.RecordExpendsDetails = this.RecordExpendsDetails.concat(data)
    this.handleOnBatchRemoveClick({ isRemoveRecord: true })
  }

  handleDeleteCSC = data => {
    data?.forEach(item => {
      const components = get(item, 'specificationId.components', [])
      const CSCRelated = components?.filter(v => {
        const dataType = get(v, 'type')
        const defaultValueType = get(v, 'defaultValue.type')
        const isOfficialCardMoney = dataType === 'money' && defaultValueType === 'officialCardMoney'
        const isOfficialCardSettlement = dataType === 'select' && defaultValueType === 'officialCardSettlement'
        return isOfficialCardMoney || isOfficialCardSettlement
      })
      const feeTypeForm = get(item, 'feeTypeForm')
      if (CSCRelated?.length && feeTypeForm) {
        CSCRelated.forEach(el => {
          if (get(feeTypeForm, el.field)) {
            feeTypeForm[el.field] = undefined
          }
        })
      }
      if (feeTypeForm?.orders) {
        feeTypeForm.orders = undefined
        feeTypeForm.ordersData = undefined
      }
    })
    this.handleRemoveToRecordExpendsClickAction(data)
  }

  handleTripEdit = line => {
    const { travelBlackList, travelBlackListV3 } = this.props
    // 行程2.0
    const trip = line?.feeTypeForm?.['u_行程规划']?.[0]
    // 行程3.0
    const tripV3 = line?.feeTypeForm?.travelPlanning?.[0]
    line.tripEdit = true
    if (trip && travelBlackList?.length) {
      line.tripEdit = !travelBlackList.includes(trip.dataLinkId)
    }
    if (tripV3 && travelBlackListV3?.length) {
      line.tripEdit = !travelBlackListV3.includes(tripV3.travelId)
    }
  }

  getReceivingConfig = () => {
    const { billSpecification = {} } = this.props
    const allowSelectionReceivingCurrency = billSpecification?.configs?.find(v => v.ability === 'pay')
      ?.allowSelectionReceivingCurrency
    return allowSelectionReceivingCurrency
  }

  render() {
    let {
      hiddenFields,
      submitterId = {},
      template,
      field,
      billSpecification = {},
      baseDataProperties,
      registrationList,
      isModify,
      external,
      isForbid,
      riskInfo,
      feeDetailUneditable,
      bus,
      tag,
      showAllFeeType,
      feeTypeVisibleObjForModify = {},
      billState,
      showPayPlan,
      inModal,
      dataSource,
      dataFromOrder,
      currentNode
    } = this.props
    let { value = [], unvisibleCount, visibleFeeTypes } = this.state
    const { apportionVisibleList = [] } = feeTypeVisibleObjForModify

    let { originalId, configs = [] } = billSpecification
    const { type, hide, name } = field
    if (hide && isHiddenFieldsInclude(hiddenFields, field)) {
      return <></>
    }
    //审批流是否设置了字段隐藏
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    if (flowHiddenFields.includes(name)) {
      return <></>
    }
    const t = type === 'details' ? (billSpecification.type === 'receipt' ? 'receipt' : 'expense') : 'requisition'
    const selectData = this.filterSelectedData()
    if (value && value.length > 1) {
      value = detailsTemplateAddReceivingAmount(value)
      value = sortFeeTypeForm(value, field?.orderByType)
    }
    value.forEach(v => {
      let feeType = v.feeTypeId || v.feeType
      feeType.fullname = feeType?.fullname || getFeeTypePath(feeTypes, feeType)
      this.handleTripEdit(v)
      return v
    })
    //验证发票的PayerInfo
    fnCheckPayerInfo(value)
    this.isEdit = !detailIsDisable(this.props)
    console.log(value)
    return (
      <Details
        billType={billSpecification.type}
        bus={bus}
        tableVm={this.tableVm}
        TM={this.TM}
        currentNode={currentNode}
        importAble={isSelectCurrencyDisable(field, value)}
        specificationComponents={template}
        specificationId={originalId}
        external={external}
        riskInfo={riskInfo}
        isEdit={this.isEdit}
        selectAble={true}
        field={field}
        type={t}
        billState={billState}
        configs={configs}
        submitterId={submitterId}
        showPayPlan={showPayPlan}
        dataSource={value}
        tagDataSource={tag?.dataSource}
        selectedData={selectData}
        visibleFeeTypes={visibleFeeTypes}
        baseDataProperties={baseDataProperties}
        onLineClick={this.handleLineClick}
        handleChange={this.handleSelectChange}
        onAddDetailClick={this.handleAddDetailClick}
        onDelDetailClick={this.handleDelDetailClick}
        thirdPartyList={registrationList}
        isModify={isModify}
        showAllFeeType={showAllFeeType}
        unvisibleCount={unvisibleCount}
        apportionVisibleList={apportionVisibleList}
        handleCheckFeeType={this.handleCheckFeeType}
        onImportInvoiceClick={this.handleImportInvoiceClick}
        onImportInvoiceOCRClick={this.handleImportInvoiceOCRClick}
        onImportOCRMedicalClick={this.handleImportOCRMedicalClick}
        onImportOverseasInvoiceClick={this.handleImportOverseasInvoiceClick}
        onImportInputInvoiceClick={this.handleImportInputInvoiceClick}
        onImportCSCClick={this.handleImportCSCClick}
        onBatchApportionClick={this.handleBatchApportionClick}
        onBatchRemoveDetailClick={this.handleOnBatchRemoveClick}
        onImportExcelClick={this.handleBatchImport}
        onSelectDataLink={this.handleSelectDataLink}
        onRecordExpendsClick={this.handleRecordExpendsClick} //导入随手记
        onQuickExpendsClick={this.handleQuickExpendsClick} //导入快速报销
        onImportAliPayInvoiceClick={this.handleImportAliPayInvoiceClick}
        onImportAifaPiaoInvoiceClick={this.handleImportAifaPiaoInvoiceClick}
        onThirdPartyImportClick={this.handleThirdPartyImportClick}
        isForbid={isForbid}
        feeDetailUneditable={feeDetailUneditable}
        onCopyDetailClick={this.handleDetailCopy}
        onImportApplyDetail={this.handleImportApplyDetail}
        onRemoveToRecordExpendsClick={this.handleRemoveToRecordExpendsClick} //将明细移除至随手记
        billSpecification={billSpecification}
        inModal={inModal}
        cancelLimit={FLAG.cancelLimit}
        dataFromOrder={dataFromOrder}
        allowSelectionReceivingCurrency={this.getReceivingConfig()}
      />
    )
  }
}
