.element-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  :global {
    .money-wrapper {
      display: flex;
      align-items: center;
      .img-wrapper {
        margin-left: 5px;
      }
      .no-money-style {
        font-family: inherit;
      }
    }
    .formula {
      word-wrap: break-word;
      padding-top: 5px;
    }
    .currency-info {
      color: var(--eui-text-caption);
      .symbol {
        flex-shrink: 0;
      }
      .standard {
        flex-shrink: 0;
      }
      img {
        width: 14px;
        height: 14px;
        margin-left: 5px;
      }
    }
  }
}
