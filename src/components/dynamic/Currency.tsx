import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import CurrencyComponent from "./CurrencyComponent"
interface CPProps {
  [key: string]: any
}
interface CPState {
  [key: string]: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'ref:basedata.Enum.currency'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Curency extends PureComponent<CPProps, CPState> {
  
  render() {
    return <CurrencyComponent {...this.props}/>
  }
}

