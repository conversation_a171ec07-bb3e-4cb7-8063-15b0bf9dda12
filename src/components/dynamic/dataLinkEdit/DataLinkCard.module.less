.content-wrapper{
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  .title {
    color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
    font: var(--eui-font-body-b1);
  }

  .description {
    color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
    font: var(--eui-font-body-r1);
  }
}

.error-wrapper{
  color: var(--eui-function-danger-500, #F53F3F);
  text-align: justify;
  font: var(--eui-font-body-r1);
}

.data-link-card-wrapper{
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.avatar-wrapper{
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-icon{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  height: 48px;
}
