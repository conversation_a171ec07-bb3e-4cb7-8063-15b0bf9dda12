@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';
.trip-item {
    width: 100%;
    border: 1px solid #e5e5e5;
    box-shadow: inset 0px -0.5px 0px #e6e6e6;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px 14px;
    :global {
      .infoTop {
        display: flex;
        align-items: center;
        width: 100%;
        position: relative;
        .trip-type-icon {
          width: 20px;
          height: 20px;
          margin-right: 14px;
          border-radius: 50%;
        }
        .content-wrapper {
          flex: 1;
          .date {
            color: @color-black-1;
            .font-size-3;
            .font-weight-3;
          }
          .city {
            .font-size-2;
            color: @color-black-2;
          }
        }
        .close-icon {
          position: absolute;
          right: 0;
          top: 0;
          width: 20px;
          height: 20px;
        }
      }
      .price-wrapper{
        display: flex;
        padding-left: 34px;
        .content-money {
          font-size: 14px;
          span {
            cursor: pointer;
            color: var(--brand-base);
            margin-right: 30px;
          }
        }
      }
      .custom-field-wrapper {
        padding-left: 34px;
        >div {
          >div {
            padding: 0 2px !important;
          }
        }
      }
    }
  }