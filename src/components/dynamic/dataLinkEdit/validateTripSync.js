import { app as api } from '@ekuaibao/whispered'
import { showMessage } from '@ekuaibao/show-util'
import { get } from 'lodash'

// 同步行程回调
const handleSynchronous = async props => {
  try {
    const { dataSource } = props
    const id = get(dataSource, 'id')
    let res = await api.invokeService('@itinerary-manage:set:sync:trip', { flowId: id })
    return res?.id || ''
  } catch (error) {
    return 'error'
  }
}

// 获取行程同步状态
const getTripSyncStatus = async props => {
  const { dataSource } = props
  const id = get(dataSource, 'id')
  let response = await api.invokeService('@itinerary-manage:get:sync:trip:result', { flowId: id })
  const { result, described } = response?.value
  if (result === 'SUCCESS' && described) showMessage.success(described)
  else if (result === 'FAILED' && described) showMessage.error(described)
  else if (result === 'PENDING' && described) showMessage.warning(described)
  return result
}

// 调研删除校验借口
const delTripSyncValid = async objectId => {
  let params = {
    action: 'TRIP',
    objectId
  }
  let res = await api.invokeService('@itinerary-manage:get:sync:trip:valid', params)
  const { result, failReason } = res.value
  return { result, failReason }
}

// 删除行程回调
const checkingTripSync = async (cur_trip, type = 'delete') => {
  const { dataLinkId = null } = cur_trip[0]
  if (dataLinkId) {
    let response = await delTripSyncValid(dataLinkId)
    const { result, failReason } = response
    if (result === 'FAILED') {
      if (failReason && type === 'delete') showMessage.error(failReason)
      return false
    }
    return true
  }
  return true
}

// 获取草稿态配置
const getTravelManagementConfig = originalId => {
  return new Promise(resolve => {
    api
      .invokeService('@itinerary-manage:get:travelManagementConfig', { type: 'draftSyncTrip' })
      .then(result => {
        const { contextDetail } = result?.value
        if (contextDetail?.specificationIds.includes(originalId) && contextDetail?.enable) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
      .catch(err => {
        resolve(false)
      })
  })
}

// 获取同步按钮显示状态
const getTripSyncBtnStatus = async props => {
  const statusList = ['rejected', 'draft']
  const { dataSource } = props
  const isDraft = statusList.includes(dataSource?.state) ? true : false
  const originalId =
    typeof get(dataSource, 'form.specificationId.originalId') === 'string'
      ? get(dataSource, 'form.specificationId.originalId')
      : get(dataSource, 'form.specificationId.originalId.id')
  let result_config = await getTravelManagementConfig(originalId)
  if (result_config && isDraft) {
    return true
  }
  return false
}

export { handleSynchronous, checkingTripSync, getTripSyncBtnStatus, getTripSyncStatus }
