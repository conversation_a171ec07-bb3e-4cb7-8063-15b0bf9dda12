// @i18n-ignore
import { cloneDeep, get, isString, sortBy, uniqBy } from 'lodash'
import moment from 'moment'
//'FLIGHT', ,'TRAIN',
export let white = ['HOTEL', 'TAXI', 'FOOD', 'COMMON']

export let MoneyWhite = ['FLIGHT', 'HOTEL', 'TRAIN']
export default class Tool {
  allTemplate: any = []
  currentTemplate: any = []
  props: any = {}
  tripManageMap: any = {}
  configs: any = []
  isShowMultipleTrip: boolean = false
  limitEntityIds: any = []
  total: number = 0
  isMoney: boolean = false
  cityGroup: any = {
    FLIGHT: false,
    HOTEL: false,
    TRAIN: false,
    TAXI: false,
    FOOD: false,
    COMMON: false
  }
  constructor(props: any) {
    this.init(props)
  }
  init(props: any) {
    this.props = props
    this.limitEntityIds = props?.field?.limitEntityIds || []
    this.initMap()
  }
  getSubmitValues(arr: any, isDetail?: boolean) {
    this.isMoney = false
    if (!arr) {
      return []
    }
    let number = 0
    let data = arr.map((i: any) => {
      number += this.getMoney(i)
      if (this.isMoney == false) {
        this.isMoney = MoneyWhite.includes(i.tripType)
      }
      return this.getSubmitValue(i, isDetail)
    })

    this.total = number
    return data
  }
  getMoney(data: any) {
    if (MoneyWhite.includes(data?.tripType) && data.money) {
      if (data.tripType == 'HOTEL' && data.endTime && data.startTime) {
        const daysNumber = moment(data.endTime).diff(data.startTime, 'days') || 0
        return daysNumber * data.money
      } else {
        return data.money
      }
    }
    return 0
  }
  getEntityId(obj) {
    let entityItem = this.getTemplateId(obj?.dataLinkTemplateId) || this.getTemplateType(obj?.tripType)
    return entityItem
  }
  getSubmitValue(obj: any, isDetail?: boolean) {
    let entityItem = this.getTemplateId(obj?.dataLinkTemplateId) || this.getTemplateType(obj?.tripType)
    let trip: any = {
      dataLinkId: obj.dataLinkId || null,
      dataLinkTemplateId: entityItem?.templateId,
      dataLinkForm: {},
    }
    if (isDetail) {
      trip.incomplete = false
    }
    if (entityItem) {
      trip.dataLinkForm[this.getKey('场景')] = obj.sceneList ? JSON.stringify(obj.sceneList) : ''
      trip.dataLinkForm[entityItem.bindkeys?.startCity] = obj.startCity
      if (entityItem.bindkeys?.endCity) {
        trip.dataLinkForm[entityItem.bindkeys?.endCity] = obj.endCity
      }
      obj.startCity = obj.startCity === '[]' ? undefined : obj.startCity
      obj.endCity = obj.endCity === '[]' ? undefined : obj.endCity
      if (isDetail && (!obj.startCity || (entityItem.bindkeys?.endCity && !obj.endCity))) {
        trip.incomplete = true
      }

      trip.dataLinkForm[entityItem.bindkeys?.startTime] = obj.startTime
      if (entityItem.bindkeys?.endTime) {
        trip.dataLinkForm[entityItem.bindkeys?.endTime] = obj.endTime
      }
      if (obj.startCity && obj.endCity && entityItem.isCityRange) {
        trip.dataLinkForm[entityItem.bindkeys?.name] = `${JSON.parse(obj.startCity)[0]?.label} - ${JSON.parse(obj.endCity)[0]?.label
          }`
      } else if (obj.startCity) {
        trip.dataLinkForm[entityItem.bindkeys?.name] = `${JSON.parse(obj.startCity)[0]?.label}`
      } else {
        trip.dataLinkForm[entityItem.bindkeys?.name] = undefined
      }
      if (obj.money) {
        trip.dataLinkForm[entityItem.bindkeys?.money] = obj.money
      }
      let type = entityItem?.entity?.type
      if (['CUSTOM', 'TRAIN', 'FLIGHT'].includes(type)) {
        if (entityItem.isCityRange) {
          trip.dataLinkForm[this.getKey('住宿地')] = this.setAllCitys(obj.startCity, obj.endCity)
        } else {
          trip.dataLinkForm[this.getKey('出发地')] = obj.startCity || obj.endCity
          trip.dataLinkForm[this.getKey('目的地')] = obj.endCity || obj.startCity
        }
        if (entityItem.isDateRange) {
          trip.dataLinkForm[this.getKey('行程日期')] = obj.startTime || obj.endTime
        } else {
          trip.dataLinkForm[this.getKey('入住日期')] = obj.startTime || obj.endTime
          trip.dataLinkForm[this.getKey('离店日期')] = obj.endTime || obj.startTime
        }
        if (trip.dataLinkForm[this.getKey('离店日期')] < trip.dataLinkForm[this.getKey('入住日期')]) {
          trip.dataLinkForm[this.getKey('离店日期')] = trip.dataLinkForm[this.getKey('入住日期')]
        }
      }
    }
    return trip
  }
  setAllCitys(citys: string, citye: string) {
    let s = [],
      e = []
    if (citys) {
      s = JSON.parse(citys)
    }
    if (citye) {
      e = JSON.parse(citye)
    }
    let all = uniqBy(s.concat(e), 'key')
    let group = all.find(i => i?.type == 'cityGroup')
    if (group) {
      all = [group]
    }
    return JSON.stringify(all)
  }
  setConfig(data: any[]) {
    this.configs = data
  }
  setCityGroup(data: any) {
    let arr = get(data, 'value.contextDetail') || []
    if (arr.length) {
      arr.forEach(i => {
        i.travelTypes.forEach(u => {
          this.cityGroup[u] = i.multiple
        })
      })
    }
    this.initMap()
  }
  setTemplate(arr: any[]) {
    let u = []
    if (arr.length > 0) {
      arr = cloneDeep(arr)
      u = arr
        .filter(item => item.entity.parentId)
        .map((i: any) => {
          let type = i?.entity?.type
          let config = this.getConfigId(i?.entity?.id, i?.entity?.name)
          //是否内置
          if (white.includes(type)) {
            i = {
              ...i, ...this.tripManageMap[type], configName: config?.name, travelSceneConfig: {
                sceneTypeField: config?.sceneTypeField,
                userItinerarySystemDefaultSelectAll: config?.userItinerarySystemDefaultSelectAll
              }
            }
          } else {
            i = this.setTempData(i)
          }
          return i
        })

      this.allTemplate = u
      const { type, value } = this.props?.field?.defaultValue
      if (this.limitEntityIds?.length > 0) {
        this.currentTemplate = u.filter(i => i?.entity?.active == true && this.limitEntityIds?.includes(i?.entity?.id))
      } else if (type !== 'none' && value.length) {
        this.currentTemplate = u.filter(i => value?.includes(i?.entity?.type))
      } else {
        this.currentTemplate = u.filter(i => i?.entity?.active == true).slice(0, 9)
      }
      this.isShowMultipleTrip = this.currentTemplate.filter(i => i?.isReturn).length > 0
    }
  }
  setTempData(i) {
    let config = this.getConfigId(i?.entity?.id, i?.entity?.name)
    let isDateRange = config?.dateTypeField?.dateType == 'multiple'
    let isCityRange = config?.cityTypeField?.cityType == 'multiple'
    let dates = config?.dateTypeField?.field
    let citys = config?.cityTypeField?.field
    let data = {
      ...i,
      label: isCityRange ? '出发' : '城市',
      labelend: '到达',
      requireText: (isCityRange ? '出发' : '') + '城市不能为空!',
      isDateRange,
      isReturn: false,
      isCityRange,
      isDefault: false,
      bindkeys: {
        startCity: citys && citys[0] ? citys[0] : null,
        endCity: citys && citys[1] ? citys[1] : null,
        startTime: dates && dates[0] ? dates[0] : null,
        name: this.getKey('name'),
        money: this.getKey('参考报价'),
        endTime: dates && dates[1] ? dates[1] : null
      }
    }
    let type = i?.entity?.type
    if (type == 'FLIGHT' || type == 'TRAIN') {
      data.multiple = isCityRange == false ? true : this.cityGroup[type]
      data.isReturn = true
      data.isDefault = true
    }
    if (config.isRoundTrip) {
      data.isReturn = true
    }
    data.configName = config.name
    data.travelSceneConfig = {
      sceneTypeField: config.sceneTypeField,
      userItinerarySystemDefaultSelectAll: config.userItinerarySystemDefaultSelectAll
    }
    data?.entity?.active = config?.active
    return data
  }
  getConfigId(id, name) {
    return this.configs.find(i => {
      return i.id == id || name == i.name
    })
  }
  getCurrentTemp(obj: any) {
    let u = cloneDeep(this.currentTemplate)
    if (obj) {
      let a = this.getTemplateCur(this.currentTemplate, obj)
      if (!a) {
        let objcur = this.getTemplateCur(this.allTemplate, obj)
        if (objcur) {
          objcur = cloneDeep(objcur)
          objcur.entity.active = false
          u.push(objcur)
        }
      }
    }
    return u
  }
  getTemplateId(id: string) {
    return this.allTemplate.find(i => i?.templateId == id)
  }
  getTemplateType(tripType: string) {
    return this.allTemplate.find(j => j?.entity?.type === tripType)
  }
  getTemplateCur(arr, obj) {
    return white.includes(obj.tripType)
      ? arr.find(i => i.entity.type == obj.tripType)
      : arr.find(i => i.templateId == obj.dataLinkTemplateId)
  }
  getValues(arr: any) {
    if (!arr) {
      return []
    }
    return arr
      .map((i: any) => {
        return this.getValue(i)
      })
      .filter(i => i && i.name)
  }
  getValue(obj: any) {
    let entityItem = this.getTemplateId(obj?.dataLinkTemplateId) || this.getTemplateType(obj?.tripType)
    const tripType = get(entityItem, 'entity.type')
    let trip: any = { ...obj }
    trip.dataLinkId = obj.dataLinkId
    trip.editable = this.getTripEditable(obj, this.props?.travelBlackList || [])

    if (entityItem && entityItem.bindkeys) {
      trip.startCity = get(obj, `dataLinkForm.${entityItem.bindkeys.startCity}`)
      trip.endCity = get(obj, `dataLinkForm.${entityItem.bindkeys.endCity}`)
      trip.startTime = get(obj, `dataLinkForm.${entityItem.bindkeys.startTime}`)
      trip.endTime = get(obj, `dataLinkForm.${entityItem.bindkeys.endTime}`)

      trip.money = get(obj, `dataLinkForm.${entityItem.bindkeys.money}`)

      const sceneList = get(obj, `dataLinkForm.${this.getKey('场景')}`)

      try {
        trip.sceneList = isString(sceneList) && sceneList.length ? JSON.parse(sceneList) : undefined
      } catch (error) {

      }

      trip.tripType = tripType
      trip.dataLinkTemplateId = obj.dataLinkTemplateId || entityItem?.templateId
      trip.entity = cloneDeep(entityItem)
      trip.name = this.getCityName(trip)
      if (['CUSTOM', 'TRAIN', 'FLIGHT'].includes(tripType)) {
        if (entityItem.isCityRange) {
          trip.endCity = get(obj, `dataLinkForm.${entityItem.bindkeys.endCity}`) || get(obj, `dataLinkForm.${this.getKey('目的地')}`) || get(obj, `dataLinkForm.${this.getKey('出发地')}`) || get(obj, `dataLinkForm.${this.getKey('住宿地')}`)
        }
        if (entityItem.isDateRange) {
          trip.endTime = get(obj, `dataLinkForm.${entityItem.bindkeys.endTime}`) || get(obj, `dataLinkForm.${this.getKey('离店日期')}`) || get(obj, `dataLinkForm.${this.getKey('入住日期')}`) || get(obj, `dataLinkForm.${this.getKey('行程日期')}`)
        }
        if (!trip.startCity) {
          trip.startCity = get(obj, `dataLinkForm.${this.getKey('住宿地')}`) || get(obj, `dataLinkForm.${this.getKey('出发地')}`) || get(obj, `dataLinkForm.${this.getKey('目的地')}`)
        }
        if (!trip.startTime) {
          trip.startTime = get(obj, `dataLinkForm.${this.getKey('行程日期')}`) || get(obj, `dataLinkForm.${this.getKey('入住日期')}`) || get(obj, `dataLinkForm.${this.getKey('离店日期')}`)
        }
        if (trip.startTime > trip.endTime) {
          trip.endTime = trip.startTime
        }
        trip.name = this.getCityName(trip)
        // console.log(trip)
      }
    }
    return this.getTripEditableByTime(trip)
    // return trip
  }
  getCityName(obj: any) {
    const pathKey = i18n.currentLocale === 'en-US' ? 'enLabel' : 'label'
    if (obj.startCity && obj.endCity) {
      const sCity = JSON.parse(obj.startCity)?.[0]
      const eCity = JSON.parse(obj.endCity)?.[0]
      return `${sCity?.[pathKey] || sCity?.label} - ${eCity?.[pathKey] || eCity?.label}`
    } else if (obj.startCity) {
      const sCity = JSON.parse(obj.startCity)[0]
      return `${sCity?.[pathKey] || sCity?.label}`
    }
    return null
  }

  getValueConfigs(arr: any) {
    if (!arr) {
      return []
    }
    return arr.map((i: any) => {
      return this.getValueConfig(i)
    })
  }
  getValueConfig(obj: any) {
    let entityItem = this.getTemplateId(obj?.dataLinkTemplateId) || this.getTemplateType(obj?.tripType)
    const tripType = get(entityItem, 'entity.type')
    let trip: any = { ...obj }
    trip.dataLinkId = obj.dataLinkId
    if (entityItem && entityItem.bindkeys) {
      trip.tripType = tripType
      trip.dataLinkTemplateId = obj.dataLinkTemplateId || entityItem?.templateId
      trip.entityItem = cloneDeep(entityItem)
      trip.entity = trip.entityItem
    }
    return trip
  }
  getKey(name: string) {
    if (!this.key) {
      let u = this.props?.field?.referenceData?.fields || []
      let name = u[0].name
      this.key = name.substr(0, name.lastIndexOf('_') + 1)
    }
    return this.key + name
  }

  getLabel(label: string) {
    let u = this.props?.field?.referenceData?.fields || []
    return u.find(i => i.label == label)?.name
  }
  initMap() {
    this.tripManageMap = {
      FLIGHT: {
        label: '出发',
        labelend: '到达',
        requireText: '出发城市不能为空!',
        isDateRange: false,
        isReturn: true,
        isCityRange: true,
        isDefault: true,
        multiple: this.cityGroup['FLIGHT'],
        bindkeys: {
          startCity: this.getKey('出发地'),
          endCity: this.getKey('目的地'),
          startTime: this.getKey('行程日期'),
          name: this.getKey('name'),
          money: this.getKey('参考报价'),
          endTime: null
        }
      },
      HOTEL: {
        label: '住宿',
        requireText: '住宿城市不能为空!',
        isDateRange: true,
        isReturn: false,
        isCityRange: false,
        isDefault: true,
        multiple: this.cityGroup['HOTEL'],
        bindkeys: {
          startCity: this.getKey('住宿地'),
          endCity: null,
          startTime: this.getKey('入住日期'),
          name: this.getKey('name'),
          money: this.getKey('参考报价'),
          endTime: this.getKey('离店日期')
        }
      },
      TRAIN: {
        label: '出发',
        requireText: '出发城市不能为空!',
        isDateRange: false,
        isReturn: true,
        isCityRange: true,
        isDefault: true,
        multiple: this.cityGroup['TRAIN'],
        bindkeys: {
          startCity: this.getKey('出发地'),
          endCity: this.getKey('目的地'),
          startTime: this.getKey('行程日期'),
          name: this.getKey('name'),
          money: this.getKey('参考报价'),
          endTime: null
        }
      },
      TAXI: {
        label: '城市',
        requireText: '城市不能为空!',
        isDateRange: true,
        isReturn: false,
        isCityRange: false,
        isDefault: true,
        multiple: this.cityGroup['TAXI'],
        bindkeys: {
          startCity: this.getKey('住宿地'),
          endCity: null,
          startTime: this.getKey('入住日期'),
          name: this.getKey('name'),
          endTime: this.getKey('离店日期')
        }
      },
      FOOD: {
        label: '城市',
        requireText: '城市不能为空!',
        isDateRange: true,
        isReturn: false,
        isCityRange: false,
        isDefault: true,
        multiple: this.cityGroup['FOOD'],
        bindkeys: {
          startCity: this.getKey('住宿地'),
          endCity: null,
          startTime: this.getKey('入住日期'),
          name: this.getKey('name'),
          endTime: this.getKey('离店日期')
        }
      },
      COMMON: {
        label: '城市',
        requireText: '城市不能为空!',
        isDateRange: true,
        isReturn: false,
        isCityRange: false,
        isDefault: true,
        multiple: this.cityGroup['COMMON'],
        bindkeys: {
          startCity: this.getKey('住宿地'),
          endCity: null,
          startTime: this.getKey('入住日期'),
          name: this.getKey('name'),
          endTime: this.getKey('离店日期')
        }
      }
    }
  }
  // 申请单回退校验行程是否可编辑
  getTripEditable(trip, tripList) {
    // const curTrip = tripList.find(i => i.travelInfoId === trip.dataLinkId)
    // return curTrip?.isWithdrawn ?? true
    return !tripList.find(i => i === trip.dataLinkId)
  }

  getTripDateEditable(trip, tripList) {
    if (!trip) {
      return false
    }
    let entityItem = this.getTemplateId(trip?.dataLinkTemplateId) || this.getTemplateType(trip?.tripType)
    let editable = this.getTripEditable(trip, tripList)
    return !editable ? entityItem.isDateRange : true
  }

  /**
   * @description 判断行程开始日期是否为第二天及之后
   * @param trip
   * @returns
   */
  getTripEditableByTime(trip) {
    const { startTime } = trip;
    const editable = startTime > moment().endOf('day').format('x')
    return {
      ...trip,
      // TODO: 此处先注释
      // editable
    }
  }
}
