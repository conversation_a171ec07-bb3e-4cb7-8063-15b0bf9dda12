@import '~@ekuaibao/eui-styles/less/token.less';

.data-link-err {
  .font-size-2;
  .font-weight-2;
}
.data-link-red {
  .font-size-2;
  .font-weight-2;
  color: @color-error;
}

.data-link-single {
  width: 100%;
  padding: 16px 16px 1px;
  border-radius: 8px;
  background: var(--eui-bg-body-overlay, #F7F8FA);
  position: relative;

  &-clear {
    color: var(--eui-primary-pri-500, #2555FF);
    font: var(--eui-font-note-r2);
    cursor: pointer;
    position: absolute;
    right: 0;
    top: -23px;
  }

  [data-cy="TagSelector@Input"] {
    background-color: #fff;
  }
}
.data-link-input-wrapper {
  width:100%;
  :global {
    .link-card-box {
      display: flex;
      justify-content: left;
      align-items: center;
      cursor: pointer;
      padding: 12px;
      border-bottom: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.10));
    }
    .link-card-box:hover {
      background: rgba(29, 43, 61, 0.06);
      box-shadow: 0px 0px 0px 0px rgba(29, 43, 61, 0.15);
    }
    .link-card-box-only {
      padding: 16px 12px;
      height: 78px;
      border-bottom: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.10));
      background-color: #ffffff;
    }

    .card-bottom {
      .font-size-2;
      height: 22px;
      margin-top: @space-4;
      font-weight: 400;
      color: @color-brand-2;
      span {
        cursor: pointer;
        &:nth-child(1) {
          margin-right: @space-6;
        }
      }
    }
    .datalink-select {
      display: flex;
      padding-bottom: 8px;
    }
    .ant-input {
      cursor: pointer;
    }
  }
}
