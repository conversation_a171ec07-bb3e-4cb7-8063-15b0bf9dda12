import React, { PureComponent } from 'react'
import moment from 'moment'
import { Avatar } from '@hose/eui'
import { isObject, isArray } from '@ekuaibao/helpers'
import { get } from 'lodash'
import { OutlinedEditDeleteTrash } from '@hose/eui-icons'
import styles from './DataLinkCard.module.less'
import classNames from 'classnames'

export default class DataLinkCard extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      temp: null
    }
  }
  componentDidMount() {
    let { templates, value } = this.props
    this.updateTemp(templates, value)
  }
  updateTemp = (templates, value) => {
    let t = templates.find(item => {
      return item.templateId === value.dataLinkTemplateId
    })
    if (t && t.listTemplate) {
      this.setState({
        temp: t.listTemplate.content.expansion
      })
    }
  }
  componentWillReceiveProps = np => {
    if (np.templates !== this.props.templates) {
      this.updateTemp(np.templates, np.value)
    }
  }
  getKey = (data, k) => {
    let t = Object.keys(data)[0]
    t = t.substr(0, t.lastIndexOf('_')) + k.substr(k.lastIndexOf('_'))
    return data[t]
  }
  matchList = (fields, data) => {
    let arr = []
    fields.forEach(item => {
      try {
        const val = this.getKey(data, item.name)

        if (item.type === 'text' || item.type === 'number' || item.type === 'code') {
          val && arr.push(val)
        } else if (item.type === 'money') {
          val && arr.push(`${val.standard}${val.standardSymbol}`)
        } else if (item.type === 'switcher' && typeof val === 'boolean') {
          arr.push(val ? i18n.get('启用中') : i18n.get('已停用'))
        } else if (item.type === 'date' && val) {
          if (typeof val === 'number') {
            arr.push(moment(val).format(`YYYY/MM/DD`))
          } else if (val.end) {
            arr.push(moment(val.start).format(`YYYY/MM/DD`) + i18n.get('至') + moment(val.end).format(`YYYY/MM/DD`))
          }
        } else if (item.type === 'ref') {
          if (item.entity === 'basedata.city' && typeof val === 'string') {
            const ts = JSON.parse(val)
            Array.isArray(ts) && arr.push(ts.map(i => i.label).join(','))
          } else {
            if (isObject(val)) {
              arr.push(val.name)
            } else if (isArray(val)) {
              arr.push(val.map(item => item.name).join(','))
            }
          }
        }
      } catch (e) { }
    })
    return arr.join(' | ')
  }

  getAvatarData = () => {
    const { value } = this.props
    const { temp } = this.state
    const fields = get(temp, 'personalOnLeft.fields', [])
    if (!fields.length) {
      return ''
    }
    const field = fields[0]
    const path = `dataLinkForm.${field.name}`
    const user = get(value, path)
    return get(user, 'avatar', '')
  }

  renderAvatar = () => {
    const avatar = this.getAvatarData()
    return <Avatar src={avatar} size="large" shape="circle" />
  }

  render() {
    let { value, onDelete, onlineClick, isShowError } = this.props
    let { temp } = this.state
    if (temp === null) {
      return null
    }
    const isShowPersonalOnLeft = get(temp, 'isShowPersonalOnLeft', false)
    const isShowDelete = typeof onDelete === 'function'
    const titleValue = this.matchList(temp.title.fields, value.dataLinkForm)
    const descriptionValue = this.matchList(temp.description.fields, value.dataLinkForm)
    return (
      <div className={styles['data-link-card-wrapper']} onClick={onlineClick}>
        {isShowDelete && <span className={styles['delete-icon']} onClick={(e) => {
          e.stopPropagation();
          onDelete()
        }}>
          <OutlinedEditDeleteTrash fontSize={16} color='var(--eui-icon-n2)' />
        </span>}
        {isShowPersonalOnLeft && this.renderAvatar()}
        <div className={classNames(styles['content-wrapper'], "content-wrapper")}>
          {titleValue && <div className={styles['title']}>{titleValue}</div>}
          {descriptionValue && <div className={styles['description']}>{descriptionValue}</div>}
          {isShowError && <div className={styles['error-wrapper']}>存在问题，请修改</div>}
        </div>
      </div>
    )
  }
}
