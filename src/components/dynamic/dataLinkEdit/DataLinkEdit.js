import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { dataLinkEditValidator } from '../../validator/validator'
import { wrapperDataLinkEdit } from '../../layout/FormWrapper'
import { required } from '../../validator/validator'
import DataLinkEditComp from './DataLinkEditComp'

@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData, name } = field
      return (type === 'dataLinkEdits' && referenceData.type !== 'TRIP') || name === 'budgetAdjustDetails'
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level != 0 || field.name === 'budgetAdjustDetails') {
      return callback()
    }
    let { behaviour = 'INSERT' } = field

    if (!['UPDATE', 'REF'].includes(behaviour)) {
      return dataLinkEditValidator(field, rule, value, callback)
    }

    return callback(required(field, value))
  },
  wrapper: wrapperDataLinkEdit(false, '', {}, false, false, true)
})
export default class DataLinkEdit extends PureComponent {
  render() {
    return <DataLinkEditComp {...this.props} /> //组件拆出来，别的地方也用到了
  }
}
