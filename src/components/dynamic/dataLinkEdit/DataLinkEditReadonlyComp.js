import React, { PureComponent } from 'react'
import styles from './DataLinkEdit.module.less'
import { app as api } from '@ekuaibao/whispered'
import DataLinkEditSingle from './DataLinkEditSingle'
import MessageCenter from '@ekuaibao/messagecenter'
import DataLinkCard from './DataLinkCard'
import { parseAsMeta } from '@ekuaibao/lib/lib/parse'
import { cloneDeep } from 'lodash'
import DataLinkEditTable from './DataLinkEditTable'
import DataLinkEditHeader from './DataLinkEditHeader'
import { getBudgetAdjustColumns, getBudgetAdjustDetails } from '../../utils/BudgetAdjust'
import { getTrueKey } from '../../../lib/utils'
import CarBusinessCard from '../../../elements/dataLink-card/MyCarBusinessCard'
import { getBoolVariation } from '../../../lib/featbit'
export default class DataLinkEditReadonly<PERSON>omp extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.fieldBus ?? new MessageCenter()
    let { value } = this.props
    this.state = {
      templates: [],
      templatesold: [],
      items: value && value.length > 0 ? value : []
    }
  }

  componentDidMount() {
    this.getTemplate()
    this.bus.watch('element:datalink:card:click', this.handleSelectDataLink)
  }
  componentWillUnmount() {
    this.bus.un('element:datalink:card:click', this.handleSelectDataLink)
  }
  handleSelectDataLink = ({ entityInfo, field, showClose, disabledStaff }) => {
    const { ability = '', name = '' } = field
    if (ability === 'contractSettle') {
      disabledStaff = true
    }
    if (getTrueKey(name) === 'relationContract') {
      disabledStaff = true
    }
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: { ...entityInfo, entityId: field.referenceData },
      field,
      showClose,
      disabledStaff
    })
  }
  resetTemp = items => {
    return items.map(entityType => {
      let nt = cloneDeep(entityType)
      const fields = entityType.selectedFields
      let components = parseAsMeta(entityType, cloneDeep(fields), true)
      let temp = components.filter(i => {
        // @i18n-ignore
        return i.name != 'visibility' && i.name != 'active' && i.name != 'ownerId'
      })
      nt.components = temp
      return nt
    })
  }

  loadBudgetAdjustDetails = () => {
    const { value } = this.props
    if (value?.length) {
      getBudgetAdjustDetails({ ids: value }).then(res => {
        if (res?.items?.length) {
          const entityId = res.items[0].entityId
          this.columns = getBudgetAdjustColumns(entityId)
          this.setState({ templates: this.columns, items: res.items })
        }
      })
    }
  }

  getTemplate = async () => {
    let {
      field: { behaviour = 'INSERT', subTypeId, name, showTemplateId: tableTemplateId, showType },
      value
    } = this.props
    if (name === 'budgetAdjustDetails') {
      this.loadBudgetAdjustDetails()
      return
    }
    const res = await api
      .invokeService('@bills:get:getDataLinkEditTemplate', {
        id: this.props.field.referenceData.id,
        type: behaviour === 'INSERT',
        tableTemplateId,
        needFormula: getBoolVariation('ao-2-datalink-show-calculate') ? true : false
      })
    const items = subTypeId ? res.items.filter(i => i?.entity?.id === subTypeId) : res.items
    let ntes = this.resetTemp(items)
    let its = ntes.filter(i => i.entity.active === true)
    if (behaviour === 'REF' && value?.length) {
      const dataLinkIds = value.map(v => v.dataLinkId)
      try {
        const resp = await api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds, type: showType })
        let data = resp.items || []
        const newValue = value.map((it, idx) => {
          const cur = data.find(v => v.data.dataLink.id === it.dataLinkId)
          // CYXQ-67858 工单问题解决，兼容处理
          if (getBoolVariation('cyxq-67858')) {
            return { ...it, dataLinkForm: cur?.data?.dataLink }
          }
          const dataLinkForm = {}
          Object.keys(it.dataLinkForm).forEach(key => {
            dataLinkForm[key] = cur?.data?.dataLink[key]
          })
          return { ...it, dataLinkForm: dataLinkForm }
        })
        this.setState({ items: newValue, templates: its, templatesold: ntes })
      } catch (error) {
        this.setState({ templates: its, templatesold: ntes })
      }
    } else {
      this.getDataLinkEditItems(its, ntes)
    }
  }
  getDataLinkEditItems = (its, ntes) => {
    const { field, flowId, detailId, tripId, isDetail = false, isTrip = false, shouldUpdate = true } = this.props
    let params = undefined
    if (flowId) {
      if (isDetail && detailId && shouldUpdate) {
        //type 类别:form(表头), details(明细),trips(行程的明细)
        params = { flowId, detailId, type: 'details', fieldName: field.name }
      } else if (isTrip && tripId && shouldUpdate) {
        params = { flowId, detailId: tripId, type: 'trips', fieldName: field.name }
      } else if (!isDetail && !isTrip) {
        params = { flowId, type: 'form', fieldName: field.name }
      }
    }
    if (params) {
      api.invokeService('@bills:get:getDataLinkEditItems', { ...params }).then(res => {
        let object = {
          templates: its,
          templatesold: ntes
        }
        if (res.items && Array.isArray(res.items) && res.items.length > 0) {
          object.items = res.items
        }
        this.setState(object)
      })
    } else {
      this.setState({
        templates: its,
        templatesold: ntes
      })
    }
  }

  handleDetail = (obj, index) => {
    const { bus, external, field } = this.props
    let { templatesold } = this.state
    if (field?.name === 'budgetAdjustDetails') return

    if (field?.referenceData?.platformId?.type === 'PRIVATE_CAR') {
      return api.open('@bills:BillStackerModal', {
        viewKey: 'MyCarBusinessInfo',
        referenceData: field?.referenceData,
        data: { form: obj.dataLinkForm },
        isMulti: true
      })
    }

    if (field.behaviour === 'REF') {
      api.open('@bills:DataLinkDetailModal', {
        entityInfo: { dataLink: { id: obj.dataLinkId }, entityId: field.referenceData },
        field,
        showClose: true,
        disabledStaff: false
      })
    } else {
      api.open('@bills:SelectDataLinkEditModal', {
        entityInfo: {
          value: { ...obj },
          status: 'only',
          field,
          templates: templatesold,
          templatesold,
          external,
          bus
        }
      })
    }
  }
  renderList = () => {
    let { items, templatesold } = this.state
    const { field } = this.props
    const type = field?.referenceData?.platformId?.type
    if (items.length === 0) {
      return ''
    }
    return items.map((i, index) => {
      if (type === 'PRIVATE_CAR') {
        return (
          <div className={styles['dataLink-input-wrapper']}>
            <CarBusinessCard field={this.props.field} value={i} isMulti={true} />
          </div>
        )
      }
      return (
        <div className="link-card-box link-card-box-only" key={index}>
          <DataLinkCard value={i} templates={templatesold} onlineClick={() => this.handleDetail(i, index)} />
        </div>
      )
    })
  }
  componentWillReceiveProps(np) {
    if (np.value != this.props.value) {
      this.setState(
        {
          items: np.value && np.value.length > 0 ? np.value : []
        },
        () => {
          this.getTemplate()
        }
      )
    }
  }

  handleRowClick = (record) => {
    const result = this.state.items[record.index]
    this.handleDetail(result)
  }
  render() {
    let {
      field: { label, importMode, behaviour = 'INSERT', showType, name },
      elements,
      field,
      bus,
      showHeader = true,
      external,
      isForbid,
      flowId
    } = this.props
    let { items, templatesold, templates } = this.state
    const isBudgetAdjust = name === 'budgetAdjustDetails'
    if (showType === 'TABLE' || isBudgetAdjust) {
      return (
        <DataLinkEditTable
          field={field}
          dataSource={items}
          templates={templates}
          templatesold={templatesold}
          bus={this.bus}
          flowBus={bus}
          external={external}
          isForbid={isForbid}
          flowId={flowId}
          onRowClick={this.handleRowClick}
        />
      )
    }
    if (!items || (items && items.length === 0)) {
      return (
        <div>
          <DataLinkEditHeader field={field} />
          {i18n.get('无')}
        </div>
      )
    }
    if (importMode === 'SINGLE' && behaviour === 'INSERT') {
      return (
        <div className={styles['data-link-input-wrapper']}>
          {showHeader && <DataLinkEditHeader field={field} />}
          <DataLinkEditSingle
            elements={elements}
            bus={this.bus}
            value={items[0] || {}}
            templates={templatesold}
            label={label}
            templatesold={templatesold}
            handleType={() => { }}
            status={'only'}
          />
        </div>
      )
    } else {
      return (
        <div className={styles['data-link-input-wrapper']}>
          {showHeader && <DataLinkEditHeader field={field} isForbid={isForbid} external={external} flowId={flowId} />}
          {showType === 'FIELD' ? (
            <DataLinkEditSingle
              elements={elements}
              bus={this.bus}
              value={items[0] || {}}
              templates={templatesold}
              label={label}
              templatesold={templatesold}
              handleType={() => { }}
              status={'only'}
            />
          ) : (
            this.renderList()
          )}
        </div>
      )
    }
  }
}
