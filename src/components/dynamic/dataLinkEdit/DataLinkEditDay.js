import styles from './DataLinkEditDay.module.less'
import React, { Component } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Dynamic } from '@ekuaibao/template'
import Loading from '@ekuaibao/loading'
import { Form } from 'antd'
import { debounce } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getBoolVariation } from '../../../lib/featbit'
/**
* PRP-20749
* 由于第一次渲染textarea未渲染出,使用getValue获取值不全
* 固使用了debounce,经过多次尝试后确定为200
*/
function create(T) {
  return Form.create({
    onValuesChange: debounce((props, changedValues) => {
      if (!changedValues) return
      props.bus.emit('dynamic:value:changed', changedValues)
    }, 200)
  })(T)
}

@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data
}))
export default class DataLinkEditDay extends Component {
  state = {
    needCalculateUpdateFields: null
  }
  constructor(props) {
    super(props)
  }
  componentDidMount() {
    const { bus } = this.props
    bus.on('dynamic:value:changed', this.handleChange)
    this.getFormulaValues()
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('dynamic:value:changed', this.handleChange)
  }
  getNeedCalculateUpdateFields = () => {
    const { template } = this.props
    const { needCalculateUpdateFields } = this.state
    if (needCalculateUpdateFields) {
      return needCalculateUpdateFields
    }
    const calculateUpdateFields = []
    template?.entity?.fields?.forEach((item) => {
      if (item.formula) {
        calculateUpdateFields.push(item.name)
      }
    })
    this.setState({
      needCalculateUpdateFields: calculateUpdateFields
    })
    return calculateUpdateFields
  }
  // 自动计算
  getFormulaValues = debounce(async () => {
    if (!getBoolVariation('ao-2-datalink-show-calculate')) return
    try {
      const { bus, template, dataLinkId } = this.props
      let formValue = await bus.getValue()
      const params = {
        dataLinkFormList: [{
          form: formValue,
          dataLinkId
        }],
        dataLinkEntityId: template?.entity?.id
      }
      const result = await api.invokeService('@bills:get:getDataLinkEditCalculate', params)
      const newValues = {}
      const needCalculateUpdateFields = this.getNeedCalculateUpdateFields()
      Object.keys(result[0].form).forEach((key) => {
        if (needCalculateUpdateFields.includes(key)) {
          newValues[key] = result[0].form[key]
        }
      })
      bus.setValue(newValues)
    } catch (error) {
      console.log(error)
    }
  }, 500)

  debouncedGetFormulaValues = debounce(this.getFormulaValues, 500)
  
  handleChange = e => {
    const { template } = this.props
    let isNeedFormula = false
    if (Object.keys(e).length > 0) {
      const c = template?.entity?.fields?.find(item => item.name === Object.keys(e)[0])
      isNeedFormula = Boolean(c && c.calculation.dependenciesBy.length)
    }
    if (isNeedFormula) {
      this.getFormulaValues()
    }
  }
  renderTemplate = () => {
    const { value, bus, template, flowId = '', elements } = this.props
    if (!template || template.length == 0) {
      return null
    }
    let temp = template.components
    return (
      <Dynamic
        canRealTime
        bus={bus}
        create={create}
        layout="vertical"
        loading={Loading}
        elements={elements}
        template={temp}
        value={value}
        flowId={flowId}
        currencySwitch={true}
        baseDataProperties={this.props.baseDataProperties}
      />
    )
  }
  render() {
    return <div className={styles['data-link-edit-warp']}>{this.renderTemplate()}</div>
  }
}
