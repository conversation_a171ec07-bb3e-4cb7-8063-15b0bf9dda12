import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, get, sortBy, uniqBy, isString } from 'lodash'
import { wrapperDataLinkEdit } from '../../layout/FormWrapper'
import { tripDataLinkEditValidator, tripDataLinkSceneValidator } from '../../validator/validator'
import { app as api } from '@ekuaibao/whispered'
import TripCard from './TripCard'
import styles from './TripDataLink.module.less'
import AddTripCard from '../../../plugins/bills/layers/add-trip-modal/AddTripCard'
import { filterTripTemplate } from '../utils'
import TripTool from './TripTool'
import { MoneyMath } from '@ekuaibao/money-math'
import { standardValueMoney } from '../../../lib/misc'
import { message, Popover } from 'antd'
import { Button } from '@hose/eui'
import { Modal } from '@hose/eui'
const OPENLIST = ['FLIGHT', 'HOTEL', 'TRAIN']
import moment from 'moment'
import TravelClosedLoop from '../TravelClosedLoop'
import { isZero } from '../../../lib/misc';
import { checkingTripSync, getTripSyncBtnStatus, getTripSyncStatus } from './validateTripSync'
import { OutlinedTipsAdd, OutlinedDirectionSwitch } from "@hose/eui-icons"
import { Resource } from '@ekuaibao/fetch'
const v2CityGroup = new Resource('/api/v2/basedata/cityGroup/')
const { getBoolVariation } = api.require('@lib/featbit');
@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData } = field
      return type === 'dataLinkEdits' && referenceData.type === 'TRIP'
    }
  },
  validator: (field, props) => async (rule, value) => {
    if (rule.level != 0) {
      return Promise.resolve()
    }
    // 先校验场景
    const sceneError = await tripDataLinkSceneValidator(field, props)
    if (sceneError) throw sceneError
  
    // 再校验内容
    const editError = tripDataLinkEditValidator(field, value)
    if (editError) throw editError
  
    return Promise.resolve()
  },
  wrapper: wrapperDataLinkEdit(false, '', {}, false, true)
})
@EnhanceConnect(state => ({
  powersList: state['@common'].powers.powersList || [],
  travelBlackList: state['@bills'].travelBlackList,
  travelManagementConfig: state['@tpp-v2'].travelManagementConfig
}))
export default class TripDataLink extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    let { field } = this.props
    field.fieldBus = this.bus
    field.fieldBus.onChange = props.onChange

    this.tripTool = new TripTool(this.props)
    this.state = {
      tripsList: [],
      field: field,
      template: [],
      tripsTemplate: [],
      loading: true,
      intents: [],
      isDetailChange: false,
      showTripSyncBtn: false,
      travelerList: [],
      AITripContextDetail: null,
      customTripTypeList: [],
      entityFormList: []
    }
    this.originalTrip = props.value
  }
  formatValue = (value) => {
    let u = this.tripTool.getValues(value)
    return u
  }
  componentWillMount = async () => {
    let cityGroup = await api.invokeService('@tpp-v2:get:travelManagementConfig', { type: 'cityGroup' })
    this.tripTool.setCityGroup(cityGroup)
    await api.invokeService('@tpp-v2:get:travelManagementConfig', { type: 'tripBlackList' })
    this.getTripsTemplate()
    await api.invokeService('@tpp-v2:get:getTravelManagement').then(res => {
      this.setState({ customTripTypeList: res?.items || [] })
      this.tripTool.setConfig(res?.items || [])
    })
    await this.getTemplate()
    getBoolVariation('fkrd-4299-ai-trip-planning') && await this.getAITripConfig()
    const { bus } = this.props
    bus.on('set:tripdatalink:value', this.setTripDataLinkValue)
    this.setState({
      loading: false
    })
    bus.on('set:tripdatalink:traveler:change', this.updateMoney)
    bus.watch('get:tripdatalink:value', this.getTripDataLinkValue)
    bus.watch('tripdataLink:checkDateValiable', this.checkDateValiable)
    bus.watch('tripDataLink:get:traverler:list', this.getTravelerList)
    let res_showtripbtn = await getTripSyncBtnStatus(this.props)
    this.setState({
      showTripSyncBtn: res_showtripbtn
    })
  }
  componentDidMount() {
    this.props.bus.setValidateLevel(-1)

  }

  getTravelerList = async () => {
    const { bus, isDetail } = this.props
    if (!isDetail) {
      const value = await bus.getFieldsValue()
      const submitter = value?.submitterId || {}
      const submitterId = isString(submitter) ? submitter : submitter?.id
      const travelerList = value?.travelers?.map(v => isString(v) ? v : v?.id)?.filter(v => v) || []
      if (submitterId && !travelerList.length) {
        travelerList.push(submitterId)
      }
      return travelerList
    } else {
      const billsValue = await api.invoke('get:bills:value')
      const value = await bus.getFieldsValue()
      const submitter = billsValue?.values?.submitterId || {}
      const submitterId = isString(submitter) ? submitter : submitter?.id
      const travelers = value?.travelers || []
      let travelerList = travelers?.map(v => isString(v) ? v : v?.id)?.filter(v => v) || []
      if (!travelerList.length) {
        travelerList = billsValue?.values?.travelers?.map(v => isString(v) ? v : v?.id)?.filter(v => v) || []
      }
      if (submitterId && !travelerList.length) {
        travelerList.push(submitterId)
      }
      return travelerList
    }
  }

  checkDateValiable = (tripInfo) => {
    const { value, travelBlackList } = this.props
    if (!this.originalTrip) return
    const prevTrip = tripInfo ? this.originalTrip?.find(v => v.dataLinkId === tripInfo.dataLinkId) : this.originalTrip?.[0]
    const currentTrip = (tripInfo ? tripInfo : value?.[0]) || {}
    const relatedOrder = !this.tripTool.getTripEditable(currentTrip, travelBlackList)
    const entityItem = this.tripTool.getTemplateId(currentTrip?.dataLinkTemplateId)
    if (!prevTrip?.dataLinkId || !currentTrip?.dataLinkId || !relatedOrder || !entityItem?.isDateRange) return
    let startTime
    let endTime
    let prevStartTime
    let prevEndTime
    Object.keys(prevTrip?.dataLinkForm).forEach(key => {
      if (key?.includes('入住日期')) {
        startTime = tripInfo ? currentTrip.startTime : currentTrip?.dataLinkForm?.[key]
        prevStartTime = prevTrip?.dataLinkForm?.[key]
      }
      if (key?.includes('离店日期')) {
        endTime = tripInfo ? currentTrip.endTime : currentTrip?.dataLinkForm?.[key]
        prevEndTime = prevTrip?.dataLinkForm?.[key]
      }
    })

    if (JSON.stringify([startTime, endTime]) !== JSON.stringify([prevStartTime, prevEndTime])) {
      return api.invokeService('@bills:check:purchase:travel', {
        travelId: currentTrip.dataLinkId,
        startTime,
        endTime
      }).catch(error => {
        if (error.errorMessage) {
          Modal.error({
            title: error?.errorMessage,
          })
          return 'ERROR'
        }
      })
    }
  }

  getTripDataLinkValue = async () => {
    return this.state.tripsList
  }

  getTripsTemplate = () => {
    return api.invokeService('@bills:get:getTripsTemplate').then(res => {
      const tripsTemplate = res.items
      this.setState({
        tripsTemplate
      })
    })
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('set:tripdatalink:value', this.setTripDataLinkValue)
    bus.un('set:tripdatalink:traveler:change', this.updateMoney)
    bus.un('get:tripdatalink:value', this.getTripDataLinkValue)
    bus.un('tripdataLink:checkDateValiable', this.checkDateValiable)
    bus.un('tripDataLink:get:traverler:list', this.getTravelerList)
  }

  async componentWillReceiveProps(nextProps) {
    const { value, field: { behaviour = 'INSERT' } }  = nextProps
    const { field, customTripTypeList } = this.state
    const templete = await api.invokeService('@bills:get:getDataLinkEditTemplate', {
      id: field.referenceData.id,
      type: behaviour === 'INSERT'
    })
    const entityFormList = filterTripTemplate(templete?.items, field)

    const shouldShowError = value?.some(item => {
      const { dataLinkTemplateId, dataLinkForm } = item
      if (!dataLinkTemplateId) return false
    
      const currentEntity = entityFormList?.find(entity => entity?.templateId === dataLinkTemplateId)
      const customTrip = customTripTypeList?.find(trip => trip?.entityId === currentEntity?.entity?.id) 
      if (!customTripTypeList || !customTrip) return true
      return customTrip?.sceneTypeField === 'open_required' && (!Object.keys(dataLinkForm).some(key => key.includes('场景') && dataLinkForm[key]))
      // 目前AI创建的行程数据中不会有场景属性，所以这里先只检查key
    })

    api.watch('checkSceneRequired', () => {
      return shouldShowError ? 'invalid' : 'valid'
    })
  }

  // 修改出行人清除行程规划
  setTripDataLinkValue = () => {
    const { bus, isDetail } = this.props
    if (isDetail) {
      bus.emit('set:trip:value')
    } else {
      bus.setFieldsValue({
        ['u_行程规划']: [] // @i18n-ignore
      })
      this.setState({
        tripsList: []
      })
    }
  }

  getAITripConfig = async () => {
    api.invokeService('@itinerary-manage:get:travelManagementConfig', { type: 'AITravelPlanConfig' }).then(res => {
      this.setState({ AITripContextDetail: res?.value?.contextDetail || {} })
    })
  }

  getTemplate = () => {
    let {
      field: { behaviour = 'INSERT' },
      onChange,
      value = []
    } = this.props
    const { field } = this.state
    return api
      .invokeService('@bills:get:getDataLinkEditTemplate', {
        id: field.referenceData.id,
        type: behaviour === 'INSERT'
      })
      .then(res => {
        const template = filterTripTemplate(res.items, field)
        this.setState({
          entityFormList: template
        })
        const newValue = []
        value.forEach(item => {
          const { dataLinkTemplateId } = item
          if (template.find(j => j.templateId === dataLinkTemplateId)) {
            newValue.push(item)
          }
        })
        if (newValue.length !== value.length) {
          onChange && onChange(newValue)
        }
        this.tripTool.init(this.props)
        this.tripTool.setTemplate(res.items)
        this.setState({
          template: this.tripTool.currentTemplate || [],
          tripsList: this.formatValue(newValue, template)
        })
      })
  }

  handleChange = list => {
    list = list.map((i, index) => {
      i.editable = this.state.tripsList[index]?.editable ?? true
      return i
    })

    const result = this.sortList(list)
    let values = this.tripTool.getValueConfigs(result)
    this.setState({
      tripsList: values
    })
    let { isDetail } = this.props
    let { isDetailChange } = this.state
    let u = this.tripTool.getSubmitValues(result, isDetail)
    if (isDetail) {
      if (isDetailChange == false) {
        this.setState({
          isDetailChange: true
        })
      } else {
        setTimeout(() => {
          this.setMoney()
        }, 300)
      }
    } else {
      setTimeout(() => {
        this.setMoney()
      }, 300)
    }

    let { onChange } = this.props
    onChange && onChange(u)

    this.handleAutoAssignExpenseTypeFields(u)
  }

  updateMoney = users => {
    setTimeout(() => {
      const { tripsList } = this.state
      if (tripsList.length) {
        message.warning('更新出行人后，需要重新选择行程的场景')
      }
      let { onChange } = this.props
      let u = this.tripTool.getSubmitValues(tripsList)
      onChange && onChange(u)
      this.setMoney()
    }, 300)
  }
  setMoney = async () => {
    let { template = [] } = this.props
    let money = this.tripTool.total || 0
    let amount = 0
    if (!this.tripTool.isMoney || !this.getTmcStatus()) {
      return
    }
    if (api.has('get:bills:value')) {
      let value = await this.props.bus.getFieldsValue()
      if (value && value.travelers && value.travelers.length) {
        money *= value.travelers.length
      }
    }
    amount = standardValueMoney(new MoneyMath(money.toFixed(2)).value)
    // 行程获取 为0 的时候就不赋值给金额字段了，目前场景是只有行程未有报价，后续有报价时可考虑本位币时其他币种的情况
    if (template?.find(i => i.name == 'requisitionMoney') && !isZero(amount.standard)) {
      this.props?.form?.setFieldsValue({
        requisitionMoney: amount
      })
    } else if (template?.find(i => i.name == 'amount') && !isZero(amount.standard)) {
      this.props?.form?.setFieldsValue({
        amount: amount
      })
    }
  }

  handleAdd = isSingle => {
    const { tripsList } = this.state
    const { billSpecification } = this.props
    api
      .open('@bills:AddTripModal', {
        bus: this.props.bus,
        isSingleDataLink: false,
        template: this.tripTool.getCurrentTemp(),
        editable: true,
        isSingle,
        tripTool: this.tripTool,
        billSpecification: billSpecification
      })
      .then(result => {
        const newTripsList = [...tripsList, ...result]
        this.handleChange(newTripsList)
        this.handleAutoAssignDateRangeField()
      })
  }

  handleAutoAssignExpenseTypeFields = trip => {
    let params = {
      travelData: {
        u_行程规划: [...trip]
      }
    }
    api.invokeService('@bills:get:travel:date:range', params).then(result => {
      const { startTime, endTime, accommodation, departure, destination } = result?.value
      this.props?.bus.emit('assign:date:value', startTime == 0 ? +new Date() : startTime)
      this.props?.bus.emit('assign:date:range:value', [
        startTime == 0 ? +new Date() : startTime,
        endTime == 0 ? +new Date() : endTime
      ])

      this.props?.bus.emit('assign:city:value', {
        accommodation: this.handleCityValue(accommodation),
        departure: this.handleCityValue(departure),
        destination: this.handleCityValue(destination)
      })
    })
  }

  handleCityValue = (cityValue) => {
    let handle_cityValue = ''
    if (cityValue) {
      if (cityValue.split('},')?.length > 1) {
        handle_cityValue = cityValue.split('},')[0] + '}]'
      } else {
        handle_cityValue = cityValue
      }
    }
    return handle_cityValue
  }

  handleAutoAssignDateRangeField = () => {
    const { tripsList } = this.state
    let u = this.tripTool.getSubmitValues(cloneDeep(tripsList))
    let params = {
      travelData: {
        u_行程规划: [...u]
      }
    }
    api.invokeService('@bills:get:travel:date:range', params).then(result => {
      const { startTime, endTime } = result?.value
      this.props?.bus.emit('assign:date:range:value', [
        startTime == 0 ? +new Date() : startTime,
        endTime == 0 ? +new Date() : endTime
      ])
    })
  }

  renderTripSyncContent = () => {
    const { tripsList } = this.state
    let newArr = []
    let TYPE_MAP = {
      TAXI: '用车',
      FLIGHT: '飞机',
      TRAIN: '火车',
      HOTEL: '酒店'
    }
    tripsList?.forEach(item => {
      if (!newArr.includes(item.tripType) && item.dataLinkId) newArr.push(item.tripType)
    })
    return (
      <div className="tripSyncContent">
        {newArr.map(item => (
          <span onClick={() => this.handleClickOrderTrip(item)}>{TYPE_MAP[item]}</span>
        ))}
      </div>
    )
  }

  handleClickOrderTrip = async enType => {
    const sources = [`BUTTON_${enType}_ORDER`]
    const result = await Promise.all([
      api.invokeService('@custom-triptype:get:all:travel:intent', { sources }),
      api.invokeService('@auth-check:get:mall:auth:query', { type: sources[0] })
    ])
    const items = result[0]?.items || []
    const authQuery = result[1] || ''
    items.forEach(i => {
      const type = /\?/.test(i.source) ? '&' : '?'
      if (!i.source.includes('token')) {
        i.source = i.source + `${type}${authQuery}`
      }
    })
    sources.forEach(type => {
      api.thirdResources.deleteByType(type)
    })
    let filterItems = items.filter(i => i.realm === 'TRAVELONE')
    api.thirdResources.add(filterItems)
    api.request({
      type: `BUTTON_${enType}_ORDER` || 'BUTTON_TRAVEL_ORDER'
    })
  }

  handleEdit = async index => {
    const { showTripSyncBtn } = this.state
    const { travelBlackList, billSpecification } = this.props
    if (showTripSyncBtn) {
      let newArr = cloneDeep(this.state.tripsList)
      let check_res = await checkingTripSync(newArr.splice(index, 1), 'edit')
      let errorMessage = `行程已经订票，无法编辑`
      if (!check_res) return message.warning(i18n.get(errorMessage))
    }
    const { originalValue } = this.props
    const tripsList = cloneDeep(this.state.tripsList)
    const value = tripsList[index]
    // 行程变更后，可以对行程进行编辑，编辑时间导致排序改变后，不能通过索引找行程
    const originalTrip = get(originalValue, 'u_行程规划', [])
    const originalTripValue = originalTrip.find(el => el.dataLinkId === value.dataLinkId)
    api
      .open('@bills:AddTripModal', {
        bus: this.props.bus,
        originalTripValue,
        isSingleDataLink: false,
        template: this.tripTool.getCurrentTemp(value),
        editable: value.editable,
        value,
        isSingle: true,
        tripTool: this.tripTool,
        dateEditable: this.tripTool.getTripDateEditable(value, travelBlackList),
        onCheckDateVailable: this.checkDateValiable,
        billSpecification
      })
      .then(result => {
        tripsList[index] = result[0]
        this.handleChange(tripsList)
        this.handleAutoAssignDateRangeField()
      })
  }
  fnGetMallSensorParams = () => {
    const staff = api.getState()['@common'].userinfo?.staff
    const isTraveler = api.getState()['@common'].mallRole?.mallRole === '0'
    const param = {
      category: isTraveler ? i18n.get('散客') : i18n.get('企业'),
      staffId: staff?.userId,
      staffName: staff?.name,
      corName: staff?.corporation?.name,
      corpId: staff?.corporation?.id
    }
    return param
  }
  fnMallSensorTrack = trackName => {
    window.TRACK && window.TRACK(trackName, this.fnGetMallSensorParams())
  }
  sensorTrack = async type => {
    switch (type) {
      case 'FLIGHT':
        this.fnMallSensorTrack('pcPlanning_planeApplication_price_click')
        return
      case 'TRAIN':
        this.fnMallSensorTrack('pcPlanning_trainApplication_price_click')
        return
      case 'HOTEL':
        this.fnMallSensorTrack('pcPlanning_hotelApplication_price_click')
        return
      default:
        return
    }
  }
  exposureSensorTrack = async type => {
    switch (type) {
      case 'FLIGHT':
        this.fnMallSensorTrack('pcPlanning_planeApplication_price_view')
        return
      case 'TRAIN':
        this.fnMallSensorTrack('pcPlanning_trainApplication_price_view')
        return
      case 'HOTEL':
        this.fnMallSensorTrack('pcPlanning_hotelApplication_price_view')
        return
      default:
        return
    }
  }

  handleDelete = async (e, index) => {
    e.stopPropagation()
    const { tripsList, showTripSyncBtn } = this.state
    if (showTripSyncBtn) {
      let newArr = cloneDeep(tripsList)
      let check_res = await checkingTripSync(newArr.splice(index, 1))
      if (!check_res) return
    }
    tripsList.splice(index, 1)
    this.handleChange(tripsList)
    this.handleAutoAssignDateRangeField()
  }

  sortList = tripsList => {
    const newDataList = sortBy(tripsList, item => {
      const { startTime, endTime } = item
      return startTime ? startTime : endTime
    })
    return newDataList
  }
  getIntent = () => {
    this.TK = `BUTTON_HOTEL_MALL_PRICE_IN_TIME`
    const sources = [this.TK, 'BUTTON_FLIGHT_MALL_PRICE_IN_TIME', 'BUTTON_TRAIN_MALL_PRICE_IN_TIME']
    return api.invokeService('@custom-triptype:get:all:travel:intent', { sources }).then(result => {
      if (result?.items) {
        let items = (result.items || []).map(i => {
          return i
        })
        this.setState({
          intents: items
        })
      }
    })
  }
  getCitysAjax = trip => {
    let cityajax = []
    let startCity = JSON.parse(trip.startCity || {})
    if (startCity?.length > 0) {
      if (startCity[0]?.type == 'cityGroup') {
        cityajax.push(
          api.invokeService('@custom-triptype:get:getCityGroupId', { id: startCity[0].key, type: trip.tripType })
        )
      } else {
        cityajax.push(
          api.invokeService('@custom-triptype:get:getCityParentId', {
            ids: startCity.map(i => i.key).join(','),
            type: trip.tripType
          })
        )
      }
    }
    let endCity = ''
    let two = trip.endCity && trip.startCity
    if (two) {
      endCity = JSON.parse(trip.endCity || {})
      if (endCity?.length > 0) {
        if (endCity[0]?.type == 'cityGroup') {
          cityajax.push(
            api.invokeService('@custom-triptype:get:getCityGroupId', { id: endCity[0].key, type: trip.tripType })
          )
        } else {
          // if (trip.tripType == 'TRAIN') {
          //   cityajax.push(cityFetch.GET('[ids]', { ids: endCity.map(i => i.key).join(',') }))
          // } else {
          cityajax.push(
            api.invokeService('@custom-triptype:get:getCityParentId', {
              ids: endCity.map(i => i.key).join(','),
              type: trip.tripType
            })
          )
          // }
        }
      }
    }
    return { cityajax, startCity, endCity, two }
  }
  getCitys = (city, tripType, data) => {
    let result = { citys: [] }
    if (city && city.length > 0) {
      if (city[0]?.type == 'cityGroup') {
        result.cityName = city[0].label
      }

      if (tripType != 'HOTEL' && tripType != 'TRAIN') {
        result.citys = uniqBy(data?.items || [], 'code').filter(i => i.haveFlight)
      } else {
        result.citys = uniqBy(data?.items || [], 'code')
      }
    }

    // if (tripType == 'TRAIN') {
    //   result.citys = result.citys.filter(i => i?.extendInfo?.trainCode?.length > 0)
    // } else if (tripType == 'FLIGHT') {
    //   result.citys = result.citys.filter(i => i?.extendInfo?.airportCode?.length > 0)
    // }
    return result
  }
  handleOpen = async (e, trip, index) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    this.TK = `BUTTON_${trip.tripType}_MALL_PRICE_IN_TIME`
    let { tripsList } = this.state
    const sources = [this.TK]
    let { cityajax, startCity, endCity, two } = this.getCitysAjax(trip)
    this.sensorTrack(trip.tripType)
    if (!this.state.intents.length) {
      await this.getIntent()
    }

    Promise.all([...cityajax, api.invokeService('@auth-check:get:mall:auth:query', { type: this.TK })]).then(result => {
      let { intents } = this.state
      const authQuery = result[result.length - 1] || ''
      let data = cloneDeep(trip)
      data.isPc = true
      let items = cloneDeep(intents)
      if (data.startTime && typeof data.startTime != 'number') {
        data.startTime = moment(data.startTime).valueOf()
      }
      if (data.endTime && typeof data.endTime != 'number') {
        data.endTime = moment(data.endTime).valueOf()
      }
      let scity = this.getCitys(startCity, trip.tripType, result[0])
      data.startCity = scity

      let ecity = {}
      if (two) {
        ecity = this.getCitys(endCity, trip.tripType, result[1])
        data.endCity = ecity
      }
      if (scity.citys.length == 0) {
        message.error(i18n.get('出发城市没有') + i18n.get(trip.tripType == 'TRAIN' ? '火车站' : '机场'))
        return
      }
      if (two && ecity.citys.length == 0) {
        message.error(i18n.get('到达城市没有') + i18n.get(trip.tripType == 'TRAIN' ? '火车站' : '机场'))
        return
      }
      items.forEach(i => {
        i.data = data
        let type = /\?/.test(i.source) ? '&' : '?'
        if (!i.source.includes('token')) {
          i.source = i.source + `${type}${authQuery}`
        }
      })
      api.thirdResources.deleteByType(this.TK)
      api.thirdResources.add(items)
      api.request({ type: this.TK }).then(res => {
        tripsList[index].money = res.money || 0
        this.handleChange(tripsList)
      })
    })
  }
  getTmcStatus = () => {
    let { powersList = [] } = this.props
    let tmcStatus = powersList.find(i => i.powerCode == '120209')
    return get(tmcStatus, 'state') === 'using'
  }

  renderList() {
    const { tripsList } = this.state;
    const { entityFormList, customTripTypeList } = this.state;
    const groupKeys = [...new Set(tripsList?.map(t => t?.startTime))]?.sort();
    const firstGroupKey = groupKeys[0];
    const lastGroupKey = groupKeys[groupKeys.length - 1];
    const isSingleGroup = tripsList?.length === 1 || tripsList?.every(item => item?.startTime === tripsList[0]?.startTime)
  
    return (
      <div className="trips-list-wrapper">
        {tripsList?.map((item, index) => {
          const { dataLinkTemplateId } = item;
          const currentEntity = entityFormList?.find(entity => entity?.templateId === dataLinkTemplateId);
          const customTrip = customTripTypeList?.find(trip => trip?.entityId === currentEntity?.entity?.id);
          return (
            <div
              style={{ background: 'none' }}
              className="trips-item"
              key={index}
              onClick={() => this.handleEdit(index)}
            >
              <TripCard
                trip={item}
                isNewGroup={item?.startTime !== tripsList?.[index - 1]?.startTime}
                isFirstGroup={item?.startTime === firstGroupKey}
                isLastGroup={item?.startTime === lastGroupKey}
                isSingleGroup={isSingleGroup}
                onlyOne={tripsList?.length === 1}
                index={index}
                handleOpen={this.handleOpen}
                showButton={OPENLIST.includes(item?.tripType) && this.getTmcStatus()}
                tripsTemplate={this.state?.tripsTemplate || []}
                exposureSensorTrack={this.exposureSensorTrack}
                handleDelete={e => this.handleDelete(e, index)}
                isNewVersion
                sceneTypeField={customTrip?.sceneTypeField}
              />
            </div>
          );
        })}
      </div>
    );
  }

  handleRefreshOrder = async () => {
    let status = await getTripSyncStatus(this.props)
    if (status !== 'PENDING') {
      this.props.bus.invoke('bills:update:flow', { id: this.props?.dataSource?.id })
    }
  }

  render() {
    const { template, tripsList, loading, showTripSyncBtn, AITripContextDetail } = this.state
    const { isDetail = false, field, powersList, originalValue, value = [], travelBlackList, billSpecification } = this.props
    const tripPower = powersList.find(i => i.powerCode === 'cargo_travel_management')
    if (get(tripPower, 'state') === 'expire') {
      return <div className={styles['data-link-red']}>{i18n.get('企业购买的授权已过期')}</div>
    }
    if (this.tripTool?.currentTemplate?.length == 0 && tripsList?.length == 0 && loading == false) {
      return i18n.get('暂无可用类型，请联系管理员')
    }
    const isShowMultipleTrip = this.tripTool.isShowMultipleTrip //template.length === 5
    // 根据费用明细索引，取行程规划
    const tripIndex = get(this.props, 'index', 0)
    const originalTripValue = get(originalValue, `form.details[${tripIndex}].feeTypeForm['u_行程规划'][0]`) // @i18n-ignore
    const needQueryTripStatus = tripsList?.filter(item => item.dataLinkId)?.length ? false : true
    const dateEditable = this.tripTool.getTripDateEditable(tripsList?.[0], travelBlackList)
    return (
      <div className={styles['trip-details-wrapper']}>
        <TravelClosedLoop value={value} />
        {!isDetail && this.renderList()}
        <div id="map-container"></div>
        <div className="trip-select-wrapper">
          {/* 费用类型添加单条 */}
          {isDetail ? (
            <div>
              {(AITripContextDetail || !getBoolVariation('fkrd-4299-ai-trip-planning')) && (
                <AddTripCard
                  bus={this.props.bus}
                  onRef={ref => {
                    field.child = ref
                  }}
                  fieldOpt={field}
                  originalTripValue={originalTripValue}
                  value={tripsList.length > 0 && tripsList[0]}
                  isSingleDataLink={true}
                  isSingle={true}
                  template={this.tripTool.getCurrentTemp(tripsList.length > 0 ? tripsList[0] : null)}
                  editable={tripsList.length ? tripsList[0].editable : true}
                  onOk={this.handleChange}
                  isDetail={true}
                  showMoney={this.getTmcStatus()}
                  showButton={true}
                  handleOpen={this.handleOpen}
                  dateEditable={dateEditable}
                  billSpecification={billSpecification}
                  AITripContextDetail={AITripContextDetail}
                />
              )}
            </div>
          ) : (
            <div className="add-button-wrapper">
              <Button size='small' category='secondary' className='add-button' onClick={() => this.handleAdd(true)}>
                <OutlinedTipsAdd style={{ fontSize: 20, margin: '0 4px -1px 0' }}/>
                {i18n.get('单程')}
              </Button>
              {isShowMultipleTrip && (
                <Button size='small' category='secondary' className='add-button' onClick={() => this.handleAdd(false)}>
                  <OutlinedDirectionSwitch style={{ fontSize: 20, margin: '0 4px -2px 0' }}/>
                  {i18n.get('往返')}
                </Button>
              )}
              {showTripSyncBtn ? (
                needQueryTripStatus ? (
                  <Button size='small' category='secondary' className="trip-sync" onClick={this.handleRefreshOrder}>
                    {i18n.get('订购')}
                  </Button>
                ) : (
                  <Popover content={this.renderTripSyncContent()}>
                    <Button size='small' category='secondary' className="trip-sync">{i18n.get('订购')}</Button>
                  </Popover>
                )
              ) : (
                ''
              )}
            </div>
          )}
        </div>
      </div>
    )
  }
}
