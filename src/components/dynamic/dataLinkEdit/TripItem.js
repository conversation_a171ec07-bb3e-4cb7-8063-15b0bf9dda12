import React from 'react'
import styles from './TripItem.module.less'
import { Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
const EKBIcon = api.require('@elements/ekbIcon')

const TripItem = ({ tripType, dateString, cityStr, readonly, editable, showButton, money, type, onOpen, onDeleteTrip, onShowDetail, customField, tooltipStr }) => {
    const handleGetPrice = (e) => {
        onOpen && onOpen(e)
    }
    const handleDeleteTrip = (e) => {
        onDeleteTrip && onDeleteTrip(e)
    }
    const handleShowDetail = () => {
        onShowDetail && onShowDetail()
    }
    return (
        <section className={styles['trip-item']} onClick={() => handleShowDetail()}>
            <div className="infoTop">
                <img className="trip-type-icon" src={tripType.icon} style={{ background: tripType.color }} />
                <div className="content-wrapper">
                    <div className="date">{dateString}</div>
                    <Tooltip title={tooltipStr || ''}>
                        <span className="city">{cityStr}</span>
                    </Tooltip>
                </div>
                {!readonly && editable && <div onClick={e => handleDeleteTrip(e)}><EKBIcon name="#ico-7-a-Group87" className="close-icon" /></div>}
            </div>
            <div className="price-wrapper">
                {showButton && <div className="content-money"><span onClick={e => handleGetPrice(e)}>{money ? '重新获取 > ' : '获取参考报价 > '}</span></div>}
                {money ? (
                    <div className="content-money">
                        <div>
                            {i18n.get('参考报价:{money}元/', { money })}
                            {i18n.get(type == 'HOTEL' ? '晚' : '张')}
                        </div>
                    </div>
                ) : null}
            </div>
            <div className="custom-field-wrapper">
                {customField}
            </div>
        </section>
    )
}

export default TripItem