import React, { Component, createContext } from 'react'
import { Form, Table, Popconfirm } from 'antd'
import { FormComponentProps } from 'antd/es/form'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './DataLinkEditTable.module.less'
import { includes, isEqual, cloneDeep } from 'lodash'
import { resetDataLinEdits, formatCellValue } from './utils'
import fnGetFieldLabel from '../../../utils/fnGetFieldLabel'
import EKBIcon from '../../../../elements/ekbIcon'
import { ENUM_TYPES } from '../../../consts'
import DataLinksCell from './DataLinksCell'
import RefCell from './RefCell'
import AttachmentsCell from './AttachmentsCell'
import SimpleCell from './SimpleCell'
import CityCell from './CityCell'
import SwitchCell from './SwitchCell'
import MoneyCell from './MoneyCell'
import DateRangeCell from './DateRangeCell'
import DateCell from './DateCell'
import ListRefOrganizationStaffCell from './ListRefOrganizationStaffCell'
import RefOrganizationStaffCell from './RefOrganizationStaffCell'
import PayeeInfoCell from './PayeeInfoCell'
import EnumCell from './EnumCell'
import SimpleEditCell from './SimpleEditCell'
import NumberEditCell from './NumberEditCell'
import SwitchEditCell from './SwitchEditCell'
import DateEditCell from './DateEditCell'
import DateRangeEditCell from './DateRangeEditCell'
import EnumEditCell from './EnumEditCell'
import CityEditCell from './CityEditCell'
import PayeeInfoEditCell from './PayeeInfoEditCell'
import RefOrganizationStaffEditCell from './RefOrganizationStaffEditCell'
import ListRefOrganizationStaffEditCell from './ListRefOrganizationStaffEditCell'
import RefDepartmentEditCell from './RefDepartmentEditCell'
import RefEditCell from './RefEditCell'
import DataLinkEditCell from './DataLinkEditCell'
import DataLinkListEditCell from './DataLinkListEditCell'
import MoneyEditCell from './MoneyEditCell'
import AttachmentsEditCell from './AttachmentsEditCell'
import NumberCell from './NumberCell'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import {
  OutlinedEditBatchYes,
  OutlinedTipsAdd,
  OutlinedDirectionDownload,
  OutlinedDirectionUpload
} from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
import moment from 'moment'
import { Space } from '@hose/eui'
import { realName } from '../../../../elements/util'

interface IProps extends FormComponentProps {
  isEdit?: boolean
  field?: any
  dataSource?: any[]
  onSelect?: () => void
  onGetData?: (data: any, index?: number | string) => void
  onFieldEdit?: (data: any, index: any) => void
  editing?: boolean
  authStaffStaffMap?: any
  bus?: any
  columns?: any[]
  components?: any[]
  noTransColumns?: boolean
  canCreate?: boolean
  editingKey?: string | number
  onGetActiveDataIndex: (editingKey: string | number) => void
  rowSelection?: {
    type?: string
    onChange?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void
  }
  type?: 'settlementChecking' | undefined
  entityId: string
  isHabSetValue?: boolean
  pagination?: any
  onJumpLastPage?: (total: number) => void
}

interface IState {
  dataSource?: any[]
  editingKey?: string | number
}
const EditableContext = createContext(null)

class EditableCell extends Component<IProps> {
  renderCellItem = props => {
    const {
      field: { type }
    } = props
    if (type === 'textarea' || type === 'text') {
      return <SimpleEditCell {...props} />
    } else if (type === 'number') {
      return <NumberEditCell {...props} />
    } else if (type === 'switcher') {
      return <SwitchEditCell {...props} />
    } else if (type === 'date') {
      return <DateEditCell {...props} />
    } else if (type === 'dateRange') {
      return <DateRangeEditCell {...props} />
    } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
      return <EnumEditCell {...props} />
    } else if (type === 'city') {
      return <CityEditCell {...props} />
    } else if (type === 'payeeInfo') {
      return <PayeeInfoEditCell {...props} />
    } else if (type === 'ref:organization.Staff') {
      return <RefOrganizationStaffEditCell {...props} />
    } else if (type === 'list:ref:organization.Staff') {
      return <ListRefOrganizationStaffEditCell {...props} />
    } else if (type === 'ref:organization.Department') {
      return <RefDepartmentEditCell {...props} useEUI />
    } else if (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      type !== 'ref:organization.Department' &&
      type !== 'ref:basedata.Enum.currency' &&
      !includes(ENUM_TYPES, type)
    ) {
      return <RefEditCell {...props} />
    } else if (type === 'dataLink') {
      return <DataLinkEditCell {...props} />
    } else if (type === 'dataLinks') {
      return <DataLinkListEditCell {...props} />
    } else if (type === 'money') {
      return <MoneyEditCell {...props} />
    } else if (type === 'attachments' || type === 'aiAttachments') {
      return <AttachmentsEditCell {...props} />
    } else {
      return '未知类型'
    }
  }

  renderCell = ({ getFieldDecorator }) => {
    const { editing, children, ...restProps } = this.props
    return <td {...restProps}>{editing ? this.renderCellItem({ getFieldDecorator, ...this.props }) : children}</td>
  }

  render() {
    return <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
  }
}

@EnhanceConnect(state => ({
  authStaffStaffMap: state['@common'].authStaffStaffMap
}))
class EditTableWrapper extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: props.dataSource,
      editingKey: props.editingKey || ''
    }
  }
  componentWillMount(): void {
    const { bus = new MessageCenter() } = this.props
    bus.watch('get:edit:table:values', this.handleGetEditTableValues)
  }

  componentWillUnmount(): void {
    let { bus = new MessageCenter() } = this.props
    bus.un('get:edit:table:values', this.handleGetEditTableValues)
  }

  handleGetEditTableValues = () => {
    return this.state.dataSource || []
  }

  componentDidMount(): void {
    this.fnDataSource(this.props)
  }
  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (!isEqual(nextProps.dataSource, this.props.dataSource)) {
      this.fnDataSource(nextProps)
    }
  }
  fnDataSource = props => {
    const { dataSource = [], editingKey = '' } = props
    this.setState({ dataSource, editingKey })
  }

  handleSave(form, key) {
    const { components, onGetData, type } = this.props
    form.validateFields((error, row) => {
      if (error) {
        return
      }
      row = formatCellValue(row, components || [], type)
      // @ts-ignore
      const newData = [...this.state.dataSource]
      const index = newData.findIndex(item => key === item.index)
      if (index > -1) {
        const item = newData[index]
        newData.splice(index, 1, {
          ...item,
          ...row
        })
        this.setState({ dataSource: newData, editingKey: '' })
      } else {
        newData.push({ ...row })
        this.setState({ dataSource: newData, editingKey: '' })
      }
      onGetData?.(newData, '')
    })
  }

  handleCreate = () => {
    const { components, onGetActiveDataIndex, onJumpLastPage } = this.props
    const { dataSource, editingKey } = this.state
    if (editingKey) {
      typeof onJumpLastPage === 'function' && onJumpLastPage(dataSource.length)
      return 
    }
    const index = dataSource.length
    const newData = { index }
    components?.forEach(el => {
      newData[el?.name] = el.type === 'switcher' ? false : ''
    })
    const data = [...dataSource, newData]
    this.setState({
      // @ts-ignore
      dataSource: data,
      editingKey: index
    })
    typeof onJumpLastPage === 'function' && onJumpLastPage(data.length)
    onGetActiveDataIndex?.(index)
  }

  handleSelect = () => {
    const { onSelect } = this.props
    const { editingKey } = this.state

    if (editingKey) return
    onSelect?.()
  }

  handleCancel = () => {
    this.setState({ editingKey: '' })
  }

  handleEdit = (record, key) => {
    this.setState({ editingKey: key })
  }

  handleDelete = (key, e) => {
    e.stopPropagation()
    e.preventDefault()
    const { onGetData } = this.props
    // @ts-ignore
    const newData = [...this.state.dataSource]
    const dataFilter = newData.filter(item => {
      return item.index !== key
    })
    this.setState({ dataSource: dataFilter })
    onGetData?.(dataFilter)
  }

  getColumns = () => {
    const {
      authStaffStaffMap,
      components,
      noTransColumns,
      isEdit,
      rowSelection,
      onFieldEdit,
      field,
      isHabSetValue
    } = this.props
    if (noTransColumns) {
      return components
    }
    const columns =
      components?.map((item, index) => {
        const { field, customData, type } = item
        return {
          title: realName({name: item.label, enName: item.enLabel}),
          dataIndex: field,
          key: field,
          field: item,
          editable: this.props.type === 'settlementChecking' ? item.editable : true,
          width: index !== components.length - 1 ? 100 : components.length > 5 ? 100 : undefined,
          render: (text, record) => {
            if (type === 'textarea' || type === 'text') {
              return <SimpleCell text={text} name={item.name} />
            } else if (type === 'number') {
              return <NumberCell text={text} field={item} />
            } else if (type === 'city') {
              return <CityCell text={text} />
            } else if (type === 'switcher') {
              return <SwitchCell text={text} />
            } else if (type === 'money') {
              return <MoneyCell text={text} />
            } else if (type === 'dateRange') {
              return <DateRangeCell text={text} field={item} />
            } else if (type === 'date') {
              return <DateCell text={text} field={item} />
            } else if (type === 'list:ref:organization.Staff') {
              return <ListRefOrganizationStaffCell text={text} authStaffStaffMap={authStaffStaffMap} />
            } else if (type === 'ref:organization.Staff') {
              return <RefOrganizationStaffCell text={text} />
            } else if (type === 'payeeInfo') {
              return <PayeeInfoCell text={text} />
            } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
              return <EnumCell text={text} customData={customData} />
            } else if (
              type?.startsWith?.('ref') &&
              type !== 'ref:organization.Staff' &&
              !includes(ENUM_TYPES, type) &&
              type !== 'ref:basedata.Enum.currency'
            ) {
              // 包含部门
              return <RefCell text={text} />
            } else if (type === 'dataLink' || type === 'dataLinks') {
              return <DataLinksCell text={text} isHabSetValue={isHabSetValue} />
            } else if (type === 'attachments' || type === 'aiAttachments') {
              return <AttachmentsCell value={text} />
            } else {
              return '未知类型'
            }
          }
        }
      }) || ([] as any[])
    if (isEdit) {
      columns.push({
        title: i18n.get('操作'),
        dataIndex: 'operation',
        key: 'operation',
        width: 96,
        fixed: 'right',
        render: (text, record) => {
          const { editingKey } = this.state
          const editable = this.isEditing(record)
          return editable ? (
            <div className="btn-group">
              <EditableContext.Consumer>
                {form => (
                  <EKBIcon
                    className="icon-save icon-style stand-20-icon"
                    name="#EDico-zf-save"
                    onClick={() => this.handleSave(form, record.index)}
                  />
                )}
              </EditableContext.Consumer>
              <Popconfirm title="确定取消?" onConfirm={() => this.handleCancel()}>
                <EKBIcon className="icon-cancel icon-style stand-20-icon" name="#EDico-zf-off" />
              </Popconfirm>
            </div>
          ) : (
            <div className="btn-group">
              {record.disabledByState ? null : (
                // @ts-ignore
                <a disabled={editingKey !== ''}>
                  {field?.behaviour !== 'REF' && (
                    <EKBIcon
                      className="icon-style stand-20-icon"
                      name="#EDico-zf-edit"
                      onClick={() => this.handleEdit(record, record.index)}
                    />
                  )}
                  <EKBIcon
                    className="icon-style stand-20-icon"
                    name="#EDico-zf-delete"
                    onClick={e => this.handleDelete(record.index, e)}
                  />
                </a>
              )}
            </div>
          )
        }
      })
    }
    if (!rowSelection) {
      columns.unshift({
        title: '',
        dataIndex: 'index',
        key: 'index',
        width: 20,
        render: (text, record) => {
          return (
            <div className="edit-number-wrapper">
              {/* <SimpleCell text={text + 1} name={undefined} /> */}
              <span>{text + 1}</span>
              {onFieldEdit && (
                <EKBIcon
                  className="icon-style stand-20-icon show"
                  name="#EDico-zoom"
                  onClick={() => onFieldEdit(record, record.index)}
                />
              )}
            </div>
          )
        }
      })
    }
    return columns
  }
  isEditing = record => record.index === this.state.editingKey

  renderReminder = () => {
    return (
      <div style={{ color: 'var(--eui-function-danger-500)' }}>
        <p>{i18n.get('注意')}</p>
        <p>{i18n.get('1、如果要新增数据请下载空白模版')}</p>
        <p>{i18n.get('2、导入后单据表格中原有数据将清空，并将Excel中的数据展示在表格中')}</p>
      </div>
    )
  }

  handleImport = () => {
    const { entityId, onGetData, field } = this.props
    api
      .open('@bills:ImportDetailByExcel', {
        type: 'datalink4flow',
        flag: {
          id: entityId,
          fieldName: field?.label
        },
        renderReminder: this.renderReminder
      })
      .then(data => {
        const flattenData = data?.map((item, index) => ({
          ...item.dataLinkForm,
          dataLinkTemplateId: item.dataLinkTemplateId,
          dataLinkId: item.dataLinkId,
          index
        }))
        this.setState({ dataSource: flattenData })

        onGetData?.(flattenData)
      })
  }
  handleExport = async () => {
    const { field, type } = this.props
    const { staff } = await api.open('@third-party-manage:ExportOption', {
      showStaff: true,
      hideColumnOption: true
    })
    const newData = cloneDeep(this.state.dataSource).map(item => {
      const dataLinkId = item.dataLinkId || null
      const dataLinkTemplateId = item.dataLinkTemplateId
      delete item.disabledByState
      delete item.dataLinkId
      delete item.dataLinkTemplateId
      delete item.index
      return {
        dataLinkId,
        dataLinkTemplateId: dataLinkTemplateId,
        dataLinkForm: resetDataLinEdits(item)
      }
    })
    const body = {
      items: newData,
      form: {
        staffType: staff
      }
    }
    Fetch.POST(
      `/api/v1/datalink/excel/export/datalink4flow/$${this.props.entityId}`,
      { corpId: encodeURIComponent(Fetch.ekbCorpId) },
      {
        body,
        headers: { accept: '*' },
        isBlob: true
      }
    )
      .then(blob => {
        const el = getLinkNodeElement()
        const url = window.URL.createObjectURL(blob)
        const l = type === 'settlementChecking' ? '账单调整' : field?.label
        const filename = `${l}_${moment().format('YYYYMMDD_HHmmss')}.xlsx`
        const name = decodeURIComponent(filename as string)
        el.setAttribute('href', url)
        el.setAttribute('download', name)
        triggerClick(el)
      })
      .catch(err => { })
  }

  render() {
    const { dataSource } = this.state
    const { bus, form, canCreate, rowSelection, onSelect, isEdit, field, onRowClick, type, pagination = false } = this.props
    let columns = this.getColumns()
    columns = columns.map(col => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: record => ({
          record,
          bus,
          dataIndex: col.dataIndex,
          title: col.title,
          field: col.field,
          editing: this.isEditing(record),
          form
        })
      }
    })

    if (!columns.length) {
      return null
    }
    const otherProps = rowSelection ? { rowSelection } : {}

    return (
      <EditableContext.Provider value={form}>
        <Space>
          {onSelect && (
            <span onClick={this.handleSelect} className={styles.itemCreateBtn}>
              <OutlinedEditBatchYes /> {i18n.get('选择')}
            </span>
          )}
          {canCreate && (
            <span onClick={this.handleCreate} className={styles.itemCreateBtn}>
              <OutlinedTipsAdd /> {i18n.get('点击添加')}
            </span>
          )}
          {(canCreate || onSelect) && field?.behaviour !== 'REF' && (
            <span onClick={this.handleImport} className={styles.itemCreateBtn}>
              <OutlinedDirectionDownload /> {i18n.get('导入')}
            </span>
          )}
          {type !== 'settlementChecking' && isEdit && field?.behaviour !== 'REF' && (
            <span onClick={this.handleExport} className={styles.itemCreateBtn}>
              <OutlinedDirectionUpload /> {i18n.get('导出')}
            </span>
          )}
        </Space>
        {/* @ts-ignore */}
        <Table
          className={styles.dataLinkEditTable}
          components={{
            body: {
              cell: EditableCell
            }
          }}
          bordered
          dataSource={dataSource}
          columns={columns}
          pagination={pagination}
          size="middle"
          scroll={{ x: 300 }}
          onRowClick={onRowClick}
          {...otherProps}
        />
      </EditableContext.Provider>
    )
  }
}

export default Form.create()(EditTableWrapper)
