import React, { FC } from 'react'
import { Form } from 'antd'
import { DatePicker } from '@hose/eui'
import { required } from '../../../validator/validator'
import moment from 'moment'
import { formatDateTime, getShowTime } from '../../../utils/fnPredefine4Date'
import { isDisable } from '../../../utils/fnDisableComponent'
import { formItemStyle, itemInnerStyle } from './utils'
const { RangePicker } = DatePicker
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}
const DateRangeEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { field, withTime, isClear = false, dateTimeType } = fieldInfo
  const disabled = isDisable(props)
  const validatorDate = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  let date = void 0
  const value = record[`${field}`]
  if (value) {
    let start = value.start ? moment(value.start) : value.start
    let end = value.end ? moment(value.end) : value.end
    date = [start, end]
  }
  const dateTime = formatDateTime(withTime, dateTimeType)
  const dateType = dateTime === 'YYYY-MM' ? 'month' : 'date'
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorDate }],
        initialValue: date
      })(
        <RangePicker
          style={itemInnerStyle}
          format={dateTime}
          allowClear={isClear}
          disabled={disabled}
          showTime={getShowTime(withTime, dateTimeType)}
          mode={[dateType, dateType]}
        />
      )}
    </FormItem>
  )
}

export default DateRangeEditCell
