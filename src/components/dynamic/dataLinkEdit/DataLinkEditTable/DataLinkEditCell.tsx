import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { Input } from '@hose/eui'
import { formatDataLink, formItemStyle, itemInnerStyle } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../../../validator/validator'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { get } from 'lodash'
import { showModal } from '@ekuaibao/show-util'
import { getAssignmentRuleById } from '../../../../plugins/bills/bills.action'
import { checkPriority } from '../../../utils/fnFormartDatalinkData'
import { dimensionValueVisible } from '../../../utils/fnDimension'
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
}

const DataLinkEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any>()
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { field, optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  placeholder = optional ? i18n.get('(选填)') + placeholder : placeholder
  const disabled = isDisable(props)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  const initState = () => {
    const value = record[`${field}`] || []
    let id = ''
    if (value) {
      id = typeof value === 'string' ? value : value.id || get(value, 'data.dataLink.id')
    }
    if (!id) {
      setValue(value)
      return
    } else {
      api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(res => {
        setValue({ ...res.value, id: id })
      })
    }
  }

  useEffect(() => {
    initState()
  }, [])

  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(<InputWrapper {...props} placeholder={placeholder} disabled={disabled} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      islock: false,
      ledgerLockList: []
    }
  }
  handleClick = () => {
    const {
      bus,
      flowId,
      external,
      value,
      field: { allMatchList, referenceData, label, filterId, dependence, allowCancelDependence, field }
    } = this.props
    const { islock, ledgerLockList, dependenceParams } = this.state
    const groupType = get(referenceData, 'platformId.groupType')
    const type = get(referenceData, 'platformId.type')
    fnCheckNeedPhone()({ groupType, type }).then(res => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal', { type }).then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }
      return bus
        .invoke('element:select:dataLink', {
          referenceData,
          flowId,
          selectedEntity: value,
          islock,
          ledgerLockList,
          filterId,
          dataLink: { id: referenceData.id, selectedEntity: value, type, name: label },
          dependenceParams: dependence && dependence.length ? dependenceParams : undefined,
          allowCancelDependence: allowCancelDependence,
          allMatchList,
          field
        })
        .then(result => {
          const { data } = result
          this.getDataLinkTempById(data.dataLink.id)
          this.setTripBasedata(data, type)
          emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
        })
    })
  }

  getDataLinkTempById = (id, assignment = true) => {
    const { onChange } = this.props
    if (!id) {
      onChange && onChange()
      return
    }
    api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(async res => {
      if (assignment) {
        // 第一次进入的时候不启动联动赋值，因为如果表单上面有值，会被联动赋值覆盖
        this.setFormValue(res.value.data)
      }
      onChange && onChange({ ...res.value, id: id })
    })
  }
  setFormValue = data => {
    const {
      field: { assignmentRule = {}, isLinkageAssignment, referenceData }
    } = this.props
    if (isLinkageAssignment) {
      if (referenceData.id !== data.dataLink.entityId) {
        // 获取子类业务对象赋值规则，子类业务对象只有一个赋值规则
        api.dispatch(getAssignmentRuleById([data.dataLink.entityId])).then(result => {
          const ruleList = result.items
          this.fnUpdateData(data, ruleList[0] || assignmentRule)
        })
      } else {
        this.fnUpdateData(data, assignmentRule)
      }
    }
  }

  fnUpdateData = (data, assignmentRule) => {
    const { bus, template, form, submitterId } = this.props
    const { fields = [] } = assignmentRule
    const {
      field: { referenceData }
    } = this.props
    let valueMap: Record<string, any> = {}
    fields.forEach(item => {
      template.forEach(v => {
        if (item.targetField === v.field && checkPriority(v)) {
          let value = data.dataLink[item.sourceField]
          if (value !== undefined) {
            if (v.type === 'number') {
              valueMap[v.name] = `${value * 1}`
            } else if (v.type.startsWith('ref:basedata.Dimension')) {
              if (!!value) {
                value.needCheckValue = true
              }
              valueMap[v.name] = dimensionValueVisible(value, submitterId) ? value : undefined
            } else if (v.type === 'dataLink') {
              const id = get(value, 'data.dataLink.id')
              // fix bug：XSG-12499
              // 如果业务对象存在联动赋值的情况
              if (id && v.isLinkageAssignment) {
                // 业务对象关联带出相关业务对象的数据填充到表单
                api
                  .invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' })
                  .then(async res => {
                    const resData = res.value.data
                    if (referenceData.id !== resData.dataLink.entityId) {
                      // fix bug: XSG-13696
                      // 获取子类业务对象赋值规则，
                      const rule = v.assignmentRule
                      if (rule) {
                        this.fnUpdateData(resData, rule)
                      } else {
                        api.dispatch(getAssignmentRuleById([resData.dataLink.entityId])).then(result => {
                          const ruleList = result.items
                          // 当业务对象有赋值规则的时候再进行子业务对象联动赋值
                          if (ruleList && ruleList.length > 0) {
                            this.fnUpdateData(resData, ruleList[0])
                          }
                        })
                      }
                    }
                  })
              }
              valueMap[v.name] = { ...value, id }
            } else if (v.type === 'payeeInfo') {
              const payeeValue = form.getFieldValue('payeeId') //单据收款信息为多收款人时不赋值，明细上的收款信息要赋值
              valueMap[v.name] = payeeValue && payeeValue.multiplePayeesMode ? payeeValue : value
            } else if (v.type === 'text' && value?.location) {
              valueMap[v.name] = value?.name || value?.address
            } else {
              valueMap[v.name] = value
            }
          }

          if (value === undefined && v.editable === false && v.defaultValue && v.defaultValue.type === 'none') {
            valueMap[v.name] = value
          }
        }
      })
    })
    for (let key in valueMap) {
      if (valueMap[key] === null) {
        valueMap[key] = undefined
      }
    }
    !!form && form.setFieldsValue(valueMap)
    if (valueMap.loanMoney) {
      bus.emit('loanMoney:changed', valueMap.loanMoney)
    }
  }

  // 将明细中的坐席赋值
  setTripBasedata = (data, type) => {
    let { isDetail, form } = this.props
    let tripType = get(data, 'dataLink.entity.type')
    let id = get(data, 'dataLink.entity.parentId')
    if (isDetail && type == 'TRAVEL_MANAGEMENT' && id && (tripType == 'FLIGHT' || tripType == 'TRAIN')) {
      let name = this.getTemplateTrip(tripType)
      let value = this.getDataLinkValue(data, id, tripType)
      if (name && value) {
        let obj = {}
        obj[name] = value
        form && form.setFieldsValue(obj)
      }
    }
  }
  getTemplateTrip = type => {
    let { template = [] } = this.props
    const Map = {
      TRAIN: 'ref:basedata.Enum.TrainSeatType',
      FLIGHT: 'ref:basedata.Enum.CabinType'
    }
    let name = ''
    template &&
      template.forEach(i => {
        if (i && i.type && i.type == Map[type]) {
          name = i.name
        }
      })
    return name
  }
  getDataLinkValue = (data, id, tripType) => {
    let value = ''
    // @i18n-ignore
    const F = {
      经济舱: 'ECONOMY',
      商务舱: 'BUSINESS',
      头等舱: 'FIRST'
    }
    // @i18n-ignore
    const T = {
      硬座: 'YZ',
      软座: 'RZ',
      硬卧: 'YW',
      软卧: 'RW',
      高级软卧: 'GJRW',
      一等座: 'YD',
      二等座: 'ED',
      商务座: 'SW',
      高铁动卧: 'DW'
    }
    if (tripType == 'FLIGHT') {
      value = get(data, `dataLink.E_${id}_航班舱型`)
      value = value ? F[value] : ''
    } else if (tripType == 'TRAIN') {
      value = get(data, `dataLink.E_${id}_火车坐席`) || get(data, `dataLink.E_${id}_火车席位`)
      value = value ? T[value] : ''
    }
    return value
  }
  render() {
    const { disabled, value, placeholder } = this.props
    const v = formatDataLink(value)
    return (
      <Input
        disabled={disabled}
        style={itemInnerStyle}
        placeholder={placeholder}
        value={v}
        onClick={this.handleClick}
        onFocus={e => {
          e.target.blur()
        }}
      />
    )
  }
}

export default DataLinkEditCell
