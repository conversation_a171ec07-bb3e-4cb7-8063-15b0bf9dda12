import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formatPayeeInfoField, formItemStyle } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../../../validator/validator'
import CustomInput from './CustomInput'
import PayeeInfoCell from './PayeeInfoCell'

const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  // TODO:
  template: any
}
const PayeeInfoEditCell: FC<IProps> = props => {
  const [value, setValue] = useState()
  const [isDependence, setIsDependence] = useState<boolean | undefined>()
  const [dependenceMap, setDependenceMap] = useState<any[]>()
  const [dependenceList, setDependenceList] = useState()
  const [multiplePayeesMode, setMultiplePayeesMode] = useState(false)
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, bus, title } = props
  const { field, dependence, source } = fieldInfo
  const validator = (rule, value, callback) => {
    if (multiplePayeesMode) {
      if (!value?.id && source && source === 'dataLink') {
        return callback(i18n.get('not-empty', { label: i18n.get(title) }))
      }
      return callback(undefined)
    }
    callback(required(fieldInfo, value))
  }

  const handleDependenceChange = () => {
    // TODO:
  }
  const initState = () => {
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    setMultiplePayeesMode(api.getState('@bills').multiplePayeesMode)
    setIsDependence(isDependence)
    setDependenceMap(dependenceMap)
    const value = record[`${field}`]
    setValue(value)
  }
  useEffect(() => {
    initState()
    bus.on('on:dependence:change', handleDependenceChange)
    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
    }
  }, [])

  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(<InputWrapper {...props} isDependence={isDependence} dependenceList={dependenceList} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any> {
  handleClick = () => {
    const { field: fieldInfo, bus, template, isDependence, dependenceList, value } = this.props
    const { dependence, name } = fieldInfo
    let list
    const isFeeDetailPayeeId = name === 'feeDetailPayeeId'
    if (isDependence) {
      if (isFeeDetailPayeeId) {
        const depFieldNames = dependence.map(el => el.dependenceId)
        const hasDepField = !!template.find(el => depFieldNames.includes(el.name))
        if (hasDepField) {
          list = dependenceList
        }
      } else {
        list = dependenceList
      }
    }
    bus
      .invoke('element:ref:select:payee', value, list, isFeeDetailPayeeId, {
        allowCancelDependence: fieldInfo?.allowCancelDependence
      })
      .then(data => {
        if (data) {
          this.handleChange(data)
        }
      })
  }
  handleChange = data => {
    const { onChange } = this.props
    onChange(data)
  }

  render() {
    const {
      value,
      form: { getFieldError },
      dataIndex
    } = this.props
    const error = getFieldError(dataIndex)
    const { fakeInputDisabled, placeholderValue, payeeValue } = formatPayeeInfoField(this.props, value)
    return (
      <CustomInput placeholder={placeholderValue} onClick={this.handleClick} error={error} disabled={fakeInputDisabled}>
        <PayeeInfoCell text={payeeValue} showEmpty />
      </CustomInput>
    )
  }
}

export default PayeeInfoEditCell
