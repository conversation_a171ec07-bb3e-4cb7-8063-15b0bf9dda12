import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { Input, Modal } from '@hose/eui'
import { formatListRefOrganizationStaffField, formItemStyle, itemInnerStyle } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../../../validator/validator'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { castArray, isString } from 'lodash'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { clearableTripDataLink } from '../../../../lib/lib-util'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
}
const ListRefOrganizationStaffEditCell: FC<IProps> = props => {
  const [value, setValue] = useState([])
  const [dependenceList, setDependenceList] = useState<any[]>([])
  const [isDependence, setIsDependence] = useState<boolean | undefined>()
  const [dependenceMap, setDependenceMap] = useState<any[]>()
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, bus, title } = props
  const { field, editable, allowExternalStaff, dependence } = fieldInfo
  const placeholder = getPlaceholder(fieldInfo)
  const disabled = !isDisable(props)
  const validator = (rule, value, callback) => {
    if (value && editable && !allowExternalStaff) {
      if (value.find(o => o.external)) {
        const externalRoot = api.getState()['@common'].externalDepartment?.data
        callback(
          i18n.get(`{label}的取值规则不允许选择`, { label: title }) +
          (externalRoot ? externalRoot[0].name : i18n.get('外部人员'))
        )
      }
    }
    callback(required(fieldInfo, value))
  }

  const initState = () => {
    let isDependence = !!dependence?.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    setIsDependence(isDependence)
    setDependenceMap(dependenceMap)
    const authStaffStaffMap = api.getState('@common').authStaffStaffMap
    const value = (record?.[`${field}`] || [])?.map(item => {
      if (item?.id && item?.name) {
        return item
      }
      if (isString(item) && Object.keys(authStaffStaffMap)?.length) {
        return authStaffStaffMap[item]
      }
      return item
    }).filter(f => !!f)
    setValue(value)
  }
  const handleDependenceChange = () => { }
  useEffect(() => {
    initState()
    bus.on('on:dependence:change', handleDependenceChange)
    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
    }
  }, [])

  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(
        <InputWrapper
          {...props}
          disabled={!disabled}
          placeholder={placeholder}
          dependenceList={dependenceList}
          isDependence={isDependence}
        />
      )}
    </FormItem>
  )
}

class InputWrapper extends Component<any> {
  handleClick = async () => {
    let {
      bus,
      value = [],
      field: fieldInfo,
      travelManagementConfig,
      form,
      submitterId,
      dependenceList,
      isDependence
    } = this.props
    const { allowExternalStaff, allowCancelDependence, valueRangeFilter } = fieldInfo
    const newValue = castArray(value).map(item => (typeof item === 'string' ? item : item?.id))
    if (isDependence && !dependenceList.length) {
      if (allowCancelDependence) {
        const shouldSelectAllUser = await new Promise((resolve, reject) => {
          Modal.confirm({
            title: i18n.get('您没有可以选择的员工'),
            okText: i18n.get('知道了'),
            cancelText: i18n.get('选择所有员工'),
            onCancel: () => resolve(true),
            onOk: () => resolve(false)
          })
        })
        if (!shouldSelectAllUser) {
          return
        }
        // 取消依赖
        dependenceList = undefined
      } else {
        return showModal.error({
          title: i18n.get('您没有可以选择的员工')
        })
      }
    }
    let staffRangeRule = false
    if (valueRangeFilter && valueRangeFilter !== 'false') {
      //后端定义这个“false”表示取消限制
      staffRangeRule = valueRangeFilter
    }
    if (allowExternalStaff) {
      let checkedList = []
      const internalStaffIds = value.filter(staff => !staff.external).map(line => line.id)
      checkedList.push({
        type: 'department-member',
        multiple: true,
        checkedKeys: internalStaffIds
      })
      const externalStaffIds = value.filter(staff => staff.external).map(line => line.id)
      checkedList.push({
        type: 'external',
        multiple: true,
        checkedKeys: externalStaffIds
      })
      bus.invoke('element:ref:select:staffs', { checkedList, allowExternalStaff, staffRangeRule }).then(data => {
        let staffs = []
        data.checkedList.forEach(checked => (staffs = staffs.concat(checked.checkedData || [])))
        this.handleChange(staffs)
        bus.emit('set:tripdatalink:traveler:change')
      })
    } else {
      bus
        .invoke('element:ref:select:staffs', {
          checkedKeys: newValue,
          multiple: true,
          dataSource: isDependence ? dependenceList : undefined,
          allowExternalStaff,
          staffRangeRule
        })
        .then(async data => {
          const checkStaffs = () => {
            if (data.length > 100) {
              showMessage.error(fieldInfo.label + i18n.get('不能超过100人'))
              return false
            }
            this.handleChange(data)
            return true
          }
          bus.emit('set:tripdatalink:traveler:change')
          // 存在[出行人][行程规划]且行程管理配置黑名单时，切换出行人时需校验提示
          if (
            fieldInfo.field === 'travelers' &&
            travelManagementConfig?.contextDetail?.length &&
            form.getFieldValue('u_行程规划')?.length // @i18n-ignore
          ) {
            const { clearable, travelerId } = clearableTripDataLink(data, value, submitterId)
            if (clearable) {
              showModal.confirm({
                title: i18n.get('提示'),
                content: i18n.get('根据行程配置，切换出行人时，您的行程明细将被清空，是否继续？'),
                cancelText: i18n.get('取消'),
                okText: i18n.get('确定'),
                onOk() {
                  if (checkStaffs()) {
                    bus.emit('set:tripdatalink:value')
                    bus.emit('set:tripdatalink:traveler:id', travelerId)
                  }
                }
              })
            } else {
              if (checkStaffs()) {
                bus.emit('set:tripdatalink:traveler:id', travelerId)
              }
            }
          } else {
            checkStaffs()
          }
        })
    }
  }
  handleChange = data => {
    const { onChange } = this.props
    onChange(data)
  }
  render() {
    const { disabled, value, placeholder } = this.props
    const { myValue } = formatListRefOrganizationStaffField(value)
    return (
      <Input
        disabled={disabled}
        style={itemInnerStyle}
        placeholder={placeholder}
        value={myValue}
        onClick={this.handleClick}
        onFocus={e => {
          e.target.blur()
        }}
      />
    )
  }
}

export default ListRefOrganizationStaffEditCell
