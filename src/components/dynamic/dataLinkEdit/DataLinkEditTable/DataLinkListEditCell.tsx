import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { Input } from '@hose/eui'
import { formatDataLink, formItemStyle, itemInnerStyle } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../../../validator/validator'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { get } from 'lodash'
import { showModal } from '@ekuaibao/show-util'
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
}

const DataLinkListEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any[]>([])
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { field, optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  placeholder = optional ? i18n.get('(选填)') + placeholder : placeholder
  const disabled = isDisable(props)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  const isEntity = (value: any) => {
    return value[0]?.data && value[0]?.path && value[0]?.template && value[0]?.data?.dataLink
  }
  const initState = () => {
    const value = record[`${field}`] || []
    if (value && Array.isArray(value) && value.length > 0) {
      if (isEntity(value)) {
        value.forEach((i: any) => {
          i.id = i.data.dataLink.id
        })
        setValue(value)
      } else {
        const ids = value.map(i => i?.id || get(i, 'data.dataLink.id') || i) 
        api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds: ids }).then((values: any) => {
          let data = values.items || []
          data.forEach((i: any) => {
            i.id = i.data.dataLink.id
          })
          setValue(data)
        })
      }
    }
  }

  useEffect(() => {
    initState()
  }, [])

  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(<InputWrapper {...props} placeholder={placeholder} disabled={disabled} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      islock: false
    }
  }
  handleClick = () => {
    const {
      bus,
      flowId,
      value,
      field: { referenceData, label, filterId }
    } = this.props
    const { islock } = this.state
    const groupType = get(referenceData, 'platformId.groupType')
    const type = get(referenceData, 'platformId.type')

    let values = value ? (Array.isArray(value) ? value : [value]) : []
    fnCheckNeedPhone()({ groupType, type }).then((res: any): any => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal').then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }
      return bus
        .invoke('element:select:dataLink', {
          referenceData,
          flowId,
          selectedEntity: values,
          values: values,
          islock,
          filterId,
          multiple: true,
          dataLink: { id: referenceData.id, selectedEntity: values, type, name: label }
        })
        .then((result: any) => {
          const { data } = result
          this.getDataLinkTempById(data)
        })
    })
  }
  getDataLinkTempById = (ids: any[]) => {
    const { onChange } = this.props
    api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds: ids }).then((values: any) => {
      let data = values.items || []
      data.forEach((i: any) => {
        i.id = i.data.dataLink.id
      })
      onChange(data)
    })
  }
  render() {
    const { disabled, value, placeholder } = this.props
    const v = formatDataLink(value)
    return (
      <Input
        disabled={disabled}
        style={itemInnerStyle}
        placeholder={placeholder}
        value={v}
        onClick={this.handleClick}
        onFocus={e => {
          e.target.blur()
        }}
      />
    )
  }
}

export default DataLinkListEditCell
