import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formatRefDataId, formItemStyle } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../../../validator/validator'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { cloneDeep, get, isObject } from 'lodash'
import TreeSelectSingle from '../../../../elements/puppet/TreeSelectSingle'
import { getIdByTreeDataIsExist } from '../../../../elements/puppet/rc-tree/util'
import { canSelectParent } from '../../../utils/fnInitalValue'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  form: any
  getExpenseStandardItemsLength: any
}

const RefDepartmentEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any>()
  const [isDependence, setIsDependence] = useState<boolean | undefined>()
  const [dependenceMap, setDependenceMap] = useState<any[]>()
  const [dependenceList, setDependenceList] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])
  const [originDependenceList, setOriginDependenceList] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [originDepartments, setOriginDepartments] = useState<any[]>([])
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, submitterId } = props
  const { field, dependence, optional } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  const initState = () => {
    let isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    setIsDependence(isDependence)
    setDependenceMap(dependenceMap)
    const value = record[`${field}`]
    setValue(value)
  }

  const fnFilterDepartmentSelectable = (
    department,
    rangeArr,
    selectableDepIdArr,
    onlyLeafCanBeSelected,
    parentDepartmentArr,
    visibleDepsArr
  ) => {
    const active = get(department, 'active', true)
    // @ts-ignore
    const depParentIdArr = [...parentDepartmentArr, department.id]
    if (onlyLeafCanBeSelected && department.children && department.children.length) department.selectable = false
    if (active && department.selectable !== false) {
      department.selectable = rangeArr.includes(department.id)
      if (department.selectable) {
        visibleDepsArr.push(...depParentIdArr)
        selectableDepIdArr.push(department.id)
      }
    }

    if (department.children && department.children.length) {
      department.children = department.children.map(dep =>
        fnFilterDepartmentSelectable(
          dep,
          rangeArr,
          selectableDepIdArr,
          onlyLeafCanBeSelected,
          depParentIdArr,
          visibleDepsArr
        )
      )
    }
    return department
  }

  //检查模版中是否配置了选项，让提交人只能选择其所在部门
  const fnFilterDeptForOnlyBelongDepartment = (field, departments, submitterId) => {
    if (field && field.onlyBelongDepartment) {
      const submitterDepartments = get(submitterId, 'departments', [])
      const onlyLeafCanBeSelected = !canSelectParent(field)
      if (submitterDepartments.length) {
        const submitterDepartmentsArr = submitterDepartments.map(dep => {
          if (typeof dep === 'string') return dep
          return dep.id
        })
        const deptArr = cloneDeep(departments)
        //1. 获取可选部门: selectableDepIdArr
        let selectableDepIdArr = []
        //2. 获取可展示出来的部门id，是否有必要去重？ visibleDepsArr
        let visibleDepsArr = []
        let deps = deptArr.map(dep =>
          fnFilterDepartmentSelectable(
            dep,
            submitterDepartmentsArr,
            selectableDepIdArr,
            onlyLeafCanBeSelected,
            [],
            visibleDepsArr
          )
        )
        // @ts-ignore
        if (!selectableDepIdArr.length > 0) return []
        //3. 给部门加上hide属性
        deps = deps.map(dep => fnAddAttributeInDep(dep, visibleDepsArr))
        if (selectableDepIdArr.length === 1) {
          const departmentsMap = api.getState('@common').departmentVisibility.mapData
          if (Object.keys(departmentsMap).length > 0) {
            const depValue = departmentsMap[selectableDepIdArr[0]]
            if (depValue) {
              const { id, name, code, form } = depValue
              setValue({ id, name, code, form })
            }
          }
        }
        return deps
      }
    }
    return departments
  }

  const fnAddAttributeInDep = (department, visibleDepsArr) => {
    if (!visibleDepsArr.includes(department.id)) department.hide = true
    if (department.children && department.children.length) {
      department.children = department.children.map(dep => fnAddAttributeInDep(dep, visibleDepsArr))
    }
    return department
  }

  const getDept = () => {
    // @ts-ignore
    const id = isObject(value) ? value.id : value
    Promise.all([
      api.dataLoader('@common.departmentVisibility').load(),
      api.invokeService('@common:get:department:by:id', id)
    ])
      .then(result => {
        const departmentVisibility = result[0]
        const dept = result[1]
        let originDepartments = get(departmentVisibility, 'data', [])
        const active = get(dept, 'active', true)
        if (!active) {
          // @ts-ignore
          originDepartments = [{ ...dept, name: dept.name }, ...originDepartments]
        }
        //与依赖性相关时，需要用依赖性过滤后的数组获取取值范围
        // const stateParams = {
        //   originDepartments,
        //   dependenceList: fnFilterDeptForOnlyBelongDepartment(fieldInfo, originDependenceList, submitterId),
        //   departments: fnFilterDeptForOnlyBelongDepartment(fieldInfo, originDepartments, submitterId)
        // }
        // this.setState(stateParams, () => {
        //   this.setState({ loading: false })
        // })
        setOriginDepartments(originDepartments)
        const dependenceList = fnFilterDeptForOnlyBelongDepartment(fieldInfo, originDependenceList, submitterId)
        setDependenceList(dependenceList)
        const departments = fnFilterDeptForOnlyBelongDepartment(fieldInfo, originDepartments, submitterId)
        setDepartments(departments)
      })
      .catch(() => {
        setLoading(false)
      })
  }
  useEffect(() => {
    initState()
    getDept()
  }, [])
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(
        <CustomSelectRefDepartment
          {...props}
          field={fieldInfo}
          placeholder={placeholder}
          isDependence={isDependence}
          dependenceList={dependenceList}
          departments={departments}
        />
      )}
    </FormItem>
  )
}

class CustomSelectRefDepartment extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = { useDependenceData: true }
  }
  handleOnChange = data => {
    const { onChange } = this.props
    onChange(data)
  }
  getDependenceListData = () => {
    const { useDependenceData } = this.state
    const { field: fieldInfo, form, isDependence, dependenceList, departments } = this.props
    const { field } = fieldInfo
    const value = form.getFieldsValue()
    const fieldValue = value && value[field]
    if (isDependence && fieldValue?.id && !getIdByTreeDataIsExist(dependenceList, fieldValue?.id)) {
      return departments
    } else if (useDependenceData && isDependence) {
      return dependenceList
    } else {
      return departments
    }
  }
  render() {
    const { useDependenceData } = this.state
    const { field: fieldInfo, placeholder, value, isDependence, getExpenseStandardItemsLength } = this.props
    const { editable, optional, allowCancelDependence, Component, multiple, selectRange } = fieldInfo
    const disabled = isDisable(this.props)
    const { id, checkedId } = formatRefDataId(value)
    const data = {
      id: id ? id : checkedId,
      refKey: 'RefDepartmentTreeSelect',
      placeholder,
      dropdownMatchSelectWidth: true,
      treeNodeData: this.getDependenceListData(),
      mode: useDependenceData && isDependence ? 'dependence' : 'normal',
      optional: optional,
      disabled,
      Component,
      multiple,
      onlyLeafCanBeSelected: !canSelectParent(fieldInfo),
      onChange: this.handleOnChange,
      getExpenseStandardItemsLength,
      range: selectRange,
      displayValue: value,
      // TODO: 
      isChangePosition: true,
      notFoundContent:
        allowCancelDependence && useDependenceData ? (
          <div className="cancel-dependence">
            {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
            <a href="javascript:void 0" onClick={() => this.setState({ useDependenceData: false })}>
              {i18n.get('查看全量数据')}
            </a>
          </div>
        ) : (
          undefined
        )
    }
    return <TreeSelectSingle {...this.props} disabled={!editable} field={fieldInfo} data={data} showActive useEUI/>
  }
}

export default RefDepartmentEditCell
