import React, { FC } from 'react'
import { Form } from 'antd'
import { DatePicker } from '@hose/eui'
import { isDisable } from '../../../utils/fnDisableComponent'
import { required } from '../../../validator/validator'
import moment from 'moment'
import { ENUM_DATE_TYPE } from '../../../consts'
import { formatDateTime, getShowTime } from '../../../utils/fnPredefine4Date'
import { formItemStyle, itemInnerStyle } from './utils'
const { MonthPicker } = DatePicker
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}

const DateEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { field, withTime, isClear = false, dateTimeType } = fieldInfo
  const disabled = isDisable(props)
  const validatorDate = (rule, value, callback) => {
    // const { defaultValue } = field
    // if (defaultValue && defaultValue.type === 'predefine' && defaultValue.value === 'repayment.date') {
    //   let loanDate = props.form.getFieldValue('loanDate')
    //   if (value && value < loanDate) return callback(i18n.get('还款日期应该晚于借款日期,请重新选择'))
    // }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorDate }],
        initialValue: record[`${field}`] && moment(record[`${field}`])
      })(
        dateTimeType === ENUM_DATE_TYPE.YEAR_MONTH ? (
          <MonthPicker style={itemInnerStyle} allowClear={isClear} disabled={disabled}/>
        ) : (
          <DatePicker
            style={itemInnerStyle}
            format={formatDateTime(withTime, dateTimeType)}
            allowClear={isClear}
            disabled={disabled}
            showTime={getShowTime(withTime, dateTimeType)}
          />
        )
      )}
    </FormItem>
  )
}

export default DateEditCell
