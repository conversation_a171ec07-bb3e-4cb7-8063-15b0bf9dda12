import { app as api } from '@ekuaibao/whispered'
import { get, isArray, cloneDeep } from 'lodash'
import { getValue } from '../../../../elements/dataLink-card/utils/dataLinkUtils'
import { getStaffShowValue } from '../../../../elements/utilFn'
import { isDisable } from '../../../utils/fnDisableComponent'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { formatWithTime } from '../../../utils/fnPredefine4Date'

const formItemStyle = { margin: 0 }

const itemInnerStyle = { width: '100%' }

const formatCellValue = (row, components, type) => {
  const vk = Object.keys(row)
  vk.forEach(item => {
    const curr = components.find(i => i.name === item)
    const v = row[item]
    const { withTime, dateTimeType } = curr
    if (curr.type === 'date') {
      row[item] = v ? formatWithTime(withTime, v, dateTimeType, type) : v
    }
    if (curr.type === 'dateRange') {
      if (!v) {
        row[item] = undefined
      } else {
        let start = v?.[0] ? formatWithTime(withTime, v?.[0], dateTimeType) : v?.[0]
        let end = v?.[1] ? formatWithTime(withTime, v?.[1], dateTimeType) : v?.[1]
        row[item] = { start, end }
      }
    }
  })
  return row
}

const formatPayeeInfoField = (props, value) => {
  const { field: fieldInfo, multiplePayeesMode, isRecordExpends, noDefault = false } = props
  const { isFeeDetail = false, source, forbidEdit } = fieldInfo

  let placeholder = getPlaceholder(fieldInfo)
  const disabled = isDisable(props)
  let fakeInputDisabled = multiplePayeesMode ? (isFeeDetail ? disabled : multiplePayeesMode) : disabled
  if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled) {
    fakeInputDisabled = multiplePayeesMode && disabled
  } else if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled === false) {
    fakeInputDisabled = multiplePayeesMode && !disabled
  } else if (multiplePayeesMode && source && source === 'dataLink') {
    fakeInputDisabled = multiplePayeesMode && disabled
  }
  let placeholderValue =
    !isFeeDetail && multiplePayeesMode && source !== 'dataLink' ? i18n.get('多收款人') : placeholder
  let payeeValue = value
  if (multiplePayeesMode && !isFeeDetail && source !== 'dataLink' && !isRecordExpends) {
    payeeValue = i18n.get('多收款人')
  } else if (!multiplePayeesMode && !value && !noDefault) {
    payeeValue = api.getState('@common.defaultPayee')
  }
  if (isRecordExpends) {
    fakeInputDisabled = false
    payeeValue = value
    placeholderValue = placeholder
  }
  if (forbidEdit !== undefined && forbidEdit) {
    fakeInputDisabled = true
  }
  return { fakeInputDisabled, placeholderValue, payeeValue }
}

const formatRefOrganizationStaffField = (value, props) => {
  const staffDisplayConfig = api.getState('@common').organizationConfig.staffDisplayConfig
  const {
    ownerId,
    field: { field }
  } = props
  let myValue = ''
  if (value && value.name) {
    const showValue = getStaffShowValue(value, staffDisplayConfig)
    myValue = `${value.name}${showValue}`
  }
  const userName = get(ownerId, 'name', '')
  if (myValue && userName && field === 'submitterId' && value.name !== userName) {
    myValue = i18n.get('generation-submit', { __k0: myValue, __k1: userName })
  }
  return { myValue }
}

const formatListRefOrganizationStaffField = value => {
  const staffDisplayConfig = api.getState('@common').organizationConfig.staffDisplayConfig
  let myValue = ''
  let users = []
  if (staffDisplayConfig?.length < 2) {
    users =
      isArray(value) && value?.length > 0 ? value?.filter(item => !!item).map(item => item?.name) : [value?.name || '']
  } else {
    users =
      isArray(value) && value?.length > 0
        ? value
          .filter(item => !!item)
          .map(line => {
            const showValue = getStaffShowValue(line, staffDisplayConfig)
            return `${line?.name}${showValue}`
          })
        : [value?.name || '']
  }
  myValue = users?.join(',')
  return { myValue }
}

const formatRefDataId = (value = {}) => {
  let checkedId = []
  if (typeof value === 'string') {
    value = {
      id: value
    }
  } else if (value instanceof Array) {
    let arr = []
    value?.length < 1
      ? (checkedId = [])
      : value.forEach(v => {
        arr.push(v?.id || v)
        checkedId = arr
      })
  }
  // @ts-ignore
  const id = value?.id
  return { id, checkedId }
}

const formatDataLink = data => {
  data = isArray(data) ? data : [data]
  const value = data.map(item => {
    const d = get(item, 'data')
    const titleField = get(item, 'template.content.expansion.title.fields')
    const title = getValue(titleField, d) || item?.name
    return title
  })
  return value.join('、')
}

function resetDataLinEdits(form) {
  const cloneForm = cloneDeep(form)
  for (let it in cloneForm) {
    const value = cloneForm[it]
    if (value && typeof value === 'object' && value.id) {
      cloneForm[it] = value.id
    }
    if (isArray(value)) {
      let ids = []
      value.forEach(line => {
        if (line && typeof line === 'object' && line.id) {
          ids.push(line.id)
        } else if (line && typeof line === 'string') {
          ids.push(line)
        }
      })
      cloneForm[it] = ids
    }
    if (cloneForm[it] && Array.isArray(cloneForm[it]) && cloneForm[it].length > 0 && cloneForm[it][0].id) {
      cloneForm[it] = cloneForm[it].map(i => i.id)
    }
  }
  return cloneForm
}
export {
  formItemStyle,
  itemInnerStyle,
  formatCellValue,
  formatPayeeInfoField,
  formatRefOrganizationStaffField,
  formatListRefOrganizationStaffField,
  formatRefDataId,
  formatDataLink,
  resetDataLinEdits
}
