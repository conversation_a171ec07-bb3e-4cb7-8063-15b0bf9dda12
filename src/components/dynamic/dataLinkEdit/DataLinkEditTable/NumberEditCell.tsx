import React, { FC } from 'react'
import { Form } from 'antd'
import { InputNumber } from '@hose/eui'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { required } from '../../../validator/validator'
import { formItemStyle, itemInnerStyle } from './utils'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}
const NumberEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, title, record, field: fieldInfo } = props
  const {
    optional,
    editable,
    field,
    dataType = {},
    unit: unitFiled = '',
    step: stepField,
    max,
    min,
    scale: filedScale = 0
  } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const unit = dataType.unit || unitFiled
  const step = dataType.scale ? 1 / Math.pow(10, dataType.scale) : stepField
  const disabled = isDisable(props)
  const validator = (rule, value, callback) => {
    let re = /^-?[0-9]+$/
    let { scale = 0 } = dataType
    if (filedScale) {
      scale = filedScale
    }
    if (scale) {
      re = new RegExp(`^-?(([1-9]\\d*)|0)(\\.\\d{1,${scale}})?$`)
    }
    if (value) {
      let msg = scale ? i18n.get('please-number-format', { scale }) : i18n.get('请输入整数')
      if (!re.test(value)) return callback(msg)
      if (!editable && value * 1 > 10000000000) {
        return callback(i18n.get('cannot-be-greater', { label: title, max: 10000000000 }))
      }
      if (!editable && value * 1 < -10000000000) {
        return callback(i18n.get('cannot-be-less', { label: title, min: -10000000000 }))
      }
      if (editable && value * 1 > max * 1) {
        return callback(i18n.get('cannot-be-greater', { label: title, max }))
      }
      if (editable && value * 1 < min * 1) {
        return callback(i18n.get('cannot-be-less', { label: title, min }))
      }
      //   if (fieldInfo.name === 'min' || fieldInfo.name === 'minLength') {
      //     let fieldName = fieldInfo.name === 'min' ? 'max' : 'maxLength'
      //     let max = props.form.getFieldValue(fieldName)
      //     if (max && value && value * 1 > max * 1) {
      //       return callback(i18n.get('最小值不能大于最大值'))
      //     }
      //   }
    }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: record[`${field}`]
      })(
        <InputNumber
        style={itemInnerStyle}
          placeholder={placeholder}
          formatter={value => `${value}${unit}`}
          // @ts-ignore
          parser={value => value.replace(unit, '')}
          step={step}
          disabled={disabled}
        />
      )}
    </FormItem>
  )
}

export default NumberEditCell
