import React from 'react'
import { Tooltip } from 'antd'
import styles from './Popover.module.less'
import { formatLinkText } from '../../../../elements/DataLinkTable/tableUtil'
const getTotalTime = (formatStr: number) => {
  let m = Math.ceil((formatStr % 3600000) / 60000)
  let h = Math.floor(formatStr / 3600000)
  if (h > 0) {
    return i18n.get('{__k0}时{__k1}分', { __k0: h, __k1: m })
  }
  return i18n.get('{__k0}分', { __k0: m })
}

const SimpleCell = ({ text, name }) => {
  const { textValue, domValue } = formatLinkText(text, name)
  let finalText = textValue || i18n.get('无')
  if (name?.indexOf('行驶总时间') > -1) {
    finalText = text ? getTotalTime(text) : i18n.get('无')
  } else if (Array.isArray(text)) {
    finalText = text.length ? text.map(v => v.address).join('-') : i18n.get('无')
  } else if (text?.address || text?.name) {
    const withAddress = text?.address ? `(${text?.address})` : ''
    finalText = text?.name ? `${text?.name}${withAddress}` : text.address
  } else if (text) {
    return (
      <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
        <Tooltip title={finalText}>{domValue || finalText}</Tooltip>
      </div>
    )
  }

  return (
    <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
      <Tooltip title={finalText}>{finalText}</Tooltip>
    </div>
  )
}

export default SimpleCell
