import React, { <PERSON> } from 'react'
import { Form } from 'antd'
import { Switch } from '@hose/eui'
import { isAllowModifyFiled } from '../../../utils/fnDisableComponent'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  record: any
  field: any
  currentNode: any
}

const SwitchEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo, currentNode } = props
  const { field, editable, name } = fieldInfo
  const disabled = !isAllowModifyFiled(currentNode, name) || !editable
  return (
    <FormItem style={{ margin: 0 }}>
      {getFieldDecorator(dataIndex, {
        valuePropName: 'checked',
        initialValue: record[`${field}`]
      })(<Switch disabled={disabled} />)}
    </FormItem>
  )
}

export default SwitchEditCell
