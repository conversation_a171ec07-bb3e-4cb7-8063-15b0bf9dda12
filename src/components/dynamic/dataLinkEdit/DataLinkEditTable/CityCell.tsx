import { Popover } from 'antd'
import React from 'react'
import styles from './Popover.module.less'
import { isObject, isString } from '@ekuaibao/helpers'
const CityCell = ({ text }) => {
  let cityList = []
  try {
    cityList = isString(text) ? JSON.parse(text) || [] : isObject(text) ? [text] : []
  } catch (error) {}
  if (cityList.length === 1) {
    return <div className={styles.tableReadonlyWidth}>{cityList.map(item => item.label || item.name)}</div>
  } else if (cityList.length === 0) {
    return <div className={styles.tableReadonlyWidth}>{i18n.get('无')}</div>
  } else {
    const content = (
      <div className={styles.popoverContainer}>
        {cityList.map((item, index) => (
          <div className={styles.item} key={index}>
            {item.label || item.name}
          </div>
        ))}
      </div>
    )
    return (
      <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
        <Popover placement="topLeft" content={content}>
          {cityList.map(v => v.label).join(i18n.get('、'))}
        </Popover>
      </div>
    )
  }
}

export default CityCell
