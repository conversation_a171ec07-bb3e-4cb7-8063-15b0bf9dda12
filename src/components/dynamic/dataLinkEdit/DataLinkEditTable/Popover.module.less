.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-ellipsis-mixin(@lines: 3) {
    white-space: inherit;
    display: -webkit-box;
    -webkit-line-clamp: @lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.popoverContainer {
    width: 180px;
    max-height: 204px;
    border-radius: 8px;
    overflow-y: auto;
    font-size: 14px;
    color: var(--eui-text-title);

    .fileItem {
        display: flex;
        align-items: center;

        .fileName {
            margin-left: 10px;
            .ellipsis
        }

        margin-bottom: 12px;

        &:last-of-type {
            margin-bottom: 0;
        }
    }

    .item {
        width: 100%;
        margin-bottom: 12px;

        &:last-of-type {
            margin-bottom: 0;
        }

        .ellipsis
    }
}


.tableReadonlyWidth {
    font-size: 14px;
    color: var(--eui-text-caption);
    display: flex;
    align-items: center;

    &.textEllipsis3Lines {
       .text-ellipsis-mixin(2);

        span {
            .text-ellipsis-mixin(2);
        }

        .fileItem {
            .fileName {
                .text-ellipsis-mixin(2);
            }
        }
    }

    span {
        .ellipsis
    }

    .fileItem {
        display: flex;
        align-items: center;
        width: 100%;

        .fileName {
            margin-left: 10px;
            .ellipsis
        }
    }

    .ellipsis
}