import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formatRefDataId, formItemStyle } from './utils'
import { required } from '../../../validator/validator'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import RefEditCellInner from '../../../../elements/puppet/Ref'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
  bus: any
  submitterId: string
  form: any
  getExpenseStandardItemsLength: any
}
const RefEditCell: FC<IProps> = props => {
  const [value, setValue] = useState<any>()
  const [dependenceList, setDependenceList] = useState<any[]>([])
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { field, optional, defaultPlaceholder } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }

  const initState = () => {
    const value = record[`${field}`]
    setValue(value)
  }
  useEffect(() => {
    initState()
  })

  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(<CustomSelectRef {...props} field={fieldInfo} placeholder={placeholder} dependenceList={dependenceList} />)}
    </FormItem>
  )
}

class CustomSelectRef extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = { dimensionList: [] }
  }
  handleOnChange = (data?) => {
    const { onChange } = this.props
    onChange(data)
  }
  handleDimensionList = dimensionList => {
    this.filterLastChoice(dimensionList, undefined, this.props.value)
    this.setState({ dimensionList })
  }
  filterLastChoice = (list, preValue, nextValue) => {
    const { bus } = this.props
    if (nextValue && nextValue.id && (!nextValue.code || nextValue.needCheckValue)) {
      const map = treeDataToMap(list)
      let vv = map[nextValue.id]
      if (!vv || !vv.active) {
        bus.setValidateLevel(1)
        preValue ? this.handleOnChange(preValue) : this.handleOnChange()
        // TODO:
        // this.__value = nextValue
      } else {
        this.handleOnChange(vv)
      }
    }
  }
  render() {
    const { dimensionList = [] } = this.state
    const {
      field: fieldInfo,
      placeholder,
      value,
      isDependence,
      getExpenseStandardItemsLength,
      submitterId,
      isModify,
      fromSupplier,
      flowId,
      form,
      id: dimensionType,
      // TODO:
      dependenceListOnLoading,
      dependenceList
    } = this.props
    const {
      optional,
      allowCancelDependence,
      Component,
      multiple,
      selectRange,
      dataType,
      type,
      defaultValue,
      isCanViewAllDataWithResultBlank,
      allMatchList,
      hideCode,
      isShowFullPath
    } = fieldInfo
    const { entity = '' } = dataType
    const param = isModify ? { name: entity, flowId } : { name: entity }
    const disabled = isDisable(this.props)
    const { id, checkedId } = formatRefDataId(value)
    const data = {
      id: id ? id : checkedId,
      placeholder,
      disabled,
      optional: optional,
      onChange: this.handleOnChange,
      Component,
      multiple,
      getExpenseStandardItemsLength,
      allMatchList,
      hideCode,
      isShowFullPath,
      isChangePosition: true
    }
    return (
      <RefEditCellInner
        {...this.props}
        submitterId={submitterId?.id}
        fromSupplier={fromSupplier}
        data={data}
        allowCancelDependence={allowCancelDependence}
        dimensionList={dimensionList}
        param={param}
        onlyLeafCanBeSelected={'all' !== selectRange}
        onDimensionChange={this.handleDimensionList}
        isDependence={isDependence}
        dependenceList={dependenceList}
        dimensionType={dimensionType}
        type={type}
        isCanViewAllDataWithResultBlank={isCanViewAllDataWithResultBlank}
        defaultValue={defaultValue}
        dependenceListOnLoading={dependenceListOnLoading}
        form={form}
        field={fieldInfo}
        useEUI={true}
      />
    )
  }
}

export default RefEditCell
