import React, { FC } from 'react'
import { Form } from 'antd'
import { Input } from '@hose/eui'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { checkedAddress, checkedEmail, required } from '../../../validator/validator'
import { formItemStyle, itemInnerStyle } from './utils'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}
const SimpleEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, title, record, field: fieldInfo } = props
  const { optional, field, maxLength, minLength, name, editable, validator } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + placeholder
  const disabled = isDisable(props)
  const validatorInput = (rule, value, callback) => {
    if (!editable && value && value.length > 1000) {
      return callback(i18n.get('cannot-exceed-words', { label: title, maxLength: 1000, length: value.length }))
    }
    if (editable && value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label: title, maxLength, length: value.length }))
    }
    if (editable && value && value.length < minLength) {
      return callback(i18n.get('cannot-less-words', { label: title, minLength }))
    }
    if (name === 'receiver_email' || name === 'email') {
      return callback(checkedEmail(value))
    }
    if (name === 'receiver_address') {
      return callback(checkedAddress(value))
    }
    if (!!value && !!validator?.(value)) {
      return callback(validator(value))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validatorInput }],
        initialValue: record[`${field}`]
      })(<Input style={itemInnerStyle} placeholder={placeholder} disabled={disabled} />)}
    </FormItem>
  )
}

export default SimpleEditCell
