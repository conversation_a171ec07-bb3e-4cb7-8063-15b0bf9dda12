import React, { Component, FC, useEffect, useState } from 'react'
import { Form } from 'antd'
import { formItemStyle } from './utils'
import { required } from '../../../validator/validator'
import { isDisable } from '../../../utils/fnDisableComponent'
import { app as api } from '@ekuaibao/whispered'
import AttachmentsCell from './AttachmentsCell'
import CustomInput from './CustomInput'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}

const AttachmentsEditCell: FC<IProps> = props => {
  const [value, setValue] = useState()
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  let { field } = fieldInfo
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  const initState = () => {
    const value = record[`${field}`] || []
    setValue(value)
  }

  useEffect(() => {
    initState()
  }, [])
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(<InputWrapper {...props} />)}
    </FormItem>
  )
}

class InputWrapper extends Component<any, any> {
  handleClick = () => {
    const { onChange } = this.props
    api.open('@bills:AttachmentsModal', { params: { ...this.props } }).then(res => {
      onChange?.(res)
    })
  }
  render() {
    const {
      value,
      form: { getFieldError },
      dataIndex
    } = this.props
    const disabled = isDisable(this.props)
    const error = getFieldError(dataIndex)
    return (
      <CustomInput placeholder="请上传文件" disabled={disabled} onClick={this.handleClick} error={error}>
        <AttachmentsCell value={value} showEmpty />
      </CustomInput>
    )
  }
}

export default AttachmentsEditCell
