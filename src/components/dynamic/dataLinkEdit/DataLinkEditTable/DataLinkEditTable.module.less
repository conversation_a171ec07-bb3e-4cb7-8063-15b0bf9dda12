.dataLinkEditTableWrapper {
  width: 100%;
  
  .dataLinkEditTableHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  :global {
    .index-column {
      width: 40px !important;
    }

    .eui-table tbody tr td:first-child,
    .eui-table thead tr th:first-child {
      padding-left: 8px !important;
    }

    .btn-group {
      display: flex;
      align-items: center;

      .icon-style {
        color: var(--eui-icon-n2);

        &:first-of-type {
          margin-right: 20px;
        }
      }

      .icon-save {
        color: #30bf78;
      }

      .icon-cancel {
        color: var(--eui-function-danger-500);
      }
    }

    .edit-number-wrapper {
      display: flex;
      align-items: center;
      cursor: pointer;

      .show {
        display: none;
      }

      &:hover {
        .show {
          display: inline-flex;
        }
      }
    }
  }

  .dataLinkEditTable {
    width: 100%;
  }
}

.dataLinkEditEmptyWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 168px;
  border: 1px dashed rgba(29, 33, 41, 0.2);
  border-radius: 6px;
}

.itemCreateBtn {
  font-size: 14px;
  color: var(--eui-text-link-normal);
  cursor: pointer;
  margin-right: 5px;
  display: flex;
  align-items: center;
}


.cellWrapper {
  position: relative;
  padding: 8px;

  &.errorCell {
    background-color: var(--eui-function-danger-50);
    border: 1px solid var(--eui-function-danger-500);
  }
}

.errorMessage {
  position: absolute;
  bottom: -20px;
  left: 0;
  color: var(--eui-function-danger-500);
  font-size: 12px;
  white-space: nowrap;
}

.fullScreenWrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 999;
  padding: 20px;
  overflow: auto;

  .dataLinkEditTable {
    height: calc(100vh - 100px);

    :global {
      .eui-table-wrapper {
        height: 100%;
      }

      .eui-table-body {
        overflow-y: auto;
        max-height: calc(100vh - 200px);
      }
    }
  }
}