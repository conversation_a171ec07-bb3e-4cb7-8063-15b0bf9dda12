import { app as api } from '@ekuaibao/whispered'
import React, { useEffect, useState } from 'react'
import { isArray, get } from 'lodash'
import { getValue } from '../../../../elements/dataLink-card/utils/dataLinkUtils'
import { Popover } from 'antd'
import styles from './Popover.module.less'
const DataLinksCell = ({ text, isHabSetValue }) => {
  const [data, setData] = useState([])
  useEffect(() => {
    text = isArray(text) ? text : [text]
    const dataLinkIds = text.map(i => i?.id || i?.data?.dataLink?.id || i).filter(i => i)
    if (dataLinkIds.length && !isHabSetValue) {
      api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds }).then((values: any) => {
        const data = values.items || []
        data.forEach((i: any) => {
          i.id = i.data.dataLink.id
          const titleField = get(i, 'template.content.expansion.title.fields')
          const data = get(i, 'data')
          const name = getValue(titleField, data)
          i.name = name
        })
        setData(data)
      })
    } else {
      setData(text)
    }
  }, [text])

  if (data.length === 1) {
    return <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>{data.map(i => i?.name)}</div>
  } else if (data.length === 0) {
    return <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>{i18n.get('无')}</div>
  } else {
    const content = (
      <div className={styles.popoverContainer}>
        {data.map((item, index) => (
          <div className={styles.item} key={index}>
            {item?.name}
          </div>
        ))}
      </div>
    )
    return (
      <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
        <Popover placement="topLeft" content={content}>
          {data.map(i => i?.name).join(i18n.get('、'))}
        </Popover>
      </div>
    )
  }
}

export default DataLinksCell
