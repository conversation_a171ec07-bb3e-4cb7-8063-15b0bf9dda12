import React, { FC } from 'react'
import { Form } from 'antd'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { required } from '../../../validator/validator'
import { formItemStyle, itemInnerStyle } from './utils'
import { get } from 'lodash'
import EnumEditCellInner from '../../../../elements/puppet/RefEnum'
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}

const EnumEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { optional, field } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const disabled = isDisable(props)
  const value = record[`${field}`]
  const entity = get(fieldInfo, 'dataType.entity', '')
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(
        <EnumEditCellInner
          {...props}
          disabled={disabled}
          entity={entity}
          style={itemInnerStyle}
          placeholder={placeholder}
          optional={optional}
          useEUI={true}
        />
      )}
    </FormItem>
  )
}

export default EnumEditCell
