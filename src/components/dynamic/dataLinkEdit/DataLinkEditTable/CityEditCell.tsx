import React, { FC } from 'react'
import { app } from '@ekuaibao/whispered'
import { Form } from 'antd'
import { getPlaceholder } from '../../../utils/fnGetFieldLabel'
import { isDisable } from '../../../utils/fnDisableComponent'
import { required } from '../../../validator/validator'
import { formItemStyle, itemInnerStyle } from './utils'
const CityPicker = app.require<any>('@components/dynamic/CityPickerComponent')
const FormItem = Form.Item
interface IProps {
  getFieldDecorator: any
  dataIndex: string
  title: string
  record: any
  field: any
}
const CityEditCell: FC<IProps> = props => {
  const { getFieldDecorator, dataIndex, record, field: fieldInfo } = props
  const { optional, field, multiple = false  } = fieldInfo
  let placeholder = getPlaceholder(fieldInfo)
  if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
  const disabled = isDisable(props)
  const value = record[`${field}`]
  const validator = (rule, value, callback) => {
    callback(required(fieldInfo, value))
  }
  return (
    <FormItem style={formItemStyle}>
      {getFieldDecorator(dataIndex, {
        rules: [{ validator: validator }],
        initialValue: value
      })(
        <CityPicker
          {...props}
          isAuto={disabled}
          otherStyle={itemInnerStyle}
          placeholder={placeholder}
          optional={optional}
          multiple={multiple}
        />
      )}
    </FormItem>
  )
}

export default CityEditCell
