import React from 'react'
import { formatLang } from '../../../../lib/lib-util'
import styles from './Popover.module.less'
const EnumCell = ({ text, customData }) => {
  const IFCustomData = customData && customData.find(item => item.code === text)
  return (
    <>
      {IFCustomData ? (
        <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
          {text && IFCustomData['name'] ? IFCustomData['name'] : i18n.get('无')}
        </div>
      ) : (
        <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
          {text && text[formatLang()] ? text[formatLang()] : i18n.get('无')}
        </div>
      )}
    </>
  )
}

export default EnumCell
