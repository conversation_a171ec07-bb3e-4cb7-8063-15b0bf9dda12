import React from 'react'
import { isArray } from 'lodash'
import styles from './Popover.module.less'
const RefCell = ({ text }) => {
  if (isArray(text)) {
    return <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>{text.map(i => i?.name).join(i18n.get('、'))}</div>
  }
  return <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>{text?.name || i18n.get('无')}</div>
}

export default RefCell
