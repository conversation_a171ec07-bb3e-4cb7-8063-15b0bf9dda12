import React from 'react'
import { timeConvert } from '../../../utils/fnPredefine4Date'
import styles from './Popover.module.less'
const DateRangeCell = ({ text, field }) => {
  const { withTime, dateTimeType } = field
  // @ts-ignore
  const { start, end } = text || {}
  return (
    <div className={`${styles.tableReadonlyWidth} ${styles.textEllipsis3Lines}`}>
      {timeConvert(withTime, dateTimeType, start) + ' - ' + timeConvert(withTime, dateTimeType, end)}
    </div>
  )
}

export default DateRangeCell
