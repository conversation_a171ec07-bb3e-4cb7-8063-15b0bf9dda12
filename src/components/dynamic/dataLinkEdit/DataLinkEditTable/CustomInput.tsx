import React, { FC } from 'react'
import styles from './CustomInput.module.less'
interface IProps {
  placeholder: string
  disabled: boolean
  onClick: () => void
  error: any[]
}
const CustomInput: FC<IProps> = props => {
  const { placeholder, disabled, onClick, error, children } = props
  return (
    <div
      placeholder={placeholder}
      onClick={!disabled ? onClick : null}
      className={disabled ? styles.disabledWrapper : error ? styles.customInputWrapperError : styles.customInputWrapper}
    >
      {children}
    </div>
  )
}

export default CustomInput
