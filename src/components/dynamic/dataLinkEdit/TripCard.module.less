@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.trip-card {
  width: 100%;
  height: 100px;
  border: 1px solid #e5e5e5;
  box-shadow: inset 0px -0.5px 0px #e6e6e6;
  border-radius: 8px;
  margin-bottom: 12px;
  padding-left: 14px;
  padding-top: 16px;
  padding-right: 14px;
  :global {
    .infoTop {
      display: flex;
      align-items: center;
      width: 100%;
      position: relative;
      .trip-type-icon {
        width: 32px;
        height: 32px;
        margin-right: 14px;
      }
      .content-wrapper {
        flex: 1;
        .date {
          color: @color-black-1;
          .font-size-3;
          .font-weight-3;
        }
        .city {
          .font-size-2;
          color: @color-black-2;
        }
      }
      .close-icon {
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        height: 20px;
      }
    }
    .content-money {
      display: flex;
      font-size: 14px;
      padding-left: 46px;
      span {
        cursor: pointer;
        color: var(--brand-base);
        margin-right: 30px;
      }
    }
  }
}
