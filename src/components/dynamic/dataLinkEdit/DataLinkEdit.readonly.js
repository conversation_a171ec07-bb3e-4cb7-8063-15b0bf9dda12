import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import DataLinkEditReadonlyComp from './DataLinkEditReadonlyComp'
@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData, name } = field
      return (type === 'dataLinkEdits' && referenceData.type !== 'TRIP') || name === 'budgetAdjustDetails'
    }
  },
  validator: (field, props) => (rule, value, callback) => {},
  wrapper: wrapper(true, false, false, true)
})
export default class DataLinkEdit extends PureComponent {
  render() {
    return <DataLinkEditReadonlyComp {...this.props} />
  }
}
