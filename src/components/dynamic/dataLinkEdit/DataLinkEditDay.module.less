@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.data-link-edit-warp {
  :global {
    header {
      display: flex;
      align-items: center;

      h4 {
        font-size: 13px;
        font-weight: 500;
        color: #333;
        flex: 1;
      }

      .delete-btn {
        cursor: pointer;
      }
    }

    section {
      margin-top: 8px;

      .trip-type {
        margin-bottom: 24px;
        .label {
          padding-right: 8px;
          line-height: 26px;
          .font-size-2;
          color: rgba(29, 43, 61, 0.75);
        }
        .label:before {
          display: inline-block;
          margin-right: 4px;
          content: '*';
          line-height: 1;
          .font-size-1;
          color: #ff7c7c;
        }
        .label:after {
          content: ':';
          margin: 0 8px 0 2px;
          position: relative;
          top: -0.5px;
        }
      }

      .ant-select-selection-selected-value {
        .icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          box-sizing: border-box;
          vertical-align: middle;
          border-radius: 20px;
          margin-right: 8px;
          line-height: 10px;
          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
}
