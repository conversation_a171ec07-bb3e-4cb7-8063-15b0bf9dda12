@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.data-link-red {
  .font-size-2;
  .font-weight-2;
  color: @color-error;
}

.trip-details-wrapper {
  width: 100%;

  :global {

    .ant-btn {
      margin-right: @space-4;
      border: 1px solid #b9e6eb;
    }

    .trip-select-wrapper {
      // margin-top: @space-5;

      .add-button-wrapper {
        display: flex;
        margin-top: 12px;

        .add-button {
          color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
        }

        .add-button, .trip-sync {
          margin-right: 8px;
        }
      }
    }

    .trips-list-wrapper {

      .trips-item {
        position: relative;
        border-radius: @radius-2;

        // &:hover {
        //   cursor: pointer;
        //   background: rgba(29, 43, 61, 0.06) !important;
        // }

        .icon-wrapper {

          &::before,
          &::after {
            position: absolute;
            left: 50%;
            display: block;
            content: '';
            width: 1px;
            height: 20px;
            background: rgba(29, 43, 61, 0.09);
          }

          &::before {
            top: -60%;
          }

          &::after {
            top: 100%;
          }
        }

        .delete-wrapper {
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          width: 24px;
          height: 24px;
          background: #f4526b;

          img {
            transform: translate(50%, -30%);
          }
        }

        &:first-child .icon-wrapper::before,
        &:last-child .icon-wrapper::after {
          display: none;
        }
      }
    }
  }
}

:global{
  .tripSyncContent {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    width: 100px;
    text-align: center;
    span {
      display: inline-block;
      width: 80px;
      height: 30px;
      color: #fff;
      border-radius: 2px;
      background-color: #22b2cc;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      border-color: transparent;
      line-height: 30px;
      margin: 10px 0px;
    }

  }
}