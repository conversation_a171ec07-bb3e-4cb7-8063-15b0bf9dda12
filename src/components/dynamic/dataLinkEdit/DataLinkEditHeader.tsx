import React, { FC, useState } from 'react'
import { Menu, Dropdown } from 'antd'
import fnGetFieldLabel from '../../utils/fnGetFieldLabel'
import styles from './DataLinkEditHeader.module.less'
import FormItemLabelWrap from '../../layout/FormItemLabelWrap/FormItemLabelWrap'
import { realName } from '../../../elements/util'

const MenuItem = Menu.Item
interface IProps {
  field: any
  isEdit: boolean
  templates?: any[]
  dataLinkTemplateId?: string
  onChangeDataLinkTemplateId?: (id: string) => void
  clearButton?: React.ReactNode | null
}

const DataLinkEditHeader: FC<IProps> = props => {
  const { field, isEdit, templates = [], dataLinkTemplateId = '', onChangeDataLinkTemplateId, clearButton, external, flowId, isForbid } = props
  const [visible, setVisible] = useState(false)
  const handleVisibleChange = show => {
    setVisible(show)
  }
  const handleMenuClick = selectKey => {
    onChangeDataLinkTemplateId?.(selectKey.key)
    setVisible(false)
  }
  const menu = (
    <Menu onClick={handleMenuClick}>
      {templates.map(item => (
        <MenuItem key={item.templateId}>{realName({ name: item.entity.name, enName: item.entity.enName })}</MenuItem>
      ))}
    </Menu>
  )

  const dropdownName = () => {
    const entity = templates.find(item => item.templateId === dataLinkTemplateId)?.entity
    return realName({ name: entity?.name, enName: entity.enName })
  }

  return (
    <div className={styles.header}>
      <div className={styles.title}>
        {!(field?.optional || !isEdit) && (
          <span style={{ color: '#ff7c7c', height: '20px', marginRight: '4px' }}>*</span>
        )}
        <FormItemLabelWrap
          isForbid={isForbid}
          field={field}
          flowId={flowId}
          isDetail={false}
          isEdit={false}
          external={external}
        >
          <span>{fnGetFieldLabel(field)}</span>
        </FormItemLabelWrap>
        {clearButton}
      </div>
      <div>
        {templates.length > 1 && (
          <Dropdown overlay={menu} onVisibleChange={handleVisibleChange} visible={visible}>
            <span>{dropdownName()}</span>
          </Dropdown>
        )}
      </div>
    </div>
  )
}

export default DataLinkEditHeader
