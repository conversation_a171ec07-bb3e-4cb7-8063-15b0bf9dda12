import React from 'react'
import styles from './DataLinkEditSingle.module.less'
import { Form } from 'antd'
import { cloneDeep } from 'lodash'
import DataLinkEditDay from './DataLinkEditDay'
import DataLinkSelect from './DataLinkSelect'
const FormItem = Form.Item

class DataLinkEditSingle extends React.Component {
  constructor(props) {
    super(props)
    let { value } = props
    this.state = {
      items: [],
      dataLinkTemplateId: value.dataLinkTemplateId || '',
      oldDataLinkTemplateId: value.dataLinkTemplateId || '',
      value: value.dataLinkForm || {},
      dataLinkId: value.dataLinkId || null,
      templates: [],
      template: ''
    }
  }
  componentDidMount() {
    let { status } = this.props
    this.updateprops()
    if (!status || status !== 'only') {
      this.props.bus.on('dynamic:value:changed', this.handleChange)
    }

   
  }
  updateprops = (templates = this.props.templates, templatesold = this.props.templatesold) => {
    let { dataLinkTemplateId, oldDataLinkTemplateId } = this.state
  
    let arrp = templates.filter(i => {
      return i.entity.parentId === ''
    })
    let arrt = [...templates]

    if (dataLinkTemplateId !== '') {
      let obj = templatesold.find(t => t.templateId === dataLinkTemplateId)
      let oldobj = templatesold.find(t => t.templateId === oldDataLinkTemplateId)
      if (oldobj && oldobj.entity.active === false ) {
        arrt.push(oldobj)
      }
      let newst = {}
      
      if (obj && obj.components) {
        newst.template = cloneDeep(obj)
      } else if (arrp.length > 0) {
        newst.template = cloneDeep(arrp[0])
        newst.dataLinkTemplateId = arrp[0].templateId
      }
      this.setState({
        ...newst,
        templates: arrt
      })
    } else {
      if (arrt.length == 0 && arrp.length > 0) {
        this.setState(
          {
            dataLinkTemplateId: arrp[0].templateId,
            template: cloneDeep(arrp[0]),
            templates: []
          },
          () => {
            this.handleChange()
          }
        )
      } else if (arrt.length > 0) {
        this.setState(
          {
            dataLinkTemplateId: arrt[0].templateId,
            template: cloneDeep(arrt[0]),
            templates: arrt
          },
          () => {
            this.handleChange() //this.handleType('')
          }
        )
      }
    }
  }
  componentWillReceiveProps = np => {
    if (
      np.value.dataLinkForm !== this.props.value.dataLinkForm ||
      np.templates.length != this.props.templates.length ||
      np.templatesold.length != this.props.templatesold.length
    ) {
      let { value } = np
      this.setState(
        {
          dataLinkTemplateId: value.dataLinkTemplateId || '',
          value: value.dataLinkForm ? { ...value.dataLinkForm } : {},
          dataLinkId: value.dataLinkId || null
        },
        () => {
          this.updateprops(np.templates, np.templatesold)
        }
      )
    }
  }
  componentWillUnmount() {
    this.props.bus.un('dynamic:value:changed', this.handleChange)
  }
  handleChange = resetdata => {
    const { bus } = this.props
    bus.getValue().then(data => {
      const d = {}
      for (let e in data) {
        if (typeof data[e] !== 'undefined' && data[e] !== null) {
          d[e] = data[e]
        }
      }
      this.handleType(this.state.dataLinkTemplateId, d)
    })
  }

  handleSaveType = templateId => {
    let { templates } = this.state
    let obj = templates.find(t => t.templateId === templateId)
    this.setState({
      template: cloneDeep(obj),
      dataLinkTemplateId: templateId
    })
    this.handleType(templateId)
  }

  handleType = (templateId, formobj = this.state.value) => {
    this.props.handleType && this.props.handleType(templateId, this.state.dataLinkId, formobj)
  }
  getTempName = id => {
    let { templates } = this.props
    let obj = templates.find(i => {
      return i.templateId === id
    })
    if (obj) {
      return obj.entity.name
    }
    return '-'
  }
  renderOnlyType = () => {
    let { label } = this.props
    return (
      <Form className="type-only form_item__label_forFix">
        <FormItem label={i18n.get('类型')}>{this.getTempName(this.props.value.dataLinkTemplateId)}</FormItem>
      </Form>
    )
  }
  renderType = (status, optional) => {
    const {
      form: { getFieldDecorator },
    } = this.props
    let { dataLinkTemplateId, templates } = this.state
    let required = true
    if (optional && optional === true) {
      required = false
    }
    if (templates.length < 2) {
      return ''
    }
    if (status && status === 'only') {
      return this.renderOnlyType()
    }
    
    return (
      <Form>
        <FormItem label={i18n.get('类型')}>
          {getFieldDecorator('type', {
            initialValue: dataLinkTemplateId === '' ? undefined : dataLinkTemplateId,
            rules: [{ required: required, message: i18n.get('请选择类型') }]
          })(
            <DataLinkSelect templates={templates} onChange={this.handleSaveType} placeholder={i18n.get('请选择类型')} />
          )}
        </FormItem>
      </Form>
    )
  }
  render() {
    const { status, optional } = this.props
    let { template, value, dataLinkId } = this.state
    return (
      <div className={styles['data-link-edit-single']}>
        <div className="modal-content">
          {this.renderType(status, optional)}
          <DataLinkEditDay
            bus={this.props.bus}
            template={template}
            value={value}
            status={status}
            elements={this.props.elements}
            dataLinkId={dataLinkId}
          />
        </div>
      </div>
    )
  }
}

export default Form.create()(DataLinkEditSingle)
