import React from 'react'
import {  Select, Radio } from 'antd'
import styles from './DataLinkSelect.module.less'

interface Props {
  [key: string]: any
}

interface State {
  [key: string]: any
}

export default class DataLinkSelect extends React.Component<Props, State> {
  constructor(props) {
    super(props)
  }
  onChange = (e: any) => {
    let { onChange } = this.props
    onChange && onChange(typeof e === 'string' ? e : e.target.value)
  }
  renderSelect = () => {
    let { templates = [], placeholder, value = undefined } = this.props
    const Options = templates.map(t => (
      <Select.Option label={t.entity.name} disabled={!t.entity.active} value={t.templateId} key={t.templateId}>
        {t.entity.name}
      </Select.Option>
    ))
    return (
      <Select defaultValue={value} placeholder={placeholder} onChange={this.onChange}>
        {Options}
      </Select>
    )
  }
  renderRadio = () => {
    let { templates = [], placeholder, value = undefined } = this.props
    const Options = templates.map(t => (
      <Radio.Button disabled={!t.entity.active} value={t.templateId} key={t.templateId}>
        {t.entity.name}
      </Radio.Button>
    ))
    return (
      <div className={styles['groupdview']}>
        <Radio.Group defaultValue={value} placeholder={placeholder} onChange={this.onChange} buttonStyle="solid">
          {Options}
        </Radio.Group>
      </div>
    )
  }
  render() {
    let { templates = [] } = this.props
    return templates.length > 4 ? this.renderSelect() : this.renderRadio()
  }
}
