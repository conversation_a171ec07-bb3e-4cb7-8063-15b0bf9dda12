import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import { getRecordLink } from './helpers/getRecordLink'
import MutilDimensionListSearchComp from './MutilDimensionListSearchComp'
import MutilDimensionListSearchCompV2 from './CustomDimension/MutilDimensionListSearchCompV2Refactored'
import { enableRecordOptimization } from '../../lib/featbit'

// @ts-ignore
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select_search')
    }
  },
  initialValue(props) {
    const { field = {}, value, lastChoice } = props
    if (!value) {
      const constValue = constantValue(field)
      if (constValue) {
        return [constValue]
      }
      const lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) {
        return lastVal.split(',')
      }
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(
  state => ({
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class MutilDimensionList extends React.Component<any, any> {
  isFlow = () => {
    const { businessType } = this.props
    return ['FLOW', 'DETAILS'].includes(businessType)
  }

  render(): React.ReactNode {
    if (this.isFlow() && enableRecordOptimization()) {
      return <MutilDimensionListSearchCompV2 {...this.props as any} />
    }
    return <MutilDimensionListSearchComp {...this.props} />  // 组件拆出来了
  }
}
