/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-03-19 11:38:33
 * @Last Modified by: xingdev
 * @Last Modified time: 2020-06-08 17:49:17
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select } from 'antd'
const { Option } = Select
import { wrapper } from '../layout/FormWrapper'
import styles from './payment.module.less'
import { app as api } from '@ekuaibao/whispered'
const getEnums = api.invokeServiceAsLazyValue('@supplier-file:get:supplier:enums')
// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'payment'
  },
  wrapper: wrapper(true)
})
export default class PaymentReadOnly extends PureComponent<any> {
  render() {
    const { value = {} } = this.props
    const { periodMap } = getEnums()
    return <div className={styles.wrapper}>{i18n.get(`按{__k0}结算`, { __k0: periodMap[value.period] })}</div>
  }
}
