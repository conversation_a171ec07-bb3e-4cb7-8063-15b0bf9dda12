import React, { FC } from 'react'
import styles from './TravelClosedLoop.module.less'
import { Tooltip } from 'antd'
type TravelClosedLoopItem = {
  message: string
  travelCloseLoop: boolean
}
interface ITravelClosedLoopProps {
  value: TravelClosedLoopItem[]
}
const checkIsBool = (val: boolean) => val === false || val === true
const checkNotLoop = list => {
  const item = list.find(i => checkIsBool(i.travelCloseLoop)) ?? {}
  if (item?.travelCloseLoop === undefined) return 'none'
  if (!!!item?.travelCloseLoop) return item
  if (!!item?.travelCloseLoop) return false
}
const TravelClosedLoop: FC<ITravelClosedLoopProps> = props => {
  const { value = [] } = props
  if (!!!value || !!!value?.length) return <></>
  const render = () => {
    const notLoop = checkNotLoop(value)
    return notLoop === 'none' ? (
      <></>
    ) : notLoop ? (
      <Tooltip title={i18n.get(notLoop.message ?? '')}>
        <span className={styles['travel-closed-loop']}>{i18n.get('行程未闭环')}</span>
      </Tooltip>
    ) : (
      <span className={styles['travel-closed-loop']}>{i18n.get('行程闭环')}</span>
    )
  }
  return render()
}

export default TravelClosedLoop
