/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020-06-23 10:31.
 */
import { forIn, get, union } from 'lodash'
export const getTravelOrderData = dataLink => {
  let data = {}
  forIn(dataLink, (value, key) => {
    const lastIndex = key.lastIndexOf('_')
    data[lastIndex > -1 ? key.substring(lastIndex + 1) : key] = value
  })
  return data
}
export const isTravelOrder = field => {
  const platformType = get(field, 'referenceData.platformId.type', '')
  const type = get(field, 'referenceData.type', '')
  const isTravelOrder = platformType === 'TRAVEL_MANAGEMENT' && type === 'ORDER'
  return isTravelOrder
}
/**
 * @description [行程规划]全局组件根据[自定义扩展-行程配置]过滤行程选项
 * @param {*} template: 所有行程[TRAIN、FLIGHT、TAXI、HOTEL、FOOD]模版
 * @param {*} field: 行程规划字段配置
 * @returns 需要展示的行程列表
 */
export const filterTripTemplate = (template, field) => {
  const { type, value } = field.defaultValue
  const limitEntityIds = field.limitEntityIds
  //先判断模板上是否有判断。如果模板上没有判断的话用行程管理里面的配置
  let templateList = template.filter(item => item.entity.parentId)
  if (limitEntityIds && limitEntityIds.length > 0) {
    templateList = templateList.filter(i => limitEntityIds.includes(i.entity.id))
    return templateList
  } else if (type !== 'none' && value.length) {
    templateList = templateList.filter(i => value.includes(i.entity.type))
    return templateList
  }
  return templateList
}