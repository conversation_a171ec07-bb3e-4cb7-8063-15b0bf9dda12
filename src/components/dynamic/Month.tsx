import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { DatePicker } from 'antd'
import moment from 'moment'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import 'moment/locale/zh-cn'
@EnhanceField({
  descriptor: {
    type: 'date-month'
  },
  validator: (field, props) => (rule, value, callback) => {
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class DateMonth extends PureComponent {
  constructor(props) {
    super(props)
    moment.locale('zh-cn')
  }

  handleChange = date => {
    const { onChange, field } = this.props
    const result = date.format('YYYY-MM')
    onChange?.(result)
  }
  render() {
    const { value, field } = this.props
    const v = value && moment(value)
    return (
      <DatePicker.MonthPicker
        style={{ width: '100%' }}
        value={v}
        placeholder={field.placeholder}
        onChange={this.handleChange}
        allowClear={false}
        size="large"
      />
    )
  }
}
