import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { includes, get } from 'lodash'
import { ENUM_TYPES } from '../consts'
import RefEnumView from '../../elements/puppet/RefEnum'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return includes(ENUM_TYPES, type)
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  initialValue(props) {
    let { field = {}, value, lastChoice, isModify, isDetail, detailId} = props
    const notUseDefaultValue = isModify && isDetail && !detailId ? false : isModify
    if (!value && !notUseDefaultValue) {
      let constVal = constantValue(field)
      if (constVal) return constVal
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return lastVal
    }
    return undefined
  },
  wrapper: wrapper()
})
export default class RefEnum extends PureComponent {
  render() {
    let { field, value } = this.props
    let entity = get(field, 'dataType.entity', '')
    let disabled = isDisable(this.props)
    let { optional } = field
    let placeholder = getPlaceholder(field)
    if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    return (
      <RefEnumView
        {...this.props}
        disabled={disabled}
        value={value}
        entity={entity}
        placeholder={placeholder}
        optional={optional}
      />
    )
  }
}
