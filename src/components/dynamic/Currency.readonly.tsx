import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './Currency.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Select} from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
interface CPProps {
  [key: string]: any
}
interface CPState {
  [key: string]: any
}

const { Option } = Select

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'ref:basedata.Enum.currency'
  },
  wrapper: wrapper(true)
})
@EnhanceConnect((state: any) => ({
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
export default class Curency extends PureComponent<CPProps, CPState> {
  constructor(props: CPProps) {
    super(props)
    let { field, value } = this.props
    this.state = {
      allData: []
    }
  }

  async componentDidMount() {
    await api.dataLoader('@common.getCurrencyAll').load()
    const getCurrencyAll = api.getState()['@common'].getCurrencyAll?.slice()
    this.setState({
      allData: getCurrencyAll || []
    })
  }

  render() {
    let { allData } = this.state
    let { value } = this.props
    let valueName = allData?.find(i => value == i?.numCode || value?.id === i?.numCode || value?.numCode === i?.numCode)
    return (
      <div className={styles['currency-wrapper']}>
        {valueName ? `${valueName?.name} (${valueName?.strCode})` : <span className="currency_span">-</span>}
      </div>
    )
  }
}
