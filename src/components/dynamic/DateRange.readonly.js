/**************************************************
 * Created by nanyuanting<PERSON> on 12/07/2017 16:53.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { timeConvert } from '../utils/fnPredefine4Date'

@EnhanceField({
  descriptor: {
    type: 'dateRange'
  },
  wrapper: wrapper(true)
})
export default class DateRangeReadonly extends PureComponent {
  fnFormatDate(value = {}, withTime, dateTimeType) {
    let { start, end } = value
    return timeConvert(withTime, dateTimeType, start, true) + ' - ' + timeConvert(withTime, dateTimeType, end, true)
  }

  render() {
    let { value, field } = this.props
    let { withTime, dateTimeType } = field
    return <span>{this.fnFormatDate(value, withTime, dateTimeType)}</span>
  }
}
