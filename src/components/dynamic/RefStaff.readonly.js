/**************************************************
 * Created by kaili on 2017/8/7 上午10:55.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import PopoverWrapper from '../../elements/namePopover/PopoverWrapper'
import { get } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import UserBadge from '../../elements/profile-card/user-badge'

@EnhanceField({
  descriptor: {
    type: 'ref:organization.Staff'
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => ({
  staffDisplayConfig: state['@common'].organizationConfig.staffDisplayConfig
}))
export default class RefStaff extends PureComponent {
  render() {
    const { value, ownerId, field, staffDisplayConfig = [] } = this.props

    return (
      <div className="horizontal">
        {value ? (
          <PopoverWrapper
            info={value}
            name={<UserBadge user={value} extraField={staffDisplayConfig[1]} baseFiled={staffDisplayConfig[0]}/>}
            external={value?.external}
            trigger="hover"
            placement="bottomLeft"
          />
        ) : (
          i18n.get('无')
        )}
        {value && ownerId && value.id !== ownerId.id && get(field, 'name') === 'submitterId' && (
          <>
            <PopoverWrapper
              info={ownerId}
              name={<UserBadge user={ownerId} extraField={staffDisplayConfig[1]} baseFiled={staffDisplayConfig[0]} extra={i18n.get('（代提交）')} />}
              trigger="hover"
              placement="bottomLeft"
            />
          </>
        )}
      </div>
    )
  }
}
