/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 11/07/2017 17:16.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import Annotation from '../../elements/puppet/Annotation'
import { app as api } from '@ekuaibao/whispered'
import { Col, Row } from 'antd'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'annotation'
  }
})
export default class AnnotationE extends PureComponent {
  handleOpenUrl = () => {
    let { field = {} } = this.props
    api.emit('@vendor:open:link', field.url)
  }

  render() {
    let { field = {}, layout } = this.props
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <Row style={field.style}>
          <Col span={layout?.wrapperCol && layout?.wrapperCol.span}>
            <Annotation label={fnGetFieldLabel(field)} url={field.url} onClick={this.handleOpenUrl} />
          </Col>
        </Row>
      </UniversalComponent>
    )
  }
}
