import React from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import { cloneDeep } from 'lodash'
import styles from './MutilDimensionList.readonly.module.less'
import { toJS } from 'mobx'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'

// @ts-ignore
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return type.startsWith('list:ref:basedata.Dimension')
    }
  },
  initialValue(props) {
    const { field = {}, value, lastChoice } = props
    if (!value) {
      const constValue = constantValue(field)
      if (constValue) {
        return [constValue]
      }
      const lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) {
        return lastVal.split(',')
      }
    }
  },
  wrapper: wrapper(true)
})
export default class MutilDimensionList extends React.Component<any, any> {
  state: any = { dimension: [] }
  componentWillMount() {
    this.initDimension(this.props)
  }
  initDimension = (props: any) => {
    const { field, value } = props
    // const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
    if (value && value?.length) {
      let val = null
      try {
        val = cloneDeep(value).map(v => {
          if (typeof v !== 'string') {
            return v.id
          }
          return v
        })
      } catch (e) {
        val = cloneDeep(toJS(value)).map(v => {
          if (typeof v !== 'string') {
            return v.id
          }
          return v
        })
      } finally {
        if (!Array.isArray(val)) {
          val = []
        }
      }
      if (val.length > 300) {
        const promiseArr = []
        for (let index = 0; index < Math.ceil(val.length / 300); index++) {
          const list = []
          for (let j = index * 300; j < 300 * (index + 1); j++) {
            const element = val[j]
            list.push(element)
          }
          promiseArr.push(api.invokeService('@bills:get:dimension', list.join(',')))
        }
        Promise.all(promiseArr).then((res: any) => {
          let result = []
          res.forEach(element => {
            result = result.concat(element.items)
          })
          this.setState({ dimension: result })
        })
      } else {
        api.invokeService('@bills:get:dimension', val.join(',')).then((res: any) => {
          this.setState({ dimension: res.items })
        })
      }
    }
  }
  render() {
    const { field = {} } = this.props
    const { dimension = [] } = this.state
    const str = dimension.map((item: any, idx: number) => {
      const itemName = getContentByLocale(item, 'name')
      return (
        <div>
          {!field?.hideCode ? (
            <span>
              {itemName}
              <span className="ref-code">
                {' '}
                {i18n.get('（')}
                {item.code}
                {i18n.get('）')}
              </span>
            </span>
          ) : (
            ` ${itemName}`
          )}
          {idx !== dimension.length - 1 && ','}
        </div>
      )
    })
    return <div className={styles['mutil-dimension']}>{dimension.length ? str : i18n.get('无')}</div>
  }
}
