/**
 *  Created by pw on 2019-03-11 18:57.
 */
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import React, { PureComponent } from 'react'
import FeeTypeSelect from '../../elements/feeType-tree-select'
import { wrapper } from '../layout/FormWrapper'
import { FieldInterface } from '../layout/types'
import { required } from '../validator/validator'

interface RefFeetypeInputTagsInterface {
  field: any
  value: any
  feeTypes: []
  onChange: Function
  hidden?: boolean
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'ref:feetype:input:tags'
  },
  validator: (field: FieldInterface) => (rule: any, value: any, callback: any) => {
    if (field.validator) {
      field.validator(rule, value, callback)
      return
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data
}))
export default class RefFeetypeInputTags extends PureComponent<RefFeetypeInputTagsInterface> {
  handleFeeTypeChange = (checkedKeys: []) => {
    const { onChange } = this.props
    onChange && onChange(checkedKeys)
  }

  render() {
    const { feeTypes, value, hidden } = this.props
    if (hidden) {
      return null
    }
    const { field } = this.props
    return (
      <FeeTypeSelect
        className="w-100b"
        size="large"
        value={value}
        multiple={true}
        treeCheckable={true}
        placeholder={field.placeholder}
        feeTypes={feeTypes}
        onChange={this.handleFeeTypeChange}
      />
    )
  }
}
