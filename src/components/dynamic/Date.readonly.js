/**************************************************
 * Created by nanyuanting<PERSON> on 12/07/2017 16:53.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { timeConvert } from '../utils/fnPredefine4Date'

@EnhanceField({
  descriptor: {
    type: 'date'
  },
  wrapper: wrapper(true)
})
export default class DateReadonly extends PureComponent {
  render() {
    const { value, field } = this.props
    const { withTime, dateTimeType } = field
    let valueStr = timeConvert(withTime, dateTimeType, value, true)

    if (!value && field.source === 'dataLink') {
      valueStr = i18n.get('无')
    }
    return <span className="date-readonly">{valueStr}</span>
  }
}
