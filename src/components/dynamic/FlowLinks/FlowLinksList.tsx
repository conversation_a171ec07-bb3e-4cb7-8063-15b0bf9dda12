import React, { useEffect } from 'react'
import moment from 'moment'
import { Tooltip } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import ActiveWrapper from '../../../elements/ekbc-basic/active-wrapper/active-wrapper'
import style from './FlowLinks.module.less'
import { showMessage } from '@ekuaibao/show-util'
interface IFlowLinks {
  [key: string]: any
}

export const errorFn = err => showMessage.error(err.msg ?? err.message ?? err.errorMessage)
const FlowLinksList: React.FC<IFlowLinks> = props => {
  const { value, onChange, showDel } = props
  const handleRemove = val => {
    onChange?.(value?.filter(it => it?.id !== val?.id))
  }
  const handleClick = val => {
    app.invokeService('@bills:get:flow-info', { id: val?.id, checkPermissions: false }).then(({ value }) => {
      app.open('@bills:BillInfoModal', { dataSource: value })
    })
  }

  return (
    <div>
      {value?.map((it: any = {}) => {
        const { form = {} } = it || {}
        return (
          <ActiveWrapper
            onRemove={handleRemove}
            onClick={handleClick}
            style={{ cursor: 'pointer' }}
            isEditable={showDel}
            data={it}
          >
            <div className={style['check-item']}>
              <div className={style['left']}>
                <div className={style['title']}>
                  <Tooltip title={form?.title}>{form?.title}</Tooltip>
                </div>
                <div className={style['label']}>
                  {form?.code}
                  &nbsp;&nbsp;&nbsp;
                  {moment(it?.createTime).format('YYYY/MM/DD')}
                </div>
              </div>
              <div className={style['info']}>{i18n.get('查看详情')}</div>
            </div>
          </ActiveWrapper>
        )
      })}
    </div>
  )
}
export default FlowLinksList
