import { showMessage } from '@ekuaibao/show-util'

export const getSpecId = (specId: string, isFeeTypeSetting: boolean, feeTypes: any[], templateType: string) => {
    if (!isFeeTypeSetting) return specId
    const feetype = feeTypes?.find(it => it.id === specId)
    switch (templateType) {
        case 'requisition':
            specId = feetype?.requisitionSpecificationId ?? specId
            break
        case 'expense':
            specId = feetype?.expenseSpecificationId ?? specId
            break
        case 'receipt':
            specId = feetype?.receiptSpecificationId ?? specId
            break
    }
    return specId
}

export const errorFn = err => showMessage.error(err.msg ?? err.message ?? err.errorMessage)