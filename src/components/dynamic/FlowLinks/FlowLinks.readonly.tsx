import React, { useState, useCallback, useEffect } from 'react'
import FlowLinksList from './FlowLinksList'
import { app } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { wrapper } from '../../layout/FormWrapper'
interface IFlowLinks {
  [key: string]: any
}

const FlowLinksReadonly: React.FC<IFlowLinks> = props => {
  const { value = [], hiddenFlowLinks = false } = props
  return (
    <div>
      {hiddenFlowLinks || value?.length === 0 ? (
        <div>{i18n.get('无')}</div>
      ) : (
        <FlowLinksList showDel={false} value={value}></FlowLinksList>
      )}
    </div>
  )
}
export default EnhanceField(({
  descriptor: {
    type: 'flowLinks'
  },
  wrapper: wrapper(true)
} as unknown) as IComponentDef)(FlowLinksReadonly)
