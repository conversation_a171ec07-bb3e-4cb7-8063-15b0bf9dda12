@import '~@ekuaibao/eui-styles/less/token.less';

.check-item {
    width: 100%;
    border: 1px solid #e6e6e6;
    padding: 12px;
    border-radius: 4px;
    line-height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    &:hover {
        box-shadow: 0 1px 6px 0 hsla(0, 0%, 80%, 0.65);
    }
    .left{
        display: flex;
        flex-direction: column;
        width: 85%;
    }

    .title {
        width: 100%;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .label {
        font-size: 12px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.75);
    }

    .info {
        cursor: pointer;
        color: var(--brand-base);
        width: 60px;
    }
}