import React, { useState, useCallback, useEffect, PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import { app } from '@ekuaibao/whispered'
import { cloneDeep } from 'lodash'
import { Button } from '@hose/eui'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import FlowLinksList from './FlowLinksList'
import { required } from '../../validator/validator'
import style from './FlowLinks.module.less'
import { showMessage } from '@ekuaibao/show-util'
import { fnParseFormValue } from '../../../plugins/bills/util/parse'
import { getSpecId } from './utils'
interface IFlowLinks {
  [key: string]: any
}
export const errorFn = err => showMessage.error(err.msg ?? err.message ?? err.errorMessage)
const formatIds = list => list.map(it => it?.id) ?? []
const formatList = (list, ids) => list.filter(it => ids?.includes(it?.id)) ?? []
const isAuto = (editable, billSpecification, isSettlement = false, isRecordExpends = false) =>
  !!!editable && !billSpecification?.id?.includes('system:对账单') && !isSettlement && !isRecordExpends

@EnhanceField({
  descriptor: {
    type: 'flowLinks'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper(),
  initialValue(props) {
    return []
  }
})
export default class FlowLinks extends PureComponent<IFlowLinks, IFlowLinks> {
  constructor(props: IFlowLinks) {
    super(props)
  }

  componentDidMount() {
    const { bus, field = {}, billSpecification, isSettlement, isRecordExpends = false } = this.props
    const { editable } = field
    isAuto(editable, billSpecification, isSettlement, isRecordExpends) &&
      bus?.on('dynamic:value:changed', this.getFlowLinkList)
    isAuto(editable, billSpecification, isSettlement, isRecordExpends) && this.getFlowLinkList({})
  }
  componentWillUnmount() {
    const { bus, field = {}, billSpecification, isSettlement, isRecordExpends = false } = this.props
    const { editable } = field
    isAuto(editable, billSpecification, isSettlement, isRecordExpends) &&
      bus?.un('dynamic:value:changed', this.getFlowLinkList)
  }
  changeSpecId = params => {
    const { isDetail, billSpecification = {}, feeType = {} } = this.props
    if (isDetail) {
      params.calculateFlag = 'DETAIL'
      params.formData.specificationId = getSpecId(feeType?.id ?? '', isDetail, [feeType], billSpecification?.type)
    }
    return params
  }
  handlerOpenFlow = () => {
    const formData = this.getFormData()
    const { value = [], isDetail, onChange, baseDataProperties = [], billSpecification = {} } = this.props
    let params: any = {
      formData: fnParseFormValue(formData, billSpecification, baseDataProperties),
      value: formatIds(value),
      isDetail
    }
    params = this.changeSpecId(params)
    app.open('@bills:FlowLinksSelectModal', params).then((res: any) => {
      const { flows = [], dataSource = [] } = res
      onChange && onChange(formatList(dataSource, flows))
    })
  }

  getFlowLinkList = formVal => {
    if (Reflect.ownKeys(formVal)?.find(it => it === 'flowLinks')) return
    const { onChange, baseDataProperties = [], billSpecification = {} } = this.props
    const formData = this.getFormData()
    let params: any = { formData: fnParseFormValue(formData, billSpecification, baseDataProperties) }
    params = this.changeSpecId(params)
    app
      .invokeService('@bills:get:flow:link:list', params)
      .then(result => {
        const { items = [] } = result
        onChange && onChange(items ?? [])
      })
      .catch(errorFn)
  }
  getFormData = () => {
    const { form } = this.props
    return form?.getFieldsValue()
  }
  handlerChange = flowLinks => {
    const { onChange } = this.props
    onChange && onChange(flowLinks)
  }
  render() {
    const { value = [], field = {}, isRecordExpends = false } = this.props
    const { editable } = field
    return (
      <div className={style['flow-links']}>
        {!!editable && !isRecordExpends && <Button category='secondary' size='small' onClick={this.handlerOpenFlow}>{i18n.get('选择')}</Button>}
        {(!editable && value?.length === 0) || isRecordExpends ? (
          <div>{i18n.get('无')}</div>
        ) : (
          <FlowLinksList onChange={this.handlerChange} showDel={editable} value={value}></FlowLinksList>
        )}
      </div>
    )
  }
}
