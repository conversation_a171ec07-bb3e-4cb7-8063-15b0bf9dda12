/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-03-11 15:28:30
 * @Last Modified by: <PERSON><PERSON>k<PERSON>
 * @Last Modified time: 2019-03-18 23:18:43
 */

import { EnhanceField } from '@ekuaibao/template'
import React, { Component } from 'react'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'

import { CPProps } from './CityPickerComponent'
import CityComponentNew from './city-picker-new'
import { isDisable } from '../utils/fnDisableComponent'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'city'
  },
  validator: (field: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    try {
      value = value && JSON.parse(value)
      callback(required(field, value))
    } catch (e) {
      callback(e)
    }
  },
  wrapper: wrapper()
})
export default class City extends Component<CPProps> {
  render() {
    const CitySelector = CityComponentNew;
    const { field } = this.props
    const { multiple = false } = field
    const isAuto = isDisable(this.props)
    return <CitySelector {...this.props} multiple={multiple} isAuto={isAuto} />
  }
}
