import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import DataLinkCard from '../../elements/dataLink-card/DataLinkCard'
import { app as api } from '@ekuaibao/whispered'
import styles from './DataLink.module.less'
import { isTravelOrder } from './utils'
import { get } from 'lodash'
interface CPProps {
  [key: string]: any
}
interface CPState {
  datalinkValue: any[]
  [key: string]: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'dataLinks'
  },
  wrapper: wrapper(true)
})
export default class DataLink extends PureComponent<CPProps, CPState> {
  constructor(props: CPProps) {
    super(props)
    this.state = { datalinkValue: [] }
  }
  isEntity(value: any) {
    return value[0].data && value[0].path && value[0].template && value[0].data.dataLink
  }

  componentDidMount() {
    const { value, field } = this.props
    if (value && Array.isArray(value) && value.length > 0) {
      if (this.isEntity(value)) {
        this.setState({ datalinkValue: value })
      } else {
        this.getDataLinkTempById(value.map(i => i.id))
      }
    }
  }

  getDataLinkTempById = (ids: any[]) => {
    const newIds = ids.filter(item => item) //有时候会有null的情况
    if (!newIds.length) return
    api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds: newIds }).then((values: any) => {
      const data = values.items || []
      data.forEach((i: any) => {
        i.id = i.data.dataLink.id
      })
      this.setState({ datalinkValue: data })
    })
  }

  componentWillReceiveProps(nextProps: any) {
    const preValue = this.props.value
    const nextValue = nextProps.value
    if (preValue !== nextValue && nextValue && Array.isArray(nextValue) && nextValue.length > 0) {
      if (this.isEntity(nextValue)) {
        this.setState({ datalinkValue: nextValue })
      } else {
        this.getDataLinkTempById(nextValue.map(i => i.id || i)) // 有的时候只拿到了id，有的时候又拿到了一个对象。。。
      }
    }
  }

  hanldeOnClick = (value: any) => {
    const { field, disabledStaff = false } = this.props
    const isOrder = isTravelOrder(field)
    const dataLink = get(value, 'data.dataLink')
    const tripType = get(dataLink, 'entity.type', '')
    if (value) {
      if (isOrder) {
        api.open('@bills:TripOrderPopup', {
          title: i18n.get('订单详情'),
          entityInfo: { ...value.data },
          tripType: tripType
        })
      } else {
        api.open('@bills:DataLinkDetailModal', {
          entityInfo: {
            dataLink: { id: this.isEntity([value]) ? value.data.dataLink.id : value.id },
            entityId: field.referenceData
          },
          field,
          disabledStaff,
          showClose: true
        })
      }
    }
  }

  render() {
    const { datalinkValue = [] } = this.state
    const { field } = this.props
    if (datalinkValue.length === 0) {
      return <div>{i18n.get('无')}</div>
    }
    return (
      <div className={styles['dataLink-input-wrapper']} style={{ width: '100%' }}>
        <div className="card-list">
          {datalinkValue.map((item: any, index: any) => (
            <DataLinkCard
              field={field}
              key={index}
              entityInfo={item}
              onClick={() => this.hanldeOnClick(item)}
              isReaonly={true}
            />
          ))}
        </div>
      </div>
    )
  }
}
