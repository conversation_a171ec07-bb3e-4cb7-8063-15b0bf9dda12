/**
 *  Created by pw on 2021/7/29 下午4:32.
 *  desc: 目前这个组件没有任何业务含义，纯属是为了配合历史遗留问题而存在(字段设置隐藏是通过wrapper实现的)
 */
import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'

@((EnhanceField as any)({
  descriptor: {
    type: 'subsidy'
  },
  wrapper: wrapper()
}))
export default class Subsidy extends Component {
  render() {
    return <div />
  }
}
