@import '~@ekuaibao/web-theme-variables/styles/colors';

.wrapper {
  padding: 4px 7px;
  width: 100%;
  min-height: 32px;
  line-height: 22px;
  border: 1px solid @gray-5;
  border-radius: 2px;
  cursor: pointer;
  background: #ffffff;
  white-space: normal;
  word-break: break-all;
  &:hover {
    border-color: var(--brand-base);
  }
}

.wrapper:empty::before {
  color: lightgrey;
  content: attr(placeholder);
}

.wrapper_disabled {
  padding: 4px 7px;
  width: 100%;
  min-height: 32px;
  line-height: 22px;
  color: @gray-8;
  background-color: @gray-3;
  border: 1px solid @gray-5;
  border-radius: 2px;
  cursor: not-allowed;
}

.wrapper_disabled:empty::before {
  color: lightgrey;
  content: attr(placeholder);
}

//.ellipsis {
//  overflow: hidden;
//  text-overflow: ellipsis;
//  word-break: break-all;
//  white-space: nowrap;
//}

.border {
  border: 1px solid @gray-5 !important;
}

.place-top:before {
  border-top: 8px solid @gray-5 !important;
}
