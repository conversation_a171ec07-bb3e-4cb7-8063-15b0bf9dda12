/**
 * Created by <PERSON><PERSON> on 2017/9/14.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { UIContainer as Container } from '@ekuaibao/whispered'
import { getAllApportionSpecification } from '../../plugins/bills/util/billFetchUtil'
import styles from './Amortization.module.less'
import { wrapper } from '../layout/FormWrapper'
@EnhanceField({
  descriptor: {
    type: 'amortizes'
  },
  wrapper: wrapper(true, 'vertical')
})
export default class AmortizationReadonly extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { feeAmount: props.feeAmount }
  }

  componentDidMount() {
    this.getApportionfLebal()
  }
  getApportionfLebal = async () => {
    const { value, bus, field } = this.props
    if (!(value && value.length)) return {}
    const specificationIds = field?.specificationIds
    const apportionResult = await getAllApportionSpecification('amortize')
    const list = apportionResult?.items || []
    const specification = list.find(item => item.originalId === specificationIds[0])
    const { baseDataProperties } = this.props
    const { configs = [] } = specification
    const config = configs.find(v => v.ability === 'amortize')
    const { label, name } = baseDataProperties.find(f => f.name === config?.amortizeMoneyField) || {}
    bus &&
      bus.getFieldsValue().then(value => {
        this.setState({ feeAmount: value[name], apportionfLebal: label, specification: specification })
      })
  }
  render() {
    const {
      bus,
      value,
      external,
      isForbid,
      field: apportionField = {},
      apportionVisibleList,
      showAllFeeType,
      submitterId,
      detailId,
      billSpecification
    } = this.props
    if (!value) {
      return null
    }
    const { feeAmount, specification, apportionfLebal } = this.state
    return (
      <div className={styles['amortization-wrapper']}>
        <Container
          name="@apportion:Amortization"
          value={value}
          external={external}
          template={specification}
          isForbid={isForbid}
          apportionField={apportionField}
          isEdit={false}
          editable={false}
          apportionfLebal={apportionfLebal}
          feeAmount={feeAmount}
          apportionVisibleList={apportionVisibleList}
          showAllFeeType={showAllFeeType}
          submitterId={submitterId?.id}
          feeDetailId={detailId}
          billSpecificationId={billSpecification?.id}
          billBus={bus}
        />
      </div>
    )
  }
}
