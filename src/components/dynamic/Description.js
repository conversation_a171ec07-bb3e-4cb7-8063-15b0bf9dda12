/**
 * Created by <PERSON><PERSON> on 2017/8/16.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'

@EnhanceField({
  descriptor: {
    type: 'description'
  },
  wrapper: wrapper(true, {
    labelCol: { span: 0 },
    wrapperCol: { span: 24 }
  })
})
export default class Description extends PureComponent {
  render() {
    let { value } = this.props.field

    return <span style={{ color: 'rgba(29, 43, 61, 0.5)' }}>{value}</span>
  }
}
