.search-mode {
  :global {
    .eui-select-tree-switcher {
      display: none;
    }
  }
}

.custom-dimension-tree-select-popup {
  :global {
    .eui-tree-select-loadmore-button {
      &:hover {
        background: transparent !important;
      }
    }

    .eui-select-tree-node-content-wrapper {
      min-width: 0;
    }
  }
}

.custom-dimension-select-search-popup {
  :global {
    .eui-select-loadmore-option-wrapper {
      &:hover {
        background: transparent !important;
      }
    }

    .eui-select-item-option-content {
      &:hover {
        background: transparent !important;
      }
    }
  }
}
