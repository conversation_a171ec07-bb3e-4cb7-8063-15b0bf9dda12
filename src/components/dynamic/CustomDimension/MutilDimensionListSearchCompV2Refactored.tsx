import React, { useEffect, useCallback } from 'react'
import { LoadableSelect } from '@hose/eui'
import { OutlinedEditSearch } from '@hose/eui-icons'
import { isDisable } from '../../utils/fnDisableComponent'
import { getPlaceholder } from '../../utils/fnGetFieldLabel'
import styles from './TreeSelect.module.less'
import { SearchItem } from './components'

// 引入分层抽象的Hook
import {
  useSelectDependence,
  useSearchableSelect,
  useDisplayValue
} from './hooks'

interface MutilDimensionListSearchCompV2RefactoredProps {
  field: any
  value: any[]
  onChange: (value: any) => void
  bus: any
  billSpecification: any
  specificationId: any
  submitterId: any
  // ... 其他必要的props
}

function MutilDimensionListSearchCompV2Refactored(props: MutilDimensionListSearchCompV2RefactoredProps) {
  const { field, value, onChange, bus } = props

  // 1. 依赖关系Hook
  const dependenceHook = useSelectDependence({
    field,
    props
  })

  // 2. 基础搜索Hook
  const searchHook = useSearchableSelect({
    field,
    fetchFunction: dependenceHook.createFetchFunction(),
  })

  const { displayValue, getDisplayValue } = useDisplayValue(value, field, props, dependenceHook, searchHook.data)

  // 4. 处理取消依赖的回调 - 在所有Hook定义后
  const handleCancelDependency = useCallback(() => {
    // 重新搜索数据，设置cancelRelation为true
    searchHook.searchWithParams({ 
      searchValue: searchHook.searchText, 
      cancelRelation: true 
    })
  }, [searchHook])

  // 5. 设置取消依赖回调到Hook中
  useEffect(() => {
    dependenceHook.setCancelDependencyCallback(handleCancelDependency)
  }, [dependenceHook, handleCancelDependency])

  // 处理多选的搜索逻辑
  const handleSearch = (searchValue: string) => {
    if (searchValue.trim()) {
      searchHook.onSearch(searchValue)
    } else {
      // 清空搜索时显示缓存的默认值项
      searchHook.setData([])
      searchHook.setHasNextPage(false)
    }
  }

  // 处理多选变化
  const handleChange = (checkedIds: string[]) => {
    onChange && onChange(checkedIds)
  }

  // 重写加载更多，支持多选缓存合并
  const handleLoadMore = async () => {
    const lastItem = searchHook.data[searchHook.data.length - 1]
    const result = await searchHook.searchWithParams({
      searchValue: searchHook.searchText,
      lastItemId: lastItem.id,
      lastItemCode: lastItem.code,
      isLoadMore: true
    })

    if (result?.items) {
      // 对于多选，需要合并缓存项
      searchHook.setData(searchHook.data.concat(result.items))
      searchHook.setHasNextPage(result.hasNextPage)
    }

    return result?.items || []
  }

  // 处理依赖变化中的多选逻辑
  const handleDependenceChange = async (params: any, options: any = {}) => {
    const result = await dependenceHook.processDependenceChange(params, options)
    
    // 如果不匹配依赖键，直接返回
    if (!result) {
      return
    }

    const { autoAssignmentItem, matchDefaultValue, newValue } = result

    if (!matchDefaultValue) {
      if (newValue !== undefined) {
        // 使用processDependenceChange返回的newValue
        onChange && onChange(newValue)
      } else if (autoAssignmentItem) {
        // 多选模式：自动分配项转为数组
        const autoValue = [autoAssignmentItem.id]
        onChange && onChange(autoValue)
      } else if (value?.length) {
        // 有现有值：获取完整记录并缓存
        onChange && onChange(value)
      } else {
        // 清空所有
        onChange && onChange(undefined)
      }
    }
  }

  // 组件挂载时的初始化和事件监听
  useEffect(() => {
    // 注册依赖变化事件监听
    bus.on('on:dependence:change', handleDependenceChange)
    
    // 初始化触发依赖变化
    setTimeout(() => {
      const id = value && value.length > 0 ? value[0]?.id || value[0] : undefined
      bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true })
    }, 200)

    // 清理事件监听
    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
    }
  }, [])  // 注意依赖数组，确保handleDependenceChange不会重复注册

  useEffect(() => {
    const ids = value?.map(it => it?.id || it)
    getDisplayValue(ids)
  }, [])

  const placeholder = getPlaceholder(field)
  const disabled = isDisable(props)

  return (
    <div>
      <LoadableSelect
        data-testid="custom-dimension-multi-select-search"
        popupClassName={`${styles['custom-dimension-select-search-popup']}`}
        mode="multiple"
        optionLabelProp="name"
        style={{ width: '100%' }}
        placeholder={placeholder}
        value={displayValue}
        showSearch={true}
        allowClear
        disabled={disabled}
        onChange={handleChange}
        onSearch={handleSearch}
        filterOption={false}
        notFoundContent={dependenceHook.emptyText}
        showArrow
        suffixIcon={<OutlinedEditSearch />}
        options={searchHook.toRenderData(searchHook.data, (item: any) => <SearchItem {...item} searchText={searchHook.searchText} />)}
        hasMore={() => searchHook.hasNextPage}
        onLoadMore={handleLoadMore}
        loadMoreRender={searchHook.loadMoreRender}
      />
    </div>
  )
}

export default MutilDimensionListSearchCompV2Refactored
