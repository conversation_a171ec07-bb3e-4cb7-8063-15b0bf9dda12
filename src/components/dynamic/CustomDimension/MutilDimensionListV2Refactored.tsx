import React, { useEffect, useCallback, useState } from 'react'
import { isDisable } from '../../utils/fnDisableComponent'
import { getPlaceholder } from '../../utils/fnGetFieldLabel'
import { LoadableTreeSelect } from '@hose/eui'
import { dependenceLogger } from '../../utils/fnAutoDependence'
import styles from './TreeSelect.module.less'
import { SearchItem, TreeItem } from './components'

// Hooks
import {
  useDisplayValue,
  useMultiTreeValue,
  useTreeSelectDependence,
  useTreeData,
  useTreeSearch,
  useLazyLoad
} from './hooks'

interface MutilDimensionListV2RefactoredProps {
  field: any
  onComponentLoadFinished?: (field: any) => void
  bus: any
  value: any[]
  billSpecification: any
  specificationId: string
  submitterId: any
  onChange: (value: any) => void
  billState: string
  form: any
  detailId?: string
  isDetail?: boolean
}

const MutilDimensionListV2Refactored: React.FC<MutilDimensionListV2RefactoredProps> = props => {
  const { field, onComponentLoadFinished, bus, value } = props

  const [autoAssignmentItem, setAutoAssignmentItem] = useState<any>(null)

  // 1. 依赖关系Hook
  const dependenceHook = useTreeSelectDependence({ field, props })

  // 2. 树形数据Hook
  const treeDataHook = useTreeData({
    fetchFunction: dependenceHook.createFetchFunction(),
    formatDataFunction: dependenceHook.formatData,
    props
  })

  // 3. 搜索Hook
  const searchHook = useTreeSearch({
    fetchFunction: dependenceHook.createFetchFunction()
  })

  // 4. 多选值处理Hook
  const valueHook = useMultiTreeValue({ field, props, dependenceHook })

  const { displayValue, getDisplayValue } = useDisplayValue(
    value,
    field,
    props,
    dependenceHook,
    treeDataHook.treeData,
    autoAssignmentItem
  )

  // 5. 解决循环依赖
  const handleCancelDependency = useCallback(() => {
    treeDataHook.initializeTreeData({ cancelRelation: true })
  }, [treeDataHook])

  useEffect(() => {
    dependenceHook.setCancelDependencyCallback(handleCancelDependency)
  }, [dependenceHook, handleCancelDependency])

  useEffect(() => {
    getDisplayValue(value)
  }, [])

  // 6. 初始化维度列表
  const initDimensionList = async (query: any = {}) => {
    const { isSpecialCancelDependency, _isUseDep } = dependenceHook.getDependenceStatus()
    if (isSpecialCancelDependency || !_isUseDep || query?.cancelRelation) {
      // 不使用依赖数据，使用全量数据
      const result = await treeDataHook.loadNodeData('', query)
      if (result) {
        const { items, hasNextPage } = result
        dependenceHook.setDependenceList(dependenceHook.formatData(items))
        treeDataHook.initTreeState(dependenceHook.formatData(items), hasNextPage)
        return items
      }
    }
  }

  const { handleDropdownVisibleChange, getDropdownContent, loading } = useLazyLoad({
    fetchData: async () => {
      return initDimensionList()
    }
  })

  // 7. 事件监听
  useEffect(() => {
    const handleDependenceChange = async (event: any, options: any = {}) => {
      const result = await dependenceHook.processDependenceChange(event, options)
      if (result) {
        const { items, hasNextPage, autoAssignmentItem } = result
        const newValue = await valueHook.processMultiSelectDependence(result, options)

        autoAssignmentItem && setAutoAssignmentItem(autoAssignmentItem)

        // 同步更新树形数据，确保 handleLoadMore 能正常工作
        if (items) {
          treeDataHook.initTreeState(dependenceHook.formatData(items), hasNextPage)
        }

        dependenceLogger(event.key, newValue, props)
      }
    }

    bus.on('on:dependence:change', handleDependenceChange)
    bus.on('set:delegator', dependenceHook.handleDelegatorChanged)

    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
      bus.un('set:delegator', dependenceHook.handleDelegatorChanged)
    }
  }, [bus])

  // 8. 初始化
  useEffect(() => {
    if (onComponentLoadFinished && !dependenceHook.hasEmitHandleDependenceChangeRef.current) {
      onComponentLoadFinished(field)
    }
  }, [])

  // 9. 处理节点变化
  const handleNodeChange = (newValue: any[]) => {
    valueHook.handleChange(newValue)
  }

  const disabled = isDisable(props)
  const { optional, defaultPlaceholder, isCanViewAllDataWithResultBlank } = field

  let placeholder = getPlaceholder(field)
  if (optional) {
    placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
  }

  const { dependenceViewAllData } = dependenceHook.getDependenceStatus()
  const { searchMode, searchResults } = searchHook

  let showAllPath = field && field.isShowFullPath
  const treeNodeLabelProp = showAllPath ? 'fullPath' : 'title'

  // 根据搜索模式决定显示的数据
  const treeRenderData = searchMode
    ? searchResults.map(item => treeDataHook.formatSearchItem(item, (item: any) => <SearchItem {...item} searchText={searchHook.currentSearchTextRef.current} />))
    : treeDataHook.toRenderTreeData(treeDataHook.treeData, (item: any) => <TreeItem {...item} />)

  return (
    <div>
      <LoadableTreeSelect
        data-testid="custom-dimension-multi-tree-select"
        showSearch
        multiple
        style={{ width: '100%' }}
        disabled={disabled}
        placeholder={placeholder}
        value={displayValue}
        showCheckedStrategy={'SHOW_ALL'}
        onSearch={searchHook.handleSearch}
        allowClear={optional || true}
        popupClassName={`${styles['custom-dimension-tree-select-popup']} ${searchMode ? styles['search-mode'] : ''}`}
        notFoundContent={getDropdownContent({
          isEmpty: treeRenderData.length === 0,
          loading: dependenceHook.isLoading || loading || searchHook.searchPageInfo.loading,
          notFoundContent:
            dependenceViewAllData || isCanViewAllDataWithResultBlank ? (
              <div className="cancel-dependence">
                {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
                <a href="javascript:void 0" onClick={() => dependenceHook.cancelDependence()}>
                  {i18n.get('查看全量数据')}
                </a>
              </div>
            ) : (
              undefined
            )
        })}
        autoClearSearchValue={false}
        showArrow
        size="middle"
        onChange={handleNodeChange}
        treeNodeLabelProp={treeNodeLabelProp}
        filterTreeNode={false}
        treeExpandedKeys={treeDataHook.treeExpandedKeys}
        onTreeExpand={treeDataHook.handleTreeExpand}
        treeData={treeRenderData}
        hasMore={(node: any) => {
          if (searchMode) {
            // 搜索模式下，只有根层级有加载更多
            return searchHook.searchPageInfo.hasNextPage && !searchHook.searchPageInfo.loading
          } else {
            // 树形模式下，每个节点都可能有加载更多
            const nodeKey = node?.id || 'root'
            const pageInfo = treeDataHook.nodePageInfo[nodeKey]
            return pageInfo?.hasNextPage && !pageInfo?.loading
          }
        }}
        onLoadMore={searchMode ? searchHook.handleSearchLoadMore : treeDataHook.handleLoadMore}
        onDropdownVisibleChange={handleDropdownVisibleChange}
      />
    </div>
  )
}

export default MutilDimensionListV2Refactored
