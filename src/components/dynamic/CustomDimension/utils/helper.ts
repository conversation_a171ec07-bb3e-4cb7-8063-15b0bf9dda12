const findNodeById = (id: string, treeData: any[]): any => {
  // DFS递归查找节点 - 返回单个节点或null
  const dfs = (items: any[]): any => {
    for (const item of items) {
      if (item.id === id) {
        return item
      }
      if (item.children && item.children.length > 0) {
        const found = dfs(item.children)
        if (found) {
          return found
        }
      }
    }
    return null
  }
  return dfs(treeData)
}

export {
  findNodeById
}
