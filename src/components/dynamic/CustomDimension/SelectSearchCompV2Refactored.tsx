import React, { useEffect, useCallback, useState } from 'react'
import { LoadableSelect } from '@hose/eui'
import { OutlinedEditSearch } from '@hose/eui-icons'
import { isDisable } from '../../utils/fnDisableComponent'
import { getPlaceholder } from '../../utils/fnGetFieldLabel'
import styles from './TreeSelect.module.less'
import { SearchItem } from './components'

// 引入分层抽象的Hook
import {
  useDelegatorSelect,
  useCurrencyDimension,
  useSingleSelectValue,
  useSelectDependence,
  useSearchableSelect,
  useDisplayValue
} from './hooks'

interface SelectSearchCompV2RefactoredProps {
  field: any
  value: any
  onChange: (value: any) => void
  bus: any
  billSpecification: any
  specificationId: any
  submitterId: any
  lastChoice?: any
  billState?: string
  detailId?: string
  isDetail?: boolean
  form?: any
  // ... 其他必要的props
}

function SelectSearchCompV2Refactored(props: SelectSearchCompV2RefactoredProps) {
  const { field, value, onChange, bus } = props
  const [autoAssignmentItem, setAutoAssignmentItem] = useState<any>()

    // 1. 依赖关系Hook
  const dependenceHook = useSelectDependence({
    field,
    props
  })

  // 2. 基础搜索Hook
  const searchHook = useSearchableSelect({
    field,
    fetchFunction: dependenceHook.createFetchFunction(),
  })

  // 3. 处理取消依赖的回调 - 在所有Hook定义后
  const handleCancelDependency = useCallback(() => {
    // 重新搜索数据，设置cancelRelation为true
    searchHook.searchWithParams({ 
      searchValue: searchHook.searchText, 
      cancelRelation: true 
    })
  }, [searchHook])

  // 4. 设置取消依赖回调到Hook中
  useEffect(() => {
    dependenceHook.setCancelDependencyCallback(handleCancelDependency)
  }, [dependenceHook, handleCancelDependency])

  // 5. 货币维度Hook
  const currencyHook = useCurrencyDimension({
    field,
    bus
  })

  // 6. 代理人Hook
  useDelegatorSelect({
    bus,
    searchWithParams: searchHook.searchWithParams
  })

  const { displayValue, getValue } = useDisplayValue(value, field, props, dependenceHook, searchHook.data, autoAssignmentItem)

  // 7. 单选值处理Hook
  const valueHook = useSingleSelectValue({
    field,
    props,
    value,
    getValue
  })


  // 处理单选变化
  const handleChange = (newValue: any) => {
    const foundValue = searchHook.data.find(item => item.id === newValue) || newValue

    // 清空搜索相关状态
    searchHook.setData([])
    searchHook.setHasNextPage(false)

    onChange && onChange(foundValue)

    // 处理货币维度变化
    currencyHook.handleDimensionCurrencyChange(foundValue)
  }

  // 处理依赖变化中的单选逻辑
  const handleDependenceChange = async (params: any, options: any = {}) => {
    const result = await dependenceHook.processDependenceChange(params, options)
    
    // 如果不匹配依赖键，直接返回
    if (!result) {
      return
    }

    const { newValue } = result

    onChange && onChange(newValue)
    newValue && setAutoAssignmentItem(newValue)

    // 处理货币维度变化
    if (newValue) {
      currencyHook.handleDimensionCurrencyChange(newValue)
    }
  }

  // 组件挂载时的初始化和事件监听
  useEffect(() => {
    // 注册依赖变化事件监听
    bus.on('on:dependence:change', handleDependenceChange)
    
    // 处理默认值
    valueHook.handleDefaultValue(props)

    // 触发依赖变化事件
    setTimeout(() => {
      const id = typeof value === 'object' ? value?.id : value
      bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true })
      // 初始化时处理货币维度
      currencyHook.handleDimensionCurrencyChange(value, true)
    }, 200)

    // 清理事件监听
    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
    }
  }, [])

  // 获取placeholder
  const getFieldPlaceholder = () => {
    const { optional } = field
    let placeholder = getPlaceholder(field)
    if (optional) {
      placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    }
    return placeholder
  }

  const disabled = isDisable(props)

  return (
    <div>
      <LoadableSelect
        data-testid="custom-dimension-select-search"
        popupClassName={`${styles['custom-dimension-select-search-popup']}`}
        style={{ width: '100%' }}
        placeholder={getFieldPlaceholder()}
        value={displayValue}
        showSearch={true}
        allowClear
        disabled={disabled}
        onChange={handleChange}
        onSearch={searchHook.onSearch}
        filterOption={false}
        notFoundContent={dependenceHook.emptyText}
        suffixIcon={<OutlinedEditSearch />}
        hasMore={() => searchHook.hasNextPage}
        onLoadMore={searchHook.onLoadMore}
        options={searchHook.toRenderData(searchHook.data, (item: any) => <SearchItem {...item} searchText={searchHook.searchText} />)}
        autoClearSearchValue={false}
        loadMoreRender={searchHook.loadMoreRender}
        optionLabelProp='_label'
      />
    </div>
  )
}

export default SelectSearchCompV2Refactored
