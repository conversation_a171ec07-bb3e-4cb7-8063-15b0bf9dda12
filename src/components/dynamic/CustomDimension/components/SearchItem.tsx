import React from 'react'
import { Tooltip, Ellipsis } from '@hose/eui'
import Highlighter from 'react-highlight-words'

import searchStyles from './SearchItem.module.less'

const SearchItem = (item: any): any => {
  const { title, fullPath, disabled = false, searchText } = item

  return (
    <div
      className={`${searchStyles['search-item-container']} ${disabled ? searchStyles['search-item-disabled'] : ''}`}
      data-testid={`custom-dimension-search-item-${item.id}`}
    >
      <Tooltip
        title={
          <div>
            <div>{title}</div>
            {fullPath && <div>{fullPath}</div>}
          </div>
        }
        placement="bottom"
        mouseEnterDelay={0.5}
        zIndex={10000}
      >
        <Highlighter
          searchWords={[searchText]}
          textToHighlight={title}
          className={searchStyles['search-item-title']}
          autoEscape={true}
          highlightClassName={searchStyles['highlight']}
        />
        {fullPath && <Ellipsis content={fullPath} rows={1} className={searchStyles['search-item-path']} />}
      </Tooltip>
    </div>
  )
}

export default SearchItem
