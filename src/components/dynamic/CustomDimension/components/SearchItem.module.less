.search-item-container {
  // width: 100%;
  // max-width: 100%; // 确保不会超出父容器
  padding: 4px 0px;
  box-sizing: border-box; // 确保padding不影响总宽度
  min-width: 0;

  .search-item-title {
    display: block; // 改为block，避免inline-block的空白间隙
    width: 100%; // 添加宽度限制
    color: var(--eui-text-title);
    font: var(--eui-font-body-b1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
  }

  .highlight {
    color: var(--eui-primary-pri-500);
    font: var(--eui-font-body-b1);
  }

  mark {
    background-color: transparent;
    padding: 0;
  }

  .search-item-path {
    width: 100%; // 添加宽度限制
    color: var(--eui-text-caption);
    font: var(--eui-font-note-r2);
  }

  &.search-item-disabled {
    .search-item-title {
      color: var(--eui-text-disabled);
      font: var(--eui-font-body-b1);
    }

    .search-item-path {
      color: var(--eui-text-disabled);
      font: var(--eui-font-body-b1);
    }

    cursor: not-allowed;
  }

  // &:hover:not(.search-item-disabled) {
  //   border-radius: 4px;
  //   background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
  // }

  display: flex;
  flex-direction: column;
  justify-content: center;
}
