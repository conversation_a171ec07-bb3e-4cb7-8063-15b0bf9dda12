.tree-item-container {
  // width: 100%;
  // max-width: 100%; // 确保不会超出父容器
  padding: 0px 8px;
  box-sizing: border-box; // 确保padding不影响总宽度

  .tree-item-title {
    width: 100%; // 添加宽度限制
    color: var(--eui-text-title);
    font: var(--eui-font-body-b1);
  }

  &.tree-item-disabled {
    .tree-item-title {
      color: var(--eui-text-disabled);
      font: var(--eui-font-body-b1);
    }

    cursor: not-allowed;
  }

  // &:hover:not(.tree-item-disabled) {
  //   border-radius: 4px;
  //   background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
  // }

  display: flex;
  flex-direction: column;
  justify-content: center;
}
