import React from 'react'
import { Tooltip, Ellipsis } from '@hose/eui'
import treeItemStyles from './TreeItem.module.less'

const TreeItem = (item: any): any => {
  const { title, disabled = false } = item

  return (
    <div
      data-testid={`custom-dimension-tree-item-${item.id}`}
      className={`${treeItemStyles['tree-item-container']} ${disabled ? treeItemStyles['tree-item-disabled'] : ''}`}
    >
      <Tooltip
        title={title}
        placement="bottom"
        mouseEnterDelay={0.5}
        zIndex={10000}
      >
        <Ellipsis content={title} rows={1} className={treeItemStyles['tree-item-title']} />
      </Tooltip>
    </div>
  )
}

export default TreeItem
