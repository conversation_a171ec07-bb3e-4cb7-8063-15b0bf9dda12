import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'

export const useGetByIds = (field: any, props: any, dependenceHook: any) => {
  // 通过ID获取记录的方法
  const getRecordLinkById = async (ids: string[] = [], query: any = {}): Promise<any[]> => {
    if (!ids?.length) {
      return []
    }

    const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
    const dependenceFormData = await dependenceHook.getFormValue()

    const res = await api.invokeService('@bills:get:record:link:v2:by:ids', {
      form: dependenceFormData,
      entity,
      flowSpecId: props.billSpecification.id,
      detailSpecId: props.specificationId,
      dimensionItemFieldName: field.field,
      cancelRelation: !dependenceHook.useDependenceData,
      batchApportion: false,
      apportionSpecId: undefined,
      submitterId: props.submitterId?.id,
      defaultValues: ids || [],
      ...query
    })

    return res
  }

  return {
    getRecordLinkById
  }
}
