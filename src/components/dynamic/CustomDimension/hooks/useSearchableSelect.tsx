import React, { useState, useRef } from 'react'
import { debounce } from 'lodash'
import { uuid } from '@ekuaibao/helpers'
import { Button } from '@hose/eui'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'

interface UseSearchableSelectOptions {
  field: any
  fetchFunction: (params: any) => Promise<any>
  debounceMs?: number
  pageSize?: number
}

interface UseSearchableSelectReturn {
  // 状态
  searchText: string
  data: any[]
  hasNextPage: boolean
  
  // 方法
  onSearch: (value: string) => void
  onLoadMore: () => Promise<any[]>
  loadMoreRender: (info: any) => JSX.Element
  toRenderData: (data: any[], renderSearchItem?: (item: any) => any) => any[]
  
  // 工具方法
  setData: (data: any[]) => void
  setHasNextPage: (hasNext: boolean) => void
  clearSearch: () => void
  searchWithParams: (params: any) => Promise<any>
}

export function useSearchableSelect({
  field,
  fetchFunction,
  debounceMs = 800,
}: UseSearchableSelectOptions): UseSearchableSelectReturn {
  
  // 状态管理
  const [searchText, setSearchText] = useState('')
  const [data, setData] = useState<any[]>([])
  const [hasNextPage, setHasNextPage] = useState(false)
  
  // refs
  const currentIdx = useRef<string | null>(null)
  const currentValueRef = useRef('')

  // 数据获取
  const fetchData = async (query: any = {}) => {
    const randomId = uuid(10)
    currentIdx.current = randomId

    const res = await fetchFunction({
      searchText: query.searchText,
      ...query
    })

    // 只有最新的请求才处理结果，避免竞态条件
    if (currentIdx.current === randomId) {
      return res
    }
    return null
  }

  // 搜索数据
  const performSearch = async ({
    searchValue,
    lastItemId,
    lastItemCode,
    isLoadMore = false,
    ...extraParams
  }: any = {}) => {
    const action = await fetchData({
      searchText: searchValue,
      lastId: lastItemId,
      lastCode: lastItemCode,
      ...extraParams
    })

    if (!action) return

    const { items, hasNextPage: nextHasNextPage } = action

    if (!searchValue) {
      // 没有搜索词时清空数据
      setData([])
    } else {
      if (isLoadMore) {
        // 加载更多：追加数据
        setData(prevData => prevData.concat(items))
      } else {
        // 新搜索：替换数据
        setData(items)
      }
      setHasNextPage(nextHasNextPage)
    }

    return { items, hasNextPage: nextHasNextPage }
  }

  // 搜索处理（防抖）
  const onSearch = debounce((currentValue: string) => {
    const trimValue = currentValue.trim()

    if (trimValue.length) {
      setSearchText(trimValue)
      currentValueRef.current = trimValue
      performSearch({ searchValue: trimValue })
    } else {
      currentValueRef.current = ''
      setData([])
      setHasNextPage(false)
      setSearchText('')
    }
  }, debounceMs)

  // 加载更多
  const onLoadMore = async () => {
    if (!data.length) return []
    
    const lastItem = data[data.length - 1]
    const result = await performSearch({
      searchValue: currentValueRef.current,
      lastItemId: lastItem.id,
      lastItemCode: lastItem.code,
      isLoadMore: true
    })
    
    return result?.items || []
  }

  // 加载更多渲染
  const loadMoreRender = (info: any) => {
    const { loading, onLoadMore } = info
    
    return (
      <Button 
        category="text" 
        theme="highlight" 
        loading={loading}
        onClick={onLoadMore}
      >
        {loading ? i18n.get('正在加载中...') : i18n.get('点击加载更多')}
      </Button>
    )
  }

  // 数据格式化
  const toRenderData = (data: any[], renderSearchItem?: (item: any) => any) => {
    const { isShowFullPath, hideCode } = field || {}
    return data.map(d => {
      const showName = getContentByLocale(d, 'name')
      const showText = !hideCode 
        ? i18n.get(`{__k0}（{__k1}）`, { __k0: showName, __k1: d.code }) 
        : showName

      const displayName = isShowFullPath && d?.fullPath ? `${d?.fullPath} / ${showName}` : showName
      return {
        ...d,
        label: renderSearchItem
          ? renderSearchItem({
              ...d,
              title: showText,
              fullPath: d.fullPath,
              disabled: d.disabled || d.active === false,
              id: d.id
            })
          : showText,
        _label: displayName,
        value: d.id
      }
    })
  }

  // 清空搜索
  const clearSearch = () => {
    setSearchText('')
    setData([])
    setHasNextPage(false)
    currentValueRef.current = ''
  }

  // 暴露内部搜索方法供业务Hook使用
  const searchWithParams = (params: any) => performSearch(params)

  return {
    // 状态
    searchText,
    data,
    hasNextPage,
    
    // 方法
    onSearch,
    onLoadMore,
    loadMoreRender,
    toRenderData,
    
    // 工具方法
    setData,
    setHasNextPage,
    clearSearch,
    searchWithParams
  }
} 