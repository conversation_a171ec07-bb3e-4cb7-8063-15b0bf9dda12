import React, { useState, useCallback, useRef } from 'react'
import { Spin } from '@hose/eui'

/**
 * 生成下拉框内容的工具函数
 */
export const getDropdownContent = ({
  loading,
  hasError,
  isEmpty,
  notFoundContent,
  loadingText = i18n.get('数据加载中...'),
  errorText = i18n.get('数据加载失败，请重试'),
}) => {
  if (loading && isEmpty) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        padding: '12px'
      }}>
        <Spin direction='vertical' text={loadingText}/>
      </div>
    )
  }
  
  if (hasError && isEmpty) {
    return (
      <div style={{ 
        padding: '12px', 
        textAlign: 'center',
        color: '#ff4d4f'
      }}>
        <span>{errorText}</span>
      </div>
    )
  }
  
  if (isEmpty) {
    if (notFoundContent) {
      return notFoundContent
    }
    return undefined
  }
  
  return undefined
}

/**
 * 简单的懒加载 Hook
 * 只在用户首次点击下拉框时加载数据
 */
export const useLazyLoad = ({
  // 数据请求函数
  fetchData,
  // 数据格式化函数
  formatData = (data) => data
}) => {
  // 数据状态
  const [data, setData] = useState([])
  // 加载状态
  const [loading, setLoading] = useState(false)
  // 是否已经加载过数据
  const [hasLoaded, setHasLoaded] = useState(false)
  // 错误状态
  const [error, setError] = useState(null)
  // 请求的 ref，用于防止重复请求
  const requestRef = useRef(null)

  // 处理下拉框打开事件
  const handleDropdownVisibleChange = useCallback(async (visible) => {
    // 如果下拉框打开且还没有加载过数据
    if (visible && !hasLoaded && !loading && !requestRef.current) {
      setLoading(true)
      setError(null)
      
      try {
        // 创建请求 Promise
        const requestPromise = fetchData()
        requestRef.current = requestPromise
        
        const result = await requestPromise
        
        // 格式化数据
        const formattedData = formatData(result)
        setData(formattedData)
        setHasLoaded(true)
      } catch (err) {
        console.error('useLazyLoad fetchData error:', err)
        setError(err)
      } finally {
        setLoading(false)
        requestRef.current = null
      }
    }
  }, [fetchData, formatData, hasLoaded, loading])

  // 重置状态
  const reset = useCallback(() => {
    setData([])
    setLoading(false)
    setHasLoaded(false)
    setError(null)
    requestRef.current = null
  }, [])

  // 手动刷新数据
  const refresh = useCallback(async () => {
    reset()
    setLoading(true)
    setError(null)
    
    try {
      const result = await fetchData()
      const formattedData = formatData(result)
      setData(formattedData)
      setHasLoaded(true)
    } catch (err) {
      console.error('useLazyLoad refresh error:', err)
      setError(err)
    } finally {
      setLoading(false)
    }
  }, [fetchData, formatData, reset])

  return {
    // 数据状态
    data,
    loading,
    setLoading,
    hasLoaded,
    error,
    
    // 操作方法
    handleDropdownVisibleChange,
    reset,
    refresh,
    
    // 计算属性
    isEmpty: !data || data.length === 0,
    hasError: !!error,
    
    // 工具函数
    getDropdownContent: (options = {}) => getDropdownContent({
      loading,
      hasError: !!error,
      isEmpty: !data || data.length === 0,
      notFoundContent: options.notFoundContent,
      loadingText: options.loadingText,
      errorText: options.errorText,
      ...options
    })
  }
} 