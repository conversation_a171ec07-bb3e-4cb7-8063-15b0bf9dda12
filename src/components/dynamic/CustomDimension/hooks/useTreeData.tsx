import React,{ useState, useRef } from 'react'

interface NodePageInfo {
  hasNextPage: boolean
  loading: boolean
  loaded: boolean
}

interface UseTreeDataOptions {
  fetchFunction: (query: any) => Promise<any>
  formatDataFunction?: (items: any[]) => any[]
  props?: any
}

interface UseTreeDataReturn {
  treeData: any[]
  treeExpandedKeys: string[]
  nodePageInfo: Record<string, NodePageInfo>
  loadedNodes: Set<string>
  treeDataMapRef: React.MutableRefObject<Record<string, any>>
  handleTreeExpand: (expandedKeys: string[]) => Promise<void>
  handleLoadMore: (node: any) => Promise<any[]>
  formatTreeItem: (item: any, parentNode?: any) => any
  toRenderTreeData: (dependenceListData: any[], renderTreeItem?: (item: any) => any) => any[]
  initializeTreeData: (query?: any) => Promise<void>
  loadNodeData: (parentId: string, query?: any) => Promise<any>
  updateTreeData: (nodes: any[], targetId: string, newChildren: any[], append?: boolean) => any[]
  findNodeById: (id: string) => any
  setTreeData: React.Dispatch<React.SetStateAction<any[]>>
  formatSearchItem: (item: any, renderSearchItem?: (item: any) => any) => any
  setNodePageInfo: React.Dispatch<React.SetStateAction<Record<string, NodePageInfo>>>
  setTreeExpandedKeys: React.Dispatch<React.SetStateAction<string[]>>
  initTreeState: (rootData: any[], hasNextPage: boolean) => void
}

const LOADABLE_MORE_ROOT_ID = '__loadable_more_root__'

/**
 * 树形数据管理Hook - 处理树形展开、节点管理、分页状态
 */
export function useTreeData({ 
  fetchFunction,
  formatDataFunction ,
  props
}: UseTreeDataOptions): UseTreeDataReturn {
  const [treeData, setTreeData] = useState([])
  const [treeExpandedKeys, setTreeExpandedKeys] = useState<string[]>([])
  const [nodePageInfo, setNodePageInfo] = useState<Record<string, NodePageInfo>>({
    root: { hasNextPage: false, loading: false, loaded: false }
  })
  const [loadedNodes, setLoadedNodes] = useState(new Set<string>())
  const treeDataMapRef = useRef<Record<string, any>>({})

  const { field } = props
  const { hideCode } = field

  // 通用的数据加载方法
  const loadNodeData = async (parentId: string, query: any = {}): Promise<any> => {
    try {
      const action = await fetchFunction({
        parentId,
        ...query
      })

      const { items = [], hasNextPage = false } = action || {}

      return {
        items: formatDataFunction ? formatDataFunction(items) : items,
        hasNextPage,
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      return null
    }
  }

  // 通用的树形数据更新方法
  const updateTreeData = (nodes: any[], targetId: string, newChildren: any[], append: boolean = false): any[] => {
    if(targetId === '') {
      return append ? nodes.concat(newChildren) : newChildren
    }

    return nodes.map(node => {
      if (node.id === targetId) {
        // 找到目标节点
        const existingChildren = node.children || []
        const updatedChildren = append ? existingChildren.concat(newChildren) : newChildren

        return Object.assign({}, node, {
          children: updatedChildren
        })
      } else if (node.children && node.children.length > 0) {
        // 递归查找子节点
        return Object.assign({}, node, {
          children: updateTreeData(node.children, targetId, newChildren, append)
        })
      }
      return node
    })
  }

  // 处理树展开
  const handleTreeExpand = async (expandedKeys: string[]): Promise<void> => {
    if (expandedKeys.length === 0) {
      setTreeExpandedKeys([])
      return
    }

    const expandedNodeId = expandedKeys[expandedKeys.length - 1]

    // 更新展开状态
    setTreeExpandedKeys(expandedKeys)

    // 如果节点已经加载过子数据，不需要重新加载
    if (loadedNodes.has(expandedNodeId)) {
      return
    }

    // 加载子节点数据
    const result = await loadNodeData(expandedNodeId)
    if (!result) return

    const { items, hasNextPage } = result

    // 更新树形数据
    const updatedTreeData = updateTreeData(
      treeData,
      expandedNodeId,
      items,
      false // 不是追加模式
    )

    // 标记节点已加载
    setLoadedNodes(prev => {
      const updated = new Set(prev)
      updated.add(expandedNodeId)
      return updated
    })

    setTreeData(updatedTreeData)

    setNodePageInfo(prev => ({
      ...prev,
      [expandedNodeId]: {
        hasNextPage,
        loading: false,
        loaded: true
      }
    }))
  }

  const findNodeById = (id: string): any => {
    // DFS递归查找节点 - 返回单个节点或null
    const dfs = (items: any[]): any => {
      for (const item of items) {
        if (item.id === id) {
          return item
        }
        if (item.children && item.children.length > 0) {
          const found = dfs(item.children)
          if (found) {
            return found
          }
        }
      }
      return null
    }
    return dfs(treeData)
  }

  // 处理加载更多
  const handleLoadMore = async (node: any): Promise<any[]> => {
    const { value } = node
    let lastItem = null, parentId = ''
    if(value === LOADABLE_MORE_ROOT_ID) {
      lastItem = treeData[treeData.length - 1]
    } else {
      // 普通节点
      const parentNode = findNodeById(value)
      const children = parentNode.children || []
      lastItem = children[children.length - 1]
      parentId = value
    }
    const result = await loadNodeData(parentId, {
      lastId: lastItem?.id,
      lastCode: lastItem?.code,
    })

    if (!result) {
      return []
    }

    const { items, hasNextPage } = result
    const nodeKey = parentId || 'root'

    // 更新分页信息
    const updatedNodePageInfo = {
      [nodeKey]: {
        hasNextPage,
        loading: false,
        loaded: true
      }
    }

    // 更新树形数据（追加模式）
    const updatedTreeData = updateTreeData(
      treeData,
      parentId,
      items,
      true // 追加模式
    )

    setTreeData(updatedTreeData)

    setNodePageInfo(prev => ({
      ...prev,
      ...updatedNodePageInfo
    }))

    return items
  }

  const formatSearchItem = (item: any, renderSearchItem?: (item: any) => any): any => {
    const { id, name, enName, code, active, fullPath, leaf } = item
    let showName = i18n.currentLocale === 'en-US' && enName ? enName : name
    
    if(!hideCode && code) {
      showName = `${showName}(${code})`
    }

    // 如果有自定义渲染函数，使用它
    const finalTitle = renderSearchItem
      ? renderSearchItem({
          title: showName,
          fullPath,
          disabled: !active,
          id,
        })
      : showName

    treeDataMapRef.current[id] = item

    return Object.assign({}, item, {
      title: finalTitle,
      label: finalTitle,
      value: id,
      isLeaf: leaf,
      name: showName,
      fullPath,
      disabled: !active
    })
  }

  // 格式化单个树节点项
  const formatTreeItem = (item: any, parentNode?: any, renderTreeItem?: (item: any) => any): any => {
    const { id, name, enName, code, active, leaf } = item
    let showName = (i18n.currentLocale === 'en-US' && enName ? enName : name)
    const _name = showName
    const activeName = active ? showName : `${showName}${i18n.get('(已停用)')}`
    if(!hideCode) {
      showName = `${showName}${code ? `(${code})` : ''}`
    }

    const parentPath = parentNode ? parentNode.fullPath || parentNode._name : ''
    const fullPath = parentPath ? `${parentPath} / ${_name}` : _name
    const _fullPath = parentPath ? `${parentPath} / ${showName}` : showName

    treeDataMapRef.current[id] = item

    return Object.assign({}, item, {
      title: showName,
      value: id,
      isLeaf: leaf,
      name: showName,
      _name,
      fullPath,
      _fullPath,
      disabled: !active
    })
  }

  // 转换为渲染的树数据
  const toRenderTreeData = (dependenceListData: any[], renderTreeItem?: (item: any) => any): any[] => {
    const dfs = (items: any[], parentNode: any) => {
      return items?.map(item => {
        const { children, ...rest } = item
        const newItem = formatTreeItem(rest, parentNode, renderTreeItem)
        return Object.assign({}, newItem, {
          children: children ? dfs(children, newItem) : []
        })
      })
    }

    return dfs(dependenceListData, null)
  }

  // 初始化树数据
  const initializeTreeData = async (query: any = {}): Promise<void> => {
    const result = await loadNodeData('', query)
    if (result) {
      setTreeData(result.items)
      setNodePageInfo(prev => ({
        ...prev,
        root: { hasNextPage: result.hasNextPage, loading: false, loaded: true }
      }))
      setTreeExpandedKeys([])
    }
  }

  const initTreeState = (rootData: any[], hasNextPage: boolean) => {
    setTreeExpandedKeys([])
    setTreeData(rootData)
    setNodePageInfo({
      root: { hasNextPage, loading: false, loaded: false }
    })
    setLoadedNodes(new Set<string>())
  }

  return {
    treeData,
    treeExpandedKeys,
    nodePageInfo,
    loadedNodes,
    treeDataMapRef,
    handleTreeExpand,
    handleLoadMore,
    formatTreeItem,
    toRenderTreeData,
    initializeTreeData,
    loadNodeData,
    updateTreeData,
    findNodeById,
    setTreeData,
    formatSearchItem,
    setNodePageInfo,
    setTreeExpandedKeys,
    initTreeState,
  }
} 