import { useState, useEffect } from 'react'

interface UseDelegatorSelectOptions {
  bus: any
  searchWithParams?: (params: any) => Promise<any>
}

interface UseDelegatorSelectReturn {
  delegator: any
  handleDelegatorChanged: (newDelegator: any) => void
}

export function useDelegatorSelect({
  bus,
  searchWithParams
}: UseDelegatorSelectOptions): UseDelegatorSelectReturn {
  
  const [delegator, setDelegator] = useState(null)

  // 处理代理人变化
  const handleDelegatorChanged = (newDelegator: any) => {
    setDelegator(newDelegator)
    
    // 代理人变化时重新搜索数据
    if (searchWithParams) {
      searchWithParams({ 
        submitterId: newDelegator?.id 
      })
    }
  }

  // 事件监听
  useEffect(() => {
    bus.on('set:delegator', handleDelegatorChanged)
    
    return () => {
      bus.un('set:delegator', handleDelegatorChanged)
    }
  }, [])

  return {
    delegator,
    handleDelegatorChanged
  }
} 