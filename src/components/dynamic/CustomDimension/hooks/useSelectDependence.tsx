import React, { useState, useRef } from 'react'
import { get } from 'lodash'
import { isObject } from '@ekuaibao/helpers'
import { SpecialAllowCancelDependenceUtils } from '../../types'
import { dependenceLogger } from '../../../utils/fnAutoDependence'
import { app as api } from '@ekuaibao/whispered'

interface UseSelectDependenceOptions {
  field: any
  props: any
}

interface UseSelectDependenceReturn {
  // 状态
  useDependenceData: boolean
  isDependenceListEmpty: boolean
  emptyText: any
  
  // 工具方法 - 不直接监听事件，让组件决定如何使用
  isSpecialCancelDependency: () => boolean
  getDependenceKey: () => string[]
  getFormValue: () => Promise<any>
  getEntity: () => string
  createFetchFunction: () => (params: any) => Promise<any>
  processDependenceChange: (params: any, options?: any) => Promise<{
    items: any[]
    autoAssignmentItem?: any
    matchDefaultValue?: boolean
    newValue?: any
  }>
  
  // 状态更新方法
  setUseDependenceData: (value: boolean) => void
  setIsDependenceListEmpty: (value: boolean) => void
  setEmptyText: (value: any) => void
  
  // 设置取消依赖回调的方法
  setCancelDependencyCallback: (callback: () => void) => void
}

export function useSelectDependence({
  field,
  props
}: UseSelectDependenceOptions): UseSelectDependenceReturn {
  
  // 状态管理
  const [useDependenceData, setUseDependenceData] = useState(true)
  const [isDependenceListEmpty, setIsDependenceListEmpty] = useState(false)
  const [emptyText, setEmptyText] = useState<any>('')
  
  // 使用 useRef 存储取消依赖的回调函数，避免循环依赖
  const cancelDependencyCallbackRef = useRef<(() => void) | null>(null)
  
  const { bus, value, billSpecification, specificationId, submitterId } = props

  // 获取实体类型
  const getEntity = () => {
    return get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
  }

  // 判断是否为特殊取消依赖
  const isSpecialCancelDependency = () => {
    const { allowCancelDependence = false } = field
    return SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
  }

  // 获取依赖键
  const getDependenceKey = () => {
    const { dependence } = field
    return (dependence || [])?.map(d => d.dependenceId) || []
  }

  // 获取表单值
  const getFormValue = async () => {
    const { feeType } = props
    const formData = await bus.getFieldsValue()
    const dependenceFormData = {}
    const useDependenceKeys = getDependenceKey()
    useDependenceKeys.forEach(key => {
      dependenceFormData[key] = isObject(formData[key]) ? formData[key].id : formData[key] 
      if(key === 'flow.FeeType') {
        dependenceFormData[key] = feeType?.id
      }
    })
    return dependenceFormData
  }

  // 创建获取数据的函数
  const createFetchFunction = () => {
    return async (query: any = {}) => {
      const entity = getEntity()
      const dependenceFormData = await getFormValue()

      const res = await api.invokeService('@bills:get:record:link:v2', {
        form: dependenceFormData,
        entity,
        defaultValue: value && value.id,
        flowSpecId: billSpecification.id,
        detailSpecId: specificationId,
        dimensionItemFieldName: field.field,
        cancelRelation: !useDependenceData,
        batchApportion: false,
        submitterId: submitterId?.id,
        searchText: query.searchText,
        ...query
      })

      return res
    }
  }

  // 处理依赖变化 - 只处理通用逻辑，返回结果让组件决定如何使用
  const processDependenceChange = async ({ key, id, dependenceFeeType = false }, options: any = {}) => {
    const { billState, detailId, isDetail, form } = props
    const { allowCancelDependence = false } = field
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit
    const useDependenceKeys = getDependenceKey()

    if (!useDependenceKeys.includes(key)) {
      return null
    }

    const fetchFunction = createFetchFunction()
    const action = await fetchFunction({ parentId: '' })
    const { items, autoAssignmentItem, matchDefaultValue } = action
    
    let newValue = undefined

    bus.setValidateLevel(1)
    if (isInitLoad) {
      const fieldsValue = form.getFieldsValue()
      newValue = fieldsValue[field?.field] ?? undefined
    } else if (autoAssignmentItem) {
      newValue = autoAssignmentItem
    }
    dependenceLogger(key, newValue, props)

    // 更新依赖状态
    if (isSpecialCancelDependency()) {
      setIsDependenceListEmpty(false)
      setUseDependenceData(false)
      setEmptyText('')
    } else if (items.length === 0 && allowCancelDependence) {
      setIsDependenceListEmpty(true)
      setUseDependenceData(true)
      setEmptyText(
        <div className="cancel-dependence">
          {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
          <a href="javascript:void(0)" onClick={() => {
            setUseDependenceData(false)
            // 调用存储在ref中的回调函数
            cancelDependencyCallbackRef.current && cancelDependencyCallbackRef.current()
          }}>
            {i18n.get('查看全量数据')}
          </a>
        </div>
      )
    } else {
      setIsDependenceListEmpty(false)
      setUseDependenceData(true)
      setEmptyText('')
    }

    return { ...action, autoAssignmentItem, matchDefaultValue, newValue }
  }

  // 设置取消依赖回调函数
  const setCancelDependencyCallback = (callback: () => void) => {
    cancelDependencyCallbackRef.current = callback
  }

  return {
    // 状态
    useDependenceData,
    isDependenceListEmpty,
    emptyText,
    
    // 工具方法
    isSpecialCancelDependency,
    getDependenceKey,
    getFormValue,
    getEntity,
    createFetchFunction,
    processDependenceChange,
    
    // 状态更新方法
    setUseDependenceData,
    setIsDependenceListEmpty,
    setEmptyText,
    
    // 设置取消依赖回调的方法
    setCancelDependencyCallback
  }
} 