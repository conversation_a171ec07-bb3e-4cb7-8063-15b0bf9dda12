import { useState, useRef } from 'react'
import { debounce } from 'lodash'

interface UseTreeSearchOptions {
  fetchFunction: (query: any) => Promise<any>
  debounceMs?: number
}

interface SearchPageInfo {
  hasNextPage: boolean
  loading: boolean
}

interface UseTreeSearchReturn {
  searchMode: boolean
  searchResults: any[]
  searchPageInfo: SearchPageInfo
  currentSearchTextRef: React.MutableRefObject<string>
  handleSearch: (searchValue: string) => void
  handleSearchLoadMore: () => Promise<any[]>
}

/**
 * 基础树形搜索Hook - 处理搜索模式切换和搜索状态管理
 */
export function useTreeSearch({ 
  fetchFunction,
  debounceMs = 800 
}: UseTreeSearchOptions): UseTreeSearchReturn {
  const [searchMode, setSearchMode] = useState(false)
  const [searchResults, setSearchResults] = useState([])
  const [searchPageInfo, setSearchPageInfo] = useState<SearchPageInfo>({ hasNextPage: false, loading: false })
  const currentSearchTextRef = useRef('')

  // 防抖搜索处理
  const handleSearch = debounce(async (searchValue: string) => {
    const searchText = searchValue?.trim()
    currentSearchTextRef.current = searchText || ''

    if (!searchText) {
      // 清空搜索，回到树形模式
      setSearchMode(false)
      setSearchResults([])
      setSearchPageInfo({ hasNextPage: false, loading: false })
      return
    }

    // 进入搜索模式
    setSearchMode(true)
    setSearchResults([])
    setSearchPageInfo({ hasNextPage: false, loading: true })

    try {
      const action = await fetchFunction({
        searchText
      })

      const { items = [], hasNextPage = false } = action || {}

      setSearchResults(items || [])
      setSearchPageInfo({ hasNextPage, loading: false })
    } catch (error) {
      console.error('搜索失败:', error)
      setSearchPageInfo({ hasNextPage: false, loading: false })
    }
  }, debounceMs)

  // 搜索结果加载更多
  const handleSearchLoadMore = async (): Promise<any[]> => {
    if (searchPageInfo.loading || !searchPageInfo.hasNextPage) {
      return []
    }

    setSearchPageInfo(prev => ({ ...prev, loading: true }))

    try {
      const lastItem = searchResults[searchResults.length - 1]
      const action = await fetchFunction({
        searchText: currentSearchTextRef.current,
        lastId: lastItem?.id,
        lastCode: lastItem?.code
      })

      const { items = [], hasNextPage = false } = action || {}
      const updatedResults = searchResults.concat(items || [])

      setSearchResults(updatedResults)
      setSearchPageInfo({ hasNextPage, loading: false })

      return items
    } catch (error) {
      console.error('搜索加载更多失败:', error)
      setSearchPageInfo({ hasNextPage: false, loading: false })
      return []
    }
  }

  return {
    searchMode,
    searchResults,
    searchPageInfo,
    currentSearchTextRef,
    handleSearch,
    handleSearchLoadMore
  }
} 