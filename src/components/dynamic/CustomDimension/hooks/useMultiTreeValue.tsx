import { isAllowCancelDependenceClearValue } from '../../../utils/fnInitalValue'
import { useGetByIds } from './useGetByIds'

interface UseMultiTreeValueOptions {
  field: any
  props: any
  dependenceHook: any
}

interface UseMultiTreeValueReturn {
  handleChange: (newValue: any[]) => void
  processMultiSelectDependence: (dependenceResult: any, options?: any) => Promise<any>
}

/**
 * 多选TreeSelect值处理Hook - 处理MutilDimensionListV2组件的多选逻辑
 */
export function useMultiTreeValue({ 
  field, 
  props, 
  dependenceHook 
}: UseMultiTreeValueOptions): UseMultiTreeValueReturn {
  const { value, onChange, form, billState, detailId, isDetail, bus } = props

  const handleChange = (newValue: any[]): void => {
    onChange(newValue)
  }

  const { getRecordLinkById } = useGetByIds(field, props, dependenceHook)

  // 处理多选依赖变化的特有逻辑
  const processMultiSelectDependence = async (
    dependenceResult: any, 
    options: any = {}
  ): Promise<any> => {
    const { autoAssignmentItem, items, matchDefaultValue } = dependenceResult
    const { allowCancelDependence } = field
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit

    let newValue = undefined
    if (isInitLoad) {
      const formValue = form.getFieldsValue()
      // 初始化进来 当前value 值赋值
      newValue = formValue[field?.field] ?? undefined
      onChange(newValue)
    } else if (!matchDefaultValue) {
      bus.setValidateLevel(1)
      if (autoAssignmentItem) {
        newValue = [autoAssignmentItem.id]
        onChange(newValue)
      } else if (value?.length) {
        const items = await getRecordLinkById(value)
        newValue = items.map((v: any) => v.id)
        if (isAllowCancelDependenceClearValue(allowCancelDependence, newValue?.length, value)) {
          onChange(newValue)
        }
      } else if (isAllowCancelDependenceClearValue(allowCancelDependence, true, value)) {
        onChange(undefined)
      }
    }

    return newValue
  }

  return {
    handleChange,
    processMultiSelectDependence
  }
} 