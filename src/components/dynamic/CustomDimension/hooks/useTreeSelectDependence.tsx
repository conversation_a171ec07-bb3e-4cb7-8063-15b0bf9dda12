import { useState, useRef } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { isObject, uuid } from '@ekuaibao/helpers'
import { get } from 'lodash'
import { SpecialAllowCancelDependenceUtils } from '../../types'
import { addFullPath } from '../../../utils/fnInitalValue'

interface UseTreeSelectDependenceOptions {
  field: any
  props: any
}

interface DependenceStatus {
  _isUseDep: boolean
  isSpecialCancelDependency: boolean
  dependenceViewAllData: boolean
}

interface UseTreeSelectDependenceReturn {
  dependenceList: any[]
  useDependenceData: boolean
  delegator: any
  hasEmitHandleDependenceChangeRef: React.MutableRefObject<boolean>
  getDependenceStatus: () => DependenceStatus
  getDependenceKey: () => string[]
  getFormValue: () => Promise<any>
  createFetchFunction: () => (query?: any) => Promise<any>
  processDependenceChange: (params: any, options?: any) => Promise<any>
  cancelDependence: () => void
  setCancelDependencyCallback: (callback: () => void) => void
  handleDelegatorChanged: (newDelegator: any) => Promise<void>
  formatData: (items: any[]) => any[]
  setDependenceList: React.Dispatch<React.SetStateAction<any[]>>
  setUseDependenceData: React.Dispatch<React.SetStateAction<boolean>>
  isLoading: boolean
}

/**
 * 树选择依赖关系Hook - 处理依赖关系的通用逻辑
 */
export function useTreeSelectDependence({ 
  field, 
  props 
}: UseTreeSelectDependenceOptions): UseTreeSelectDependenceReturn {
  const [dependenceList, setDependenceList] = useState([])
  const [useDependenceData, setUseDependenceData] = useState(true)
  const [delegator, setDelegator] = useState(null)
  const currentIdxRef = useRef(null)
  const cancelDependencyCallbackRef = useRef<(() => void) | null>(null)
  const hasEmitHandleDependenceChangeRef = useRef(false)
  const [isLoading, setIsLoading] = useState(false)

  const { bus, billSpecification, specificationId, submitterId, value } = props

  // 获取依赖键
  const getDependenceKey = (): string[] => {
    const { dependence, autoDependence } = field
    return (dependence || []).concat(autoDependence || [])?.map((d: any) => d.dependenceId) || []
  }

  // 获取表单值
  const getFormValue = async (): Promise<any> => {
    if (!bus) return {}
    const { feeType } = props
    const formData = await bus.getFieldsValue()
    const dependenceFormData: any = {}
    const useDependenceKeys = getDependenceKey()
    useDependenceKeys.forEach(key => {
      if(key === 'flow.FeeType') {
        dependenceFormData[key] = feeType?.id
      } else {
        dependenceFormData[key] = isObject(formData[key]) ? formData[key].id : formData[key]
      }
    })
    return dependenceFormData
  }

  // 获取依赖状态
  const getDependenceStatus = (): DependenceStatus => {
    const { allowCancelDependence = false, dependence } = field
    const isDependence = dependence && !!dependence.length
    // 是否使用依赖数据
    const _isUseDep = useDependenceData && isDependence
    // 特殊企业在可以取消依赖的情况下使用全量数据
    const isSpecialCancelDependency = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
    // 在非特殊企业允许使用依赖的情况下使用依赖数据查看全量数据
    const dependenceViewAllData =
      !isSpecialCancelDependency && allowCancelDependence && _isUseDep && dependenceList.length === 0
    return {
      _isUseDep,
      isSpecialCancelDependency,
      dependenceViewAllData
    }
  }

  // 创建获取数据的函数
  const createFetchFunction = () => {
    return async (query: any = {}): Promise<any> => {
      const dependenceFormData = await getFormValue()
      const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')

      const randomId = uuid(10)
      currentIdxRef.current = randomId // 标明promise的执行顺序

      const action = await api.invokeService('@bills:get:record:link:v2', {
        flowSpecId: billSpecification.id,
        detailSpecId: specificationId,
        dimensionItemFieldName: field.field,
        cancelRelation: query?.cancelRelation || false,
        batchApportion: false,
        submitterId: delegator?.id || submitterId?.id,
        form: dependenceFormData,
        entity,
        defaultValue: isObject(value) ? value.id : value,
        parentId: '',
        ...query
      })

      if (currentIdxRef.current === randomId) {
        return action
      }
    }
  }

  // 处理依赖变化的核心逻辑
  const processDependenceChange = async (
    { key, dependenceFeeType = false, id }: any, 
    options: any = { isInit: false }
  ): Promise<any> => {
    hasEmitHandleDependenceChangeRef.current = true
    const useDependenceKeys = getDependenceKey()

    if (!useDependenceKeys.includes(key)) {
      return null
    }

    setIsLoading(true)
    const action = await createFetchFunction()()
    const { autoAssignmentItem, items, matchDefaultValue } = action || {}

    const { isSpecialCancelDependency, _isUseDep } = getDependenceStatus()
    if (!isSpecialCancelDependency && _isUseDep) {
      setDependenceList(formatData(items || []))
    }

    setIsLoading(false)
    return action
  }

  // 取消依赖
  const cancelDependence = async (): Promise<void> => {
    setUseDependenceData(false)
    // 调用取消依赖的回调
    if (cancelDependencyCallbackRef.current) {
      setIsLoading(true)
      await cancelDependencyCallbackRef.current()
      setIsLoading(false)
    }
  }

  // 设置取消依赖回调函数
  const setCancelDependencyCallback = (callback: () => void): void => {
    cancelDependencyCallbackRef.current = callback
  }

  // 处理委托人变化
  const handleDelegatorChanged = async (newDelegator: any): Promise<void> => {
    setDelegator(newDelegator)
    const action = await createFetchFunction()({
      submitterId: newDelegator?.id
    })
    const items = action?.items || []
    setDependenceList(items)
  }

  // 格式化数据
  const formatData = (items: any[]): any[] => {
    const { isShowFullPath } = field
    isShowFullPath && addFullPath(items, '')
    return items
  }

  return {
    dependenceList,
    useDependenceData,
    delegator,
    hasEmitHandleDependenceChangeRef,
    getDependenceStatus,
    getDependenceKey,
    getFormValue,
    createFetchFunction,
    processDependenceChange,
    cancelDependence,
    setCancelDependencyCallback,
    handleDelegatorChanged,
    formatData,
    setDependenceList,
    setUseDependenceData, 
    isLoading
  }
} 