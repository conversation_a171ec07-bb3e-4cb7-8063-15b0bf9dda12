import { useEffect, useRef } from 'react'
import { getRefDefaultValue } from '../../../utils/fnInitalValue'
import { shouldUpdateValue } from '../../../utils/DependenceUtil'

interface UseSingleSelectValueOptions {
  field: any
  props: any
  value: any
  getValue: (item: any, triggerChange?: boolean) => Promise<any>
}

interface UseSingleSelectValueReturn {
  handleDefaultValue: (props: any) => void
}

export function useSingleSelectValue({
  field,
  props,
  value,
  getValue
}: UseSingleSelectValueOptions): UseSingleSelectValueReturn {
  const prevPropsRef = useRef<any>()

  // 处理默认值
  const handleDefaultValue = (props: any) => {
    const { value, field } = props
    const { dependence } = field
    const isDependence = dependence && dependence.length
    
    if (!value && !isDependence) {
      const defaultValue = getRefDefaultValue(props)
      if(defaultValue) {
        getValue(defaultValue, true)
      }
    }
  }

  // 处理props变化
  useEffect(() => {
    const { lastChoice } = props
    if (prevPropsRef.current && prevPropsRef.current.lastChoice !== lastChoice) {
      handleDefaultValue(props)
    }
    prevPropsRef.current = { ...prevPropsRef.current, lastChoice }
  }, [props.lastChoice])

  // 处理value变化
  useEffect(() => {
    const prevValue = prevPropsRef.current?.value
    if (value !== prevValue && typeof value === 'string') {
      getValue(value).then(result => {
        if(result) {
          props.onChange(result)
        }
      })
    }
    
    if (prevPropsRef.current && prevPropsRef.current.value !== value) {
      shouldUpdateValue(prevPropsRef.current, props)
    }
    prevPropsRef.current = { ...prevPropsRef.current, value }
  }, [value, field])

  return {
    handleDefaultValue
  }
} 