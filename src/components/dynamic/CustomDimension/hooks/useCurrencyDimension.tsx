import { app as api } from '@ekuaibao/whispered'
import { isObject } from '@ekuaibao/helpers'
import { handleGetDataById } from '../../../utils/fnInitalValue'

interface UseCurrencyDimensionOptions {
  field: any
  bus: any
  value?: any
  allStandardCurrency?: any[]
  legalEntityCurrencyPower?: any
}

interface UseCurrencyDimensionReturn {
  handleDimensionCurrencyChange: (currentValue: any, isInit?: boolean) => Promise<void>
  fnHandleDimentionCurrenncy: (valueData: any, flag?: boolean) => Promise<void>
  handleLegalEntityMultiCurrency: (items: any[], oldValue: any, newValue: any) => void
}

export function useCurrencyDimension({
  field,
  bus,
  value,
  allStandardCurrency,
  legalEntityCurrencyPower
}: UseCurrencyDimensionOptions): UseCurrencyDimensionReturn {

  // 处理维度货币变化
  const handleDimensionCurrencyChange = async (currentValue: any, isInit = false) => {
    const baseCurrencyId = currentValue?.form?.baseCurrencyId
    
    // 法人实体多币别变化处理
    if (field?.name === 'legalEntityMultiCurrency' && !isInit) {
      bus.emit('dimention:multi:currency:change')
    }
    
    // 如果有基础货币ID，处理货币和汇率
    if (field?.name === 'legalEntityMultiCurrency' && baseCurrencyId) {
      try {
        // 获取所有标准货币
        const currencies = allStandardCurrency || api.getState()['@common'].allStandardCurrency || []
        
        // 获取货币汇率
        const { items: rates = [] } = await api.invokeService(
          '@currency-manage:get:currency:rates:by:Id', 
          baseCurrencyId
        )
        
        // 找到对应的货币信息
        const currency = currencies.find((item: any) => item.id === baseCurrencyId)
        
        if (currency) {
          // 触发各种货币变化事件
          bus.emit('dimention:currency:change', { currency, rates })
          bus.emit('dimension:currency:change:payPlan:change', currency)
          
          // 更新维度货币状态
          api.invokeService('@bills:update:dimention:currency', { 
            currency, 
            rates, 
            dimention: currentValue 
          })
        }
      } catch (error) {
        console.error('处理货币维度变化时出错:', error)
      }
    }
  }

  // 完整的货币维度处理逻辑（从原RefCPV2复制）
  const fnHandleDimentionCurrenncy = async (valueData: any, flag?: boolean) => {
    let baseCurrencyId = valueData?.form?.baseCurrencyId
    const dimensionId = isObject(valueData) ? valueData.id : valueData
    const same = dimensionId === value?.id

    if (field?.name === 'legalEntityMultiCurrency' && !flag && !same && bus) {
      bus.emit('dimention:multi:currency:change', valueData)
    }

    if (
      !baseCurrencyId &&
      field?.name === 'legalEntityMultiCurrency' &&
      legalEntityCurrencyPower &&
      dimensionId?.length
    ) {
      const result = await handleGetDataById(field, dimensionId)
      baseCurrencyId = result?.form?.baseCurrencyId
    }

    if (field?.name === 'legalEntityMultiCurrency' && baseCurrencyId) {
      const { items: rates = [] } = await api.invokeService('@currency-manage:get:currency:rates:by:Id', baseCurrencyId)
      const currencies = allStandardCurrency || api.getState()['@common'].allStandardCurrency || []
      const currency = currencies.find((item: any) => item.id === baseCurrencyId)

      if (flag && bus) {
        bus.emit('dimention:currency:init', { currency, rates })
      } else if (bus) {
        bus.emit('dimention:currency:change', { currency, rates })
        bus.emit('dimension:currency:change:payPlan:change', currency)
      }

      api.invokeService('@bills:update:currencyRatesList', {
        items: (rates || []).concat({ ...currency, rate: currency?.rate || 1 })
      })
      api.invokeService('@bills:update:dimention:currency', { currency, rates, dimention: valueData })
    }
  }

  // 处理多币种法人实体
  const handleLegalEntityMultiCurrency = (items: any[], oldValue: any, newValue: any) => {
    if (field?.name === 'legalEntityMultiCurrency' && !!items?.length && bus) {
      if (items.length === 1) {
        const item0 = items[0]
        item0?.id !== oldValue?.id && bus.emit('dimention:multi:currency:change')
      } else if (oldValue?.id !== newValue?.id) {
        bus.emit('dimention:multi:currency:change')
      }
    }
  }

  return {
    handleDimensionCurrencyChange,
    fnHandleDimentionCurrenncy,
    handleLegalEntityMultiCurrency
  }
} 