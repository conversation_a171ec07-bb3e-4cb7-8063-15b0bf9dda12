import { useState } from 'react'

import { useGetByIds } from './useGetByIds'
import { findNodeById } from '../utils/helper'
import { useDepValueEffect } from './useDepValEffect'

interface Item {
  id: string
  [key: string]: any
}

export const useDisplayValue = (value: any, field: any, props: any, dependenceHook: any, dataSource: any, autoAssignmentItem?: any) => {
  const [displayValue, setDisplayValue] = useState<string | string[] | null>()

  const { getRecordLinkById } = useGetByIds(field, props, dependenceHook)

  const isAutoAssignmentItem = (item: string | Item) => {
    if(typeof item === 'object' && item.id && item.id === autoAssignmentItem?.id) return true
    return false
  }

  const getRealItem = (item: string | Item): Item | string | null => {
    const id = typeof item === 'string' ? item : item.id
    const realItem = findNodeById(id, dataSource)
    if(realItem) return id

    if(isAutoAssignmentItem(item)) return item

    return null
  }

  const getId = (item: string | Item) => {
    if(typeof item === 'object' && item.id) return item.id
    return item as string
  }

  const getItemByRemote = async (ids: string[]) => {
    return getRecordLinkById(ids)
  }

  const getAllItems = async (item: string | Object | Array<string | Object>) => {
    if(Array.isArray(item)) {
      const ids = item.map(getId)
      const items1 = item.map(getRealItem).filter(Boolean)
      const remoteIds = ids.filter(id => !items1.some(item => item === id || (typeof item === 'object' && item.id === id)))
      if(remoteIds.length) {
        const remoteItems = await getItemByRemote(remoteIds)
        return ids.map(id => items1.concat(remoteItems).find(item => item === id || (typeof item === 'object' && item.id === id))).filter(Boolean)
      }
      return items1
    }

    const realItem = getRealItem(item as Item | string)
    if(realItem) return realItem

    const itemId = getId(item as Item | string)
    const remoteItems = await getItemByRemote([itemId])
    return remoteItems?.[0]
  }

  const format = (item: Item, field: any) => {
    if(!item) return undefined
    // 为string 说明可以回显，不需要格式化
    if(typeof item === 'string') return item
    const { isShowFullPath, hideCode } = field
    const { code, fullPath, name, enName } = item

    const showName = i18n.currentLocale === 'en-US' && enName ? enName : name
    const displayName = isShowFullPath && fullPath ? `${fullPath} / ${showName}` : showName
    const fakeLabel = !hideCode && code 
      ? i18n.get(`{__k0}({__k1})`, { __k0: displayName, __k1: code }) 
      : displayName

    return fakeLabel
  }

  const formatDisplayValue = (items: Item[] | Item, field: any) => {
    if(Array.isArray(items)) {
      return items.map(item => format(item, field))
    }

    return format(items, field)
  }

  const getValue = async (item: string | Object | Array<string | Object>, triggerChange: boolean = false) => {
    const items = await getAllItems(item)
    triggerChange && props?.onChange(items)
    return items
  }

  const getDisplayValue = async (item: string | Object | Array<string | Object>) => {
    if(!item) {
      setDisplayValue(undefined)
      return
    }
    const items = await getValue(item)
    const displayValue = formatDisplayValue(items, field)
    setDisplayValue(displayValue)
    return displayValue
  }

  useDepValueEffect(value, () => {
    getDisplayValue(value)
  })

  useDepValueEffect(dataSource, () => {
    getDisplayValue(value)
  })

  useDepValueEffect(field, () => {
    getDisplayValue(value)
  })

  useDepValueEffect(autoAssignmentItem, () => {
    getDisplayValue(value)
  })

  return {
    getValue,
    getDisplayValue,
    displayValue
  }
}
