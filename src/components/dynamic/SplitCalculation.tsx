import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './SplitCalculation.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { Table, Button, Select } from 'antd'
import Money from '../../elements/puppet/Money'
import { parseFormValueAsParam } from '../../plugins/bills/util/parse'
import EmptyBody from '../../plugins/bills/elements/EmptyBody'
import { formatSplitDetail } from '../utils/fnFormatSplitDetailTable'
import { required } from '../validator/validator'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { showMessage } from '@ekuaibao/show-util'
import { cloneDeep } from 'lodash'

const withLoader = api.require('@elements/data-grid-v2/withLoader')

const SplitTable = withLoader(() => import('../../elements/puppet/SplitTable'))

export type header = { field: string, name: string, show: boolean, type: string, order: number }
export type cellValue = { field: string, value: any[], type: any }
type State = { headers?: header[], result?: cellValue[], total?: number, totalAmount?: number, showCount: boolean, showDetail: boolean, isDisabledBtn: boolean, splitGroupList: any[], selectedGroup: any, inactiveGroup: any }
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'splitCalculation'
    },
    validator: (field, props) => (rule, value, callback) => {
        if (rule.level > 0) {
            return callback()
        }
        callback(required(field, value))
    }
})
export default class SplitCalculation extends React.Component<any, State> {

    constructor(props) {
        super(props)
        const { value } = props
        this.state = {
            splitGroupList: [],
            inactiveGroup: undefined,
            selectedGroup: value?.splitGroupId,
            showCount: false,
            isDisabledBtn: false,
            showDetail: false
        }
    }
    componentWillMount() {
        this.loadSplitData()
    }

    loadSplitData = async () => {
        const { field, value } = this.props
        const { selectedGroup } = this.state
        const splitRuleId = field?.splitRuleIds[0]
        const res = await Fetch.GET(`/api/form/v2/customSplitGroup/$${splitRuleId}`)
        const activeItems = res.items.filter(el => el.active)
        if (activeItems.length == 1 && !selectedGroup) {
            this.setState({ splitGroupList: activeItems, selectedGroup: activeItems[0].id })
        } else {
            const selectedItem =  res.items.find(el => el.id === selectedGroup)
            if (selectedItem && !selectedItem.active) {
                this.setState({ splitGroupList: activeItems, inactiveGroup: `${selectedItem.fieldName}(${i18n.get('已删除')})` })
            } else {
                this.setState({ splitGroupList: activeItems })
            }
        }

        if (value && value.splitResultId) {
            const splitResult = await Fetch.GET(`/api/form/v2/customSplitResult/$${value.splitResultId}`)
            if (splitResult.value) {
                this.setState({ ...splitResult.value.splitResult, showCount: true })
            }
        }
    }

    handelSplit = async () => {
        const { bus, baseDataProperties, billSpecification, field, onChange } = this.props
        const { selectedGroup } = this.state
        const billData = await bus?.getValue()
        const param = parseFormValueAsParam(billData, billSpecification, undefined, baseDataProperties)
        try {
            this.setState({ isDisabledBtn: true, showCount: false, headers: undefined, result: undefined, total: undefined, totalAmount: undefined })
            const splitData = await Fetch.POST('/api/form/v2/customSplitRule/splitCalculation', {}, {
                body: {
                    billData: { ...param?.form },
                    splitRuleId: field?.splitRuleIds[0],
                    splitGroupId: selectedGroup
                }
            })
            if (!!splitData.value) {
                this.setState({ ...splitData.value, showCount: true, isDisabledBtn: false })
                onChange && onChange({ splitRuleId: field?.splitRuleIds[0], splitGroupId: selectedGroup })
                return
            }
            this.setState({ isDisabledBtn: false, showCount: false, headers: undefined, result: undefined, total: undefined, totalAmount: undefined })
            onChange && onChange()
        } catch (e) {
            this.setState({ showCount: false, isDisabledBtn: false, headers: undefined, result: undefined, total: undefined, totalAmount: undefined })
            onChange && onChange()
            showMessage.error(e.message)
            console.log(e.message)
        }
        
    }

    handelShowDetail = () => {
        this.setState({ showDetail: !this.state.showDetail })
    }

    handleSelectGroup = (value) => {
        this.setState({ selectedGroup: value })
    }

    renderTable = () => {
        const { headers, result, total } = this.state
        const { dataSource, columns, secondCols } = formatSplitDetail(headers, result, total)
        return (
            <div className="table-c">
                <SplitTable
                    rowKey="key"
                    bordered
                    pagination={false}
                    secondCols={secondCols}
                    dataSource={dataSource}
                    columns={columns}
                    scroll={{ x: true }}
                />
            </div>

        )
    }

    renderEmpty = () => {
        return <EmptyBody label={i18n.get('当前明细未命中补贴维度或数据为空')} />
    }
    renderError = () => {
        const { form, field } = this.props
        const { name } = field
        const { getFieldError } = form
        const error = getFieldError(name)
        if (!error) {
            return null
        }
        return (
            <div className="error">
                {i18n.get('费用拆分明细不可为空')}
            </div>
        )
    }

    render() {
        const { value, field, billSpecification } = this.props
        const { total, totalAmount, headers, showCount, showDetail, splitGroupList, selectedGroup, inactiveGroup, isDisabledBtn } = this.state
        const selectGroupItem = selectedGroup && splitGroupList.find(el => el.id === selectedGroup)
        const selectGroupVal = selectGroupItem ? selectGroupItem.fieldName : inactiveGroup
        return (
            <div className={styles['split-detail']}>
                <div className="split-group-title">{i18n.get('费用拆分明细')}</div>
                <div className="desc">
                    {i18n.get('该明细根据个人费标规则按照「同行人」 自动拆分')}
                </div>
                <div className="split-group-label">
                    { !field.optional && <span className="split-label-optional">*</span> }
                    <span>{fnGetFieldLabel(field)}</span>
                </div>
                <div className="split-header">
                    <Select placeholder={i18n.get('请选择拆分方式')} onChange={this.handleSelectGroup} className="split-group-select" value={selectGroupVal}>
                        {splitGroupList.map(el => (
                            <Select.Option key={el.id} value={el.id}>{el.fieldName}</Select.Option>
                        ))}
                    </Select>
                    <Button type="primary" disabled={!selectedGroup || isDisabledBtn} size="large" onClick={this.handelSplit}  >{i18n.get('拆分计算')}</Button>
                </div>
                {this.renderError()}
                {
                    showCount && (
                        <div className="detals-count mt-16">
                            <div>{i18n.get('费用总金额: ')} <Money style={{ display: 'inline-block', fontWeight: 600 }} value={totalAmount || 0} /></div>
                            <div>
                                <span className="detals-total">{i18n.get('总计 {count} 条', { count: total || 0 })}</span>
                                <a onClick={this.handelShowDetail}>{showDetail ? i18n.get('收起') : i18n.get('查看详情')}</a>
                            </div>
                        </div>
                    )
                }
                {
                    showDetail && (
                        <div className="detail-table">
                            {headers && total ? this.renderTable() : this.renderEmpty()}
                        </div>
                    )
                }
            </div>
        )
    }
}
