/**************************************************
 * Created by zhaohuabing on 2018/6/9 下午2:06.
 **************************************************/

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Col, Spin } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import styles from './VPhoto.readonly.module.less'

@EnhanceField({
  descriptor: {
    name: 'vphoto_order'
  }
})
export default class VPhoto extends PureComponent {
  state = { data: [] }

  componentWillMount() {
    const { flowId } = this.props
    api.invokeService('@bills:get:VPhoto:data', flowId).then(v => {
      this.setState({ data: v.items })
    })
  }

  renderDetail = data => {
    return (
      <div>
        {data.map((v, index) => {
          return (
            <div key={index}>
              <span>{v.field !== 'orderId' ? v.label + i18n.get('：') : ''}</span>
              {this.getStatisticalData(v.value)}
            </div>
          )
        })}
      </div>
    )
  }

  getStatisticalData = t => {
    return Array.isArray(t)
      ? t.map((t, index) => {
          return (
            <p key={index}>
              {t.map((c, index) => {
                return `${c.value}${c.label ? i18n.get(`（{__k0}）`, { __k0: c.label }) : ''}${index === t.length - 1 ? '' : i18n.get('，')}`
              })}
            </p>
          )
        })
      : t
  }

  render() {
    let { label } = this.props.field
    let { data } = this.state
    return (
      <div className={styles['vphoto-wrapper']}>
        <Col span={4}>
          <span className="vphoto-label">{label}{i18n.get("：")}</span>
        </Col>
        {data.length === 0 ? <Spin /> : <Col span={15}>{this.renderDetail(data)}</Col>}
      </div>
    )
  }
}
