.expenselink-wrapper {
  :global {
    .new-requisition-wrapper {
      width: 100%;
      border-radius: 8px;
      border: 1px solid var(--eui-bg-body-overlay, #F7F8FA);
      margin-top: 16px;
      background: var(--eui-bg-body-overlay, #F7F8FA);

      &:hover {
        background-color: var(--eui-bg-base, #F2F3F5);
        border: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.10));
        :global {
          .close-button {
            visibility: visible;
            cursor: pointer;
          }
        }
      }
    }


    .mt_24 {
      margin-top: 24px;
    }

    .item {
      display: flex;
      width: 100%;
      justify-content: space-between;
      border: 1px solid #e6e6e6;
      padding: 12px;
      margin-top: 12px;
      border-radius: 4px;
      line-height: 24px;

      &:hover {
        box-shadow: 0 1px 6px 0 hsla(0, 0%, 80%, 0.65);
      }

      .l1 {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
      }

      .l2 {
        font-size: 12px;
        font-weight: 400;
        color: rgba(29, 43, 61, 0.75);
      }

      .requisition-money {
        font-weight: 500;
      }

      .label {
        font-size: 12px;
        font-weight: 400;
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
        line-height: 20px;
      }

      .right {
        text-align: right;
      }
    }

    .plus-button {
      display: inline-block;
      padding: 0 20px;
      //width: 150px;
      height: 22px;
      background: rgba(255, 255, 255, 1);
      border-radius: 2px;
      border: 1px solid var(--brand-base);
      font-size: 12px;
      color: var(--brand-base);
      line-height: 22px;
      //display: flex;
      //justify-content: center;
      //align-items: center;
      cursor: pointer;

      &.disabled {
        border-radius: 2px;
        background-color: #f7f7f7;
        border: solid 1px #ebebeb;
        color: #9e9e9e;
        cursor: not-allowed;
      }

      .img {
        margin-right: 4px;
      }
    }

    .import-details {
      display: flex;
      justify-content: space-between;
      min-height: 34px;
      border-radius: 2px;
      background-color: #f1f9fd;
      border: solid 1px #dcf0fa;
      align-items: center;
      margin-top: 4px;
      padding: 3px;

      .import-actions {
        flex-shrink: 0;
      }

      .title {
        flex: 1;
        display: flex;
        align-items: center;
        padding-left: 13px;
        font-size: 12px;
        color: #333333;

        .tip-icon {
          color: var(--brand-base);
          margin-right: 5px;
        }

        .span-info {
          width: 90%;
          line-height: 1.5;
        }
      }
    }

    .import-font {
      flex-shrink: 0;
      min-width: 60px;
      font-size: 12px;
      color: var(--brand-base);
      cursor: pointer;
    }
  }
}

.expense-link-readonly {
  width: 100%;
  // TODO 样式修改
  // max-width: 736px;

  :global {
    .new-requisition-wrapper {
      width: 100%;
      border-radius: 8px;
      border: 1px solid var(--eui-bg-body-overlay, #F7F8FA);
      margin-top: 16px;
      background: var(--eui-bg-body-overlay, #F7F8FA);

      &:hover {
        background-color: var(--eui-bg-base, #F2F3F5);
        border: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.10));
        :global {
          .close-button {
            visibility: visible;
            cursor: pointer;
          }
        }
      }

      &:first-child {
        margin-top: 0;
      }
    }


    .check-detail {
      flex-shrink: 0;
      width: 60px;
      font-size: 12px;
      color: var(--brand-base);
      cursor: pointer;
    }
  }
}

:global {
  .bill-field-display-mode-horizontal {
    .new-requisition-wrapper:first-child {
      margin-top: 0;
    }
  }
}
