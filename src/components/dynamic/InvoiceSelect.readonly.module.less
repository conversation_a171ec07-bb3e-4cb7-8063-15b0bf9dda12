.invoice-select-content {
  width: 100%;
  line-height: 24px;
  :global {
    .invoice-type-title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .invoice-type {
        margin-right: 14px;
      }
      .add-invoice, .submit-risk-reason {
        margin-right: 14px;
      }
      
      .unify-invoice {
        margin-left: -10px;
      }
    }
  }
  .invoice-select-label {
    display: flex;
    align-items: center;
    .invoice-label-title {
      color: rgba(29, 43, 61, 0.5);
    }
    .invoice-review-button {
      margin-left: 8px;
      color: var(--brand-base);
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      flex: 1;
      .sort_wrapper {
        width: 240px;
        display: flex;
        justify-content: space-between;
        .no_checked {
          color: rgba(29, 43, 61, 0.5);
          cursor: pointer;
        }
        .sort_checked {
          color: var(--brand-base);
          cursor: pointer;
        }
      }
      > span,
      > img {
        vertical-align: middle;
      }
      
    }
  }
}
