import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
import styles from './LoanRuleTemplate.module.less'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { TreeSelect } from 'antd'
import { wrapper } from '../layout/FormWrapper'

const TreeNode = TreeSelect.TreeNode

interface Props {
  specifications: any
  onChange: Function
  field: any
  value: any
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'loan-rule-template'
  },
  // validator: (field: any) => (rule: any, value: any, callback: any) => {
  // return callback(undefined)
  // },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  specifications: state['@custom-specification'].specificationGroups
}))
export default class LoanRuleTemplate extends PureComponent<Props, State> {
  componentDidMount() {
    app.invokeService('@custom-specification:get:specificationGroups')
    const { onChange, value } = this.props
    if (!value) {
      onChange && onChange([])
    }
  }

  handleOnChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  fnGetGroupData = () => {
    const { specifications } = this.props
    const type = ['requisition', 'loan']
    const groupData = specifications.map(group => {
      group.specifications = group.specifications.filter(spc => {
        return !(type.indexOf(spc.type) === -1 || !spc.active)
      })
      return group
    })
    return groupData
  }

  renderTreeNode(groupData = [], disabled) {
    const loop = (data, parentNode) =>
      data.map(item => {
        item.children = item.children || item.specifications || []
        if (item.children.length > 0) {
          return (
            <TreeNode key={item.id} disabled={!item.active} value={item.id} title={item.name}>
              {loop(item.children, false)}
            </TreeNode>
          )
        } else {
          return (
            <TreeNode
              key={item.id}
              disabled={(parentNode && disabled) || !item.active}
              value={item.id}
              title={item.name}
            />
          )
        }
      })
    return loop(groupData, true)
  }

  render() {
    const { field, value } = this.props
    const { description, placeholder } = field
    const groupData = this.fnGetGroupData()
    return (
      <div className={styles['loan-rule-template-wrapper']}>
        <TreeSelect
          className="fee-type-tag"
          placeholder={placeholder || i18n.get('请选择')}
          showSearch
          multiple
          size={'large'}
          treeNodeFilterProp="title"
          notFoundContent={i18n.get('没有匹配结果')}
          treeCheckable={true}
          value={value}
          dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
          onChange={this.handleOnChange}
        >
          {this.renderTreeNode(groupData, true)}
        </TreeSelect>
        {description && <div className="description">{description}</div>}
      </div>
    )
  }
}
