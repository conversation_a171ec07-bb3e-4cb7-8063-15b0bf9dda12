/**************************************************
 * Created by zhaohuabing on 2018/6/9 下午3:57.
 **************************************************/

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input, Spin, Col } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { app as api } from '@ekuaibao/whispered'
import { showMessage } from '@ekuaibao/show-util'
import styles from './VPhoto.module.less'
import { EnhanceConnect } from '@ekuaibao/store'

@EnhanceField({
  descriptor: {
    name: 'vphoto_order'
  },
  validator: field => (rule, value, callback) => {
    if (!value) {
      return callback(i18n.get('请输入订单号'))
    }
    if (!!~value.indexOf(' ')) {
      return callback(i18n.get('请勿输入空格'))
    }
    callback(required(field, value))
  },
  wrapper: wrapper(false, {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 }
  })
})
@EnhanceConnect(state => ({
  staff: state['@common'].userinfo.staff
}))
export default class VPhoto extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      spinVisible: false,
      disabled: !!props.value
    }
  }

  onChange = e => {
    let { onChange, form, field } = this.props
    onChange && onChange(e.target.value)
    form.validateFields([field.name])
  }

  handleClick = () => {
    this.state.disabled ? this.handEmpty() : this.handleVerify()
  }

  handEmpty = () => {
    this.props.onChange('')
    this.setState({ disabled: false })
  }

  handleVerify = () => {
    const { staff, form, field, value } = this.props
    const { spinVisible } = this.state
    form.validateFields([field.name])
    if (!value || !!~value.indexOf(' ') || spinVisible) return
    if (staff && staff.cellphone) {
      this.setState({ spinVisible: true })
      api.invokeService('@bills:verify:VPhoto:orders', value).then(
        _ => {
          showMessage.success(i18n.get('验证无误'), 2)
          this.setState({ disabled: true, spinVisible: false })
        },
        err => {
          this.setState({ spinVisible: false })
          showMessage.error(err.msg, 2)
        }
      )
    } else {
      this.handleBindPhone()
    }
  }

  handleBindPhone = () => {
    const { staff } = this.props
    let user = {}
    user.bankCardNums = staff.bankCardNums
    user.defaultDepartment = staff.defaultDepartment.id
    user.email = staff.email
    user.note = staff.note
    user.id = staff.id
    api.open('@user-info:CheckUserModal', { user, type: 'VPhoto', disabled: false })
  }

  render() {
    const { disabled, spinVisible } = this.state
    const { value } = this.props
    const butText = disabled ? i18n.get('清空') : i18n.get('验证')
    return (
      <div className={styles['VPhoto']}>
        <Col span={18}>
          <Input
            size="large"
            value={value}
            disabled={disabled}
            onChange={this.onChange}
            placeholder={i18n.get('请输入订单号')}
          />
        </Col>
        <Col span={6}>
          {spinVisible ? (
            <Spin size="small" className="VPhoto-inquire" />
          ) : (
            <span className="VPhoto-inquire" onClick={this.handleClick}>
              {butText}
            </span>
          )}
        </Col>
      </div>
    )
  }
}
