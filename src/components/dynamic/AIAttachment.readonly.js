/**************************************************
 * Created by nany<PERSON>ingfeng on 10/07/2017 15:41.
 **************************************************/
import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { app as api } from '@ekuaibao/whispered'
import AttachmentList from '../../elements/puppet/attachmentList/AttachmentList'
import { wrapper } from '../layout/FormWrapper'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { BasePureComponent } from './BasePureComponent'
import { FetchAttachment } from '../utils/FetchAttachment'

const download = api.invokeServiceAsLazyValue('@bills:file:download')
@EnhanceField({
  descriptor: {
    type: 'aiAttachments'
  },
  wrapper: wrapper(true)
})
@FetchAttachment
export default class AIAttachments extends BasePureComponent {
  handleFileDownload = line => {
    download()(line)
  }
  render() {
    let { value } = this.props
    let fileList = fnFormatAttachment(value)
    return (
      <div style={{ width: '100%' }}>
        <AttachmentList
          isEdit={false}
          fileList={fileList}
          onFileDownload={this.handleFileDownload}
          onFilePreview={this.handleFilePreview}
        />
      </div>
    )
  }
}
