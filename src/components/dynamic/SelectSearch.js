/**
 * Created by <PERSON><PERSON><PERSON> on 2017/11/6.
 */

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { getRecordLink } from './helpers/getRecordLink'
import SelectSearchComp from './SelectSearchComp'
import SelectSearchCompV2 from './CustomDimension/SelectSearchCompV2Refactored'
import { enableRecordOptimization } from '../../lib/featbit/feat-switch'
@EnhanceField({
  descriptor: {
    type: 'select_search'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(
  state => ({
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class RefSelectSearch extends PureComponent {
  isFlow = () => {
    const { businessType } = this.props
    return ['FLOW', 'DETAILS'].includes(businessType)
  }

  // 是否是自定义档案
  isDimension = () => {
    const { field, type } = this.props
    return field?.dataType?.entity?.startsWith('basedata.Dimension') || type?.startsWith('ref:basedata.Dimension')
  }

  render() {
    if (this.isFlow() && enableRecordOptimization() && this.isDimension()) {
      return <SelectSearchCompV2 {...this.props} />
    }
    return <SelectSearchComp {...this.props} />
  }
}
