@import '~@ekuaibao/eui-styles/less/token';

.association-wrapper {
  width: 100%;
  min-height: 70px;
  max-width: 400px;
  background: #FFFFFF;
  border: 1px solid #E6E6E6;
  box-sizing: border-box;
  border-radius: 6px;
  display: flex;
  overflow: hidden;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 16px;
  padding-top: 0;
  :global {
    .top_view {
      margin-top: 16px;
      font-size: 14px;
      color: #333333;
      line-height: 100%;

      .sub-title {
        font-size: 12px;
        margin-top: 8px;
        color: #666666;
      }

      .bind-title {
        font-size: 12px;
        color: #666666;
      }

      .group-name {
        font-size: 15px;
        margin-top: 8px;
        word-break: break-all;

        img {
          margin-right: 8px;
        }

      }

    }

    .item {
      width: 100%;
      min-width: 188px;
      height: 40px;
      border-radius: 4px;
      display: flex;
      align-items: center;
    }

    .add {
      color: var(--brand-base);
      margin-top: 4px;
      font-size: 14px;
      line-height: 20px;
      padding-left: 8px;
      cursor: pointer;

      .add-staff {
        margin-left: 8px;
      }
    }



    .content-view {
      width: 100%;
      max-height: 180px;
      overflow: auto;

      .data {
        padding: 0 8px;
        margin-bottom: 4px;
        background: rgba(78, 89, 105, 0.04);
        justify-content: space-between;

        .left {
          display: flex;
          align-items: center;

          img {
            width: 24px;
            height: 24px;
            background: #EBECEE;
            border-radius: 24px;
            display: block;
            overflow: hidden;
          }

          .name {
            font-size: 14px;
            line-height: 20px;
            color: rgba(39, 46, 59, 0.96);
            margin-left: 8px;
          }
        }

        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 4px 8px;
          background: rgba(39, 46, 59, 0.04);
          border: 1px solid rgba(39, 46, 59, 0.08);
          border-radius: 4px;
          font-size: 12px;
          line-height: 16px;
          color: rgba(39, 46, 59, 0.48);
        }
      }

    }

    .ant-btn {
      margin-top: 15px;
      width: 100px;
      height: 36px;
      border-radius: 4px;
      font-size: 14px;
      text-align: center;
      &.btn-unlink {
        background-color: #FFF7F5;
        color: #F76560;
        border: none;
      }
      &.btn-go-dd {
        background-color: var(--brand-base);
        color: #fff;
        border: none;
        margin-right: 8px;
      }
    }
  }
}