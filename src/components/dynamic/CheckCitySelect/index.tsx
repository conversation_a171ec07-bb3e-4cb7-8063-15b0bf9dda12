import React, { useState, useCallback, useEffect } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import { app } from '@ekuaibao/whispered'
import { Checkbox, Radio } from 'antd'
import { cloneDeep } from 'lodash'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { OrderSelect } from '../OrderConfigRules/OrderSelect'
const { Group } = Radio
interface IOrderConfigRules {
  [key: string]: any
}
// 通讯录|单据城市字段
type IBaseCityRuleType = 'ADDRESS_BOOK' | 'FLOW_FIELD'
// 过滤城市全局字段
const getCityField = globalFields =>
  globalFields
    .filter(i => i.dataType.entity === 'basedata.city' && i.dataType.type === 'ref' && i.ability !== 'trip')
    .map(i => ({ name: i.label, id: i.name }))
const CheckCitySelect: React.FC<IOrderConfigRules> = (props: IOrderConfigRules) => {
  const { form, bus, value, travelEdit = false, onChange, globalFields } = props
  let val = cloneDeep(value ?? {})
  const { checkBaseCity = false, baseCityRuleType = 'ADDRESS_BOOK', baseCityField = '' } = val
  const changeBaseCity = e => {
    val.checkBaseCity = e.target.checked
    val.baseCityRuleType = 'ADDRESS_BOOK'
    val.baseCityField = ''
    onChange?.(val)
  }
  const changeCityRuleType = e => {
    val.baseCityRuleType = e.target.value
    val.baseCityField = ''
    onChange?.(val)
  }
  const changeCityField = value => {
    val.baseCityField = value
    onChange?.(val)
  }
  return (
    <div>
      <Checkbox checked={checkBaseCity} disabled={!travelEdit} onChange={changeBaseCity}>
        {i18n.get('常驻地')}
      </Checkbox>
      {!!checkBaseCity && (
        <Group className="ml-10" disabled={!travelEdit} value={baseCityRuleType} onChange={changeCityRuleType}>
          <Radio value={'ADDRESS_BOOK'}> {i18n.get('取通讯录常驻地')}</Radio>
          <Radio value={'FLOW_FIELD'}>
            {i18n.get('取单据字段')}
            <OrderSelect
              disabled={!travelEdit || baseCityRuleType !== 'FLOW_FIELD'}
              options={getCityField(globalFields)}
              onChange={changeCityField}
              value={baseCityField}
            ></OrderSelect>
          </Radio>
        </Group>
      )}
    </div>
  )
}
export default EnhanceField(({
  descriptor: {
    type: 'check-city-select'
  },
  wrapper: wrapper()
} as unknown) as IComponentDef)(CheckCitySelect)
