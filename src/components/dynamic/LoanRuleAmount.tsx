import React, { PureComponent } from 'react'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceField } from '@ekuaibao/template'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { moneyStrToStandardCurrencyMoney } from '@ekuaibao/money-math'
import { validatorMoney, required } from '../validator/validator'
import CurrencyMoney from '../../elements/currency/currency-money'

interface Props {
  value: any
  onChange: Function
}

interface State {
  data: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'loan-rule-amount'
  },
  validator: (field: any) => (rule: any, value: any, callback: any) => {
    const { max, min, label } = field
    if (!value || value.standard === '' || value.standard === undefined) {
      const errStr = i18n.get('not-empty', { label: i18n.get(label) })
      return callback(errStr)
    }
    if (value && value.standard) {
      validatorMoney(value, max, min, callback, field)
      callback(required(field, value))
    }
    return callback(undefined)
  },
  wrapper: wrapper()
})
export default class LoanRuleAmount extends PureComponent<Props, State> {
  bus: MessageCenter = new MessageCenter()
  
  constructor(props: Props) {
    super(props)
    const { value } = this.props
    const money = this.moneyStr2StandardMoneyValue(0)
    const data = !value || value.standard === '' || value.standard === undefined ? money : value
    this.state = { data }
  }

  componentDidMount() {
    const { value, onChange } = this.props
    const { data } = this.state
    if (!value) {
      onChange && onChange(data)
    }
  }

  moneyStr2StandardMoneyValue = (money: any) => {
    const standCurrency = api.getState('@common.standardCurrency')
    return moneyStrToStandardCurrencyMoney(money, standCurrency)
  }

  valueChange = (value: any) => {
    const { data } = value
    const { onChange } = this.props
    onChange && onChange(data)
  }

  render() {
    const { data } = this.state
    return (
      <div>
        <div>{i18n.get('上述员工使用上述模板借款时，每人持有的借款金额不得超过：')}</div>
        <CurrencyMoney value={data} bus={this.bus} valueChange={this.valueChange} />
      </div>
    )
  }
}
