/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020-03-19 11:38:33
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2020-06-28 16:14:34
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select } from 'antd'
const { Option } = Select
import { wrapper } from '../layout/FormWrapper'
import styles from './payment.module.less'
import { app as api } from '@ekuaibao/whispered'
const getEnums = api.invokeServiceAsLazyValue('@supplier-file:get:supplier:enums')

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'payment'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (!value || !value.opportunity || (value.opportunity === 'UNIFY' && !value.period)) {
      return callback(i18n.get('供应商类型为必填'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class Payment extends PureComponent<any> {
  handlePeriodChanged = id => {
    const { value = {}, onChange } = this.props
    value.period = id
    onChange(value)
  }

  handleOpprtunityChange = id => {
    const { bus, value = {}, onChange } = this.props
    value.opportunity = id
    if (value.opportunity ==='NOSETTLEMENT'&&value.period){
      delete value.period
    }
    onChange({ ...value, opportunity: id })
    bus.emit('opportunity:changed', id, value)
  }

  render() {
    const { value = {}, field, isEdit } = this.props
    const { opportunity, period } = getEnums()
    return (
      <div>
        <div className={styles.wrapper}>
          <Select
            className="select"
            disabled={!field.editable}
            value={value.opportunity}
            placeholder={i18n.get("请选择结算方式")}
            onSelect={this.handleOpprtunityChange}
          >
            {opportunity.map(v => {
              return <Option value={v.name}>{v.label}</Option>
            })}
          </Select>
          {value.opportunity === 'UNIFY' && (
            <Select
              className="select ml"
              disabled={!field.editable}
              value={value.period}
              placeholder={i18n.get("请选择结算周期")}
              onSelect={this.handlePeriodChanged}
            >
              {period.map(v => {
                return <Option value={v.name}>{v.label}</Option>
              })}
            </Select>
          )}
        </div>
        {!isEdit && <div className={styles.tip}>{i18n.get('创建后不可修改')}</div>}
      </div>
    )
  }
}
