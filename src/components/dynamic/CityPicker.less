/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-03-18 15:33:39
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-05-19 17:35:08
 */
@import '~@ekuaibao/eui-styles/less/token.less';
.rc-select-dropdown {
  border: 0 !important;
  z-index: 1000 !important;
  box-shadow: none !important;
  background: none !important;
}

.city-picker {
  .rc-select-selection {
    border-radius: 2px;
  }

  .rc-select-enabled,
  .rc-select-focused {
    .rc-select-selection {
      &:hover {
        border-color: var(--brand-5);
        box-shadow: 0 0 0 2px rgba(70, 106, 110, 0.2);
      }
    }
  }

  .rc-select-disabled .rc-select-selection {
    background: #f7f7f7;
  }
  .rc-select-disabled .rc-select-selection--single,
  .rc-select-disabled .rc-select-selection__choice__remove {
    color: @color-black-2;
  }
  // 增加css优先级
  .rc-select {
    width: 375px;
    .rc-select-selection__choice__remove {
      top: -1px;
      right: 3px;
      font-size: 14px;
    }
  }

  // 批量编辑表格里是简单模式，宽度和单元格同宽
  &.simple-city-picker {
    .rc-select {
      width: 100% !important;
    }
  }

  .rc-select-focused {
    .rc-select-selection {
      border-color: var(--brand-5);
      box-shadow: 0 0 0 2px var(--brand-fadeout-20);
    }
  }

  .rc-select-selection__clear {
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    font-size: 18px;
    color: rgba(156, 156, 156, 0.5);
    width: 12px;
    height: 12px;
    line-height: 12px;
    cursor: pointer;
    margin-top: 6px;
    margin-right: -10px;
  }
}

.large-city-picker {
  .rc-select {
    min-width: 375px;
    width: 100% !important;
  }
  .rc-select-selection--single {
    height: 32px;
    line-height: 32px;
    .rc-select-selection__rendered {
      height: 32px;
      line-height: 32px;
    }
    .rc-select-selection-selected-value {
      color: #333;
    }
  }
  .rc-select-selection__clear {
    margin-top: 9px;
  }
}
