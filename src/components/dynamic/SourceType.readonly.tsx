import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { Select } from 'antd'
const { Option } = Select
import { wrapper } from '../layout/FormWrapper'
import styles from './SourceType.module.less'
import { app as api } from '@ekuaibao/whispered'

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'sourceType'
  },
  wrapper: wrapper(true)
})
export default class SourceType extends PureComponent<any, any> {

  constructor(props) {
    super(props)
  }

  render() {
    const { value = {}, field } = this.props
    const { tags } = field
    const selectItem = tags.find(el => el.value === value.source)
    if (!selectItem) {
      return '-'
    }
    const sourceLabel = selectItem?.label || '-'
    const typesLabel = selectItem.children.find(el => el.value === value.type)?.label || '-'
    return (
      <div className={styles.wrapper}>
         <div className="label">{`${sourceLabel} -- ${typesLabel}`}</div>
      </div>
    )
  }
}
