/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/11.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'

@EnhanceField({
  descriptor: {
    type: 'city'
  },
  wrapper: wrapper(true)
})
export default class RefCity extends PureComponent {
  render() {
    let { value } = this.props
    try {
      value = JSON.parse(value)
        .map(v => {
          if (i18n.currentLocale === 'en-US' && v.enLabel) {
            return v.enLabel
          }
          return v.label
        })
        .join(i18n.get('、'))
    } catch (e) {
      value = null
    }
    return <>{value ? value : i18n.get('无')}</>
  }
}
