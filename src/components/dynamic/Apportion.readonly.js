/**
 * Created by <PERSON><PERSON> on 2017/9/14.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { UIContainer as Container } from '@ekuaibao/whispered'
import { get, flatten } from 'lodash'
import { ApportionContext } from './types'
import { fnHiddenFieldByConfig } from '../utils/fnHideFields'
@EnhanceField({
  descriptor: {
    type: 'apportions'
  }
})
export default class ApportionReadonly extends PureComponent {
  state = { feeAmount: this.props.feeAmount, spcName: '' }
  getApportionLabel = () => {
    const { value, template, bus } = this.props
    if (!(value && value.length)) return {}
    const { specificationId } = value[0]
    const configs = get(specificationId, 'configs', [])
    const { apportionMoneyField } = configs.find(v => v.ability === 'apportion') || {}
    const list = template?.length ? (Array.isArray(template[0]) ? flatten(template) : template) : []
    const { label, name } = list.find(v => v.name === apportionMoneyField) || {}
    bus &&
      bus.getFieldsValue().then(value => {
        this.setState({ feeAmount: value[name] })
      })
    return { apportionLabel: label }
  }

  _contextValue = undefined
  get contextValue() {
    const { form } = this.props
    const { _contextValue } = this

    if (_contextValue === form) {
      return this._contextValue
    } else {
      const result = { form }
      this._contextValue = result
      return result
    }
  }
  render() {
    const {
      bus,
      value,
      external,
      isForbid,
      field: apportionField = {},
      apportionVisibleList,
      showAllFeeType,
      submitterId,
      detailId,
      billSpecification,
      hiddenFields,
      validateError = []
    } = this.props
    const contextValue = this.contextValue
    if (!value) {
      return null
    }

    if (fnHiddenFieldByConfig(apportionField, hiddenFields, validateError)) {
      return null
    }

    const { apportionLabel } = this.getApportionLabel()
    const { feeAmount } = this.state
    return (
      <ApportionContext.Provider value={contextValue}>
        <Container
          name="@apportion:Apportion"
          value={value}
          external={external}
          apportionfLebal={apportionLabel}
          isForbid={isForbid}
          apportionField={apportionField}
          isEdit={false}
          feeAmount={feeAmount}
          apportionVisibleList={apportionVisibleList}
          showAllFeeType={showAllFeeType}
          submitterId={submitterId?.id}
          feeDetailId={detailId}
          billSpecificationId={billSpecification?.id}
          billBus={bus}
        />
      </ApportionContext.Provider>
    )
  }
}
