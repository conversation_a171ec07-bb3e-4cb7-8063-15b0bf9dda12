/**************************************************
 * Created by nany<PERSON><PERSON>feng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, includes } from 'lodash'
import { ENUM_TYPES } from '../consts'
import './Ref.less'
import classNames from 'classnames'
import { isIE } from '../../lib/misc'
import { Tooltip } from '@hose/eui'
import { isObject } from '@ekuaibao/helpers'
import { getDisplayName } from '../../elements/utilFn'
@EnhanceField({
  descriptor: {
    test({ type }) {
      return type && type.startsWith('ref') && type !== 'ref:organization.Staff' && !includes(ENUM_TYPES, type) && type !== 'ref:basedata.Enum.currency'
    }
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => ({
  noRootPathMap: state['@common'].department.noRootPathMap,
  noRootEnPathMap: state['@common'].department.noRootEnPathMap,
  allStandardCurrency: state['@common'].allStandardCurrency,
}))
export default class Ref extends PureComponent {
  componentDidMount() {
    this.fnHandleDimentionCurrenncy(this.props.value)
  }
  componentWillReceiveProps(np) {
    if (isObject(np.value) && this.props.value?.id !== np.value?.id) {
      this.fnHandleDimentionCurrenncy(np?.value)
    }
  }
  fnHandleDimentionCurrenncy = (value) => {
    const { bus, field, allStandardCurrency = [] } = this.props
    let baseCurrencyId = value?.form?.baseCurrencyId
    if (field?.name === 'legalEntityMultiCurrency' && baseCurrencyId) {
      const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
      bus.emit('dimention:currency:change', { currency })
    }
  }
  getFullPath(noRootPathMap, value, field) {
    if (JSON.stringify(field) === '{}') return value
    let { entity } = field.dataType
    if (entity.indexOf('Department') == -1) return value
    if (!value || !noRootPathMap || JSON.stringify(noRootPathMap) === '{}') return value
    let _value = cloneDeep(value)
    if (_value && _value.name && _value.id && noRootPathMap.hasOwnProperty(_value.id)) {
      _value.name = noRootPathMap[_value.id]
    }
    return _value
  }
  getValueList = () => {
    const { noRootPathMap, noRootEnPathMap, value, field } = this.props
    const pathMap = i18n.currentLocale === 'zh-CN' ? noRootPathMap : noRootEnPathMap
    const newValue = []
    if (field && field.multiple) {
      value &&
        value.forEach(item => {
          const valueItem = this.getValueItem(pathMap, item, field)
          if (valueItem) {
            newValue.push(valueItem)
          }
        })
    } else {
      const valueItem = this.getValueItem(pathMap, value, field)
      if (valueItem) {
        newValue.push(valueItem)
      }
    }
    return newValue
  }
  getValueItem = (noRootPathMap, item, field) => {
    let codeGray
    let value = this.getFullPath(noRootPathMap, item, field)
    let checkedDepartment = ''
    if (item) checkedDepartment = noRootPathMap[item.id] || ''
    let lastName = ''
    if (checkedDepartment) {
      let splitList = checkedDepartment.split('/')
      lastName = splitList[splitList.length - 1]
    } else {
      if (item) lastName = getDisplayName(item)
    }
    if (value && value.hasOwnProperty('name')) {
      let { active, code, deleted } = value
      const name = getDisplayName(value)
      const text = deleted ? i18n.get('(已删除)') : i18n.get('(已停用)')
      value = `${name}${active === false ? text : ''}`
      if (!field?.hideCode && code) {
        lastName = `${lastName}${active === false ? i18n.get('(已停用)') : ''}`
        codeGray = i18n.get('（') + code + i18n.get('）')
      }
    }
    if (value) {
      return { value, codeGray, lastName }
    } else {
      return null
    }
  }
  renderItem = (item, index) => {
    const { field } = this.props
    let showAllPath = field && field.readStatePathRule === 'choosePath' ? false : true
    return (
      <Tooltip title={item.value} placement="topLeft">
        <span key={index}>
          {showAllPath ? item.value : item.lastName}
          {item.codeGray && <span className="ref-code">{item.codeGray}</span>}
        </span>
      </Tooltip>
    )
  }
  render() {
    const list = this.getValueList()
    if (list.length > 0) {
      return (
        <div className={classNames('ref-wrapper w-100b', { 'ml-8': isIE() })}>
          {list.map((item, index) => {
            return this.renderItem(item, index)
          })}
        </div>
      )
    }
    return <span>{i18n.get('无')}</span>
  }
}
