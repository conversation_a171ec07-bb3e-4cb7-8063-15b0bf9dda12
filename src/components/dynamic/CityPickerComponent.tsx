/**
 *  Created by pw on 2020-01-10 12:32.
 */
require('rc-select/assets/index.css')
import { CityPicker } from '@ekuaibao/eui'
import { getV } from '@ekuaibao/lib/lib/help'
import { connect } from '@ekuaibao/mobx-store'
import Select from 'rc-select'
import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { debounce } from 'lodash'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
import './CityPicker.less'
import { showMessage } from '@ekuaibao/show-util'
import { message } from 'antd'
const cityFetch = new Resource('/api/v2/basedata/city/')
const fetchCityByGroup = new Resource('/api/v1/basedata/cityGroup/')
const getCityGroup = new Resource('/api/tpp/v2/travelManagement/city')

type behaviorType = 'notAllow' | 'none' | 'allow'
export interface CPProps {
  cityValue?: any[]
  hotCity: any
  searchCity: any[]
  multiple: boolean
  maxSelectCount?: number
  [key: string]: any
  disabledCity: string[]
  onGetGroupBlack?: (data: any) => void
}

interface CPState {
  currentSelectedCity: any[]
  selectCity: undefined | string | string[]
  searchValue: string | undefined
  open: boolean
  cityList: any[]
  hotCity
  cityRange: any[]
  cityGroupId: string
  disabledCityList: string[]
  blackCity: {
    behavior: behaviorType
    cities: string[]
  },
  loading: boolean
}
const defaultBlackCity = { behavior: 'none', cities: [] }
// @ts-ignore
@connect(store => {
  const { hotCity, searchCity } = store.states['@layout']
  return { hotCity, searchCity }
})
export default class City extends Component<CPProps, CPState> {
  filters = {}
  constructor(props: CPProps) {
    super(props)
    this.state = {
      currentSelectedCity: [],
      selectCity: undefined,
      searchValue: '',
      open: false,
      cityList: [],
      hotCity: {
        inland: [],
        international: []
      },
      cityRange: [],
      cityGroupId: '',
      disabledCityList: [],
      blackCity: {
        behavior: 'none',
        cities: []
      },
      loading: false
    }
  }

  private lock: any = null

  componentWillReceiveProps(nextProps: any) {
    const { value, multiple, isAuto, filters } = nextProps
    this.filters = filters
    if (filters) {
      const { travelerId, travelType } = filters
      if (travelerId !== this.props.filters?.travelerId || travelType !== this.props.filters?.travelType) {
        this.fetchCity()
        // this.getCityGroup()
        this.fetchHotCity()
        this.fetchDisabled()
        // this.getBlackCity()
        this.getCityGroupAndBlack()
      }
    }
    if (value !== this.props.value) {
      console.log("===============>value", value);

      let cityValue: any = []
      if (value) {
        cityValue = JSON.parse(value)
      }
      if (value && cityValue && cityValue.length && cityValue[0]?.type) {
        cityValue = cityValue.map(i => {
          i.name = i.label
          return i
        })
        this.setState({ selectCity: cityValue.map(i => i.label), currentSelectedCity: cityValue })
      } else if (value && cityValue && cityValue.length) {
        const ids = cityValue.map((c: any) => c.key)
        cityFetch.GET('[ids]', { ids }).then((data: any) => {
          if (data.items) {
            const selectedCity = ids.map(id => data.items.find(el => id === el.id))
            const selectCity =
              multiple || isAuto
                ? selectedCity.map((city: any) => this.formatLabel(city.fullName || city.name))
                : this.formatLabel(selectedCity[0].fullName || selectedCity[0].name)
            this.setState({ selectCity, currentSelectedCity: selectedCity })
          }
        })
      } else {
        this.setState({ selectCity: undefined, currentSelectedCity: [] })
      }
    }
  }

  componentDidMount() {
    const { value, multiple = false, isAuto, filters, bus } = this.props
    this.filters = filters
    let cityValue: any = []

    if (value) {
      cityValue = JSON.parse(value)
    }
    if (value && cityValue && cityValue.length && cityValue[0]?.type) {
      cityValue = cityValue.map(i => {
        i.name = i.label
        if (!i?.id) {
          i.id = i.key
        }
        return i
      })
      this.setState({ selectCity: cityValue.map(i => i.label), currentSelectedCity: cityValue })
    } else if (value && cityValue && cityValue.length) {
      const ids = cityValue.map((c: any) => c.key)
      cityFetch.GET('[ids]', { ids }).then((data: any) => {
        if (data.items) {
          const selectedCity = ids.map(id => data.items.find(el => id === el.id))
          const selectCity =
            multiple || isAuto
              ? selectedCity.map((city: any) => this.formatLabel(city.fullName || city.name))
              : this.formatLabel(selectedCity[0].fullName || selectedCity[0].name)
          this.setState({ selectCity, currentSelectedCity: selectedCity })
        }
      })
    }
    bus?.on('set:delegator', this.handleDelegatorChanged)
    api.on('clear:city:picker', this.onReturn)
    this.fetchHotCity()
    // this.getCityGroup()
    this.fetchDisabled()
    // this.getBlackCity()
    this.getCityGroupAndBlack()
    this.props?.bus?.on('assign:city:value', this.assignCityValue)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus?.un('set:delegator', this.handleDelegatorChanged)
    api.un('clear:city:picker', this.onReturn)
    this.props?.bus?.un('assign:city:value', this.assignCityValue)
  }

  assignCityValue = citys => {
    const { onChange, field } = this.props
    const { accommodation, departure, destination } = citys
    const { defaultValue } = field
    if (defaultValue?.type === 'predefine') {
      if (defaultValue?.value === 'accommodation') {
        accommodation && onChange && onChange(accommodation)
      } else if (defaultValue?.value === 'departure') {
        departure && onChange && onChange(departure)
      } else if (defaultValue?.value === 'destination') {
        destination && onChange && onChange(destination)
      }
    }
  }

  handleDelegatorChanged = async staff => {
    const { field = {} } = this.props
    const hasForbiddenCityGroup = getV(field, 'hasForbiddenCityGroup', false)
    if (hasForbiddenCityGroup) {
      const res = await this.getCityGroupActive(staff)
      this.setState({
        disabledCityList: res?.items || []
      })
    }
  }

  fetchHotCity() {
    api.store.dispatch('@layout/setHotCity')(this.filters, hotCity => {
      this.setState({
        hotCity
      })
    })
  }

  getCityGroupActive = staff => {
    const { field, submitterId } = this.props
    staff = staff || submitterId
    return fetchCityByGroup
      .GET(`findLimitedCityIds`, { cityGroupId: field?.cityGroupId, staffId: staff.id })
      .catch(err => showMessage.error(err))
  }

  async fetchDisabled() {
    const { form, field } = this.props
    const hasForbiddenCityGroup = getV(field, 'hasForbiddenCityGroup', false)
    if (hasForbiddenCityGroup) {
      const submitterId = form.getFieldValue('submitterId') || this?.props?.submitterId
      const res = await this.getCityGroupActive(submitterId)
      this.setState({
        disabledCityList: res?.items || []
      })
    }
  }

  fetchCity(cityId: string = '', cityType: 'inland' | 'international' = 'inland', cityGroupId: string = '') {
    this.setState(
      {
        cityGroupId: cityGroupId,
        loading: true
      },
      () => {
        cityFetch
          .GET('search/children', { cityId, country: cityType, ...this.filters, cityGroupId })
          .then((data: any) => {
            if (data.items) {
              // @ts-ignore
              this.setState({ cityList: [...data.items] })
            }
            this.setState({ loading: false })
          })
          .catch(err => this.setState({ loading: false }))
      }
    )
  }

  getCityGroupAndBlack = () => {
    const { onGetGroupBlack } = this.props
    // @ts-ignore
    if (this.filters?.travelerId || this.filters?.travelType) {
      Promise.all([
        getCityGroup.GET('/getCityGroup', { ...this.filters }),
        cityFetch.GET('getTripBlackCities', { ...this.filters })
      ]).then(res => {
        const gc = res[0]
        const bc = res[1]
        const cityRange = gc?.items?.map(i => {
          i.type = 'cityGroup'
          i.key = i.id
          return i
        })
        const blackCity = bc?.value
        this.setState({ cityRange, blackCity })
        onGetGroupBlack && onGetGroupBlack({ cityGroup: cityRange, blackCity })
      })
    } else {
      // @ts-ignore
      this.setState({ cityRange: [], blackCity: defaultBlackCity })
      onGetGroupBlack && onGetGroupBlack({ cityGroup: [], blackCity: defaultBlackCity })
    }
  }

  searchCity(value: string) {
    this.setState({ loading: true }, () => {
      const cb = () => this.setState({ loading: false })
      api.store.dispatch('@layout/setSearchCity')(value, this.filters, cb)
    })
  }

  private formatLabel(label: string, bool: boolean = false) {
    let { showType } = this.props
    let text = label.replace(/^中国,\s*/, '').replace(/,\s*/g, '/')
    if (showType == 'trip' && text.lastIndexOf('/') && bool == false) {
      text =
        i18n?.currentLocale != 'en-US'
          ? text.substr(text.lastIndexOf('/') + 1)
          : text.indexOf('/') > -1
            ? text.substr(0, text.indexOf('/'))
            : text
    }
    return text
  }

  private fnFormatCityLabel(selectCity: any) {
    if (!selectCity) {
      return null
    }

    const value = Array.isArray(selectCity) ? [...selectCity] : [selectCity]

    if (!value.length) {
      return null
    }

    return JSON.stringify(
      value.map((v: any) => {
        let data = {}
        if (v.type) {
          data = {
            key: v.id,
            label: this.formatLabel(v.name, true),
            type: v.type
          }
        } else {
          data = {
            key: v.id,
            label: this.formatLabel(v.fullName, true)
          }
        }
        return data
      })
    )
  }
  handleDelete = (index: number) => {
    const { onChange, getExpenseStandardItemsLength, external, multiple = false, updateCell } = this.props

    let result
    let { currentSelectedCity: CSC } = this.state
    // 判断重复，不予更新

    if (CSC && CSC.length) {
      CSC.splice(index, 1)
    }

    const selectCity = CSC.map((c: any) => this.formatLabel(c.fullName || c.name))

    this.setState({ selectCity, currentSelectedCity: [...CSC] })
    result = this.fnFormatCityLabel(CSC)

    onChange && onChange(result)
    updateCell && updateCell(result)
    emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }
  handleGetValue = (value: any) => {
    const {
      onChange,
      getExpenseStandardItemsLength,
      external,
      multiple = false,
      showType = '',
      updateCell,
      maxSelectCount = 0
    } = this.props

    let result

    if (multiple) {
      let { currentSelectedCity: CSC } = this.state
      let cityGroup = CSC.length > 0 && CSC[0].type == 'cityGroup'
      let addGroup = value.type == 'cityGroup'
      if (CSC.length > 0) {
        if (cityGroup && addGroup) {
          CSC = []
        } else if (cityGroup != addGroup) {
          message.info(i18n.get('城市范围和城市选择不能同时使用'))
          return
        }
      }

      // 判断重复，不予更新

      if (CSC.some(city => value.id === city.id)) {
        return
      }
      if (CSC && showType == 'trip' && maxSelectCount > 0 && CSC.length >= maxSelectCount) {
        message.info(i18n.get('城市最多添加20个'))
        return
      }
      CSC.push(value)

      const selectCity = CSC.map((c: any) => this.formatLabel(c.fullName || c.name))

      this.setState({ selectCity, currentSelectedCity: [...CSC] })
      result = this.fnFormatCityLabel(CSC)
    } else {
      this.setState({ selectCity: this.formatLabel(value.fullName || value.name), open: false })
      result = this.fnFormatCityLabel(value)
    }

    onChange && onChange(result)
    updateCell && updateCell(result)
    emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }

  // 搜索事件
  onSearch = debounce((value: string) => {
    this.setState({ searchValue: value }, () => {
      this.searchCity(value)
    })
  }, 500)

  // 黑科技 🙅🙅🙅🙅
  lockClose = () => {
    clearTimeout(this.lock)
    this.lock = setTimeout(() => {
      this.lock = null
    }, 300)
  }

  // 黑科技 🙅🙅🙅🙅
  onDropdownVisibleChange = (open: boolean) => {
    if (this.lock) {
      return
    }

    this.setState({ open })
  }

  onReturn = () => {
    this.searchCity('')
    this.setState({ searchValue: '' })
  }

  onClose = () => {
    this.setState({ open: false })
  }

  onClear = (value: string) => {
    if (value === undefined) {
      const { onChange, getExpenseStandardItemsLength, external, updateCell } = this.props

      this.setState({ selectCity: undefined })
      onChange && onChange(undefined)
      updateCell && updateCell(undefined)
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  // 多选用于多选删除
  onChange = (value: string[] | string) => {
    const { onChange, getExpenseStandardItemsLength, external, multiple = false, updateCell } = this.props
    const { currentSelectedCity } = this.state
    if (multiple && Array.isArray(value)) {
      const selected = currentSelectedCity.filter((city: any) =>
        value.some(c => c === this.formatLabel(city.fullName || city.name))
      )
      this.setState({ selectCity: [...value], currentSelectedCity: selected })
      const result = this.fnFormatCityLabel(selected)
      onChange && onChange(result)
      updateCell && updateCell(result)
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  render() {
    const {
      multiple = false,
      searchCity,
      isAuto,
      className = '',
      otherStyle = {},
      large = false,
      showRecently = true,
      showType = '',
      maxTagCount = 0,
      placeholder,
      parentProps,
      disabledCity = [],
      field = {}
    } = this.props
    const {
      selectCity,
      searchValue,
      open,
      currentSelectedCity,
      cityList,
      cityRange,
      cityGroupId,
      disabledCityList,
      blackCity,
      loading
    } = this.state
    const hotCity = showRecently ? this.props.hotCity : this.state.hotCity
    const lang = {
      domestic: i18n.get('国内'),
      international: i18n.get('国际/中国港澳台'),
      results: i18n.get('搜索结果'),
      hotCities: i18n.get('热门城市'),
      recently: i18n.get('历史选择（含国内/国际）'),
      noResults: i18n.get('没有搜索结果，要不换个关键词'),
      loading: i18n.get('努力加载中'),
      empty: i18n.get('当前权限无可选城市'),
      rangelabel: i18n.get('区域范围'),
      rangeTitle: i18n.get('查看城市'),
      backTitle: i18n.get('返回全部'),
      deleteTitle: i18n.get('已选城市')
    }

    const cityNode = (
      <div onMouseDown={this.lockClose} onMouseUp={this.lockClose} onMouseLeave={this.onClose}>
        <CityPicker
          travelType={this.filters?.travelType}
          loading={loading}
          multiple={multiple || isAuto}
          hotCity={hotCity}
          cityRange={cityRange}
          cityList={cityList}
          searchValue={searchValue}
          searchList={[...searchCity]}
          fetchCity={this.fetchCity.bind(this)}
          searchCity={this.searchCity.bind(this)}
          selectedCity={[...currentSelectedCity]}
          onChange={this.handleGetValue.bind(this)}
          onDelete={this.handleDelete.bind(this)}
          onReturn={this.onReturn}
          lang={lang}
          cityGroupId={cityGroupId}
          showRecently={showRecently}
          disabledCityList={disabledCityList.concat(disabledCity)}
          showType={showType}
          blackCity={blackCity}
        />
      </div>
    )

    let options: any = {}
    // selectCity 可能来自关联申请
    if (multiple || (isAuto && selectCity && selectCity.length > 1)) {
      options = { multiple: true, onChange: this.onChange }
      if (showType == 'trip') {
        if (maxTagCount) {
          options.maxTagCount = maxTagCount
        }
        options.maxTagTextLength = i18n?.currentLocale == 'en-US' ? 8 : 6
      }
    } else {
      options = { onChange: this.onClear }
    }
    const largeCls = large ? 'large-city-picker' : ''

    const simpleClassName = field?.isSimple ? 'simple-city-picker' : ''

    return (
      <div style={{ position: 'relative' }} className={`city-picker ${className} ${largeCls} ${simpleClassName}`}>
        <Select
          {...options}
          value={selectCity}
          disabled={isAuto}
          placeholder={placeholder || i18n.get('选择城市')}
          style={{ width: '375px', ...otherStyle }}
          dropdownRender={() => cityNode}
          onSearch={this.onSearch}
          onDropdownVisibleChange={this.onDropdownVisibleChange}
          open={open}
          showArrow={false}
          allowClear={parentProps ? parentProps.allowClear : !isAuto && !multiple}
        />
      </div>
    )
  }
}
