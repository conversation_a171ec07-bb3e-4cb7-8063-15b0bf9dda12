/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/21.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import RequisitionInfo from '../../elements/puppet/RequisitionInfo'
@EnhanceField({
  descriptor: {
    type: 'linkRequisitionInfo'
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => {
  return {
    applyList: state['@bills'].applyList
  }
})
export default class LinkRequisitionInfo extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {}
  }

  handleDetailClick = () => {
    let { value = {}, bus, flowId } = this.props
    bus && bus.emit('check:requisition:detail', { detail: value, flowId })
  }

  render() {
    let { value, applyList = [] } = this.props
    if (!value) return <div>{i18n.get('无')}</div>

    let item = { name: i18n.get('无') }
    if (typeof value === 'object') {
      item = applyList.find(item => item.id === value.id)
    }
    return <RequisitionInfo value={value} onDetailClick={this.handleDetailClick} />
  }
}
