@import '~@e<PERSON><PERSON><PERSON>/eui-styles/less/token.less';
.radio {
  margin-bottom: 25px;
  :global {
    .ant-radio-group {
      display: flex;
    }
    .item {
      margin-right: 12px;
      padding: 8px;
      height: 38px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      border: 1px solid rgba(29, 43, 61, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      .ant-radio-inner {
        position: relative;
        top: 0;
        left: 0;
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 100px;
        background-color: #fff;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
      .ant-radio-inner::after {
        display: none;
      }
      .ant-radio-inner:hover {
        border: 1px solid var(--brand-base);
      }
      .ant-radio-disabled.ant-radio-checked .ant-radio-inner {
        border-color: var(--brand-base) !important;
      }
    }
    .text {
      color: rgba(29, 43, 61, 1);
      font-size: 14px;
    }
    .active {
      background: var(--brand-fadeout-10);
      border: 1px solid var(--brand-base);
      .ant-radio-inner {
        border: 4px solid var(--brand-base);
      }
    }
  }
}
