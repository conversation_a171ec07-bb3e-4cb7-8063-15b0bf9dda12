/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { ENUM_TYPES } from '../consts'
import { includes } from 'lodash'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import RefCP from './RefCP'
import RefCPV2 from './CustomDimension/RefCPV2Refactored'
import { enableRecordOptimization } from '../../lib/featbit/feat-switch'
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return (
        type.startsWith('ref') &&
        type !== 'ref:organization.Staff' &&
        type !== 'ref:organization.StaffSimple' &&
        type !== 'ref:organization.Department' &&
        type !== 'ref:basedata.Enum.currency' &&
        !includes(ENUM_TYPES, type)
      )
    }
  },
  initialValue(props) {
    let { field = {}, value, lastChoice, isModify, detailId, isDetail } = props
    const notUseDefaultValue = isModify && isDetail && !detailId ? false : isModify
    if (!value && !notUseDefaultValue) {
      let constValue = constantValue(field)
      if (constValue) return { id: constValue }
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return { id: lastVal }
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Ref extends PureComponent {
  isFlow = () => {
    const { businessType } = this.props
    return ['FLOW', 'DETAILS'].includes(businessType)
  }

  // 是否是自定义档案
  isDimension = () => {
    const { field, type } = this.props
    return field?.dataType?.entity?.startsWith('basedata.Dimension') || type?.startsWith('ref:basedata.Dimension')
  }

  render() {
    if (this.isFlow() && enableRecordOptimization() && this.isDimension()) {
      return <RefCPV2 {...this.props} />
    }
    return <RefCP {...this.props} />
  }
}
