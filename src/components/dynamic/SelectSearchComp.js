import React, { PureComponent } from 'react'
import styles from './SelectSearch.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { isObject } from '@ekuaibao/helpers'
import { Select as AntdSelect } from 'antd'
import { Select as EUISelect } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import EkbHighLighter from '../../elements/EkbHighLighter'
import { handleGetDataById, refPropertyById } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import { shouldUpdateValue } from '../../components/utils/DependenceUtil'
import { get } from 'lodash'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { getRecordLink } from './helpers/getRecordLink'
import { SpecialAllowCancelDependenceUtils } from './types'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'
import { OutlinedEditSearch } from '@hose/eui-icons'

@EnhanceConnect(
  state => ({
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class SelectSearchComp extends PureComponent {
  constructor(props) {
    super(props)
    let { field } = props
    let { dependence } = field
    let isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      searchText: undefined,
      data: [],
      emptyText: '',
      value: props.value,
      valueObjMap: {},
      isDependenceListEmpty: false,
      useDependenceData: true, // 是否允许使用 依赖关系筛选数据（因为允许取消筛选）
      dependenceMap
    }
    this.timer = undefined
  }

  componentWillMount() {
    const { bus } = this.props
    this.handleDefaultValue(this.props)
    bus.on('on:dependence:change', this.handleDependenceChange)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
  }

  componentDidMount() {
    setTimeout(() => {
      let { value, bus, field } = this.props
      const id = isObject(value) ? value.id : value
      bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true })
      this.fnHandleDimentionCurrenncy(value, true)
    }, 200)
  }

  fnHandleDimentionCurrenncy = async (value, flag) => {
    const { bus, field } = this.props
    const baseCurrencyId = value?.form?.baseCurrencyId
    if (field?.name === 'legalEntityMultiCurrency' && !flag) {
      bus.emit('dimention:multi:currency:change')
    }
    if (field?.name === 'legalEntityMultiCurrency' && baseCurrencyId) {
      const allStandardCurrency = api.getState()['@common'].allStandardCurrency || []
      const { items: rates = [] } = await api.invokeService('@currency-manage:get:currency:rates:by:Id', baseCurrencyId)
      const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
      bus.emit('dimention:currency:change', { currency, rates })
      bus.emit('dimension:currency:change:payPlan:change', currency)
      api.invokeService('@bills:update:dimention:currency', { currency, rates, dimention: value })
    }
  }

  currentIdx = null
  handleDependenceChange = ({ key, id, dependenceFeeType = false }, options = {}) => {
    let { getRecordLink, field, value, bus, isModify, flowId, billState, form, detailId, isDetail } = this.props
    let { dependence, dataType, selectRange, dependenceCondition, allowCancelDependence = false } = field
    const isSpecialCancelDependency = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据

    if (dependence && dependence?.length) {
      let isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        const { dependenceMap } = this.state
        let list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        this.currentIdx = id //标明promise的执行顺序
        getRecordLink({
          recordSearch: list,
          entity: dataType.entity,
          range: selectRange,
          defaultValue: value && value.id,
          flowId: isModify ? flowId : '',
          dependenceFeeType,
          dependenceCondition
        }).then(action => {
          const reqIdx = id // 闭包记录id
          let data = action.payload
          let { matchDefaultValue, leafItems, count } = data
          let newValue = undefined

          if (this.currentIdx === reqIdx) {
            if (isInitLoad) {
              const fieldsValue = form.getFieldsValue()
              // 初始化进来 当前value 值赋值
              newValue = fieldsValue[field?.field] ?? undefined
              this.onChange(fieldsValue[field?.field] ?? null)
            } else if (!matchDefaultValue) {
              bus.setValidateLevel(1)
              newValue = leafItems ? leafItems : null
              this.onChange(newValue)
            }

            // TODO: 档案关系埋点
            let { billData, billSpecification, feeType, dataSource } = this.props
            const oldValue = this.props?.value
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })

            if (isSpecialCancelDependency) {
              // 如果是特殊企业，将直接使用全部数据进行搜索
              this.setState({
                isDependenceListEmpty: false,
                data: [],
                useDependenceData: false,
                emptyText: ''
              })
            } else if (count === 0 && allowCancelDependence) {
              // 如果是开启了取消依赖，将可以取消依赖
              this.setState({
                isDependenceListEmpty: true,
                data: [],
                useDependenceData: true,
                emptyText: (
                  <div className="cancel-dependence">
                    {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
                    <a href="javascript:void(0)" onClick={this.handleCancelDependency.bind(this)}>
                      {i18n.get('查看全量数据')}
                    </a>
                  </div>
                )
              })
            } else {
              // 其他情况，重置 state
              this.setState({
                isDependenceListEmpty: false,
                data: [],
                useDependenceData: true,
                emptyText: ''
              })
            }
          }
        })
      }
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.lastChoice !== nextProps.lastChoice) {
      this.handleDefaultValue(nextProps)
    }
    let { value, field } = nextProps
    if (value !== this.props.value && typeof value === 'string') {
      const { valueObjMap } = this.state
      if (!valueObjMap[value]) {
        let promise = handleGetDataById(field, value)
        promise &&
          promise.then(result => {
            if (result) {
              let { valueObjMap } = this.state
              valueObjMap[value] = result
              this.onChange(result)
              this.setState({ valueObjMap: { ...valueObjMap } })
            }
          })
      }
    }
    if (this.props.value !== nextProps.value) {
      shouldUpdateValue(this.props, nextProps)
    }
  }

  handleCancelDependency() {
    this.setState(
      {
        useDependenceData: false
      },
      () => {
        this.getDimenstionById(this._currentValue)
      }
    )
  }

  getDimenstionById(searchValue) {
    let { field, bus, getRecordLink, useEUI } = this.props
    let { dataType = {}, selectRange, dependence, dependenceCondition, allowCancelDependence = false } = field
    const { useDependenceData, isDependenceListEmpty } = this.state
    const isSpecialCancelDependency = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
    const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
    if (dependence && dependence.length && !isSpecialCancelDependency && useDependenceData) {
      if (isDependenceListEmpty) {
        return
      }
      bus.getFieldsValue().then(v => {
        let isNeedFetch = false
        let recordSearch = dependence.map((el, i) => {
          const { use, ...rest } = el
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const depVal = v[el.dependenceId]
          const id = isObject(depVal) ? depVal.id : depVal
          if (id) {
            isNeedFetch = true
            return { ...rest, dependenceId: id }
          }
          return rest
        })
        if (isNeedFetch) {
          getRecordLink({
            recordSearch,
            entity: dataType.entity,
            isSearch: true,
            range: selectRange,
            keyWord: searchValue,
            dependenceCondition
          }).then(action => {
            const { items } = action.payload
            let { searchText } = this.state
            if (!searchText) {
              this.setState({ data: [], emptyText: '' })
            } else {
              this.setState({ data: items, emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
            }
          })
        } else {
          let { searchText } = this.state
          if (!searchText) {
            this.setState({ data: [], emptyText: '' })
          } else {
            this.setState({ data: [], emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
          }
        }
      })
    } else {
      api
        .invokeService('@common:get:dimensionById', { keyWord: searchValue, range: selectRange, size: 100, id: entity })
        .then(data => {
          let { searchText } = this.state
          if (!searchText) {
            this.setState({ data: [], emptyText: '' })
          } else {
            this.setState({ data: data, emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
          }
        })
    }
  }

  handleDefaultValue(props) {
    let { value, field } = props
    let { dependence } = field
    let isDependence = dependence && dependence.length
    if (!value && !isDependence) {
      let promise = refPropertyById(props)
      promise &&
        promise.then(result => {
          if (result) {
            this.onChange(result)
          }
        })
    }
  }

  onChange = value => {
    let { onChange } = this.props
    let { data } = this.state
    let newValue = data.find(item => item.id === value) || value
    this.setState({
      emptyText: '',
      data: [],
      value: newValue
    })
    onChange && onChange(newValue)
    this.fnHandleDimentionCurrenncy(newValue)
  }

  _currentValue = ''
  onSearch = currentValue => {
    this.setState({ searchText: currentValue })
    if (currentValue.trim().length) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this._currentValue = currentValue.trim()
      this.timer = setTimeout(() => this.getDimenstionById(currentValue.trim()), 1000)
    } else {
      if (this.timer) {
        clearTimeout(this.timer)
      }

      this._currentValue = ''
      this.setState({
        data: [],
        emptyText: ''
      })
    }
  }
  convertValue = data => {
    let { field } = this.props
    let list = this.state.data
    let fakeLabel = ''
    const { isShowFullPath } = field

    if (!data) return undefined
    if (typeof data === 'string') {
      let obj = list && list.find(item => item.id === data)
      if (!obj) {
        const { valueObjMap } = this.state
        obj = valueObjMap[data]
      }
      if (!obj) return
      let { code, fullPath, fullName, active, deleted } = obj
      const displayName = getDisplayName(
        isShowFullPath,
        fullName ?? fullPath,
        active,
        getContentByLocale(obj, 'name'),
        deleted
      )
      fakeLabel =
        !field?.hideCode && code ? i18n.get(`{__k0}({__k1})`, { __k0: displayName, __k1: code }) : displayName
    } else {
      let { code, fullPath, fullName, active, deleted } = data
      const displayName = getDisplayName(
        isShowFullPath,
        fullName ?? fullPath,
        active,
        getContentByLocale(data, 'name'),
        deleted
      )
      fakeLabel =
        !field?.hideCode && code ? i18n.get(`{__k0}({__k1})`, { __k0: displayName, __k1: code }) : displayName
    }

    function getDisplayName(isShowFullPath, fullPath, active, name, deleted) {
      const text = deleted ? i18n.get('(已删除)') : i18n.get('(已停用)')
      let displayName = ''
      isShowFullPath && fullPath
        ? (displayName = `${fullPath}${active === false ? text : ''}`)
        : (displayName = `${name}${active === false ? text : ''}`)
      return displayName
    }

    return fakeLabel
  }
  render() {
    let { data = [], emptyText, searchText } = this.state
    let { value, field, useEUI } = this.props
    value = value ? value : this.state.value
    let { optional } = field
    let placeholder = getPlaceholder(field)
    if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    let value2show = this.convertValue(value) || []
    let disabled = isDisable(this.props)
    const Select = useEUI ? EUISelect : AntdSelect
    const Option = Select.Option
    const size = useEUI ? 'default' : 'large'
    const children = data.map(d => {
      const showName = getContentByLocale(d, 'name')
      const showText = !field?.hideCode ? i18n.get(`{__k0}（{__k1}）`, { __k0: showName, __k1: d.code }) : showName
      return (
        <Option key={d.id}>
          {useEUI ? (
            showText
          ) : (
            <EkbHighLighter highlightClassName={'highlight'} searchWords={[searchText]} textToHighlight={showText} />
          )}
        </Option>
      )
    })
    return (
      <div>
        <Select
          className={styles['ekb-select']}
          mode="default"
          style={{ width: '100%' }}
          placeholder={placeholder}
          value={value2show}
          showSearch={true}
          size={size}
          allowClear
          disabled={disabled}
          onChange={this.onChange}
          onSearch={this.onSearch}
          filterOption={false}
          notFoundContent={emptyText}
          suffixIcon={<OutlinedEditSearch />}
          getPopupContainer={triggerNode => triggerNode.parentNode}
        >
          {children}
        </Select>
      </div>
    )
  }
}
