import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import { FetchRefList } from '../utils/FetchRefList'
import PopoverWrapper from '../../elements/namePopover/PopoverWrapper'
import classNames from 'classnames'
import { isIE } from '../../lib/misc'
import { UserBadgeGroup } from '../../elements/profile-card/user-badge/user-badge-group'

@EnhanceConnect(state => ({
  staffDisplayConfig: state['@common'].organizationConfig.staffDisplayConfig
}))
@EnhanceField({
  descriptor: {
    type: 'list:ref:organization.Staff'
  },
  wrapper: wrapper(true)
})
@FetchRefList
export default class Travelers extends PureComponent {

  render() {
    let { value = [], staffDisplayConfig = [] } = this.props
    if (typeof value == 'string') {
      value = [value]
    }
    if (typeof value === 'undefined') {
      value = []
    }
    return (
      <div className={classNames({ 'ml-8': isIE() })}>
        {value.length ? (
          <UserBadgeGroup users={value} extraField={staffDisplayConfig[1]} baseFiled={staffDisplayConfig[0]}>
            {(user, userBadgeEl) => {
              return <PopoverWrapper info={user} name={userBadgeEl} trigger="hover" placement="bottomLeft" />
            }}
          </UserBadgeGroup>
        ) : (
          i18n.get('无')
        )}
      </div>
    )
  }
}
