import React from 'react'
import { Fetch } from '@ekuaibao/fetch'

export const ApportionContext = React.createContext({
  form: undefined
})

export class SpecialAllowCancelDependenceUtils {

  /**
   * 特殊需求, 在白名单的公司，可以取消依赖的情况下，是不会使用依赖列表的，直接使用全量数据列表
   * https://hose2019.feishu.cn/docs/doccnUlklfPsSKI49VdLw1XMlCh#
   */
  static isRefSpecial() {
    const whiteList = [
        // 测试环境公司
       // 'ID01iJdEhMzRGn', 发票禁用测试0803
       'ID01iAVxDoSkkD', // 发票禁用测试082503
      //  'ID01iAVxDoSjNB', // 发票禁用测试082502
       'BqZ3FWak0J00qg', // 肖寒伟kaqa测试企业

      // release 环境
      'LOV3ErNSDb00Vw', // 业务数据联查分摊

      // hotfix 环境测试公司
      'ID01jR6MK1IG7I', // 朝阳测试_hotfix_gray（ID：ID01jR6MK1IG7I）

      // 正式测试企业
      'ptMaTq8LIQbg00', // 【测试】羊羊测试企业 

      // 正式环境客户
      'Rlx3kgsCrz00uM', // 1. 格林晟  wkz3LxRHqy02yv   企业微信
      '59s8ib_XFc3w00', // 2. 深圳开思时代科技有限公司 Rlx3kgsCrz00uM  企业微信
      'wkz3LxRHqy02yv', // 3. 罗牛山股份有限公司  59s8ib_XFc3w00  原生

      'ID01kUkij7Uu7l', // 4. 深圳市合元科技有限公司（ID：ID01kUkij7Uu7l）
      'ID01q89hU2D5fh', // 5. 力盛云动（上海）体育科技股份有限公司
      'ID01ocBH12O7kb', // 6. 铭控传感

      'ID01nZk21XQGth', // 7.深圳市英威腾电气股份有限公司
      'ID01uHQR86Cnsb', // 8.蓝气球（北京）医学研究有限公司
      'ID01rWUzm7239R', // 9.沃森测控技术（河北）有限公司
    ]
    if (Fetch?.ekbCorpId) {
      return whiteList.includes(Fetch?.ekbCorpId)
    } else if (Fetch?.corpId) {
      return whiteList.includes(Fetch?.corpId)
    }

    return false
  }
}
