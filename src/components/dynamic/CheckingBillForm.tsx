import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { MoneyIF } from '@ekuaibao/ekuaibao_types'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { showMessage } from '@ekuaibao/show-util'
import styles from './CheckingBillForm.module.less'
import { standardValueMoney } from '../../lib/misc'
import { IField } from '@ekuaibao/template/types/Cellar'
import { get } from 'lodash'

type Props = {
  field: IField
  form: any
  value: {
    checkingBillId: string
    peroid: string
    settlementAmount: MoneyIF
    supplierAccount: string
    supplierName: string
    checkingBillCode: string
    splitType: string
  }
}

type Params = {
  billPeriod: { name: string }
  supplierAccountId: { name: string; id: string }
  supplierArchiveId: { name: string; id: string }
  hideButton: boolean
}

type MouseEventHandler<T = Element> = React.EventHandler<React.MouseEvent<T>>

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'checkingBillForm'
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
} as IComponentDef)
export default class CheckingBillForm extends PureComponent<Props> {
  handleBusiness: MouseEventHandler = async () => {
    const { checkingBillId, peroid, supplierAccount, supplierName, splitType, settlementAmount } = this.props?.value
    const { hiddenCheckBill, hiddenInvoice, hiddenSettle } = this.props?.field
    const data = await api.invokeService('@settlement:get:checking:bill', checkingBillId)
    const params: Params = {
      billPeriod: { name: peroid },
      supplierAccountId: { name: supplierAccount, id: data?.value?.supplierAccountId },
      supplierArchiveId: { name: supplierName, id: data?.value?.supplierArchiveId },
      hideButton: true
    }

    let legalEntityId = ''
    let settlement = undefined
    let expenseDepartment = null
    const hasConfig = await this.getConfigSettlementList(data?.value?.supplierAccountId)
    if (hasConfig) {
      // 费用承担部门
      const depVal = await this?.props?.form?.getFieldValue('expenseDepartment')
      expenseDepartment = get(depVal, 'id', null)
      settlement = standardValueMoney(settlementAmount?.standard)?.standard
    }else if (splitType === 'MACH') {
      // 获取 u_对账法人实体  id
      const form = this?.props?.form
      const legalEntity = await form?.getFieldValue('u_对账法人实体')
      legalEntityId = get(legalEntity, 'id', 'NONE')
      settlement = standardValueMoney(settlementAmount?.standard)?.standard
    }
    const legalEntityObj = get(this.props, 'detailData.法人实体', {})
    api.open('@bills:BusinessStatementModal', {
      value: {
        ...data.value,
        ...params,
        legalEntityObj,
        hiddenCheckBill,
        hiddenInvoice,
        hiddenSettle,
        splitType,
        legalEntityId,
        settlementAmount: settlement,
        supplierAccount,
        expenseDepartment
      }
    })
  }
  getConfigSettlementList = async (supplierAccountId: string = '') => {
    // supplierAccountId
    const { items = [] } = await api.invokeService('@settlement:get:config:settlement:list')
    return items.find((item: any) => item.supplierAccountId === supplierAccountId)?.mappings?.find((i: any) => i.sourceField === 'expenseDepartment')
  }
  handleCopyBtnClick: MouseEventHandler = (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
    showMessage.success(i18n.get('复制成功！'))
  }

  render() {
    if (!this.props.value) {
      const empty = i18n.get('无')
      return <div>{empty}</div>
    }
    const { peroid, settlementAmount, supplierAccount, supplierName, checkingBillCode } = this.props.value
    const settlement = standardValueMoney(settlementAmount.standard)
    return (
      <div className={`${styles['business-statement-wrapper']}`}>
        <div className="statement-wrapper" onClick={this.handleBusiness}>
          <div className="header">
            <span>{i18n.get('账期')}：</span>
            <span>{peroid}</span>
          </div>
          <div className="container">
            <div>
              <div className="weight">{settlement.standard}</div>
              <div>
                {i18n.get('本期结算金额')}/{i18n.get('元')}
              </div>
            </div>
            <div>
              <div>{i18n.get('供应商名称')}：</div>
              <div>{supplierName}</div>
            </div>
            <div>
              <div>{i18n.get('供应商账户')}：</div>
              <div>{supplierAccount}</div>
            </div>
            <div>
              <div>{i18n.get('对账单编码')}：</div>
              <CopyToClipboard text={checkingBillCode}>
                <div onClick={this.handleCopyBtnClick} style={{ cursor: 'pointer' }}>
                  {checkingBillCode}
                </div>
              </CopyToClipboard>
            </div>
          </div>
        </div>
      </div>
    )
  }
}
