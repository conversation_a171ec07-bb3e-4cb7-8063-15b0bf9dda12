/*
 * @Date: 2022-05-19 16:51:41
 * @LastEditors: wangminze
 * @LastEditTime: 2022-05-19 17:57:55
 * @FilePath: \web\src\components\dynamic\Cascader.js
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Cascader } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'cascader'
  },
  wrapper: wrapper()
})
export default class CascaderCompo extends PureComponent {
  onChange = value => {
    let { onChange } = this.props
    console.log('value', value)
    onChange && onChange(value)
  }

  render() {
    let { value, field, tag } = this.props
    tag = tag || field.tags || []
    let { placeholder, optional = true } = field
    let disabled = isDisable(this.props)
    return (
      <Cascader
        style={{ width: '100%' }}
        disabled={disabled}
        placeholder={placeholder}
        onChange={this.onChange}
        value={value}
        size="large"
        options={tag}
        allowClear={optional}
      />
    )
  }
}