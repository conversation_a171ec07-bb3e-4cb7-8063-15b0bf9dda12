import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import {  Select, Spin, Icon } from 'antd'
import styles from './index.module.less'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import {  get, } from 'lodash'
import {  wrapper } from '../../layout/FormWrapper'
const ViewBox = api.require('@inter-connection/view-edit/temp/index')

interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData } = field
      return type === 'engineConnect'
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback()
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  specificationGroupsList: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
  requisitionInfo: state['@bills'].requisitionInfo,
  fieldMap: state['@common'].fieldMap
}))
export default class DataLinkEdit extends PureComponent<Props, State> {
  bus: any = new MessageCenter()
  lastFetchId = 0
  constructor(props) {
    super(props)
    let id = (get(props, 'field.dataType.elemType.entity') || '').replace('engine.Connect.', '')
    let mode = this.props?.field?.importMode == 'MULTIPLE'
    this.state = {
      mode: mode || false,
      interId: id,
      data: [],
      value: [],
      valuearr: [],
      fetching: false,
      master: {}
    }
  }

  componentDidMount() {
    let { interId } = this.state
    let { IngineConnect, value } = this.props
    if(!interId){
      return 
    }
    if (!IngineConnect && (value?.length == 0 || !value)) {
      return 
    }
    this.getTemplate()
    this.getData(this.props?.value || [])
  }
  getData = (ids: any[]) => {
    if (ids.length == 0) {
      return
    }
    let { interId } = this.state
    api.invokeService('@inter-connection:get:template:data', { id: interId, ids: ids.join(',') }).then((res: any) => {
      const valuearr = res?.items || []
      this.setState({ valuearr, fetching: false })
    })
  }
  getTemplate = () => {
    let { interId } = this.state
    api.invokeService('@inter-connection:get:template:by:id', interId).then((res: any) => {
      if (res?.value) {
        this.setState({
          master: res.value
        })
      }
    })
  }

  handleClick = (i: any, index: number) => {
    let dateType=this.state?.master?.config?.dateType
    console.log(i, index)
    if(dateType=='FLOW' && i?.id){
      api.invokeService('@bills:get:flow-info', { id: i.id }).then(resp => {
        this.__billInfoPopup(resp.value)
      })
    }else if(dateType=='DATALINK' && i?.id){
      api.invokeService('@bills:get:datalink:template:byId', { entityId: i?.id, type: 'CARD' }).then(res => {
        let u=res.value?.data || {}
        u.id=i?.id
        this.getPlatform(u)
      })
    }
    
  }
  getPlatform=(u)=>{
    let platformName=u?.dataLink?.entity?.name
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: { ...u, platformName },
      showClose: true,
      viewKey: 'DataLinkDetailModal'
    })
    return
  }
  __billInfoPopup = flow => {
    let title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    const params = {
      title,
      invokeService: '@bills:get:flow-info',
      params: { id: flow.id },
      backlog: { id: -1, flowId: flow },
      reload: ()=>{}
    }
    api.open('@bills:BillInfoPopup', params, true)
  }
  renderList = () => {
    let { valuearr, master, mode } = this.state
    let data = []
    let { meta = {}, mappings } = master
    if (!mappings || mappings.length == 0 || !meta ) {
      return null
    }
    if (valuearr?.length > 0) {
      data = mode ? valuearr : [valuearr[0]]
    }
    return (
      <div >
        <ViewBox
          master={meta?.template || meta}
          temp={master}
          data={data}
          platform="pc"
          handleClick={this.handleClick}
        />
      </div>
    )
  }
  render() {
    let { interId } = this.state
    if(!interId){
      return (
        <p>{i18n.get('无法解析')}</p>
      )
    }
    return (
      <div className={styles['inter-connection-box']}>
        {this.renderList()}
      </div>
    )
  }
}
