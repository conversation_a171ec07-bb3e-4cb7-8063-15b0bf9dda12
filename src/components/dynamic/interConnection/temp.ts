export const tempBindDataLink = {
  sourceType: 'empty',
  // sourceType: 'dataLink',
  // sourceId: '38040b91f773b501d400',
  // sourceType: 'bill',
  // sourceId: 'ZagbAvGQ7Ick00:390ec4350fe5b416cb81c638bd5f5962148d8b89',
  metaId: '',
  meta: {
    style: {
      borderRadius: '4px',
      boxShadow: '0 0 0 1px rgba(29,43,61,.03), 0 2px 8px 0 rgba(29,43,61,.09)',
      lineHeight: '22px',
      overflow: 'auto',
      minHeight: '100px'
    },
    width: 350,
    scale: 0,
    height: 200,
    views: [
      {
        name: 'master_1',
        type: 'container',
        position: 'relative',
        className: 'temp-empty-box',
        style: {
          display: 'flex',
          alignItems: 'center',
          height: '40px',
          background: '#fff'
        },
        children: [
          {
            label: '标题',
            name: 'master_1',
            type: 'label',
            accept: [],
            position: 'relative',
            empty: ' ',
            style: {
              display: 'inline-block',
              width: '72px',
              margin: '0px 18px 0px 16px',
              flexShrink: 0
            }
          },
          {
            label: '标题',
            name: 'master_1',
            type: 'point',
            accept: ['*'],
            position: 'relative',
            parentShow: 'none',
            style: {
              marginRight: '10px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              color: '#9e9e9e'
            }
          }
        ]
      },
      {
        name: 'master_2',
        type: 'container',
        position: 'relative',
        className: 'temp-empty-box',
        style: {
          display: 'flex',
          alignItems: 'center',
          height: '40px',
          background: '#fff'
        },
        children: [
          {
            label: 'id',
            name: 'master_2',
            type: 'label',
            accept: [],
            position: 'relative',
            empty: ' ',
            style: {
              display: 'inline-block',
              width: '72px',
              margin: '0px 18px 0px 16px',
              flexShrink: 0
            }
          },
          {
            label: 'id',
            name: 'master_2',
            type: 'point',
            accept: ['*'],
            position: 'relative',
            parentShow: 'none',
            style: {
              marginRight: '10px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              color: '#9e9e9e'
            }
          }
        ]
      }
    ]
  },
  mapping: [
    {
      name: 'master_1',
      type: 'string',
      key: 'value',
      label: '标题2',
      format: ''
    },
    {
      name: 'master_2',
      type: 'string',
      key: 'id',
      label: 'id=',
      expression: '',
      format: ''
    },
    {
      name: 'master_3',
      type: 'string',
      key: 'E_38040b91f773b501d400_订单状态',
      label: '超标',
      expression: '',
      format: ''
    },
    {
      name: 'master_4',
      type: 'money',
      key: 'E_38040b91f773b501d400_订单金额',
      label: '价格'
    },
    {
      name: 'master_5',
      type: 'string',
      key: 'E_38040b91f773b501d400_出发机场',
      label: '出发地'
    },
    {
      name: 'master_6',
      type: 'date',
      key: 'E_38040b91f773b501d400_出发时间',
      label: '出发时间',
      format: 'HH:mm'
    },
    {
      name: 'master_7',
      type: 'string',
      key: 'E_38040b91f773b501d400_航班号',
      label: '车次'
    },
    {
      name: 'master_8',
      type: 'string',
      key: 'E_38040b91f773b501d400_到达机场',
      label: '目的地'
    },
    {
      name: 'master_9',
      type: 'date',
      key: 'E_38040b91f773b501d400_到达时间',
      label: '到达时间',
      format: 'HH:mm'
    },
    {
      name: 'master_10',
      type: 'string',
      key: 'E_38040b91f773b501d400_航空公司',
      label: '席位'
    },
    {
      name: 'master_11',
      type: 'date',
      key: 'E_38040b91f773b501d400_日期',
      label: '日期',
      format: 'YYYY-MM-DD'
    }
  ],
  config: {
    dateType: 'API',
    selectable: false,
    refKey: '',
    linkDetail: false,
    apiData: {
      url: 'http://httpbin.org/post',
      sign: 'ISV_MALL'
    }
  }
}
