@import '~@ekuaibao/eui-styles/less/token.less';

.currency-wrapper {
  :global {
    .select-box {
      width: 100%;
      height: 28px;
      
    }
    .currency_span{
        
      color:#cbcbcb;
    }
  }
}
.currency-dropdown-item {
    padding: 8px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    :global {
      .item-right {
        display: flex;
        flex-direction: row;
        .item-img {
          width: 42px;
          height: 42px;
          border-radius: 21px;
        }
        .item-content {
          margin-left: 14px;
          display: flex;
          flex-direction: column;
          .item-currency {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.5;
            text-align: left;
            color: rgba(0, 0, 0, 0.65);
          }
          .item-code {
            margin-top: 4px;
            font-size: 12px;
            line-height: 1.5;
            text-align: left;
            color: #6c6c6c;
          }
        }
      }
  
      .item-action {
        width: 48px;
        height: 22px;
        border-radius: 2px;
        background-color: #ffffff;
        border: solid 1px var(--brand-base);
        font-size: 12px;
        line-height: 20px;
        color: var(--brand-base);
        text-align: center;
        cursor: pointer;
      }
      .item-action-disable {
        font-size: 12px;
        line-height: 1.5;
        text-align: left;
        color: #9e9e9e;
      }
      
    }
  }

.currency-dropdown-menu {
    padding: 8px;
    min-width: 350px;
  }
  