import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import Painter from '@ekuaibao/painter'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getLoanStatisticsWidgetCard, fetchDSL } from '../utils/BlockUI'
import { Icon, Spin } from 'antd'
import { IMG_REG } from '@ekuaibao/lib/lib/enums'
import './BlockUI.less'

export const blockUIactions = {
  'app:open:modal': value => {
    api.open('@bills:BlockUIModal', { data: value })
  },
  'app:go': value => {
    api.emit('@vendor:open:link', value)
  },
  'app:attachment:preview': attachment => {
    if (!IMG_REG.test(attachment)) {
      api.emit('@vendor:preview', attachment, '')
      return
    }
    api.emit('@vendor:preview:images', '', attachment)
  },
  'app:attachment:download': attachment => {
    if (attachment.includes('ekuaibao')) {
      api.emit('@vendor:download', attachment)
      return
    }
    api.emit('@vendor:open:link', attachment)
  }
}

@EnhanceField({
  descriptor: {
    type: 'engineBlockUI'
  },
  wrapper: wrapper(false)
})
export default class BlockUI extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { value: undefined, isloading: true }
  }

  updateBlockUI = newSubmitter => {
    const { field } = this.props
    const referenceData = field?.dataType?.entity?.split('.').pop()
    console.log('field', field)

    if (referenceData === 'LOAN_STATISTICS') {
      const config = get(field, 'blockUIConfig.loanStatistics', 'fromBorrower')
      const staffId = newSubmitter?.id
      if (config === 'fromBorrower') {
        fetchDSL(referenceData, { staffId }).then(res => {
          this.setState({ value: res?.value || '' })
        })
      }
    } else if (field?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0) {
      getLoanStatisticsWidgetCard(this.props).then(value => {
        this.setState({ value: value.errorCode ? null : value })
      })
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('update:blockUI', this.updateBlockUI)
  }

  componentDidMount() {
    const { field, form, bus, flowId = '' } = this.props
    bus.on('update:blockUI', this.updateBlockUI)
    setTimeout(() => {
      if (field?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0) {
        getLoanStatisticsWidgetCard(this.props).then(value => {
          this.setState({ value: !value || value.errorCode ? null : value, isloading: false })
        })
      } else {
        const referenceData = field?.dataType?.entity?.split('.').pop()
        let staffId = undefined

        if (referenceData === 'LOAN_STATISTICS') {
          const values = form.getFieldsValue()
          const me = api.getState()['@common'].userinfo?.staff
          const config = get(field, 'blockUIConfig.loanStatistics', 'fromSubmitter')
          staffId = config === 'fromBorrower' ? values?.submitterId?.id ?? me.id : me.id //fromBorrower
        }

        // 集采需求
        if (referenceData === 'ENTERPRISE_PURCHASE_ORDER') {
          const data = staffId ? { staffId, flowId: flowId } : { flowId }
          const body = { data }
          fetchDSL('COMPANYORDERN8N:BLOCK_UI_RENDER', body).then(res => {
            this.setState({ value: res?.value || '', isloading: false })
          })
        } else if (referenceData === 'ADJUSTMENTNODE') {
          //KA预算调整
          const body = { data: { flowId, staffId } }
          fetchDSL(`${referenceData}:BLOCK_UI_RENDER`, body).then(res => {
            this.setState({ value: res?.value || '', isloading: false })
          })
        } else if (referenceData !== 'PEER_AUDIT_MAIN') {
          fetchDSL(
            referenceData,
            staffId ? { staffId, flowId: flowId, propertyId: field?.field } : { flowId, propertyId: field?.field }
          ).then(res => {
            this.setState({ value: res?.value || '', isloading: false })
          })
        }
      }
    }, 200)
  }

  async refreshFn() {
    this.setState({ isloading: true })
    getLoanStatisticsWidgetCard(this.props).then(value => {
      this.setState({ value: !value || value.errorCode ? null : value, isloading: false })
    })
  }

  renderSyncIcon(isWidgetCard) {
    if (isWidgetCard) {
      return <Icon type="sync" className="blockui_icon-sync" onClick={() => this.refreshFn()} />
    }
    return null
  }

  render() {
    const { value, isloading } = this.state
    const isWidgetCard = this.props.field?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0

    if (isloading || !value) {
      return (
        <div className={`blockui_container bg_f6 ${isWidgetCard ? 'widget-card' : ''}`}>
          {this.renderSyncIcon(isWidgetCard)}
          {isloading ? (
            <Spin tip="加载中..." size="large" className="blockui_loading" />
          ) : (
            <div className="blockui_error">
              <img src={require('../../images/blockui-error.png')} alt="" />
              <p>访问错误，请联系企业管理员进行排查</p>
            </div>
          )}
        </div>
      )
    }

    if (!value.blocks) {
      value.blocks = []
    }

    // block ui 附件的事件设计不合理，这里需要重新设计
    value.blocks = value.blocks.map(block => {
      if (block.type === 'attachment') {
        block.action_id = ['app:attachment:preview', 'app:attachment:download']
      }

      return block
    })

    return (
      <div className={`blockui_container ${isWidgetCard ? 'widget-card' : ''}`}>
        {this.renderSyncIcon(isWidgetCard)}
        <Painter.Painter {...value} actions={blockUIactions} />
      </div>
    )
  }
}
