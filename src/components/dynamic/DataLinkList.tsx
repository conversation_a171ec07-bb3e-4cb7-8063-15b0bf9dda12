import React, { PureComponent, Fragment } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './DataLink.module.less'
import { get } from 'lodash'
import { isDisable } from '../utils/fnDisableComponent'
import { Button } from '@hose/eui'
import DataLinkCard from '../../elements/dataLink-card/DataLinkCard'
import { app as api } from '@ekuaibao/whispered'
import { isTravelOrder } from './utils'
import { showModal } from '@ekuaibao/show-util'
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')
import { EnhanceConnect } from '@ekuaibao/store'
import { getValue } from '../../elements/dataLink-card/utils/dataLinkUtils'
interface CPProps {
  [key: string]: any
}
interface CPState {
  [key: string]: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'dataLinks'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect((state: any) => ({
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
export default class DataLink extends PureComponent<CPProps, CPState> {
  constructor(props: CPProps) {
    super(props)
    let { field } = this.props
    this.state = {
      field: field,
      islock: false
    }
  }
  isEntity(value: any) {
    return value[0]?.data && value[0]?.path && value[0]?.template && value[0]?.data?.dataLink
  }

  componentDidMount() {
    const {
      bus,
      value,
      field: { referenceData },
      onChange
    } = this.props
    bus.on('continue:add:detail', this.handleContinueAddDetail)
    if (value && Array.isArray(value) && value.length > 0) {
      if (this.isEntity(value)) {
        value.forEach((i: any) => {
          i.id = i.data.dataLink.id
        })
        onChange(value)
      } else {
        this.getDataLinkTempById(value.map(i => i?.id || i))
      }
    }
  }

  getDataLinkTempById = (ids: any[]) => {
    const { onChange } = this.props
    api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds: ids }).then((values: any) => {
      let data = values.items || []
      data.forEach((i: any) => {
        i.id = i.data.dataLink.id
      })
      onChange(data)
    })
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('continue:add:detail', this.handleContinueAddDetail)
  }

  handleContinueAddDetail = () => {
    const { onChange } = this.props
    onChange(undefined)
  }

  handleRenewSelect = () => {
    this.fetchDataLink()
  }

  fetchDataLink = () => {
    const { bus, flowId, value } = this.props
    const {
      field: { referenceData, label, filterId },
      islock
    } = this.state
    const groupType = get(referenceData, 'platformId.groupType')
    const type = get(referenceData, 'platformId.type')

    let values = value ? (Array.isArray(value) ? value : [value]) : []
    fnCheckNeedPhone()({ groupType, type }).then((res: any): any => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal').then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }
      return bus
        .invoke('element:select:dataLink', {
          referenceData,
          flowId,
          selectedEntity: values,
          values: values,
          islock,
          filterId,
          multiple: true,
          dataLink: { id: referenceData.id, selectedEntity: values, type, name: label }
        })
        .then((result: any) => {
          const { data } = result
          this.getDataLinkTempById(data)
        })
    })
  }

  handleClearRelation = () => {
    const { onChange } = this.props
    onChange([])
  }

  hanldeOnClick = (value: any) => {
    const { field } = this.props
    const isOrder = isTravelOrder(field)
    const dataLink = get(value, 'data.dataLink')
    const tripType = get(dataLink, 'entity.type', '')
    if (value) {
      if (isOrder) {
        api.open('@bills:TripOrderPopup', {
          title: i18n.get('订单详情'),
          entityInfo: { ...value.data },
          tripType: tripType
        })
      } else {
        const id = value.id || get(value, 'data.dataLink.id')
        api.open('@bills:DataLinkDetailModal', {
          entityInfo: { dataLink: { id }, entityId: field.referenceData },
          field,
          showClose: true
        })
      }
    }
  }

  renderSimple = value => {
    const titles = value
      ?.map((item: any) => {
        if (item) {
          const titleField = get(item, 'template.content.expansion.title.fields')
          const data = get(item, 'data')
          const title = getValue(titleField, data)
          return title
        }
        return null
      })
      .filter(Boolean)

    return <div className="card-list-simple">{titles?.join(',')}</div>
  }
  renderlist = () => {
    const { field, value = [], external } = this.props

    return (
      <div className="card-list">
        {field?.isSimple
          ? this.renderSimple(value)
          : value.map((item: any, index: number) => {
              return (
                <DataLinkCard
                  field={field}
                  entityInfo={item}
                  external={external}
                  key={index}
                  onClick={() => this.hanldeOnClick(item)}
                  onRenewSelet={this.handleRenewSelect}
                  onClearRelation={this.handleClearRelation}
                  isReaonly={true}
                />
              )
            })}

        <div className="card-bottom">
          <span
            onClick={e => {
              e.stopPropagation()
              e.preventDefault()
              this.handleRenewSelect()
            }}
          >
            {i18n.get('重新选择')}
          </span>
          <span
            onClick={e => {
              e.stopPropagation()
              e.preventDefault()
              this.handleClearRelation()
            }}
          >
            {i18n.get('清空关联')}
          </span>
        </div>
      </div>
    )
  }
  renderDataLink = () => {
    const { value = [] } = this.props
    const disabled = isDisable(this.props)
    return (
      <Fragment>
        {value.length > 0 ? (
          this.renderlist()
        ) : (
          <div className="datalink-select">
            <Button onClick={this.handleRenewSelect} theme="highlight" category="secondary" disabled={disabled}>
              {i18n.get('点击选择')}
            </Button>
          </div>
        )}
      </Fragment>
    )
  }

  render() {
    return <div className={styles['dataLink-input-wrapper']}>{this.renderDataLink()}</div>
  }
}
