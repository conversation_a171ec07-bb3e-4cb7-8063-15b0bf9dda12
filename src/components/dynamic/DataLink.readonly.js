/**
 *  Created by gym on 2018/5/25 下午5:05.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import CarBusinessCard from '../../elements/dataLink-card/MyCarBusinessCard'
import DataLinkCard from '../../elements/dataLink-card/DataLinkCard'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { isTravelOrder } from './utils'
const baseData = ['supplierReconciliation', 'supplierSettlement', 'supplier']
@EnhanceField({
  descriptor: {
    type: 'dataLink'
  },
  wrapper: wrapper(true)
})
export default class DataLink extends PureComponent {
  state = { datalinkValue: undefined }

  isEntity(value) {
    if (__DEV__ && value && Array.isArray(value.data)) {
      value.data = value.data[0]
    }
    return value && value.data && value.data.dataLink && value.data.dataLink.id && value.template
  }

  componentDidMount() {
    const { value, field } = this.props
    if (this.isEntity(value)) {
      this.setState({ datalinkValue: value })
    } else {
      const type = get(field, 'referenceData.platformId.type')
      if (value && type !== 'PRIVATE_CAR') {
        this.getDataLinkTempById(value.id)
      }
    }
  }

  getDataLinkTempById = id => {
    id &&
      api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(res => {
        this.setState({ datalinkValue: res.value })
      })
  }

  componentWillReceiveProps(nextProps) {
    let preValue = this.props.value
    let nextValue = nextProps.value
    if ((preValue && preValue.id) !== (nextValue && nextValue.id)) {
      if (this.isEntity(nextValue)) {
        this.setState({ datalinkValue: nextValue })
      } else {
        nextProps.value && this.getDataLinkTempById(nextProps.value.id)
      }
    } else if (!preValue) {
      this.setState({ datalinkValue: undefined })
    }
  }

  handleOnClick = () => {
    const { value, field, bus, formBus, disabledStaff = false } = this.props
    const { datalinkValue } = this.state
    const isOrder = isTravelOrder(field)
    const eventBus = formBus || bus
    const dataLink = get(datalinkValue, 'data.dataLink')
    const tripType = get(dataLink, 'entity.type', '')
    if (value) {
      if (isOrder) {
        api.open('@bills:TripOrderPopup', {
          title: i18n.get('订单详情'),
          entityInfo: { ...datalinkValue.data },
          tripType: tripType
        })
      } else {
        eventBus.emit('element:datalink:card:click', {
          entityInfo: { dataLink: { id: this.isEntity(value) ? value.data.dataLink.id : value.id } },
          field,
          showClose: true,
          disabledStaff
        })
      }
    }
  }

  render() {
    const { value, field, page } = this.props
    const isReconciliation = get(field, 'name') === 'supplierReconciliation'
    const { datalinkValue } = this.state
    let type = get(field, 'referenceData.platformId.type')
    if (type === 'PRIVATE_CAR') {
      field.label = i18n.get('行车记录')
    }
    const showCard = type === 'PRIVATE_CAR' && value
    if (showCard) {
      return <CarBusinessCard {...this.props} />
    }

    const hideOverview = baseData.indexOf(get(field, 'name')) !== -1
    return (
      <DataLinkCard
        isReconciliation={isReconciliation}
        field={field}
        isReaonly={true}
        hideOverview={hideOverview}
        entityInfo={datalinkValue}
        page={page}
        onClick={this.handleOnClick}
      />
    )
  }
}
