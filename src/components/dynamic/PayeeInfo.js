import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import FakeInput from '../../elements/puppet/FakeInput'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import PayeeInfoLine from '../../elements/puppet/PayeeInfoLine'
import { app as api } from '@ekuaibao/whispered'
import { isDisable, getModifyFileds } from '../utils/fnDisableComponent'
import SetMultiplePayee from './SetMultiplePayee'
import { EnhanceConnect } from '@ekuaibao/store'
import { getRecordLink } from './helpers/getRecordLink'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import CurrencyDropdown from '../../elements/currency/currency-dropdown'
import { OutlinedDirectionDown, IllustrationSmallNoContent } from '@hose/eui-icons'
import { message } from '@hose/eui'
import styles from './PayeeInfo.module.less'
import classNames from 'classnames'
import { debounce } from 'lodash'
import { getAllReceivingCurrency } from '../utils/fnCurrencyObj'
import { getBoolVariation } from '../../lib/featbit'
import { useNewAutomaticAssignment } from '../utils/fnAutoDependence'

@EnhanceConnect(
  state => ({
    multiplePayeesMode: state['@bills'].multiplePayeesMode,
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    standardCurrency: state['@common'].standardCurrency,
    historyCurrencyInfo: state['@bills'].historyCurrencyInfo,
    allCurrencyRates: state['@common'].allCurrencyRates
  }),
  {
    getRecordLink
  }
)
@EnhanceField({
  descriptor: {
    type: 'payeeInfo'
  },
  initialValue(props) {
    const { field, multiplePayeesMode = false, payPlanMode, payeePayPlan, noDefault = false, approveModify } = props
    const { type, value } = field.defaultValue || {}
    if (noDefault) {
      return null
    }
    if (
      (!multiplePayeesMode && type === 'predefine' && value !== 'submit.requisition' && !approveModify) ||
      field?.editable === false
    ) {
      return api.getState('@common.defaultPayee')
    } else if (multiplePayeesMode) {
      return { multiplePayeesMode, payPlanMode, payeePayPlan }
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    // 修复不能提交多收款人单据的 bug https://jira.ekuaibao.com/browse/PRO-25008
    // 该撤回会引起 PRO-24989 这个 bug 再次出现
    const { multiplePayeesMode = false, payConfig } = props
    if (multiplePayeesMode) {
      const { label, source } = field
      if (!value?.id && source && source === 'dataLink') {
        return callback(i18n.get('not-empty', { label: i18n.get(label) }))
      }
      return callback(undefined)
    } else {
      let errorTip = []
      if (payConfig?.allowSelectionReceivingCurrency && !value?.receivingCurrency) {
        errorTip.push(i18n.get('not-empty', { label: i18n.get('收款币种') }))
      }
      const message = required(field, value)
      message && errorTip.push(message)
      if (errorTip.length) return callback(errorTip.join('，'))
      return callback()
    }
  },
  wrapper: wrapper()
})
export default class PayeeInfo extends PureComponent {
  constructor(props) {
    super(props)
    const { field } = props
    const { dependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      isDependence,
      dependenceMap,
      dependenceList: [],
      allReceivingCurrency: [],
      receivingCurrency: {},
      isReceivingCurrencyEmpty: true
    }
  }

  componentWillMount() {
    let { bus } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
    bus.on('dimention:currency:init', this.initDimensionCurrencyChange)
    bus.on('dimention:currency:change', this.handleDimensionCurrencyChange)
    bus.watch('set:payeeInfo:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
    this.initReceivingCurrency()
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
    bus.un('dimention:currency:init', this.initDimensionCurrencyChange)
    bus.un('dimention:currency:change', this.handleDimensionCurrencyChange)
    bus.un('set:payeeInfo:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
  }

  handleDependenceChange = ({ key, id, dependenceFeeType = false }, options = {}) => {
    let {
      getRecordLink,
      field,
      onChange,
      form,
      value,
      flowId,
      multiplePayeesMode,
      isDetail,
      billState,
      detailId
    } = this.props
    let { dependence, dataType, dependenceCondition } = field
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据
    const isMultiplePayees = multiplePayeesMode && !isDetail
    if (dependence && dependence?.length && !isMultiplePayees) {
      let isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        const { dependenceMap } = this.state
        let list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        this.currentIdx = id //标明promise的执行顺序
        getRecordLink({
          recordSearch: list,
          entity: dataType.entity,
          defaultValue: value && value.id,
          flowId,
          dependenceFeeType,
          dependenceCondition
        }).then(action => {
          const reqIdx = id // 闭包记录id
          let { items } = action.payload
          let newValue = undefined
          if (this.currentIdx === reqIdx) {
            if (isInitLoad) {
              const fieldsValue = form.getFieldsValue()
              // 初始化进来 当前value 值赋值
              newValue = fieldsValue[field?.field] ?? undefined
              if (
                getBoolVariation('cyxq-75173-payee-dependence-optimize') &&
                !!newValue &&
                items?.length &&
                items.findIndex(v => v.id === newValue?.id) === -1
              ) {
                newValue = undefined
              }
              onChange(this.fnGetValue(newValue))
            } else {
              if (value?.id && items.findIndex(v => v.id === value.id) === -1) {
                onChange && onChange(this.fnGetValue())
              }
              if (!useNewAutomaticAssignment() && items.length === 1) {
                newValue = items[0]
                onChange && onChange(this.fnGetValue(items[0]))
              }
            }
            // TODO: 档案关系埋点
            let { billData, billSpecification, feeType, dataSource } = this.props
            const oldValue = this.props?.value
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })
            this.setState({
              dependenceList: items.map(v => v.id)
            })
          }
        })
      }
    }
  }

  handleClick = () => {
    const { bus, onChange, value, field, template } = this.props
    const { filterRules } = field
    const { isDependence, dependenceList } = this.state
    let list
    const isFeeDetailPayeeId = field?.name === 'feeDetailPayeeId'
    if (isDependence) {
      if (isFeeDetailPayeeId) {
        const depFieldNames = field.dependence.map(el => el.dependenceId)
        const hasDepField = !!template.find(el => depFieldNames.includes(el.name))
        if (hasDepField) {
          list = dependenceList
        }
      } else {
        list = dependenceList
      }
    }
    bus
      .invoke('element:ref:select:payee', value, list, isFeeDetailPayeeId, {
        allowCancelDependence: field?.allowCancelDependence,
        filterRules
      })
      .then(data => {
        this.handleValueChange(data)
      })
  }

  fnGetValue = data => {
    const { payConfig } = this.props
    const { receivingCurrency } = this.state
    if (payConfig?.allowSelectionReceivingCurrency && receivingCurrency) {
      return {
        ...(data || {}),
        receivingCurrency: receivingCurrency?.numCode
      }
    }
    return data
  }

  handleChange = mltiplePayeeObj => {
    const { bus, onChange } = this.props
    if (mltiplePayeeObj.multiplePayeesMode) {
      onChange(this.fnGetValue(mltiplePayeeObj))
    } else {
      const { field } = this.props
      const { defaultValue = {} } = field
      if (defaultValue && defaultValue.type === 'predefine' && defaultValue.value !== 'submit.requisition') {
        const payee = api.getState('@common.defaultPayee')
        if (payee) onChange(this.fnGetValue(payee))
      }
    }
    bus.emit('payeeInfo:isMultiplePayee', mltiplePayeeObj)
  }

  handleRemove = e => {
    const { bus, onChange } = this.props
    e.persist()
    e.nativeEvent.stopImmediatePropagation()
    e.stopPropagation()
    e.preventDefault()
    if (onChange) {
      onChange(this.fnGetValue({}))
      bus.emit('payeeInfo:isMultiplePayee', { multiplePayeesMode: false, payPlanMode: false, payeePayPlan: false })
    }
  }

  initReceivingCurrency = () => {
    const {
      payConfig,
      dataSource,
      onChange,
      value = {},
      bus,
      dimentionCurrencyInfo,
      standardCurrency,
      allCurrencyRates
    } = this.props
    if (!payConfig?.allowSelectionReceivingCurrency) return

    const allReceivingCurrency = getAllReceivingCurrency(
      dimentionCurrencyInfo?.currency || standardCurrency,
      allCurrencyRates,
      payConfig.currencyRange
    )
    const isReceivingCurrencyEmpty = allReceivingCurrency.length === 0
    if (isReceivingCurrencyEmpty) return

    let receivingCurrency = allReceivingCurrency[0]
    const receivingCurrencyNumCode = dataSource?.form?.receivingCurrency || dataSource?.form?.payeeId?.receivingCurrency
    if (receivingCurrencyNumCode) {
      receivingCurrency =
        allReceivingCurrency.find(item => item.numCode === receivingCurrencyNumCode) || allReceivingCurrency[0]
    }
    this.setState({ receivingCurrency, isReceivingCurrencyEmpty, allReceivingCurrency })

    bus.setValidateLevel(1)
    onChange && onChange({ ...value, receivingCurrency: receivingCurrency.numCode })
  }

  initDimensionCurrencyChange = ({ currency, rates }) => {
    const { dataSource } = this.props
    const receivingCurrencyNumCode = dataSource?.form?.receivingCurrency || dataSource?.form?.payeeId?.receivingCurrency
    if (receivingCurrencyNumCode) {
      this.fnUpdateCurrencyInfo(currency, rates, true, receivingCurrencyNumCode)
    }
  }

  handleDimensionCurrencyChange = debounce(({ currency, rates }) => {
    const { payConfig } = this.props
    const allReceivingCurrency = this.fnUpdateCurrencyInfo(currency, rates, true)
    if (payConfig?.allowSelectionReceivingCurrency) {
      this.checkDetails(allReceivingCurrency?.[0] || {})
    }
  }, 100)

  setHistoryCurrencyAndChangeRate = (originalId, historyCurrencyInfo) => {
    const dimentionCurrencyInfo = api.getState()['@bills']?.dimentionCurrencyInfo
    const { standardCurrency } = this.props
    const currency = dimentionCurrencyInfo?.currency || standardCurrency || {}
    if (originalId === currency.numCode) {
      this.fnUpdateCurrencyInfo(currency, historyCurrencyInfo, false)
    }
  }

  fnUpdateCurrencyInfo = (
    currency,
    rates,
    updateReceivingCurrencyNumCode = false,
    initReceivingCurrencyNumCode = ''
  ) => {
    const { payConfig, bus, onChange, value = {} } = this.props
    if (!payConfig?.allowSelectionReceivingCurrency) return
    const allReceivingCurrency = getAllReceivingCurrency(currency, rates, payConfig.currencyRange)

    const isReceivingCurrencyEmpty = !allReceivingCurrency.length
    this.setState({ allReceivingCurrency, isReceivingCurrencyEmpty })

    if (initReceivingCurrencyNumCode) {
      const currentReceivingCurrency =
        allReceivingCurrency.find(item => item.numCode === initReceivingCurrencyNumCode) || allReceivingCurrency[0]
      this.setState({ receivingCurrency: currentReceivingCurrency })
      bus.setValidateLevel(1)
      onChange && onChange({ ...value, receivingCurrency: currentReceivingCurrency?.numCode })
    } else if (updateReceivingCurrencyNumCode) {
      const currentReceivingCurrency = isReceivingCurrencyEmpty ? {} : allReceivingCurrency[0]
      this.setState({ receivingCurrency: currentReceivingCurrency })
      bus.setValidateLevel(1)
      onChange && onChange({ ...value, receivingCurrency: currentReceivingCurrency?.numCode })
    }
    return allReceivingCurrency
  }

  checkDetails = currency => {
    const { form, bus } = this.props
    const { details } = form?.getFieldsValue()
    bus.emit('switch:payee:currency:change', { currency })
    if (details?.length > 0) {
      message.info(i18n.get('收款币种已更改，请检查金额'))
    }
  }

  handleChangeReceivingCurrency = e => {
    const { value = {}, onChange } = this.props
    this.setState({ receivingCurrency: e })
    onChange && onChange({ ...value, receivingCurrency: e.numCode })
    this.checkDetails(e)
  }

  renderReceivingCurrency = () => {
    const { allReceivingCurrency, receivingCurrency, isReceivingCurrencyEmpty } = this.state
    return (
      <div className={classNames(styles['receiving-currency-wrapper'])}>
        <CurrencyDropdown
          trigger={['click']}
          data={allReceivingCurrency}
          checkedData={[receivingCurrency]}
          onChange={this.handleChangeReceivingCurrency}
          noSearch={isReceivingCurrencyEmpty}
          emptyIcon={isReceivingCurrencyEmpty && <IllustrationSmallNoContent fontSize={120} />}
          emptyText={
            isReceivingCurrencyEmpty && i18n.get('收款币种不可选\n请联系管理员修改单据模版上可选的收款币种范围')
          }
          useEUI
        >
          <div className="receiving-currency-content">
            {i18n.get(`收款币种（{__k0}）`, {
              __k0: isReceivingCurrencyEmpty ? i18n.get('暂无') : receivingCurrency?.strCode
            })}
            <OutlinedDirectionDown fontSize={12} className="ml-4 ai-c" />
          </div>
        </CurrencyDropdown>
      </div>
    )
  }

  handleValueChange = data => {
    const { onChange, bus } = this.props
    if (data && onChange) {
      onChange(this.fnGetValue(data))
      bus.emit('payeeInfo:isMultiplePayee', { multiplePayeesMode: false, payPlanMode: false, payeePayPlan: false })
    }
  }

  render() {
    const {
      value = {},
      field,
      multiplePayeesMode,
      payConfig,
      isModify,
      isRecordExpends,
      noDefault = false
    } = this.props
    let { optional, isFeeDetail = false, source, forbidEdit, allowClear = false } = field
    let placeholder = getPlaceholder(field)
    if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    // 若是在添加明细时 disable 状态还按照原来的逻辑判断；在编辑单据时勾选多收款人时 disabled 为 true
    const disabled = isDisable(this.props)
    let fakeInputDisabled = multiplePayeesMode ? (isFeeDetail ? disabled : multiplePayeesMode) : disabled
    if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled) {
      fakeInputDisabled = multiplePayeesMode && disabled
    } else if (multiplePayeesMode && !isFeeDetail && disabled !== undefined && disabled === false) {
      fakeInputDisabled = multiplePayeesMode && !disabled
    } else if (multiplePayeesMode && source && source === 'dataLink') {
      fakeInputDisabled = multiplePayeesMode && disabled
    }
    let placeholderValue =
      !isFeeDetail && multiplePayeesMode && source !== 'dataLink' ? i18n.get('多收款人') : placeholder
    let payeeValue = value
    if (multiplePayeesMode && !isFeeDetail && source !== 'dataLink' && !isRecordExpends) {
      payeeValue = i18n.get('多收款人')
    } else if (!multiplePayeesMode && !value && !noDefault) {
      payeeValue = api.getState('@common.defaultPayee')
    }
    if (isRecordExpends) {
      fakeInputDisabled = false
      payeeValue = value
      placeholderValue = placeholder
    }
    if (forbidEdit !== undefined && forbidEdit) {
      fakeInputDisabled = true
    }

    return (
      <div>
        <div className={styles['payee-info-wrapper']} style={{ display: 'flex' }}>
          {payConfig?.allowSelectionReceivingCurrency && this.renderReceivingCurrency()}
          <FakeInput
            className="payee-info-input"
            disabled={fakeInputDisabled}
            onClick={this.handleClick}
            placeholder={placeholderValue}
            onRemove={this.handleRemove}
            allowClear={allowClear && payeeValue?.id}
            hasPrefix={payConfig?.allowSelectionReceivingCurrency}
            emptyStatus={this.state.isReceivingCurrencyEmpty}
          >
            {(payeeValue?.id ||
              (payeeValue && typeof payeeValue === 'string' && payeeValue !== i18n.get('多收款人'))) && (
              <PayeeInfoLine value={payeeValue} onChange={this.handleValueChange} />
            )}
          </FakeInput>
        </div>
        {!isFeeDetail && payConfig?.allowMultiplePayees && (
          <SetMultiplePayee
            payConfig={payConfig}
            disabled={isModify && getModifyFileds(this.props) ? true : disabled}
            onChange={this.handleChange}
            value={value}
          />
        )}
      </div>
    )
  }
}
