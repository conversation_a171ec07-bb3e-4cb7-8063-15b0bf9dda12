/**
 * 出行人组件
 * <AUTHOR>
 * @date    2018-12-21
 */

import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import styles from './MutilStaff.module.less'
import { FetchRefList } from '../utils/FetchRefList'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { getRecordLink } from './helpers/getRecordLink'
import { clearableTripDataLink } from '../../lib/lib-util'
import { getStaffName } from '../../elements/utilFn'

@EnhanceField({
  descriptor: {
    type: 'list:ref:dataLink.Staff'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { allowExternalStaff, label, editable } = field
    if (rule.level > 0) {
      callback()
    }
    if (rule.level === 3) callback()

    if (field.optional === false) {
      if (value && value?.fullVisible) {
        callback()
      } else if (!value || (value?.role?.length < 1 && value?.staff?.length < 1 && value?.department?.length < 1)) {
        callback(i18n.get(`{__k0}不能为空`, { __k0: label }))
      }
    }
    callback()
  },
  initialValue(props) {
    let { field, value, submitterId, lastChoice } = props

    if (!value) {
      let constVal = constantValue(field)
      if (constVal) return typeof constVal === 'string' ? [constVal] : constVal
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return lastVal
    }
    let { defaultValue } = field
    let type = get(field, 'defaultValue.type', '')
    if (!value && type === 'predefine' && defaultValue.value === 'submitter') {
      value = [submitterId.id]
    }
    return value
  },
  wrapper: wrapper()
})
@EnhanceConnect(
  state => ({
    staffs: state['@common'].staffs,
    staffDisplayConfig: state['@common'].organizationConfig.staffDisplayConfig,
    travelManagementConfig: state['@tpp-v2'].travelManagementConfig
  }),
  {
    getRecordLink: getRecordLink
  }
)
@FetchRefList
export default class MutilStaff extends PureComponent {
  constructor(props) {
    super(props)
    let { field } = props
    let { dependence } = field
    let isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      dependenceList: [],
      isDependence,
      dependenceMap
    }
  }

  componentWillMount() {
    let { bus } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
  }

  currentIdx = null
  handleDependenceChange = ({ key, id, dependenceFeeType = false }) => {
    let { getRecordLink, field, onChange, bus } = this.props
    let { dependence, dataType, defaultValue, dependenceCondition } = field
    if (dependence && dependence?.length) {
      let isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        this.currentIdx = id //标明promise的执行顺序
        const { dependenceMap } = this.state
        let list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        getRecordLink({
          recordSearch: list,
          entity: dataType.elemType.entity,
          dependenceFeeType,
          dependenceCondition
        }).then(action => {
          const reqIdx = id // 闭包记录id
          let { items } = action.payload
          let newValue = items
          if (this.currentIdx === reqIdx) {
            const currentStaffs = this.props.value
            if (Array.isArray(currentStaffs)) {
              let isValid = true
              currentStaffs.forEach(staff => {
                if (items.findIndex(item => item.id === staff.id) === -1) {
                  isValid = false
                  return
                }
              })
              if (!isValid) {
                bus.setValidateLevel(3)
                newValue = undefined
                onChange(undefined)
              }
            } else if (items.length === 1) {
              bus.setValidateLevel(3)
              onChange(items)
            }
            // TODO: 档案关系埋点
            let { billData, billSpecification, feeType, dataSource, value } = this.props
            const oldValue = value
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })

            this.setState({
              dependenceList: items
            })
          }
        })
      }
    }
  }
  handleClick = () => {
    let { bus, onChange, value = {}, field } = this.props
    if (value?.fullVisible || this.getStatus()) {
      return
    }
    let department = value?.department ? value?.department.map(i => i?.id || i) : []
    let role = value?.role ? value?.role.map(i => i?.id || i) : []
    let staff = value?.staff ? value?.staff.map(i => i?.id || i) : []
    const checkedList = [
      {
        multiple: true,
        checkedKeys: staff,
        type: 'department-member'
      },
      {
        multiple: true,
        checkedKeys: department,
        type: 'department'
      },
      {
        multiple: true,
        checkedKeys: role,
        type: 'role'
      }
    ]

    bus
      .invoke('element:ref:dataLink:staffs', {
        multiple: true,
        checkedList,
        field
      })
      .then(async data => {
        onChange(data)
        return true
      })
  }
  getStatus = () => {
    let { current, type } = this.props
    return type == 'add' && current?.entity?.scoped === false
  }
  render() {
    let { value = {}, field, staffDisplayConfig = [], current, type } = this.props,
      editable = !isDisable(this.props),
      className1 = editable ? styles.wrapper : styles.wrapper_disabled,
      txt
    let users = []
    if (value?.role) {
      users.push(...value?.role)
    }
    if (value?.department) {
      users.push(...value?.department)
    }
    if (value?.staff) {
      users.push(...value?.staff)
    }
    users = users
      .filter(item => !!item)
      .map(line => {
        return getStaffName(line)
      })

    txt = users.join(',')
    if (value?.fullVisible || this.getStatus()) {
      txt = i18n.get('全部人员')
    }
    let id = 'travelers_tooltip' + field.name.replace(' ', '')
    return (
      <div>
        <div
          className={`${className1} `}
          disabled={!editable}
          onClick={e => (editable ? this.handleClick(e) : false)}
          placeholder={i18n.get(field.placeholder)}
          data-for={id}
          data-tip="dataTip"
        >
          {txt}
        </div>
      </div>
    )
  }
}
