import React, { PureComponent } from 'react'
import styles from './Currency.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Dropdown, Menu } from 'antd'
import { uniqBy } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import CurrencyChecked from '../../elements/currency/images/currency-checked.svg'
import SearchInput from '../../elements/search-input'
import { getPlaceholder } from '../utils/fnGetFieldLabel'

function MenuItem(props) {
  let { className } = props
  return <div className={className}>{props.children}</div>
}

interface CPProps {
  [key: string]: any
}
interface CPState {
  [key: string]: any
}
@EnhanceConnect((state: any) => ({
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
export default class CurrencyComponent extends PureComponent<CPProps, CPState> {
  constructor(props: CPProps) {
    super(props)
    this.state = {
      data: [],
      allData: [],
      visiblle: false
    }
  }

  async componentDidMount() {
    const { bus, currencySwitch } = this.props

    await Promise.all([
      api.dataLoader('@common.currencyConfig').load(),
      api.dataLoader('@common.allStandardCurrency').reload()
    ])

    let currentList = []
    const allStandardCurrency = api.getState()['@common'].allStandardCurrency?.slice()
    let currency = allStandardCurrency?.find(oo => oo.type === 'TENANT')
    const id = currency?.id
    if (id) {
      const currencyRateList = await api.invokeService('@currency-manage:get:currency:rates:by:Id', id)
      currency.rate = currency.rate || '1'
      currentList = currencyRateList?.items || []
      currentList.unshift(currency)
    }

    const dimentionCurrency = api.getState()['@bills'].dimentionCurrencyInfo
    if (dimentionCurrency && dimentionCurrency?.rates) {
      currentList = dimentionCurrency?.rates?.slice()
      currentList.unshift(dimentionCurrency?.currency)
    }
    currentList = uniqBy(currentList, 'numCode')
    this.setState({
      data: currentList,
      allData: currentList.slice()
    })
    currencySwitch && bus?.on('dimention:currency:change', this.handleRefChange)
  }

  handleRefChange = data => {
    let { onChange, value } = this.props
    // if(value !==data?.currency?.numCode){
    onChange && onChange('')
    // }
    let currentList = data?.rates?.slice()
    currentList.unshift(data?.currency)
    this.setState({
      data: currentList,
      allData: currentList.slice()
    })
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus?.un('dimention:currency:change', this.handleRefChange)
  }
  onVisibleChange = visible => {
    this.setState({ visible })
  }
  render() {
    let { data, visible, allData } = this.state
    let { value: checkedIds, field } = this.props
    let value = allData?.find(i => checkedIds == i?.numCode || checkedIds?.id === i?.numCode)
    const placeholder = getPlaceholder(field)
    return (
      <div className={styles['currency-wrapper']}>
        <Dropdown
          visible={visible}
          trigger={['click']}
          overlay={this.renderMenu(data)}
          placement={'bottomLeft'}
          overlayClassName={`${styles['currency-dropdown-box']}`}
          onVisibleChange={this.onVisibleChange}
        >
          <div
            className={`ant-select-lg ant-select ant-select-enabled  ${visible ? 'ant-select-open' : ''} select-box `}
          >
            <span className="ant-select-selection ant-select-selection--single">
              <div className="ant-select-selection__rendered">
                {value ? `${value?.name} (${value?.strCode})` : <span className="currency_span">{placeholder}</span>}
              </div>
            </span>
            <span className="ant-select-arrow">
              <b></b>
            </span>
          </div>
        </Dropdown>
      </div>
    )
  }
  handleValueChange = e => {
    let searchText = e.target.value
    if (searchText.trim()) {
      let searchData = []
      let allData = this.state?.allData
      allData.forEach(item => {
        if (
          !!~item.name.indexOf(searchText) ||
          !!~item.strCode.indexOf(searchText) ||
          !!~item.strCode.toLowerCase().indexOf(searchText)
        ) {
          searchData.push(item)
        }
      })
      this.setState({ data: searchData, visible: true })
    } else {
      let allData = this.state?.allData?.slice()
      this.setState({ data: allData })
    }
  }
  handleAddCurrency = (item, index) => {}
  handleChecked = item => {
    let { checkedType = 'image', onChange, multiple = false, bus, currencySwitch } = this.props
    if (checkedType === 'image') {
      let checkedIds = item.numCode
      onChange && onChange(checkedIds)
      this.setState({ visible: false })
      currencySwitch && bus?.emit('original:currency', item)
    }
  }

  renderMenu = (data = []) => {
    let { checkedType, menuStyle = {}, value: checkedIds } = this.props
    return (
      <Menu style={{ maxHeight: '270px', overflowY: 'auto', ...menuStyle }}>
        <MenuItem className={styles['currency-dropdown-menu']}>
          <SearchInput placeholder={i18n.get('搜索币种名称或代码')} onChange={this.handleValueChange} />
        </MenuItem>
        {data.length ? (
          data.map((item, index) => {
            return (
              <Menu.Item key={index}>
                <CurrencyMenuItem
                  item={item}
                  index={index}
                  checkedIds={checkedIds}
                  handleAddCurrency={this.handleAddCurrency}
                  handleChecked={this.handleChecked}
                  checkedType={checkedType}
                />
              </Menu.Item>
            )
          })
        ) : (
          <Menu.Item>
            <div className="h-300 center">{i18n.get('没有数据')}</div>
          </Menu.Item>
        )}
      </Menu>
    )
  }
}

function CurrencyMenuItem(props) {
  let { item, index, handleAddCurrency, handleChecked, checkedType = 'image', checkedIds } = props
  let { name, icon, symbol, strCode, numCode, rate } = item
  let str = checkedType === 'image' ? (rate ? i18n.get('rate', { rate }) : '') : i18n.get('symbol', { symbol })
  return (
    <div className={styles['currency-dropdown-item']} onClick={() => handleChecked(item, index)}>
      <div className="item-right">
        <img className="item-img" src={icon} />
        <div className="item-content">
          <div className="item-currency">{name}</div>
          <div className="item-code">
            {i18n.get('代码') + i18n.get(`：{__k0}（{__k1}） {__k2}`, { __k0: strCode, __k1: numCode, __k2: str })}
          </div>
        </div>
      </div>

      <ImageChecked checked={checkedIds == item?.numCode} />
    </div>
  )
}

function ImageChecked(props) {
  let { checked } = props
  return checked ? <img style={{ width: '16px', height: '16px' }} src={CurrencyChecked} /> : null
}
