import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { UIContainer as Container } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { showMessage } from '@ekuaibao/show-util'
import { MoneyMath } from '@ekuaibao/money-math'
import Big from 'big.js'
import { getMoney, fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import { isDisable } from '../utils/fnDisableComponent'
import { wrapper } from '../layout/FormWrapper'
import { formatAmortizesDateTime } from '../utils/fnDetailsApportion'
import { fnCheckApportionOtherConfig } from '../validator/validator'
import { getAllApportionSpecification } from '../../plugins/bills/util/billFetchUtil'
import { uniq, get } from 'lodash'
import moment from 'moment'
const fnDefineIsFromThirdParty = template => {
  return template.filter(t => t.editable === false && t.isFromThirdParty).length > 0
}

@EnhanceField({
  descriptor: {
    type: 'amortizes'
  },
  initialValue() {
    return undefined
  },
  wrapper: wrapper()
})
export default class Amortization extends PureComponent {
  constructor(props) {
    super(props)
    const { isModify, showAllFeeType, cannotEditAmountField, feeAmount } = props
    let disable = isDisable(props)
    let isEdit = true
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disable && cannotEditAmountField) {
      disable = true
      isEdit = false
    }
    this.state = {
      feeAmount: feeAmount, //摊销金额
      apportionFieldError: '',
      specification: undefined, //摊销模版
      disable, //是否禁用
      isEdit, //是否可编辑
      apportionTemplateAmountField: 'amount', //摊销金额字段名称
      apportionfLebal: '' //摊销金额字段的label
    }
    this.formBus = new MessageCenter()
  }

  //框架提供的
  preGetValue = validate => {
    if (this.formBus.has('get:amortization:values')) {
      return this.formBus.invoke('get:amortization:values', validate)
    }
    return undefined
  }

  componentWillMount() {
    let { bus } = this.props
    bus.on('@form:did:mount', this.setMoney)
    bus.watch('amount:changed', this.handleFeeAmountChanged)
    bus.watch('validate:amortization:value:feetype:save', this.handleFeeAmountUpdated)
    bus.on('continue:add:detail', this.handleContinueAdd)
    this.formBus.watch('update:feeType:amount', this.handleUpdateFeeTypeChanged)
  }

  async componentDidMount() {
    const { field } = this.props
    const specificationIds = field?.specificationIds
    const apportionResult = await getAllApportionSpecification('amortize')
    const list = apportionResult?.items || []
    const specification = list.find(item => item.originalId === specificationIds[0])
    const { baseDataProperties } = this.props
    const { configs = [] } = specification
    const config = configs.find(v => v.ability === 'amortize')
    const fLebal = baseDataProperties.find(f => f.name === config?.amortizeMoneyField)?.label
    this.setState({ specification, apportionfLebal: fLebal })
    this.setMoney(config && config.amortizeMoneyField)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('amount:changed', this.handleFeeAmountChanged)
    bus.un('validate:amortization:value:feetype:save', this.handleFeeAmountUpdated)
    bus.un('@form:did:mount', this.setMoney)
    bus.un('continue:add:detail', this.handleContinueAdd)
    this.formBus.un('update:feeType:amount', this.handleUpdateFeeTypeChanged)
  }

  setMoney = field => {
    let { bus } = this.props
    bus.getFieldsValue().then(value => {
      field && this.setState({ apportionTemplateAmountField: field })
      if (field && !(field in value)) {
        this.setState({
          feeAmount: field ? value[field] : value['amount'],
          apportionFieldError: '摊销模版中指定金额不存在'
        })
        return
      }
      this.setState({ feeAmount: field ? value[field] : value['amount'], apportionFieldError: '' })
    })
  }

  /**
   * 费用类型金额改变
   * @param amount
   */
  handleFeeAmountChanged = (res = {}) => {
    const { field, amount } = res
    const { apportionTemplateAmountField } = this.state
    if (!amount || apportionTemplateAmountField !== field) return
    this.setState({ feeAmount: amount })
  }

  hasDuplicates(list) {
    return uniq(list).length !== list.length
  }

  handleFeeAmountUpdated = res => {
    const { apportionTemplateAmountField } = this.state
    const { field } = this.props
    if (!this.state.isEdit) return Promise.resolve(res)
    let { amortizes } = res
    if (!amortizes?.length && field.optional) {
      return Promise.resolve(res)
    }
    if (amortizes && amortizes.length > 0) {
      const month = get(amortizes, '[0].amortizeForm.amortizeMonth') //判断是否是按照月份摊销
      if (month) {
        const isDup = this.hasDuplicates(amortizes.map(v => moment(v.amortizeForm.amortizeMonth).format('YYYY-MM')))
        if (isDup) {
          showMessage.error('摊销月份不能重复')
          return Promise.reject()
        }
      }
    }
    const amount = res[apportionTemplateAmountField]
    let apportionMoneyTotal = amortizes?.reduce((sum, line) => {
      return new MoneyMath(sum).add(line.amortizeForm.amortizeMoney).value
    }, 0)
    let apportionTotalPercent = amortizes?.reduce((sum, line) => {
      return new Big(sum).plus(line.amortizeForm.amortizePercent || 0)
    }, 0)
    let { isEqual, title } = this.fnValidateEqual(apportionMoneyTotal, apportionTotalPercent, amount, amortizes)
    if (!isEqual) {
      showMessage.error(title)
      this.setState({ errorMsg: title })
      return Promise.reject()
    }
    res.amortizes = formatAmortizesDateTime(res.amortizes)
    return Promise.resolve(res)
  }

  handleContinueAdd = () => {
    this.setState({ feeAmount: standardValueMoney(0) })
    this.formBus.emit('clear:apportion:data')
  }

  fnValidateEqual = (apportionTotalMoney, apportionTotalPercent, amount, amortizes) => {
    let title = '',
      isEqual = false
    const {
      form,
      field: { rule }
    } = this.props
    const { apportionfLebal } = this.state
    if (amount && amount.foreign) {
      isEqual = new Big(getMoney(amount))
        .minus(new Big(getMoney(apportionTotalMoney)))
        .abs()
        .lte(new Big(0.05))
    } else {
      isEqual = new Big(getMoney(apportionTotalMoney)).eq(new Big(getMoney(amount)))
    }

    if (!isEqual) {
      title = i18n.get('费用金额 ≠ 总摊销金额，请修改后重新提交', { lable: apportionfLebal })
      return { title, isEqual }
    }
    isEqual = new Big(apportionTotalPercent.toFixed(2)).eq(100)
    if (!isEqual) {
      title = i18n.get('摊销比例 ≠ 100%，请修改后重新提交')
      return { title, isEqual }
    }
    const length = amortizes.length
    //检查分摊模板其它配置
    const hasOtherConfigErr = fnCheckApportionOtherConfig(amortizes)
    if (hasOtherConfigErr) {
      title = hasOtherConfigErr
      return { title }
    }
    rule &&
      rule !== 'PERCENTAGE' &&
      amortizes.some((apportion, index) => {
        if (index < length - 2) {
          //最后两行不校验
          const { amortizeMoney, amortizePercent } = apportion.amortizeForm
          const { standard } = amortizeMoney
          const total = getMoney(amount)
          const percent = Number(total) === 0 ? new Big(total) : new Big(standard).div(total)
          isEqual = percent
            .minus(new Big(amortizePercent).div(100))
            .abs()
            .lt(0.01)
          if (!isEqual) {
            title = i18n.get('存在费用金额*摊销比例≠摊销金额的数据，请修正', { lable: apportionfLebal })
            return true
          }
        }
      })

    // 校验其他金额字段
    const apportionSpecification = amortizes[0]?.specificationId
    const otherMoneyFields = this.getOtherMoneyFieldsFromSpecification(apportionSpecification, form)
    if (otherMoneyFields?.length) {
      amortizes.forEach((apportion, idx) => {
        otherMoneyFields.forEach(el => {
          el.totalAmountCounted = new Big(getMoney(apportion.amortizeForm[el.name]))
            .plus(el.totalAmountCounted)
            .toFixed(2)
          if (length - 1 === idx) {
            if (!new Big(getMoney(el.totalAmount)).eq(el.totalAmountCounted)) {
              isEqual = false
              title = `${el.label} ≠ ${el.label}的总摊销金额，请更新后重新提交`
            }
          }
        })
      })
    }
    return { title, isEqual }
  }

  // 取其他金额字段对应的总金额
  getOtherMoneyFieldsFromSpecification = (apportionSpecification, form) => {
    const otherMoneyFields = []
    const apportionComponents = apportionSpecification?.components || []
    apportionComponents.forEach(el => {
      const compConfigs = el?.configs || []
      const otherMoneyField = compConfigs.find(config => config.sourceField === 'otherApportionMoney')
      if (otherMoneyField && form) {
        const obj = {}
        obj.totalAmount = form.getFieldValue(otherMoneyField.targetField)
        obj.name = el.field
        obj.label = el.label
        obj.totalAmountCounted = 0
        otherMoneyFields.push(obj)
      }
    })
    return otherMoneyFields
  }

  handleUpdateFeeTypeChanged = amount => {
    const { template, autoCalFields } = this.props
    const { apportionTemplateAmountField } = this.state
    const component = (template || []).find(element => element.field === apportionTemplateAmountField)

    const isAmountCalculate = fnDefineIsFormula(component.field, autoCalFields)
    const isFromThirdParty = fnDefineIsFromThirdParty(template)
    if (isAmountCalculate || isFromThirdParty) {
      return this.updateApportionsMoney()
    }
    this.setState({ feeAmount: amount })
    let { bus } = this.props
    let obj = {}
    obj[apportionTemplateAmountField] = amount
    bus.setFieldsValue({ ...obj })
  }

  updateApportionsMoney = () => {
    showMessage.error(i18n.get('费用金额不可修改，请修改分摊明细'))
  }

  render() {
    const {
      bus,
      value,
      layout,
      external,
      billSpecification,
      isForbid,
      field: apportionField = {},
      submitterId = {},
      feeDetailId = '',
      showAllFeeType,
      billTemplate,
      onChange,
      template,
      apportionVisibleList
    } = this.props
    let { feeAmount, errorMsg, isEdit, apportionFieldError, specification, apportionfLebal } = this.state
    return (
      <Container
        name="@apportion:Amortization"
        value={value}
        billTemplate={billTemplate}
        feetypeTemplate={template}
        bus={this.formBus}
        layout={layout}
        feeAmount={feeAmount}
        external={external}
        apportionfLebal={apportionfLebal}
        isForbid={isForbid}
        template={specification}
        apportionField={apportionField}
        isEdit={isEdit}
        editable={isEdit}
        errorMsg={errorMsg}
        onChange={onChange}
        apportionFieldError={apportionFieldError}
        billSpecificationId={billSpecification.id}
        submitterId={submitterId.id}
        feeDetailId={feeDetailId}
        apportionVisibleList={apportionVisibleList}
        showAllFeeType={showAllFeeType}
        billBus={bus}
      />
    )
  }
}
