import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import styles from './GroupTitle.module.less'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { trim } from 'lodash'
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'group'
  }
})
export default class GroupTitle extends PureComponent {
  render() {
    // @ts-ignore
    const label = fnGetFieldLabel(this.props?.field)
    if (!trim(label)) return null
    return  <div className={styles['group-title']}>{label}</div>
  }
}
