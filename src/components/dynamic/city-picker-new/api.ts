import { Resource } from '@ekuaibao/fetch'
import { QuerySelect } from 'ekbc-query-builder'
const cityFetch = new Resource('/api/v2/basedata/city/')
const fetchCityByGroup = new Resource('/api/v1/basedata/cityGroup/')
// const getCityGroup = new Resource('/api/tpp/v2/travelManagement/city')
const v3CityBaseData = new Resource('/api/v3/basedata/city')
const v2CityGroup = new Resource('/api/v2/basedata/cityGroup/')

const map = new Map();
const cacheKeysGetter = {
  getCityGroup: (type: 'island' | 'international') => {
    return type === 'island'? 'domesticCityGroups' : 'internationalCityGroups'
  },
  getHotCityCacheKey: (type: 'island' | 'international') => {
    return type === 'island'? 'domesticHotCity' : 'internationalHotCity'
  },
  blendDataCacheKey: 'blendData',
}

 /**
 * 国内城市按字母分组
 * 国际城市按洲分组
 */
const getCityGroups = async (type: 'island' | 'international') => {
  const cacheKey = cacheKeysGetter.getCityGroup(type)
  if (map.has(cacheKey)) {
    return map.get(cacheKey)
  }
  try {
    const result = await v3CityBaseData.GET('/group', { country: type })
    const { value } = result
    if (value) {
      map.set(cacheKey, value)
      return value
    }
  } catch (e) {
    // ignore
  }
}

const getPopularCity = async (type: 'island' | 'international') => {
  const cacheKey = cacheKeysGetter.getHotCityCacheKey(type)
  if (map.has(cacheKey)) {
    return map.get(cacheKey)
  }
  try {
    const typeText = type === 'international' ? '国外' : '国内'
    const options = {
      query: new QuerySelect().filterBy(`country=="${typeText}"`).value()
    }
    const result = await v3CityBaseData.POST('/hot', options)
    const { items } = result
    if (items) {
      map.set(cacheKey, items)
      return items
    }
  } catch (e) {}

}

export const getCityByGroup = async () => {
  if (map.has(cacheKeysGetter.blendDataCacheKey)) {
    return map.get(cacheKeysGetter.blendDataCacheKey)
  }
  try {
    const [domesticData, internationalData, domesticHotData, internationalHotData] = await Promise.all([
      getCityGroups('island'),
      getCityGroups('international'),
      getPopularCity('island'),
      getPopularCity('international')
    ])
    const mixed = {
      domestic: {
        hot: domesticHotData,
        ...domesticData
      },
      intl: {
        hot: internationalHotData,
        ...internationalData
      }
    }
    // only cache when all data is fetched completely
    // in case of caching empty data
    if (domesticData && internationalData && domesticHotData && internationalHotData) {
      map.set(cacheKeysGetter.blendDataCacheKey, mixed)
    }
    return mixed
  } catch (e) {
    return {}
  }
}
