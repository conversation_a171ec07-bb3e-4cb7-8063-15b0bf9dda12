/**
 *  Created by pw on 2020-01-10 12:32.
 */
require('rc-select/assets/index.css')
import { getV } from '@ekuaibao/lib/lib/help'
import clx from 'classnames'

import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { debounce, omit } from 'lodash'
import { CityGroup, CitySelector, CitySelectorProps, EntityType } from '@hose/pro-eui-pc-components/es/city-selector'
import '@hose/pro-eui-pc-components/es/city-selector/index.css'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
import { showMessage } from '@ekuaibao/show-util'
import { message, Select } from '@hose/eui'
import { QuerySelect } from 'ekbc-query-builder'
import { getDisplayName } from '../../../elements/utilFn'
import { getCityByGroup } from './api'
import { BehaviorType } from './types'
import { preferences } from './utils'

import { getBoolVariation } from '../../../lib/featbit'
const cityFetch = new Resource('/api/v2/basedata/city/')
const getCityGroup = new Resource('/api/tpp/v2/travelManagement/city')
const v3CityBaseData = new Resource('/api/v3/basedata/city')
const v2CityGroup = new Resource('/api/v2/basedata/cityGroup/')

const defaultSearchingResult = {
  items: [],
  start: 0,
  limit: 50,
  count: 0,
}

export interface CPProps {
  cityValue?: any[]
  searchCity: any[]
  multiple: boolean
  maxSelectCount?: number
  [key: string]: any
  disabledCity: string[]
  onGetGroupBlack?: (data: any) => void
}

interface CPState {
  currentSelectedCity: any[]
  currentSelectedGroups: any[]
  selectCity: undefined | string | string[]
  searchValue: string | undefined
  open: boolean
  cityList: any[]
  cityRange: any[]
  cityGroupId: string
  disabledCityList: string[]
  blackCity: {
    behavior: BehaviorType
    cities: string[]
  }
  loading: boolean
  dataSource: CitySelectorProps['dataSource']
  searchingResult: {
    items: any[]
    start: number
    limit: number
    count: number
  }
  searchingLoading: boolean
}
const defaultBlackCity = { behavior: BehaviorType.None, cities: [] }
// @ts-ignore
export class City extends Component<CPProps, CPState> {
  filters = {}

  constructor(props: CPProps) {
    super(props)
    this.state = {
      currentSelectedCity: [],
      currentSelectedGroups: [],
      selectCity: undefined,
      searchValue: '',
      open: false,
      cityList: [],
      cityRange: [],
      cityGroupId: '',
      disabledCityList: [],
      blackCity: {
        behavior: BehaviorType.None,
        cities: []
      },
      loading: false,
      dataSource: null,
      searchingResult: {
        ...defaultSearchingResult
      },
      searchingLoading: false
    }
  }

  componentWillReceiveProps(nextProps: any) {
    const { value, filters } = nextProps
    this.filters = filters
    if (filters) {
      const { travelerId, travelType } = filters
      if (travelerId !== this.props.filters?.travelerId || travelType !== this.props.filters?.travelType) {
        this.getCityGroupAndBlack()
      }
    }
    if (value !== this.props.value) {
      this.processSelected(nextProps)
    }
  }

  getDataSource = async () => {
    try {
      this.fetchDisabled()
      this.setState({
        dataSource: await getCityByGroup()
      })
    } catch (e) {
      // ignore
    }
  }

  isSelectingCityGroup = (ops: any) => {
    return ops && ops.length && ops[0]?.type === 'cityGroup'
  }

  /**
   * 处理已选择的数据信息
   */
  processSelected = (props: CPProps) => {
    const { value, multiple, isAuto, showType } = props
    let cityValue: any = []
    if (value) {
      cityValue = JSON.parse(value)
    }
    if (value && this.isSelectingCityGroup(cityValue)) {
      const selectCityLabels = []
      cityValue = cityValue.map(item => {
        selectCityLabels.push(item.label)
        return {
          ...item,
          name: item.label,
          ...(!item.id && item.key ? { id: item.key } : {})
        }
      })
      this.setState({ selectCity: selectCityLabels, currentSelectedGroups: cityValue })
    } else if (value && cityValue && cityValue.length) {
      const ids = cityValue.map((c: any) => c.key)
      cityFetch.GET('[ids]', { ids }).then((data: any) => {
        if (data.items) {
          const selectedCity = ids.map(id => data.items.find(el => id === el.id))
          let selectCity

          if (getBoolVariation('fkrd-4426-city-enlabel')) {
            selectCity = this.formatLabelToShow(selectedCity, showType === 'trip', multiple || isAuto)
          } else {
            selectCity = multiple || isAuto
              ? selectedCity.map((city: any) => showType === 'trip' ? city.name : this.formatLabel(city.fullName || city.name))
              : showType === 'trip' ? selectedCity[0].name : this.formatLabel(selectedCity[0].fullName || selectedCity[0].name)
          }
          this.setState({ selectCity, currentSelectedCity: selectedCity })
        }
      })
    } else {
      this.setState({ selectCity: undefined, currentSelectedCity: [], currentSelectedGroups: [] })
    }
  }

  componentDidMount() {
    const { filters, bus } = this.props
    this.filters = filters
    this.getDataSource()
    this.processSelected(this.props)
    this.getCityGroupAndBlack()

    // 在计算城市数据时，根据行程规划来计算其他字段比如消费城市的城市数据信息
    bus?.on('assign:city:value', this.assignCityValue)
    // 根据申请人来限制城市组选择
    bus?.on('set:delegator', this.handleDelegatorChanged)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus?.un('assign:city:value', this.assignCityValue)
    bus?.un('set:delegator', this.handleDelegatorChanged)
  }

  handleDelegatorChanged = () => {
    const { field = {} } = this.props
    const hasForbiddenCityGroup = getV(field, 'hasForbiddenCityGroup', false)
    if (hasForbiddenCityGroup) {
      this.fetchDisabled()
    }
  }


  assignCityValue = citys => {
    const { onChange, field } = this.props
    const { accommodation, departure, destination } = citys
    const { defaultValue } = field
    if (defaultValue?.type === 'predefine') {
      if (defaultValue?.value === 'accommodation') {
        accommodation && onChange && onChange(accommodation)
      } else if (defaultValue?.value === 'departure') {
        departure && onChange && onChange(departure)
      } else if (defaultValue?.value === 'destination') {
        destination && onChange && onChange(destination)
      }
    }
  }

  getCityGroupActive = staff => {
    const { field, submitterId } = this.props
    staff = staff || submitterId
    return v2CityGroup
      .GET(`findLimitedCityIds`, { cityGroupId: field?.cityGroupId, staffId: staff.id })
      .catch(err => showMessage.error(err))
  }

  /**
   * 单据上的城市选择，通过城市组来限制城市选择
   */
  async fetchDisabled() {
    const { form, field } = this.props
    const hasForbiddenCityGroup = getV(field, 'hasForbiddenCityGroup', false)
    if (hasForbiddenCityGroup) {
      const submitterId = form.getFieldValue('submitterId') || this?.props?.submitterId
      const res = await this.getCityGroupActive(submitterId)
      this.setState({
        disabledCityList: res?.items || []
      })
    }
  }

  fetchGroupData = async (cityGroup: CityGroup) => {
    const res = await v2CityGroup.GET('findLimitedCities', {
      cityGroupId: cityGroup.id,
      staffId: this.filters?.travelerId,
      count: 3000
    })
    const items = (res?.items || []).map(item => ({
      ...item,
      type: this.getTypeByInfo(item),
      path: getDisplayName({ enName: item.enFullName, name: item.fullName }).replaceAll(',', ' /')
    }))
    return items
  }

  getCityGroupAndBlack = () => {
    const { onGetGroupBlack } = this.props
    // @ts-ignore
    if (this.filters?.travelerId || this.filters?.travelType) {
      Promise.all([
        getCityGroup.GET('/getCityGroup', { ...this.filters }),
        getCityGroup.GET('/getTripBlackCities', { ...this.filters })
      ]).then(res => {
        const gc = res[0]
        const bc = res[1]
        const cityRange = gc?.items?.map(cityGroup => ({
          ...cityGroup,
          type: 'cityGroup'
        }))
        const blackCity = bc?.value
        this.setState({ cityRange, blackCity })
        onGetGroupBlack && onGetGroupBlack({ cityGroup: cityRange, blackCity })
      })
    } else {
      // @ts-ignore
      this.setState({ cityRange: [], blackCity: defaultBlackCity })
      onGetGroupBlack && onGetGroupBlack({ cityGroup: [], blackCity: defaultBlackCity })
    }
  }

  searchCity = async (value: string) => {
    const trimSearchValue = value.trim()
    if (!trimSearchValue) {
      this.setState({
        searchingResult: { ...defaultSearchingResult }
      })
      return
    }
    try {
      const { searchingResult } = this.state
      const options = {
        keyword: trimSearchValue,
        query: new QuerySelect()
          .filterBy(`(nameSpell.startsWithIgnoreCase(\"${trimSearchValue}\") || name.startsWithIgnoreCase(\"${trimSearchValue}\") || enName.startsWithIgnoreCase(\"${trimSearchValue}\") || enFullName.containsIgnoreCase(\", ${trimSearchValue}\") || fullName.containsIgnoreCase(\"${trimSearchValue}\"))`)
          .filterBy('treeLevel>0')
          .orderBy('treeLevel', 'ASC')
          .orderBy('code', 'ASC')
          .limit(searchingResult.start, searchingResult.limit)
          .value()
      }
      const result = await v3CityBaseData.POST('/search', options)
      this.setState({
        searchingResult: {
          ...searchingResult,
          start: searchingResult.start + searchingResult.limit,
          items: searchingResult.items.concat(this.formatToSearchList(result.items)),
          count: result.count
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      this.setState({ searchingLoading: false })
    }
  }

  getTypeByInfo = (data: any) => {
    const { country, treeLevel } = data
    const typeMap = {
      '国内0': EntityType.PROVINCE,
      '国内2': EntityType.DISTRICT,
      '国际0': EntityType.COUNTRY,
      '国际1': EntityType.PROVINCE,
    }
    const key = `${country}${treeLevel}`
    if (country === '国内') {
      return typeMap[key] || EntityType.CITY
    } else {
      return typeMap[key] || EntityType.FOREIGN_CITY
    }
  }

  formatToSearchList = (list: any) => {
    return list.map(item => ({
      ...item,
      type: this.getTypeByInfo(item),
      path: getDisplayName({ enName: item.enFullName, name: item.fullName }).replaceAll(',', ' /')
    }))
  }

  private formatLabelToShow = (selectCity, hideFullpath, isMulti) => {
    const path = i18n.currentLocale == 'en-US' ? 'enName' : 'name'
    const fullPath = i18n.currentLocale == 'en-US' ? 'enFullName' : 'fullName'
    const key = hideFullpath ? path : fullPath
    if (isMulti) {
      return selectCity.map((city: any) => city?.[key]?.replace(/^中国,\s*/, '')?.replace(/,\s*/g, '/'))
    }
    return selectCity?.[0]?.[key]?.replace(/^中国,\s*/, '')?.replace(/,\s*/g, '/')
  }
  // TODO：在行程城市选择时在判断是否需要优化
  private formatLabel(label: string, bool: boolean = false) {
    let { showType } = this.props
    let text = label.replace(/^中国,\s*/, '').replace(/,\s*/g, '/')
    if (showType == 'trip' && text.lastIndexOf('/') && bool == false) {
      text =
        i18n?.currentLocale != 'en-US'
          ? text.substr(text.lastIndexOf('/') + 1)
          : text.indexOf('/') > -1
            ? text.substr(0, text.indexOf('/'))
            : text
    }
    return text
  }

  private fnFormatCityLabel(selectCity: any) {
    if (!selectCity) {
      return null
    }

    const value = Array.isArray(selectCity) ? [...selectCity] : [selectCity]

    if (!value.length) {
      return null
    }

    return JSON.stringify(
      value.map((v: any) => {
        let data = {}
        if (v.type) {
          data = {
            key: v.id,
            label: this.formatLabel(getDisplayName({ name: v.name, enName: v.enName }), true),
            type: v.type
          }
        } else {
          data = getBoolVariation('fkrd-4426-city-enlabel') ? {
            key: v.id,
            enLabel: v.enFullName?.split(',')?.join('/'),
            label: this.formatLabel(v.fullName)
          } : {
            key: v.id,
            label: this.formatLabel(getDisplayName({ name: v.fullName, enName: v.enFullName }), true)
          }
        }
        return data
      })
    )
  }

  handleGetValue = (ops: any) => {
    console.log('handleGetValue', ops)
    const { city, group } = ops
    const {
      onChange,
      getExpenseStandardItemsLength,
      external,
      multiple = false,
      showType = '',
      updateCell,
      maxSelectCount = 0
    } = this.props
    // 组件内已处理了不能同时选择city和group的情况
    // 加载cityGroup时，为了显示类型，注入了type字段，需要过滤掉
    const curSelected = group?.length ? group : city?.map(item => omit(item, ['type']))
    if (curSelected && showType == 'trip' && maxSelectCount > 0 && curSelected.length >= maxSelectCount) {
      message.info(i18n.get('城市最多添加20个'))
      return
    }

    // 多选时，不关闭下拉框
    this.setState({ open: multiple })
    const result = this.fnFormatCityLabel(curSelected)

    onChange && onChange(result)
    updateCell && updateCell(result)
    emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }

  debounceSearchCity = debounce(this.searchCity, 1000)

  // 搜索事件
  onSearch = (value: string) => {
    // 文字修改之后需要初始化搜索结果
    this.setState({
      searchValue: value,
      searchingResult: { ...defaultSearchingResult, items: [] },
      searchingLoading: true,
    })
    this.debounceSearchCity(value)
  }

  onDropdownVisibleChange = (open: boolean) => {
    this.setState({ open, searchValue: '' })
  }

  onReturn = () => {
    this.searchCity('')
    this.setState({ searchValue: '' })
  }

  onClose = () => {
    this.setState({ open: false })
  }

  onClear = (value: string) => {
    if (value === undefined) {
      const { onChange, getExpenseStandardItemsLength, external, updateCell } = this.props

      this.setState({ selectCity: undefined })
      onChange && onChange(undefined)
      updateCell && updateCell(undefined)
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  // 多选用于多选删除
  onChange = (value: string[] | string) => {
    const { onChange, getExpenseStandardItemsLength, external, multiple = false, updateCell } = this.props
    const { currentSelectedCity } = this.state
    if (multiple && Array.isArray(value)) {
      const selected = currentSelectedCity.filter((city: any) =>
        value.some(c => c === this.formatLabel(city.fullName || city.name))
      )
      this.setState({ selectCity: [...value], currentSelectedCity: selected })
      const result = this.fnFormatCityLabel(selected)
      onChange && onChange(result)
      updateCell && updateCell(result)
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  getBlackListAndWhiteList = () => {
    const { filters, disabledCity = [], ingoreBlackList } = this.props
    const { disabledCityList, blackCity } = this.state
    if (filters?.travelerId || filters?.travelType) {
      return ingoreBlackList ? {} : preferences(blackCity)
    } else {
      return {
        disabled: disabledCityList.concat(disabledCity)
      }
    }
  }

  searchMore = () => {
    this.searchCity(this.state.searchValue)
  }

  renderDropdownContent = () => {
    const { multiple = false, isAuto, filters } = this.props
    const {
      searchValue,
      currentSelectedCity,
      currentSelectedGroups,
      cityRange,
      dataSource,
      searchingResult,
      searchingLoading,
    } = this.state
    const { enabled, disabled } = this.getBlackListAndWhiteList()
    const trimSearchValue = searchValue.trim()
    const hideDisabled = getBoolVariation('fkrd-4087-hide-disabled')
    if (trimSearchValue) {
      const patchSelectCityHandler = (value: any[]) => {
        this.handleGetValue({
          city: value.map(item => omit(item, ['type', 'path']))
        })
      }
      return (
        <CitySelector.SearchList
          selected={currentSelectedCity}
          disabledKeys={disabled}
          enabledKeys={enabled}
          language={i18n.currentLocale}
          loadOptions={{
            onLoadMore: this.searchMore,
            hasMore: searchingResult.items.length < searchingResult.count,
          }}
          multiple={multiple || isAuto}
          list={searchingResult.items}
          initialLoading={searchingLoading}
          onSelect={patchSelectCityHandler}
          searchWord={trimSearchValue}
          onCheckDisabled={this.handleCheckedDisabled}
          hideDisabled={hideDisabled}
          customRenderName={(item: any) => {
            if (!item?.haveFlight && (filters?.travelType === 'FLIGHTDEPARTURE' || filters?.travelType === 'FLIGHTDESTINATION')) {
              return i18n.currentLocale === 'en-US' ? `${item?.enName || item?.name}(No Airport)` : `${item?.name}(无机场)`
            }
            return i18n.currentLocale === 'en-US' ? `${item?.enName || item?.name}` : `${item?.name}`
          }}
        />
      )
    } else {
      const multipleSelecting = multiple || isAuto
      return (
        <CitySelector
          dataSource={dataSource}
          multiple={multipleSelecting}
          onChange={this.handleGetValue.bind(this)}
          selectedData={
            multipleSelecting
              ? {
                city: currentSelectedCity,
                group: currentSelectedGroups
              }
              : undefined
          }
          cityGroups={cityRange}
          onFetchGroupCities={this.fetchGroupData}
          disabledKeys={disabled}
          enabledKeys={enabled}
          language={i18n.currentLocale}
          onCheckDisabled={this.handleCheckedDisabled}
        />
      )
    }
  }

  handleCheckedDisabled = (city, disabledKeys = [], enableCities) => {
    const { needCheckDisabled, filters } = this.props
    if (needCheckDisabled && (filters?.travelType === 'FLIGHTDEPARTURE' || filters?.travelType === 'FLIGHTDESTINATION')) {
      return !city?.haveFlight || !!disabledKeys?.includes(city?.id) || (!!enableCities?.length && !enableCities?.includes(city?.id))
    }

    if (disabledKeys?.length > 0) {
      return disabledKeys.some((cityId) => cityId === city?.id);
    }

    if (Array.isArray(enableCities)) {
      return enableCities.every((enableId) => enableId !== city?.id);
    }

    return false
  }

  render() {
    const {
      multiple = false,
      isAuto,
      className = '',
      otherStyle = {},
      showType = '',
      maxTagCount = 0,
      placeholder,
      parentProps,
      field = {},
      selectClassName,
      size
    } = this.props

    const { selectCity, searchValue, open, loading, dataSource } = this.state

    if (!dataSource) {
      return null
    }

    let options: any = {}
    // selectCity 可能来自关联申请
    if (multiple || (isAuto && selectCity && selectCity.length > 1)) {
      options = { multiple: true, onChange: this.onChange }
      if (showType == 'trip') {
        if (maxTagCount) {
          options.maxTagCount = maxTagCount
        }
        options.maxTagTextLength = i18n?.currentLocale == 'en-US' ? 8 : 6
      }
    } else {
      options = { onChange: this.onClear }
    }

    return (
      <div
        style={{ position: 'relative', width: field?.isSimple ? '100%' : '' }}
        className={clx('city-picker', className)}
      >
        <Select
          {...options}
          size={size || 'middle'}
          showSearch
          value={selectCity}
          className={selectClassName}
          mode={multiple ? 'multiple' : ''}
          disabled={isAuto}
          placeholder={placeholder || i18n.get('选择城市')}
          style={{ width: '100%', ...otherStyle }}
          dropdownRender={this.renderDropdownContent}
          onSearch={this.onSearch}
          onDropdownVisibleChange={this.onDropdownVisibleChange}
          open={open}
          showArrow={false}
          searchValue={searchValue}
          placement='bottomLeft'
          allowClear={parentProps ? parentProps.allowClear : !isAuto && !multiple}
          // minWidth: auto 可以阻止 select 在计算下拉框大小的时候添加 min-width 导致下拉框宽度过大
          dropdownStyle={{ width: '600px', paddingTop: 0, minWidth: 'auto' }}
          dropdownMatchSelectWidth={false}
          onClear={this.onClear}
        />
      </div>
    )
  }
}
