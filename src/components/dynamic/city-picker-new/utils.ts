import { BehaviorType } from "./types"

interface PreferenceOptions<T> {
    cities: T[],
    behavior: BehaviorType
}

export const preferences = <T>(ops: PreferenceOptions<T>): { enabled?: T[], disabled?: T[] } => {
    if (ops.behavior === BehaviorType.Allow) {
        return {
            enabled: ops.cities,
        }
    } else if (ops.behavior === BehaviorType.NotAllow) {
        return {
            disabled: ops.cities
        }
    }
    return {}
}
