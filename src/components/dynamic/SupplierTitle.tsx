/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2020-03-19 12:09:45 
 * @Last Modified by: xingdev
 * @Last Modified time: 2020-06-04 18:03:20
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import styles from './SupplierTitle.module.less'
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'supplierTitle'
    }
})
export default class SupplierTitle extends PureComponent<any> {
    render() {
        const { field } = this.props
        return <div className={styles.desc}>{field.text}</div>
    }
}