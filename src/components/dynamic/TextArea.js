/**************************************************
 * Created by nanyuantingfeng on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input as AntdInput } from 'antd'
import { Input as EUIInput } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
import { changeAutoComputerDisplayValue } from '../utils/fnDefineIsFormula'
import styles from './Text.module.less'
import classNames from 'classnames'
import { debounce } from 'lodash'
import isDependency from './helpers/is-dependency'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
@EnhanceField({
  descriptor: {
    type: 'textarea'
  },
  validator: field => (rule, value, callback) => {
    let { optional, label, maxLength, minLength, editable } = field
    if (!editable && value && value.length > 1000) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength: 1000, length: value.length }))
    }
    if (editable && value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength, length: value.length }))
    }
    if (editable && value && value.length < minLength) {
      return callback(i18n.get('cannot-less-words', { label, minLength }))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper(),
  initialValue(props) {
    return ''
  }
})
export default class TextArea extends PureComponent {
  isFocus = false
  constructor(props) {
    super(props)
    const { value } = props
    const disabled = isDisable(props)
    this.state = { value, disabled }
  }

  onChange = e => {
    const { onChange, canRealTime } = this.props
    if (canRealTime) {
      // 实时的更新到表单上
      onChange && onChange(e.target.value)
    }

    this.setState({ value: e.target.value })
  }

  componentWillReceiveProps(nextProps, nextContext) {
    const { value } = nextProps
    const disabled = isDisable(nextProps)
    if (value !== this.state.value || (disabled !== undefined && disabled !== this.state.value)) {
      if (!this.isFocus) {
        // 如果是当前正在输入的内容，不进行赋值，因为本身state上的value就是最新的
        this.state.value = value
      }
      this.state.disabled = disabled
    }
  }

  handelOnFocus = () => {
    const { bus, autoCalFields, field } = this.props
    this.isFocus = true
    if (isDependency(autoCalFields, field)) {
      bus && bus.emit('savebtn:state:change', { disabled: true })
    }
  }

  onBlur = e => {
    this.isFocus = false
    const { onChange, bus, field } = this.props
    onChange && onChange(e.target.value)
    bus && bus.emit('dynamic:value:blur', { [field.field]: e.target.value })
    bus && bus.emit('savebtn:state:change', { disabled: false })
  }

  render() {
    let { field, useEUI } = this.props
    let { value, disabled } = this.state
    let { optional, maxLength } = field
    let placeholder = getPlaceholder(field)
    if (optional) placeholder = i18n.get('（选填）') + placeholder
    value = changeAutoComputerDisplayValue(value, this.props)
    const Input = useEUI ? EUIInput : AntdInput
    const autoSize = useEUI ? 'autoSize' : 'autosize'
    return (
      <Input.TextArea
        {...{ [autoSize]: { minRows: 2, maxRows: 140 } }}
        maxLength={maxLength}
        showCount
        allowClear
        placeholder={placeholder}
        disabled={disabled}
        value={value}
        onBlur={this.onBlur}
        onFocus={this.handelOnFocus}
        onChange={this.onChange}
        className={classNames(styles.textarea, { [styles['text-auto-disabled']]: disabled })}
      />
    )
  }
}
