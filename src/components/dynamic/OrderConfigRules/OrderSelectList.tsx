import React from 'react'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import style from './OrderConfigRules.module.less'
import { OrderSelect, opt } from './OrderSelect'
import { Tooltip } from 'antd'
const EKBIcon = app.require<any>('@elements/ekbIcon')
export enum changeType {
  create,
  delete,
  change,
  reset
}
export type selectValue = {
  categoryId?: string
  orderEntityId?: string
  supplierAccountId?: string
}
export interface IOrderSelectProps {
  value: selectValue
  index: number
  rules: (selectValue | any)[]
  categoryOpt?: opt[]
  orderOpt?: opt[]
  supplierOpt?: opt[]
  onChange: (type: changeType, index: number, value?: any, valueKey?: string) => void
}
export const OrderSelectList: React.FC<IOrderSelectProps> = props => {
  const { supplierOpt = [], orderOpt = [], categoryOpt = [], value = {}, onChange, index, rules } = props
  const { categoryId = '', orderEntityId = '', supplierAccountId = '' } = value
  return (
    <div className={style['rules']}>
      <T name="供应商账户:" />
      <OrderSelect
        options={supplierOpt}
        value={supplierAccountId}
        onChange={value => onChange(changeType.change, index, value, 'supplierAccountId')}
      />
      <T name="对账单的" />
      <OrderSelect
        options={categoryOpt}
        value={categoryId}
        onChange={value => onChange(changeType.change, index, value, 'categoryId')}
      />
      <T name="与订单的" />
      <OrderSelect
        options={orderOpt}
        value={orderEntityId}
        onChange={value => onChange(changeType.change, index, value, 'orderEntityId')}
      />
      <T name="品类匹配" />
      <div className={style['operation']}>
        <EKBIcon name="#EDico-plus-default" className="oper mr-8" onClick={() => onChange(changeType.create, index)} />
        {rules.length === 1 ? (
          <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
            <EKBIcon name="#EDico-scan-b" className="oper" />
          </Tooltip>
        ) : (
          <EKBIcon name="#EDico-scan-b" className="oper" onClick={() => onChange(changeType.delete, index)} />
        )}
      </div>
    </div>
  )
}
