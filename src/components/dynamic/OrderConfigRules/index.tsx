import React, { useState, useCallback, useEffect } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { QuerySelect } from 'ekbc-query-builder'
import { wrapper } from '../../layout/FormWrapper'
import { app } from '@ekuaibao/whispered'
import { cloneDeep } from 'lodash'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import { getTripDataLinkEntityList, formatCategoy } from './utils'
import { changeType, OrderSelectList } from './OrderSelectList'

interface IOrderConfigRules {
  [key: string]: any
}

const getCategoryOpt = (supplierAccountList = [], id, rules) => {
  let categoryOpt = []
  supplierAccountList.forEach(supplier => {
    if (supplier.id === id) {
      categoryOpt = formatCategoy(supplier, rules) ?? []
    }
  })
  return categoryOpt
}
const defaultRule = { categoryId: '', orderEntityId: '', supplierAccountId: '' }
let supplierAccountList: any[] = []
const OrderConfigRules: React.FC<IOrderConfigRules> = (props: IOrderConfigRules) => {
  const { form, bus, value, sourceData = {} } = props
  const { supplierArchiveType = '' } = sourceData
  const [orderOpt, setOrderOpt] = useState([])
  const [supplierOpt, setSupplierOpt] = useState([])
  let rules = cloneDeep(value) ?? [cloneDeep(defaultRule)]
  const getSettlementField = useCallback(async () => {
    const query = new QuerySelect()
    query.select('categoryIds(`...`), id, name, active, supplierArchiveType')
    supplierAccountList = (await app.invokeService('@settlement:get:supplier:account:list', query.value()))?.items ?? []
    const entityInfoChild = (await getTripDataLinkEntityList())?.children ?? []
    setOrderOpt(entityInfoChild)
    changeOptions(supplierArchiveType, false)
  }, [])
  const changeOptions = (supplierArchiveType, reset = true) => {
    const supplier = supplierAccountList.filter(i => i.supplierArchiveType === supplierArchiveType)
    setSupplierOpt(supplier)
    reset && handlerChange(changeType.reset)
  }
  const handlerChange = useCallback((type, index = '', value = '', valueKey = '') => {
    switch (type) {
      case changeType.create:
        rules.push(cloneDeep(defaultRule))
        break
      case changeType.delete:
        rules.splice(index, 1)
        break
      case changeType.change:
        if (valueKey === 'supplierAccountId') {
          rules[index]['categoryId'] = null
        }
        rules[index][valueKey] = value
        break
      case changeType.reset:
        rules = [cloneDeep(defaultRule)]
        return form?.setFieldsValue({ rules })
    }
    form?.setFieldsValue({ rules })
  }, [])
  useEffect(() => {
    bus.on('supplierArchiveType', changeOptions)
    getSettlementField()
    return () => {
      bus.un('supplierArchiveType', changeOptions)
    }
  }, [supplierArchiveType])
  return (
    <div>
      {rules?.map((item, i) => (
        <OrderSelectList
          supplierOpt={supplierOpt}
          orderOpt={orderOpt}
          categoryOpt={getCategoryOpt(supplierOpt, item.supplierAccountId, rules)}
          rules={rules}
          value={item}
          key={i}
          index={i}
          onChange={handlerChange}
        />
      ))}
    </div>
  )
}
export default EnhanceField(({
  descriptor: {
    type: 'order-config-rules'
  },
  validator: (field, props) => (rule, value, callback) => {
    let err = false
    value.forEach(i => (!i.categoryId || !i.orderEntityId || !i.supplierAccountId) && (err = true))
    if (err) {
      return callback(i18n.get('请选择匹配字段'))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback()
  },
  initialValue(props) {
    return [cloneDeep(defaultRule)]
  },
  wrapper: wrapper()
} as unknown) as IComponentDef)(OrderConfigRules)
