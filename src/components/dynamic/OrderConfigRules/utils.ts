import { app } from '@ekuaibao/whispered'
const TripType = "TRAVEL_MANAGEMENT"
export const formatTripDataLinkEntity = (list: any[], type: string) => {
    if (list?.length === 0) return list
    const _list = list.find(i => i.type === type)
    return _list
}

export const getTripDataLinkEntityList = async () => {
    const { items = [] } = await app.invokeService('@tpp-v2:get:tripDataLinkEntityList', { type: TripType })
    return formatTripDataLinkEntity(items, 'ORDER')
}

export const formatCategoy = (value: any, rules) => {
    const accoutName = value?.name ?? '';
    const categoryIds = value?.categoryIds ?? [];
    return categoryIds.map((val: any) => {
        const name = val?.name?.replace(`${accoutName}-`, '')
        val.disabled = rules.find(item => val.id === item.categoryId)
        return { ...val, name: name }
    })
}