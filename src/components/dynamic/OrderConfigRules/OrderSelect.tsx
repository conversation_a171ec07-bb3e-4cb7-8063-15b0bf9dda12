import React from 'react'
import { Select } from 'antd'
import { cloneDeep } from 'lodash'
const { Option } = Select
export type opt = {
  name: string
  label: string
  [key: string]: any
}
export interface IOrderSelect {
  options: opt[]
  value: any
  disabled?: boolean
  valueKey?: string
  labelKey?: string
  optionFilterProp?: string
  style?: any
  onChange: (value?: any) => void
}
export const OrderSelect: React.FC<IOrderSelect> = props => {
  const {
    options,
    value = [],
    optionFilterProp = 'children',
    onChange,
    valueKey = 'id',
    disabled = false,
    labelKey = 'name',
    style = { width: 100, margin: '0 10px' }
  } = props
  return (
    <Select
      showSearch
      value={value}
      disabled={disabled}
      onChange={onChange}
      optionFilterProp={optionFilterProp}
      style={style}
    >
      {cloneDeep(options)?.map(i => {
        return (
          <Option key={i.name} value={i[valueKey]} disabled={i.disabled}>
            {i[labelKey]}
          </Option>
        )
      })}
    </Select>
  )
}
