import React from 'react'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { canSelectParent, checkBelongDepartment, checkDefaultDeptValue } from '../utils/fnInitalValue'
import TreeSelectSingle from '../../elements/puppet/TreeSelectSingle'
import { isObject, isString } from '@ekuaibao/helpers'
import { cloneDeep, get, isEqual } from 'lodash'
import { shouldUpdateValue } from '../utils/DependenceUtil'
import { app as api } from '@ekuaibao/whispered'
import { getIdByTreeDataIsExist } from '../../elements/puppet/rc-tree/util'
import { EnhanceConnect } from '@ekuaibao/store'
import { getRecordLink } from './helpers/getRecordLink'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import { getBoolVariation } from '../../lib/featbit'
import { refDepartmentHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'
@EnhanceConnect(
  state => ({
    departmentsMap: state['@common'].departmentVisibility.mapData || {}
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class RefDepartmentCP extends React.Component<any, any> {
  checkedId: string[] = []
  constructor(props) {
    super(props)
    const { field, submitterId = {}, value, isPermitForm } = props
    const { dependence, allowUseDeactivatedData, editable, allowCancelDependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.checkDepForBelongDepartments(submitterId, allowUseDeactivatedData)
    const noUseDependenceData = isPermitForm && allowCancelDependence && value && editable === false // 申请单导入数据，不可编辑状态下不清空值
    this.state = {
      isDependence,
      useDependenceData: !noUseDependenceData, // 是否使用 依赖关系筛选数据（因为允许取消筛选）
      dependenceList: [],
      originDependenceList: [],
      departments: [],
      originDepartments: [],
      dependenceMap,
      loading: true
    }
  }

  // 如果配置了仅可选择所属部门，当前部门不是新的提交人的所属部门需要把值清空
  checkDepForBelongDepartments = (submitterId, allowUseDeactivatedData) => {
    const { field, value, isModify, departmentsMap } = this.props
    let dep = value
    if (typeof value === 'string') {
      dep = departmentsMap[value]
    }
    if (!(dep && dep.id)) {
      return
    }
    const valueBelongDepartment = checkDefaultDeptValue(dep, field, submitterId)
    // 开启此配置不清空删除企业
    if (allowUseDeactivatedData) {
      return
    }
    if ((!valueBelongDepartment || !valueBelongDepartment.id) && !isModify) {
      this.onChange()
    }
  }

  componentWillMount() {
    const { bus } = this.props
    if (bus) {
      bus.on('on:dependence:change', this.handleDependenceChange)
    }
  }

  componentDidMount() {
    const { value, bus, field, isModify, billState, submitterId, onChange, departmentsMap, isPermitForm } = this.props
    if (isPermitForm && isString(value) && departmentsMap[value]) {
      onChange(departmentsMap[value])
    }
    const id = isObject(value) ? value.id : value
    this.__value = value
    setTimeout(() => {
      if (bus) {
        // 重新获取props的值，因为值可能存在被更新了的情况
        const { value: propsValue } = this.props
        const propsId = isObject(propsValue) ? propsValue.id : propsValue
        bus.emit('on:dependence:change', { key: field.name, id: propsId }, { isInit: true })
      }
    }, 200)
    if (bus) {
      bus.on('set:delegator', this.handleDelegatorChanged)
    }
    this.getDept(this.props)
    if (!isModify) {
      this.filterDepartmentVisit(value)
    }
    const { defaultValue = {}, editable } = field
    if (editable === false && id && billState !== 'new' && !field?.allowUseDeactivatedData) {
      const type = get(field, 'defaultValue.type', '')
      if (type === 'predefine' && defaultValue.value === 'submit.department') {
        const defaultDepartment = submitterId && submitterId.defaultDepartment
        if (defaultDepartment) {
          const newValue = checkDefaultDeptValue(defaultDepartment, field, submitterId)
          if (newValue && newValue.id !== id) {
            onChange(newValue)
          }
        }
      }
    }
    if (value) {
      setTimeout(this.fnSetFormValue.bind(this, value), 1000)
    }
  }
  componentWillReceiveProps(np) {
    if (!this.props.value && np.value) {
      if (isObject(np.value) && isObject(np.value?.form)) {
        this.fnSetFormValue(np.value)
      } else {
        this.fnDeptIdToSetValue(isObject(np.value) ? np.value?.id : np.value)
      }
    }
    if (!isEqual(this.props.value, np.value) && np?.field?.allowUseDeactivatedData) {
      this.getDept(np)
    }
    shouldUpdateValue(this.props, np)
    if(np.value){
      this.__value = np.value
    }
  }
  componentWillUnmount() {
    const { bus } = this.props
    if (bus) {
      bus.un('set:delegator', this.handleDelegatorChanged)
      bus.un('on:dependence:change', this.handleDependenceChange)
    }
    this.__value = undefined
  }

  getDept = async props => {
    const { value, field, submitterId = {} } = props
    const id = isObject(value) ? value.id : value
    Promise.all([
      api.dataLoader('@common.departmentVisibility').load(),
      api.invokeService('@common:get:department:by:id', id)
    ])
      .then(result => {
        const departmentVisibility = result[0]
        const dept = result[1]
        let originDepartments = get(departmentVisibility, 'data', [])
        const active = get(dept, 'active', true)
        if (!active) {
          originDepartments = [{ ...dept, name: dept.name }, ...originDepartments]
        }
        // 与依赖性相关时，需要用依赖性过滤后的数组获取取值范围
        const { originDependenceList = [] } = this.state
        const stateParams = {
          originDepartments,
          dependenceList: this.fnFilterDeptForOnlyBelongDepartment(field, originDependenceList, submitterId),
          departments: this.fnFilterDeptForOnlyBelongDepartment(field, originDepartments, submitterId)
        }
        this.setState(stateParams, () => {
          this.setState({ loading: false })
        })
      })
      .catch(() => {
        this.setState({ loading: false })
      })
  }

  // 检查模版中是否配置了选项，让提交人只能选择其所在部门
  fnFilterDeptForOnlyBelongDepartment = (field, departments, submitterId) => {
    if (field && field.onlyBelongDepartment) {
      const submitterDepartments = get(submitterId, 'departments', [])
      const onlyLeafCanBeSelected = !canSelectParent(field)
      if (submitterDepartments.length) {
        const submitterDepartmentsArr = submitterDepartments.map(dep => {
          if (typeof dep === 'string') {
            return dep
          }
          return dep.id
        })
        const deptArr = cloneDeep(departments)
        // 1. 获取可选部门: selectableDepIdArr
        const selectableDepIdArr = []
        // 2. 获取可展示出来的部门id，是否有必要去重？ visibleDepsArr
        const visibleDepsArr = []
        let deps = deptArr.map(dep =>
          this.fnFilterDepartmentSelectable(
            dep,
            submitterDepartmentsArr,
            selectableDepIdArr,
            onlyLeafCanBeSelected,
            [],
            visibleDepsArr
          )
        )
        if (!selectableDepIdArr.length > 0) {
          return []
        }
        // 3. 给部门加上hide属性
        deps = deps.map(dep => this.fnAddAttributeInDep(dep, visibleDepsArr))
        if (!useNewAutomaticAssignment()) {
          if (selectableDepIdArr.length === 1) {
            const { departmentsMap = {} } = this.props
            if (Object.keys(departmentsMap).length > 0) {
              const depValue = departmentsMap[selectableDepIdArr[0]]
              if (depValue) {
                const { id, name, code, form } = depValue
                this.onChange({ id, name, code, form })
              }
            }
          }
        }
        return deps
      }
    }
    return departments
  }

  fnAddAttributeInDep = (department, visibleDepsArr) => {
    if (!visibleDepsArr.includes(department.id)) {
      department.hide = true
    }
    if (department.children && department.children.length) {
      department.children = department.children.map(dep => this.fnAddAttributeInDep(dep, visibleDepsArr))
    }
    return department
  }

  // 根据部门的可见范围为selectable属性赋值
  fnFilterDepartmentSelectable = (
    department,
    rangeArr,
    selectableDepIdArr,
    onlyLeafCanBeSelected,
    parentDepartmentArr = [],
    visibleDepsArr = []
  ) => {
    const active = get(department, 'active', true)
    const depParentIdArr = [...parentDepartmentArr, department.id]
    if (onlyLeafCanBeSelected && department.children && department.children.length) {
      department.selectable = false
    }
    if (active && department.selectable !== false) {
      if (useNewAutomaticAssignment() && !rangeArr?.length) {
        department.selectable = true
      } else {
        department.selectable = rangeArr.includes(department.id)
      }
      if (department.selectable) {
        visibleDepsArr.push(...depParentIdArr)
        selectableDepIdArr.push(department.id)
      }
    }

    if (department.children && department.children.length) {
      department.children = department.children.map(dep =>
        this.fnFilterDepartmentSelectable(
          dep,
          rangeArr,
          selectableDepIdArr,
          onlyLeafCanBeSelected,
          depParentIdArr,
          visibleDepsArr
        )
      )
    }
    return department
  }

  fnDeptIdToSetValue = async (id = '') => {
    if (!id) {
      return
    }
    const res = await api.invokeService('@common:get:department:by:id', id)
    this.fnSetFormValue(res)
  }

  // 部门字段联动赋值逻辑
  fnSetFormValue = async (value = {}) => {
    const {
      bus,
      form,
      field: { isLinkageAssignment, assignmentRule },
      template
    } = this.props
    const fields = (assignmentRule && assignmentRule.fields) || []
    if (isEqual(value, {})) {
      return
    }
    const valueMap = {}
    const tempMap = {}
    if (template) {
      template.map(oo => (tempMap[oo.field] = oo))
    }
    let formValue = {}
    if (bus) {
      formValue = await bus.getValue()
    }
    if (isLinkageAssignment) {
      fields.forEach(item => {
        const tempItem = tempMap[item.targetField]
        if (tempItem) {
          const values = value?.form?.[item.sourceField]
          if (!!values) {
            if (tempItem.type === 'number') {
              valueMap[tempItem.name] = `${values * 1}`
            } else if (tempItem.type === 'date') {
              valueMap[tempItem.name] = Number(values)
            } else {
              const currentValue = formValue[tempItem.name]
              if (currentValue && isString(values) && currentValue?.id === values) {
                valueMap[tempItem.name] = currentValue
              } else {
                valueMap[tempItem.name] = values
              }
            }
          }
        }
      })
      if (Object.keys(valueMap)?.length) {
        !!form && form.setFieldsValue(valueMap)
      }
    }
  }

  filterDepartmentVisit = value => {
    if (value && isObject(value) && value.id) {
      // 当复制单据时，当单据上的部门没有可见性时，不显示
      const valueId = value.id
      this.setDepartmentValue(valueId)
    } else if (value && typeof value === 'string') {
      this.setDepartmentValue(value)
    }
  }

  setDepartmentValue = valueId => {
    const {
      departmentsMap = {},
      field: { allowUseDeactivatedData }
    } = this.props
    if (Object.keys(departmentsMap).length > 0) {
      const values = departmentsMap[valueId]
      // 开启此配置不清空删除企业
      if (allowUseDeactivatedData) {
        return
      }
      !values && this.onChange('')
    }
  }

  currentIdx = null
  handleDependenceChange = async ({ key, id, dependenceFeeType = false }, options = {}) => {
    const {
      getRecordLink,
      field,
      value,
      bus,
      submitterId = {},
      form,
      billState,
      detailId,
      isDetail,
      isPermitForm
    } = this.props
    const {
      dependence,
      dataType,
      selectRange,
      dependenceCondition,
      allowUseDeactivatedData,
      allowCancelDependence,
      editable
    } = field
    if (isPermitForm && allowCancelDependence && value && editable === false) {
      // 申请单导入数据，不可编辑状态下不触发档案关系
      return
    }
    if (useNewAutomaticAssignment()) {
      const result = await refDepartmentHandleDenpence(
        { key, id, dependenceFeeType },
        options,
        this.onChange,
        this.fnFilterDeptForOnlyBelongDepartment,
        this.state,
        this.props
      )
      result && this.setState(result)
      return
    }
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据
    if (dependence && dependence?.length) {
      const isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (Boolean(isNeedFetch)) {
        this.currentIdx = id // 标明promise的执行顺序
        const { dependenceMap } = this.state
        const list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        getRecordLink({
          recordSearch: list,
          entity: dataType.entity,
          range: selectRange,
          dependenceFeeType,
          defaultValue: value && value.id,
          dependenceCondition,
          // 开启此配置不清空删除企业
          findDeactivated: allowUseDeactivatedData ? 'all' : 'no'
        }).then(action => {
          const reqIdx = id // 闭包记录id
          const data = action.payload
          let { matchDefaultValue, items, leafItems } = data
          if (this.currentIdx === reqIdx) {
            let newValue
            let departmentMap = undefined
            if (isInitLoad && field.allowCancelDependence) {
              const fieldsValue = form.getFieldsValue()
              // 初始化进来 当前value 值赋值
              newValue = fieldsValue[field?.field] ?? undefined
              this.onChange(newValue)
            } else if (!matchDefaultValue) {
              if (!leafItems) {
                const fieldsValue = form.getFieldsValue()
                // 初始化进来 当前value 值赋值
                newValue = fieldsValue[field?.field] ?? undefined
                const id = isObject(newValue) ? newValue.id : newValue
                departmentMap = treeDataToMap(items)
                newValue = departmentMap[id]
              } else {
                newValue = leafItems ? leafItems : undefined
              }
              // 验证所属部门是否包含当前值
              newValue = checkBelongDepartment(field, newValue, submitterId)
              if (bus) {
                bus.setValidateLevel(1)
              }

              if(!newValue && this.__value && billState === 'new' && items?.length && getBoolVariation('cyxq-71216-department-dependence')){
                const id = isObject(this.__value) ? this.__value.id : this.__value
                const map = departmentMap ? departmentMap : treeDataToMap(items)
                newValue = map[id]
              }

              this.onChange(newValue)
            }
            // TODO: 档案关系埋点
            const oldValue = value
            const { billData, billSpecification, feeType, dataSource } = this.props
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })
            const dependenceList = this.fnFilterDeptForOnlyBelongDepartment(field, items, submitterId)
            this.setState({
              dependenceList,
              originDependenceList: items
            })
          }
        })
      }
    }
  }


  handleDelegatorChanged = staff => {
    const { field, submitterId = {}, value } = this.props
    const { defaultValue = {} } = field
    if (
      staff.id !== submitterId.id &&
      defaultValue &&
      defaultValue.type === 'predefine' &&
      defaultValue.value !== 'submit.requisition'
    ) {
      const defaultDepartment = staff.defaultDepartment
      if (defaultDepartment) {
        const { id, name, code, form } = defaultDepartment
        this.onChange({ id, name, code, form })
      }
    } else if (staff.id !== submitterId.id && value && value.id) {
      // 如果配置了仅可选择所属部门，在这里做验证，当前部门不是新的提交人的所属部门需要把值清空
      this.checkDepForBelongDepartments(staff)
    }
    // 如果配置了仅可选择所属部门，重新过滤可选范围
    const { isDependence, originDepartments = [], originDependenceList = [] } = this.state
    const stateParam = {}
    const key = isDependence ? 'dependenceList' : 'departments'
    let depArr = isDependence ? originDependenceList : originDepartments
    depArr = this.fnFilterDeptForOnlyBelongDepartment(field, depArr, staff)
    stateParam[key] = depArr
    this.setState(stateParam)
  }

  onChange = value => {
    const { onChange } = this.props
    this.fnSetFormValue(value)
    onChange(value)
    if(value){
      this.__value = value
    }
  }

  handleCheckValueIsExist = (data, isExist) => {
    const { field } = this.props
    const { allowUseDeactivatedData } = field
    // 开启此配置不清空删除企业
    if (allowUseDeactivatedData) {
      return
    }

    if (!isExist) {
      this.onChange(undefined)
    }
  }

  // 取消依赖取值结果
  cancelDependenceData() {
    this.setState({
      useDependenceData: false
    })
  }

  getDependenceListData() {
    const { form, field } = this.props
    const { isDependence, dependenceList = [], departments = [], useDependenceData } = this.state
    let value
    if (form) {
      value = form.getFieldsValue()
    }
    const fieldValue = value && value[field?.field]

    let result
    if (isDependence && fieldValue?.id && !getIdByTreeDataIsExist(dependenceList, fieldValue?.id)) {
      result = departments
    } else if (useDependenceData && isDependence) {
      result = dependenceList
    } else {
      result = departments
    }
    this.fnFindOnlyOneDepartment(result)
    return result
  }

  fnFindOnlyOneDepartment = (dataSource = []) => {
    const { field, submitterId, departmentsMap = {}, value = {}, bus } = this.props
    const { rangeOnlyOneAutomaticAssignment, onlyBelongDepartment } = field
    if (!(!this.state.isDependence && useNewAutomaticAssignment() && rangeOnlyOneAutomaticAssignment)) {
      return
    }
    const submitterDepartments = get(submitterId, 'departments', [])
    const onlyLeafCanBeSelected = !canSelectParent(field)
    let submitterDepartmentsArr = []
    if (onlyBelongDepartment && submitterDepartments?.length) {
      submitterDepartmentsArr = submitterDepartments.map(dep => {
        if (typeof dep === 'string') {
          return dep
        }
        return dep.id
      })
    }
    const selectableDepIdArr = []
    dataSource.map(dep =>
      this.fnFilterDepartmentSelectable(
        dep,
        submitterDepartmentsArr,
        selectableDepIdArr,
        onlyLeafCanBeSelected
      )
    )
    if (selectableDepIdArr.length === 1) {
      if (Object.keys(departmentsMap).length > 0) {
        const depValue = departmentsMap[selectableDepIdArr[0]]
        if (depValue && depValue.id && depValue.id !== value?.id) {
          bus.setValidateLevel(1)
          const { id, name, code, form } = depValue
          this.onChange({ id, name, code, form })
        }
      }
    }
  }

  render() {
    const { field, getExpenseStandardItemsLength, isModify, isChangePosition, dropdownMatchSelectWidth } = this.props
    let { value } = this.props
    const { isDependence, loading, useDependenceData } = this.state
    value = value || {}
    if (typeof value === 'string') {
      value = {
        id: value
      }
    } else if (value instanceof Array) {
      const arr = []
      value.length < 1
        ? (this.checkedId = [])
        : value.forEach(v => {
            arr.push(v.id || v)
            this.checkedId = arr
          })
    }
    const disabled = isDisable(this.props)
    const { optional, Component, multiple, selectRange } = field
    let placeholder = getPlaceholder(field)
    if (optional) {
      placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    }
    const data = {
      id: value.id ? value.id : this.checkedId,
      refKey: 'RefDepartmentTreeSelect',
      placeholder: placeholder,
      // treeNodeData: (useDependenceData && isDependence)? dependenceList : departments,
      treeNodeData: this.getDependenceListData(),
      mode: useDependenceData && isDependence ? 'dependence' : 'normal',
      optional: optional,
      disabled: disabled,
      Component,
      multiple,
      onlyLeafCanBeSelected: !canSelectParent(field),
      onChange: this.onChange,
      getExpenseStandardItemsLength,
      range: selectRange,
      displayValue: value,
      isChangePosition,
      dropdownMatchSelectWidth,
      notFoundContent:
        field?.allowCancelDependence && useDependenceData ? (
          <div className="cancel-dependence">
            {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
            <a href="javascript:void 0" onClick={this.cancelDependenceData.bind(this)}>
              {i18n.get('查看全量数据')}
            </a>
          </div>
        ) : (
          undefined
        )
    }
    if (isModify && loading) {
      return null
    }
    return (
      <TreeSelectSingle
        disabled={!field.editable}
        data={data}
        onCheckValueIsExist={this.handleCheckValueIsExist}
        field={this.props.field}
        showActive
        useEUI={this.props.useEUI}
      />
    )
  }
}
