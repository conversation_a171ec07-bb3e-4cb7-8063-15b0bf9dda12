import StaffSelectSimple from '../../elements/puppet/staff-select-simple/index.tsx'
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { getPlaceholder } from '../utils/fnGetFieldLabel'

@EnhanceField({
  descriptor: {
    type: 'ref:organization.StaffSimple'
  },
  validator: (field, props) => (rule, value, callback) => {
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class RefStaffSimple extends PureComponent {
  render() {
    const { onChange, value, field } = this.props
    const { id, multiple, optional, defaultPlaceholder } = field
    const multipleValue = id === 'staffRange' || multiple
    const checkedIds = multipleValue ? value?.staffIds.map(staff => staff.id || staff) : value?.id ? [value.id] : null

    let placeholder = getPlaceholder(field)
    if (optional) {
      placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
    }

    return <StaffSelectSimple
      {...this.props}
      checkedIds={checkedIds}
      placeholder={placeholder}
      multiple={multipleValue}
      onChange={(staffIds, staffs) => {
        if (!multipleValue && staffs) {
          onChange && onChange(staffs[0])
        } else {
          onChange && onChange({ ...value, staffIds: staffIds })
        }
      }}
    />
  }
}
