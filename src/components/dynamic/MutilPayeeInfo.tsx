/**************************************************
 * Created by nany<PERSON>ingfeng on 11/07/2017 17:26.
 **************************************************/
import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import TagSelector from '../../elements/tag-selector'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { getV } from '@ekuaibao/lib/lib/help'
import styles from './MutilPayeeInfo.module.less'


// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'list:ref:pay.PayeeInfo'
  },
  validator: (field) => (rule, value, callback) => {
    const { maxCount } = field
    if (rule.level > 0) {
      return callback()
    }
    if (maxCount && value && value.length > maxCount) {
      return callback(i18n.get('over_count', { count: maxCount }))
    }
    return callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class MutilPayeeInfo extends Component<any, any> {
  constructor(props) {
    super(props)
  }

  handleChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  selectPayInfo = scope => {
    const { onChange, value } = this.props
    api.open('@dimension-map:choosePayeesModal', { selectPayee: value }).then(list => {
      onChange && onChange(list)
    })
  }

  renderPayeeItem = item => {
    return (
      <div className={'payee-card'}>
        <img src={item.icon} style={{ height: '18px', width: '18px' }} />
        <span className="mr-5">{item.bank || item.branch}</span>
        <span className="mr-5">{item.accountNo || item.cardNo}</span>
        <span className="mr-5">{item.accountName || item.name}</span>
      </div>
    )
  }

  render() {
    const { field, value } = this.props
    const { placeholder } = field
    return (
      <div className={styles['mutil-payeeInfo-wrapper']}>
        <TagSelector
          showAvatar
          className="mutil-payeeInfo"
          onClick={this.selectPayInfo}
          onChange={this.handleChange}
          value={value}
          renderItem={item => this.renderPayeeItem(item)}
          placeHolder={placeholder}
        />
      </div>
    )
  }
}
