/**
 * <AUTHOR> <<EMAIL>>
 * @date 2018-02-05
 * @description 差旅行程
 */

import styles from './Trips.module.less'
import React, { PureComponent } from 'react'
import { Button } from 'antd'
import moment from 'moment'
import { EnhanceField } from '@ekuaibao/template'
import { debounce } from '@ekuaibao/lib/lib/lib-util'
import { fnCheckCompleted } from '../validator/validator'
import { clone } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { isDisable } from '../utils/fnDisableComponent'
import { wrapper } from '../layout/FormWrapper'
import TripItem from './dataLinkEdit/TripItem'
import { getDays, getWeek } from '../../lib/lib-util'
import { formatDateTime, timeConvert } from '../utils/fnPredefine4Date'
import { app as api } from '@ekuaibao/whispered'
const TripsExtendsField = api.require('@bills/elements/TripsExtendsField')

const formatMap = {
  Date: (v, f = {}) => {
    return timeConvert(f.withTime, f.dateTimeType, v)
  },
  DatePeriod: (v, f = {}) => {
    const format = formatDateTime(f.withTime, f.dateTimeType)
    return i18n.get(`{__k0} 至 {__k1}`, { __k0: moment(v.start).format(format), __k1: moment(v.end).format(format) })
  },
  City: v => {
    if (v && JSON.parse(v)[0]) {
      return JSON.parse(v)[0].label
    } else {
      return i18n.get('空')
    }
  },
  Cities: (f, t) => {
    const from = f && JSON.parse(f)[0]
    const to = t && JSON.parse(t)[0]
    return i18n.get(`{__k0} —— {__k1}`, {
      __k0: from ? from.label : i18n.get('空'),
      __k1: to ? to.label : i18n.get('空')
    })
  }
}

@EnhanceField({
  descriptor: { type: 'trips' },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) return callback()
    let error = fnCheckCompleted({ value, isTrip: true })
    if (error) return callback(error)
    return callback()
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => ({
  tripTypeFullList: state['@common'].fullTripTypes
}))
export default class Trips extends PureComponent {
  constructor(props) {
    super(props)
    this.shouldUpdate = true
    this.bus = props.bus
  }

  componentDidMount() {
    this.handleCheckDetails(this.props.billSpecification)
    this.bus.on('check:details:validator', this.handleCheckDetails)
  }

  componentWillUnmount() {
    this.bus.un('check:details:validator', this.handleCheckDetails)
  }

  handleCheckDetails = billSpecification => {
    if (!billSpecification) return
    let { value = [] } = this.props
    let { name, configs = [] } = billSpecification
    let config = configs.find(v => v.ability === 'requisition')
    let tripType = {}
    if (config) {
      tripType = config.tripType
    }
    let visibleIds = tripType.ids || []

    value.forEach(v => {
      v.errorMsg = v.errorMsg || {}
      let id = typeof v.tripTypeId === 'object' ? v.tripTypeId.id : v.tripTypeId
      if (!tripType.isAll && !~visibleIds.indexOf(id)) {
        v.errorMsg = i18n.get(`该类型的行程不能使用在{__k0}里`, { __k0: name })
      } else {
        v.errorMsg = ''
      }
    })
  }

  handleAddTrips = (canEdit, currentTrips) => {
    const { bus, value, external } = this.props
    bus.invoke('element:trips:addTrip', { value, external, shouldUpdate: this.shouldUpdate, canEdit, currentTrips })
  }

  getTripType = id => {
    if (id?.endsWith(':train')) {
      return 'TRAIN'
    } else if (id?.endsWith(':hotel')) {
      return 'HOTEL'
    } else if (id?.endsWith(':flight')) {
      return 'FLIGHT'
    }
  }
  renderTripList(trips) {
    if (!trips || !trips.length) {
      return null
    }
    const { tag = {}, flowId, external, bus, isForbid } = this.props
    const isReadOnly = tag.readOnly
    const tripList = trips.map((t, index) => {
      const { specificationId, tripTypeId, tripForm } = t
      const newSpec = clone(specificationId)
      const { components = [] } = newSpec
      const defaultList = ['tripDate', 'tripDatePeriod', 'tripCity', 'tripFromCity', 'tripToCity']
      const otherComponents = components.filter(c => !~defaultList.indexOf(c.field) && !c.hide)
      newSpec.components = otherComponents
      const { tripDate, tripDatePeriod, tripCity, tripFromCity, tripToCity, ...others } = tripForm
      const itemExternal = external && external[tripForm.tripId]
      if (!tripTypeId.label) {
        tripTypeId.label = tripTypeId.active ? tripTypeId.name : tripTypeId.name + i18n.get('（已停用）')
      }

      const dateStr = tripDate
      ? formatMap['Date'](tripDate, components.find(c => c.field === 'tripDate'))
      : formatMap['DatePeriod'](tripDatePeriod, components.find(c => c.field === 'tripDatePeriod'))
    const cityStr = tripCity
      ? formatMap['City'](tripCity)
      : tripFromCity
      ? formatMap['Cities'](tripFromCity, tripToCity)
      : ''

      let dateTitle, city, dateString
      if (tripDate) {
        dateTitle = moment(tripDate).format('YYYY-MM-DD')
        dateString = `${moment(tripDate).format('MM月DD日')} ${getWeek(tripDate)}`
      } else {
        dateTitle = `${moment(tripDatePeriod.start).format('YY-MM-DD')} ${i18n.get('至')} ${moment(tripDatePeriod.end).format('YYYY-MM-DD')}`
        const days = getDays(tripDatePeriod.start, tripDatePeriod.end)
        dateString = i18n.get(`{__k0}~{__k1} 共{__k2}天{__k3}晚`, {
          __k0: moment(tripDatePeriod.start).format(i18n.get('MM月DD日')),
          __k1: moment(tripDatePeriod.end).format(i18n.get('MM月DD日')),
          __k2: days + 1,
          __k3: days
        })
      }
      if (tripTypeId?.type === 'HOTEL') {
        const tripCity = tripForm.tripCity && JSON.parse(tripForm.tripCity)
        city = tripCity ? (
          tripCity[0].label
        ) : (
          <>{i18n.get('住宿地')}</>
        )
      } else {
        const tripFromCity = tripForm.tripFromCity && JSON.parse(tripForm.tripFromCity)
        const tripToCity = tripForm.tripToCity && JSON.parse(tripForm.tripToCity)
        city = (
          <>
            {tripFromCity ? tripFromCity[0].label : i18n.get('出发地')}
            {i18n.get(' - ')}
            {tripToCity ? tripToCity[0].label : i18n.get('目的地')}
          </>
        )
      }
      const money = t?.tripForm?.tripMoney?.standard
      const type = this.getTripType(t?.tripTypeId?.id)
      return (
        <div className='trips-list-wrapper' key={index}>
          <h5>{`${i18n.get('第{__k0}程', { __k0: index + 1 })}:${dateStr}`}</h5>
          <div className='trips-item'>
            <TripItem
              tripType={tripTypeId}
              cityStr={city}
              readonly={false}
              showButton={false}
              dateString={dateString}
              editable={false}
              money={money}
              type={type}
              // onShowDetail={() => this.handleAddTrips(false, t)}
              customField={<TripsExtendsField
                labelNoWrap={true}
                isReadOnly={isReadOnly}
                specification={newSpec}
                value={others}
                external={itemExternal}
                bus={bus}
                shouldUpdate={this.shouldUpdate}
                isForbid={isForbid}
                flowId={flowId}
                tripTypeId={tripTypeId}
              />}
            />
          </div>
        </div>
      )
    })

    return <div className="trip-list">{tripList}</div>
  }

  render() {
    const { value = [], tag = {} } = this.props
    const isReadOnly = tag.readOnly
    const hasTrip = value && value.length
    const btnStr = hasTrip ? i18n.get('编辑行程') : i18n.get('添加行程')
    let disabled = isDisable(this.props)
    let style = disabled ? { pointerEvents: 'none', opacity: 0.4 } : null
    return (
      <div className={styles.trips} style={style}>
        {!isReadOnly && (
          <Button className="add-button" onClick={() => debounce(this.handleAddTrips, 20, false)()}>
            {btnStr}
          </Button>
        )}
        {this.renderTripList(value)}
      </div>
    )
  }
}
