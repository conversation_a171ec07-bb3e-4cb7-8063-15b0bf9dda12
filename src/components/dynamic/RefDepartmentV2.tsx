import React, { useEffect, useMemo, useRef, useState } from "react"
import { app as api } from '@ekuaibao/whispered'
import { TreeSelect } from "@hose/eui"
import { getPlaceholder } from "../utils/fnGetFieldLabel"
import { debounce, isEqual, isObject, isString } from 'lodash'
import { uuid } from "@ekuaibao/helpers"
import { isDisable } from "../utils/fnDisableComponent"
import { shouldUpdateValue } from "../utils/DependenceUtil"
import { getVariation } from "../../lib/featbit"
const TreeNode = TreeSelect.TreeNode

const RefDepartmentV2 = ((props) => {
  const { field, submitterId, businessType, bus, billSpecification, specificationId, value, template, form, feeType, isPermitForm, billState, isDetail, detailId } = props
  const valueRef = useRef(value)
  const { optional, dependence, allowCancelDependence, autoDependence, isLinkageAssignment, assignmentRule, editStatePathRule, allowUseDeactivatedData } = field
  const prevProps = useRef({});
  const [treeData, setTreeData] = useState([])
  const treeDataRef = useRef(treeData) // 缓存树数据
  const [searchText, setSearchText] = useState('')
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([])
  const cancelRelationRef = useRef(false)
  const isUNLIMITED = useRef(false)
  const originUnactivatedDepartment = useMemo(() => value?.id && !value?.active && value, [])
  const useDependenceKeys = (dependence?.map(d => d.dependenceId) || []).concat(autoDependence?.map(d => d.dependenceId)) || []
  const departmentMapData = api.getState('@common.department.mapData') || {}
  const treeNodeLabel = editStatePathRule === 'fullPath' ? 'fullPath' : 'name'
  const placeholder = `${optional ? i18n.get('（选填）') : ''}${getPlaceholder(field)}`
  const disabled = isDisable(props)
  const time = useRef(Number(getVariation('mfrd-3133-department-search-debounce', '500')))
  let currentRequestId = null
  const noUseDependenceData = isPermitForm && allowCancelDependence && value && field.editable === false // 申请单导入数据，不可编辑状态下不触发档案关系

  useEffect(() => {
    bus.on('on:dependence:change', handleDependenceChange)
    bus.on('set:delegator', handleDelegatorChanged)
    bus.watch(`department:check:${field.name}:value`, checkExternalSourceData)
    api.dataLoader('@contactManagement.contactConfig').load().then((contactConfig) => {
      isUNLIMITED.current = contactConfig?.filterScope === 'UNLIMITED'
    })
    
    setTimeout(() => bus.emit('on:dependence:change', { key: field.name, id: value?.id || value, isInit: true }), 200)

    if (noUseDependenceData) {
      const id = value?.id || value
      departmentMapData[id] && setTreeData(departmentMapData[id])
    }
    
    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
      bus.un('set:delegator', handleDelegatorChanged)
      bus.un(`department:check:${field.name}:value`, checkExternalSourceData)
    }
  }, [])

  useEffect(() => {
    getDepartment()
  }, [searchText])

  useEffect(() => {
    calcTreeExpandedKeys()
    treeDataRef.current = treeData
  }, [treeData])

  useEffect(() => {
    valueRef.current = value
  }, [value])
  
  useEffect(() => {
    shouldUpdateValue(prevProps.current, props)
    if (isString(value) && departmentMapData[value]) {
      onChange(departmentMapData[value])
    }
    if (!prevProps.current?.value && props.value) {
      const id = isObject(props.value) ? props.value?.id : props.value
      if (id && departmentMapData[id]) {
        fnSetFormValue(departmentMapData[id])
      }
    }
    prevProps.current = props
  }, [props])

  const calcTreeExpandedKeys = () => {
    const treeExpandedKeys = new Set()

    const findDep = (items, depth = 0) => {
      let hasMatch = false
      for (const item of items) {
        const { name, enName, code } = item

        if (searchText && (name?.includes(searchText) || enName?.includes(searchText) || code?.includes(searchText))) {
          treeExpandedKeys.add(item.id)
          hasMatch = true
        }

        if (item.children?.length) {
          const childrenMatch = findDep(item.children, ++depth)
          if (childrenMatch || depth <= 1) {
            treeExpandedKeys.add(item.id)
            hasMatch = true
          }
        }
      }
      return hasMatch
    }
    findDep(treeData)
    setTreeExpandedKeys(Array.from(treeExpandedKeys))
  }

  const getDepartment = async (delegatorStaff = undefined, isInit = false) => {
    const ramdom = uuid(10)
    currentRequestId = ramdom
    const formData = await bus.getFieldsValue()
    const filterFormData = {}
    useDependenceKeys.forEach((key: string) => {
      filterFormData[key] = isObject(formData[key]) ? (formData[key] as { id: string }).id : formData[key]
      if (key === 'flow.FeeType') {
        filterFormData[key] = feeType?.id
      }
    })
    const params = {
      businessType,
      deptFieldName: field.field,
      form: {
        ...filterFormData,
        submitterId: delegatorStaff?.id || submitterId?.id,
        // 无限制搜索场景添加当前选中部门
        [field.field]: isUNLIMITED.current ? value?.id : undefined,
      },
      flowSpecId: billSpecification.id,
      detailSpecId: specificationId,
      searchText: searchText,
      cancelRelation: cancelRelationRef.current,
    }
    try {
      const { items, autoAssignmentItem } = await api.invokeService('@bills:get:department:visible:data', params)
      if (currentRequestId === ramdom) {
        setTreeData(items)
        if (!searchText) {
          if (autoAssignmentItem && !isInit) {
            onChange(autoAssignmentItem)
          } else {
            const currentValueCanSelect = checkDepCanSelect(items, valueRef.current)
            if (!currentValueCanSelect) {
              bus.setValidateLevel(1)
              onChange(undefined)
            }
          }
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  const checkExternalSourceData = (value) => {
    const isCanSelect = checkDepCanSelect(treeDataRef.current, value)
    return Boolean(isCanSelect)
  }
  
  const checkDepCanSelect = (items, value) => {
    const depId = value?.id || value
    if (!depId) return false
    if (
      allowUseDeactivatedData &&
      originUnactivatedDepartment &&
      originUnactivatedDepartment?.id === depId &&
      !departmentMapData[originUnactivatedDepartment.id]
    ) {
      // 停用部门数据需展示
      return true
    }
    const findDep = (items) => {
      for (const item of items) {
        if (item.id === depId) {
          return item.selectable
        }
        if (item.children?.length) {
          const found = findDep(item.children)
          if (found !== undefined) return found
        }
      }
      return undefined
    }
    return findDep(items) || false
  }

  const handleDependenceChange = async ({ key, id, dependenceFeeType }, options = {}) => {
    if (noUseDependenceData) {
      return
    }
    const isInit = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit
    if (useDependenceKeys?.includes(key)) {
      getDepartment(undefined, isInit)
    }
  }

  const debounceSearch = debounce((searchText) => {
    setSearchText(searchText)
  }, time.current)

  const onSearch = (searchText: string) => {
    if (!searchText && value?.id) {
      // 当前存在选中值，但是最新的可选数据不存在，避免显示id，导致无法匹配
      setTreeData([value])
    }
    debounceSearch(searchText)
  }

  const onChange = value => {
    const dep = value && isString(value) ? departmentMapData[value] : value
    fnSetFormValue(dep)
    props.onChange(dep)
    if (searchText) {
      setSearchText('')
    }
  }
  // 部门字段联动赋值逻辑
  const fnSetFormValue = async (value = {}) => {
    const fields = (assignmentRule && assignmentRule.fields) || []
    if (isEqual(value, {})) {
      return
    }
    const valueMap = {}
    const tempMap = {}
    if (template) {
      template.map(oo => (tempMap[oo.field] = oo))
    }
    let formValue = {}
    if (bus) {
      formValue = await bus.getValue()
    }
    if (isLinkageAssignment) {
      fields.forEach(item => {
        const tempItem = tempMap[item.targetField]
        if (tempItem) {
          const values = (value as any)?.form?.[item.sourceField]
          if (!!values) {
            if (tempItem.type === 'number') {
              valueMap[tempItem.name] = `${values * 1}`
            } else if (tempItem.type === 'date') {
              valueMap[tempItem.name] = Number(values)
            } else {
              const currentValue = formValue[tempItem.name]
              if (currentValue && isString(values) && currentValue?.id === values) {
                valueMap[tempItem.name] = currentValue
              } else {
                valueMap[tempItem.name] = values
              }
            }
          }
        }
      })
      if (Object.keys(valueMap)?.length) {
        !!form && form.setFieldsValue(valueMap)
      }
    }
  }

  const handleDelegatorChanged = staff => {
    getDepartment(staff)
  }

  const onClickCancelDependence = () => {
    cancelRelationRef.current = true
    getDepartment()
  }

  const notFoundContent = allowCancelDependence && dependence?.length && !cancelRelationRef.current ? (
    <div className="cancel-dependence">
      {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
      <a href="javascript:void 0" onClick={onClickCancelDependence}>
        {i18n.get('查看全量数据')}
      </a>
    </div>
  ) : (
    undefined
  )

  const renderTreeNodes = (tree: Array<any>) => {
    let treeData = tree
    if (
      allowUseDeactivatedData &&
      originUnactivatedDepartment?.id &&
      !departmentMapData[originUnactivatedDepartment.id]
    ) {
      treeData = [originUnactivatedDepartment, ...tree]
    }
    const loop = (data) => {
      const { id, name, enName, code, selectable, children, fullPath, active } = data
      const showName = (i18n.currentLocale === 'en-US' && enName ? enName : name) + (code ? `(${code})` : '')
      const activeName = active ? showName : `${showName}${i18n.get('(已停用)')}`
      return <TreeNode
        key={id}
        value={id}
        title={activeName}
        disabled={!selectable}
        name={showName}
        fullPath={fullPath || showName}
      >
        {children?.length && children.map((item) => loop(item))}
      </TreeNode>
    }
    const treeDataResult = treeData?.map((item) => loop(item))
    return treeDataResult
  }

  const onTreeExpand = (expandedKeys: string[]) => {
    setTreeExpandedKeys(expandedKeys)
  }

  return (
    <TreeSelect
      showSearch
      style={{ width: '100%' }}
      placeholder={placeholder}
      disabled={disabled}
      allowClear
      onChange={onChange}
      treeNodeLabelProp={treeNodeLabel}
      value={value?.id || value}
      onSearch={onSearch}
      filterTreeNode={false}
      treeExpandedKeys={treeExpandedKeys}
      onTreeExpand={onTreeExpand}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      notFoundContent={notFoundContent}
    >
      {renderTreeNodes(treeData)}
    </TreeSelect>
  )
})

export default RefDepartmentV2
