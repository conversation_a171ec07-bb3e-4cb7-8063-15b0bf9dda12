import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { TimePicker } from 'antd'
import moment from 'moment'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import 'moment/locale/zh-cn'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'time'
  },
  wrapper: wrapper()
})
export default class Time extends PureComponent {
  constructor(props) {
    super(props)
    moment.locale('zh-cn')
  }

  handleChange = date => {
    const { onChange, field } = this.props
    onChange && onChange(date)
  }

  render() {
    let { value, field } = this.props

    value = value && moment(value)
    let disabled = isDisable(this.props)
    return (
      <TimePicker
        id={field.name}
        style={{ width: '100%' }}
        value={value}
        onChange={this.handleChange}
        format={'HH:mm'}
        allowClear={false}
        disabled={disabled}
        size="large"
      />
    )
  }
}
