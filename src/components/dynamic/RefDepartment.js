/**************************************************
 * Created by nany<PERSON>ingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { get } from 'lodash'
import { constantValue, lastSelectValue, checkDefaultDeptValue } from '../utils/fnInitalValue'
import RefDepartmentCP from './RefDepartmentCP'
import RefDepartmentV2 from './RefDepartmentV2'
import { useDepartmentVisible } from '../../lib/featbit/feat-switch'
@EnhanceField({
  descriptor: {
    type: 'ref:organization.Department'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  initialValue(props) {
    const { field, submitterId = {}, value, lastChoice } = props
    if (!value) {
      // value有值时，应该不会走initalValue吧？
      //这个部门字段是否配置了默认值
      //判断默认值是否是“固定值”
      let constVal = constantValue(field)
      if (constVal) return checkDefaultDeptValue({ id: constVal }, field, submitterId)
      //判断默认值是否是“上次选择”
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return checkDefaultDeptValue({ id: lastVal }, field, submitterId)
    }
    let { defaultValue = {} } = field
    let type = get(field, 'defaultValue.type', '')
    if (type === 'predefine' && !['submit.requisition', 'lastselect'].includes(defaultValue.value)) {
      let defaultDepartment = submitterId.defaultDepartment
      if (defaultDepartment) {
        let { id, name, code } = defaultDepartment
        return checkDefaultDeptValue({ id, name, code }, field, submitterId)
      }
      return checkDefaultDeptValue(defaultDepartment, field, submitterId)
    }
  },
  wrapper: wrapper()
})
export default class RefDepartment extends Component {
  render() {
    const { businessType } = this.props
    // businessType存在于单据场景，非单据场景不使用新的部门组件
    if (businessType && useDepartmentVisible()) {
      return <RefDepartmentV2 {...this.props}/>
    }
    return <RefDepartmentCP {...this.props} useEUI/>
  }
}
