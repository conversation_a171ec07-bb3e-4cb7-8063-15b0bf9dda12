/**************************************************
 * Created by nany<PERSON>ingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { DatePicker as AntdDatePicker, LocaleProvider } from 'antd'
import { DatePicker as EUIDatePicker, ConfigProvider } from '@hose/eui'
import moment from 'moment'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import 'moment/locale/zh-cn'
import { get } from 'lodash'
import { isDisable } from '../utils/fnDisableComponent'
import fnPredefine4Date, { formatWithTime, formatDateTime, getShowTime } from '../utils/fnPredefine4Date'
import { ENUM_DATE_TYPE, getLocale, getEUILocale } from '../consts'
import styles from './Date.module.less'
import classNames from 'classnames'
const { MonthPicker: AntdMonthPicker } = AntdDatePicker

@EnhanceField({
  descriptor: {
    type: 'date'
  },
  initialValue(props) {
    let { field = {} } = props
    return fnPredefine4Date(field)
  },
  validator: (field, props) => (rule, value, callback) => {
    let { defaultValue } = field
    if (defaultValue && defaultValue.type === 'predefine' && defaultValue.value === 'repayment.date') {
      let loanDate = props.form.getFieldValue('loanDate')
      if (value && value < loanDate) return callback(i18n.get('还款日期应该晚于借款日期,请重新选择'))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class DateCell extends PureComponent {
  constructor(props) {
    super(props)
    moment.locale('zh-cn')
    this.state = {
      timeField: props.timeField
    }
  }

  componentWillMount() {
    let { field, onChange, approveModify = false, logs } = this.props
    let { isFromThirdParty, withTime, dateTimeType } = field
    let typeValue = get(field, 'defaultValue.value')
    if (typeValue === 'lastSubmit.date' && !isFromThirdParty && !approveModify) {
      //多次送审取最后一次
      let date = formatWithTime(withTime, undefined, dateTimeType)
      onChange && onChange(date)
    }

    if (typeValue === 'firstSubmit.date' && !(logs && logs.length) && !isFromThirdParty) {
      //多次送审取第一次
      let date = formatWithTime(withTime, undefined, dateTimeType)
      onChange && onChange(date)
    }
    this.props.bus.on('assign:date:value', this.assignDateValue)
    this.props.bus.on('continue:add:detail', this.saveContinueAction)
  }

  componentWillUnmount = () => {
    this.timeFieldChange(null)
    this.props.bus.un('assign:date:value', this.assignDateValue)
    this.props.bus.un('continue:add:detail', this.saveContinueAction)
  }

  assignDateValue = date => {
    const { onChange, field } = this.props
    const { defaultValue, withTime, dateTimeType } = field
    if (defaultValue?.type === 'predefine' && defaultValue?.value === 'submit.travelPlan') {
      let time = formatWithTime(withTime, date, dateTimeType)
      onChange && onChange(time)
    }
  }

  saveContinueAction = () => {
    const { field = {} } = this.props
    const date = fnPredefine4Date(field)
    this.timeFieldChange(date)
  }

  timeFieldChange = date => {
    const { bus, field = {}, value } = this.props
    const { timeField } = this.state
    if (timeField && field?.name === timeField && value !== date) {
      const endOfDay = moment(date)
        .endOf('day')
        .valueOf() // 获取当天最后一秒的时间戳
      bus.emit('timeField:change', { date: endOfDay })
    }
  }

  componentDidMount() {
    this.timeFieldChange(this.props.value)
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.timeField !== this.props.timeField) {
      this.setState({ timeField: nextProps.timeField }, () => {
        this.timeFieldChange(nextProps.value)
      })
    }
  }

  handleChange = date => {
    const { onChange, field, isDetail } = this.props
    const { withTime, dateTimeType } = field
    let dateStr = formatWithTime(withTime, date, dateTimeType)
    if (isDetail) {
      this.timeFieldChange(dateStr)
    }
    onChange && onChange(dateStr)
  }

  render() {
    let { value, field, useEUI, noPopupContainer } = this.props
    let { withTime, isClear = false, dateTimeType } = field
    value = value && moment(parseInt(value))
    let disabled = isDisable(this.props)
    if (useEUI) {
      return (
        <ConfigProvider locale={getEUILocale()}>
          {dateTimeType === ENUM_DATE_TYPE.YEAR_MONTH ? (
            <EUIDatePicker
              picker="month"
              id={field.name}
              style={{ width: '100%' }}
              value={value}
              onChange={this.handleChange}
              disabled={disabled}
              className={classNames({ [styles['date-auto-disabled']]: disabled })}
            />
          ) : (
            <EUIDatePicker
              id={field.name}
              style={{ width: '100%' }}
              value={value}
              onChange={this.handleChange}
              format={formatDateTime(withTime, dateTimeType)}
              disabled={disabled}
              showTime={getShowTime(withTime, dateTimeType)}
              className={classNames({ [styles['date-auto-disabled']]: disabled })}
              getPopupContainer={triggerNode => (noPopupContainer ? document.body : triggerNode.parentNode)}
            />
          )}
        </ConfigProvider>
      )
    } else {
      return (
        <LocaleProvider locale={getLocale()}>
          {dateTimeType === ENUM_DATE_TYPE.YEAR_MONTH ? (
            <AntdMonthPicker
              id={field.name}
              style={{ width: '100%' }}
              value={value}
              onChange={this.handleChange}
              allowClear={isClear}
              disabled={disabled}
              size="large"
              className={classNames({ [styles['date-auto-disabled']]: disabled })}
            />
          ) : (
            <AntdDatePicker
              id={field.name}
              style={{ width: '100%' }}
              value={value}
              onChange={this.handleChange}
              format={formatDateTime(withTime, dateTimeType)}
              allowClear={isClear}
              disabled={disabled}
              size="large"
              showTime={getShowTime(withTime, dateTimeType)}
              className={classNames({ [styles['date-auto-disabled']]: disabled })}
            />
          )}
        </LocaleProvider>
      )
    }
  }
}
