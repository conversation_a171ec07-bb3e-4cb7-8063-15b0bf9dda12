/**
 * Created by <PERSON><PERSON><PERSON> on 2017/11/6.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'

@EnhanceField({
  descriptor: {
    type: 'select_search'
  },
  wrapper: wrapper(true)
})
export default class RefSelectSearch extends PureComponent {
  render() {
    let { value = {}, field = {} } = this.props
    const name = getContentByLocale(value, 'name')
    return name ? (
      <span>
        {name}
        {value.active === false && i18n.get('(已停用)')}
        &nbsp;&nbsp;
        {
          !field?.hideCode ?
          <span style={{ color: 'rgba(0, 0, 0, 0.25)' }}>({value['code']})</span>
          :
          null
        }
      </span>
    ) : (
      <span>{i18n.get('无')}</span>
    )
  }
}
