/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import PayeeAccountCard from '../../elements/payee-account/payee-account-card'

@EnhanceField({
  descriptor: {
    type: 'list:ref:pay.PayeeInfo'
  },
  wrapper: wrapper(true)
})
export default class PayeeInfo extends PureComponent {
  render() {
    let { value, emptyPlaceholder = null } = this.props
    if (!value) {
      return emptyPlaceholder || '-'
    }
    value = value.toJS ? value.toJS() : value
    if (!Array.isArray(value)) {
      return emptyPlaceholder || '-'
    }
    return (
      <div>
        {value.map(v => {
          return (
            <PayeeAccountCard
              hiddenActions
              {...v}
              dynamicCard="readonly"
              style={{ marginBottom: 16 }}
              className="mw-560 clear_borderhover flex-1 "
            />
          )
        })}
      </div>
    )
  }
}
