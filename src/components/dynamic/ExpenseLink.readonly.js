/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/21.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import RequisitionInfo from '../../elements/puppet/RequisitionInfo'
import AssociatedTrip from '../../elements/puppet/RelevantApply'
import style from './ExpenseLink.module.less'
import { related } from '../../elements/feeDetailViewList/Related'
import { toJS } from 'mobx'

@EnhanceField({
  descriptor: {
    test({ type }) {
      return type === 'expenseLink' || type === 'expenseLinks'
    }
  },
  wrapper: wrapper(true)
})
export default class ExpenseLinkReadonly extends PureComponent {
  constructor(props) {
    super(props)
    const { field, value } = props
    this.state = {
      hasOrderList: []
    }
    const data = Array.isArray(value) ? value : value ? [value] : []
    related.setExpenseLink(data)
    related.setExpenseSpecification(field)
  }

  handleDetailClick = value => {
    let { bus, flowId } = this.props
    bus && bus.emit('check:requisition:detail', { detail: value, flowId })
  }

  setHasOrder = (id) => {
    const { hasOrderList } = this.state
    const newOrderList = hasOrderList.slice(0)
    newOrderList.push(id)
    this.setState({
      hasOrderList: newOrderList
    })
  }
  render() {
    let { value, billSpecification, submitterId } = this.props
    const { hasOrderList } = this.state
    if (!value) return <div>{i18n.get('无')}</div>
    const v = toJS(value)
    const data = Array.isArray(v) ? v : [v]
    const tripOrderInfo = billSpecification?.configs?.find(line => line.ability === 'apply')?.tripOrderInfo
    const { id, name } = submitterId;
    // 报销单id 不是申请单id
    return (
      <div className={style['expense-link-readonly']}>
        {data.map(line => (
          <div key={line.id} className='new-requisition-wrapper'>
            <RequisitionInfo value={line} onDetailClick={this.handleDetailClick.bind(this, line)} hasOrder={hasOrderList.includes(line.id)} />
            {tripOrderInfo ? <AssociatedTrip fromExpenseLink submitterId={id} requisitionId={line.id} submitterName={name} setHasOrder={this.setHasOrder} /> : null}
          </div>
        ))}
      </div>
    )
  }
}
