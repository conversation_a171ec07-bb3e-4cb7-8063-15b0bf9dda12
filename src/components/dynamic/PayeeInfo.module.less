@import '~@ekuaibao/eui-styles/less/token.less';

.batch-add-wrapper {
  width: 100%;
  position: relative;
  :global {
    .batch-add-content {
      padding: 0 5px;
      overflow-y: auto;
      min-height: 30px;
      border: 1px solid #e6e6e6;
      &:hover {
        border-color: @color-brand-3;
      }
    }
    .batch-feeType-select {
      width: 100%;
    }
    .placeholder {
      color: #cbcbcb;
    }
  }
}

.receiving-currency-wrapper {
  background-color: var(--eui-bg-body-overlay);
  :global {
    .receiving-currency-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      border-radius: 6px 0px 0px 6px;
      border-top: 1px solid var(--eui-line-border-component);
      border-left: 1px solid var(--eui-line-border-component);
      border-bottom: 1px solid var(--eui-line-border-component);
      background-color: var(--eui-bg-body-overlay);
      font: var(--eui-font-body-r1);
      color: var(--eui-text-title);
    }
  }
}

.receiving-currency-wrapper-empty {
  :global {
    .receiving-currency-content {
      border: 1px solid var(--eui-function-danger-500);
    }
  }
}
