import React, { useState, useCallback, useEffect } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import { app } from '@ekuaibao/whispered'
import { IComponentDef } from '@ekuaibao/template/src/Cellar'
import OrderList from './OrderList'
import {
  formatTripDataLinkEntity,
  getDatalinkIds,
  formatSettlementLinkEntity,
  removeDuplicates,
  setKeyData
} from './utils'
interface IProps {
  [key: string]: any
}
let supplierAccountList = [],
  datalinkEntity = []
const OrderConfigSelect: React.FC<IProps> = (props: IProps) => {
  const { bus, value, sourceData, onChange } = props
  const [orderField, setOrderField] = useState([])
  const [settlementField, setSettlementField] = useState([])
  const getOrderField = useCallback(async () => {
    const { items = [] } = await app.invokeService('@tpp-v2:get:tripDataLinkEntityList', { type: 'TRAVEL_MANAGEMENT' })
    const fields = removeDuplicates(formatTripDataLinkEntity(items), 'label')
    setOrderField(fields)
  }, [])
  const getSettlementField = useCallback(async (supplierArchiveType = []) => {
    supplierAccountList = (await app.invokeService('@settlement:get:supplier:account:list', {}))?.items ?? []
    datalinkEntity = (await app.invokeService('@custom-specification:get:datalink:entity'))?.items ?? []
    changeOptions(supplierArchiveType)
  }, [])
  const changeOptions = supplierArchiveType => {
    const datalinkIds = getDatalinkIds(supplierAccountList, supplierArchiveType)
    const fields = removeDuplicates(formatSettlementLinkEntity(datalinkEntity, datalinkIds), 'label')
    setSettlementField(fields)
  }
  useEffect(() => {
    bus.on('supplierArchiveType', changeOptions)
    getOrderField()
    getSettlementField(sourceData?.supplierArchiveType)
    return () => {
      bus.un('supplierArchiveType', changeOptions)
    }
  }, [])
  const handlerChange = res => {
    const _value = setKeyData(res, value)
    onChange?.(_value)
  }

  return (
    <div>
      <OrderList
        orderField={orderField}
        settlementField={settlementField}
        onChange={handlerChange}
        value={value}
      ></OrderList>
    </div>
  )
}
export default EnhanceField(({
  descriptor: {
    type: 'order-config-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    let err = false
    value.forEach(i => (!i.sourceField || !i.targetField) && (err = true))
    if (err) {
      return callback(i18n.get('请选择匹配字段'))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback()
  },
  initialValue(props) {
    return [{}]
  },
  wrapper: wrapper()
} as unknown) as IComponentDef)(OrderConfigSelect)
