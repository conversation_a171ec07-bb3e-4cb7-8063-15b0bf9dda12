/*
 * @Author: <PERSON>
 * @Date: 2022-10-18 16:30:04
 * @LastEditTime: 2022-10-20 10:23:42
 * @LastEditors: Hunter
 * @Description: 
 * @FilePath: \web\src\components\dynamic\OrderConfigSelect\utils.ts
 * 可以输入预定的版权声明、个性签名、空行等
 */
import { cloneDeep } from 'lodash'
export enum changeType {
  create,
  delete,
  change
}
export const orderKey = 'sourceField'
export const settlementKey = 'targetField'
export const orderTripWhiteList = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI']
export const formatTripDataLinkEntity = (list: any[]) => {
  if (list?.length === 0) return list
  return list
    .find(i => i.type === 'ORDER')
    ?.children?.filter(i => orderTripWhiteList.includes(i.type))
    ?.reduce((res, i) => res.concat(i.fields), [])
}
export const getDatalinkIds = (list: any[], supplierArchiveType: string) => {
  if (list?.length === 0) return list
  return list
    ?.filter(i => supplierArchiveType === i.supplierArchiveType)
    ?.reduce((res, i) => res.concat(i.categoryIds), [])
}
export const formatSettlementLinkEntity = (list: any[], datalinkIds = []) => {
  if (list?.length === 0) return list
  return list.filter(i => datalinkIds.includes(i.id))?.reduce((res, i) => res.concat(i.fields), [])
}
export const removeDuplicates = (list = [], key, source = {}) => {
  return list.filter(i => {
    if (source[i[key]]) {
      return false
    }
    source[i[key]] = i[key]
    return true
  })
}
export const setKeyData = ({ key, index, val, type }, value) => {
  const _value = cloneDeep(value)
  switch (type) {
    case changeType.create:
      _value.push({})
      break
    case changeType.delete:
      _value.splice(index, 1)
      break
    case changeType.change:
      if (key === orderKey) {
        _value[index][settlementKey] = ''
      }
      _value[index][key] = val
      break
  }
  return _value
}
// date
// list
// money
// number
// ref
// text
// 金额、人员、文本、数字、部门、日期、自定义档案、城市
// const fieldTypes = [
//   'text',
//   'money',
//   'date',
//   'number',
//   'organization.Staff',
//   'organization.Department',
//   'basedata.Dimension',
//   'basedata.city'
// ]
export const filterOptionsByType = (opts, types) => {
  return opts?.filter(i => findOptItemType(i, types)) ?? []
}

export const findOptItemType = (item, types) => {
  switch (item?.type) {
    case 'text':
    case 'money':
    case 'number':
    case 'date':
      return types?.includes?.(item.type) ? item.type : false
    case 'ref':
      return types?.find?.(it => item.entity?.startsWith(it))
  }
}