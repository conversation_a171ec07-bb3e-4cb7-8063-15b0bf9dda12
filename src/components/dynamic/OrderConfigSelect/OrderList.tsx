import React from 'react'
import { OrderSelect } from '../OrderConfigRules/OrderSelect'
import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import { Tooltip } from 'antd'
import style from '../OrderConfigRules/OrderConfigRules.module.less'
const EKBIcon = app.require<any>('@elements/ekbIcon')
import { changeType, orderKey, settlementKey, filterOptionsByType, findOptItemType } from './utils'
const maxLen = 10
// 金额、人员、文本、数字、部门、日期、自定义档案、城市
const fieldTypes = [
  'text',
  'money',
  'date',
  'number',
  'organization.Staff',
  'organization.Department',
  'basedata.Dimension',
  'basedata.city'
]
interface IProps {
  [key: string]: any
}
const OrderList: React.FC<IProps> = (props: IProps) => {
  const { value = [], orderField = [], settlementField = [], onChange } = props
  const filterOpts = (value, optKey, fields, fieldTypes) => {
    return filterOptionsByType(fields, fieldTypes).map(item => ({
      ...item,
      disabled: value.find(v => item.label === v?.[optKey])
    }))
  }
  const renderList = value =>
    value.map((i, index) => (
      <div className={style['rules']}>
        <T name="当订单的" />
        <OrderSelect
          options={filterOpts(value, orderKey, orderField, fieldTypes)}
          labelKey="label"
          valueKey="label"
          value={i[orderKey] ?? ''}
          onChange={val => onChange({ type: changeType.change, index, val, key: orderKey })}
        ></OrderSelect>
        <T name="等于对账单的" />
        <OrderSelect
          options={filterOpts(value, settlementKey, settlementField, [
            findOptItemType(
              orderField?.find?.(it => it.label === i[orderKey]),
              fieldTypes
            ) ?? ''
          ])}
          labelKey="label"
          valueKey="label"
          value={i[settlementKey] ?? ''}
          onChange={val => onChange({ type: changeType.change, index, val, key: settlementKey })}
        ></OrderSelect>
        <div className={style['operation']}>
          {value?.length >= maxLen ? (
            <Tooltip title={i18n.get(`至多选择${maxLen}个条件`)} trigger="click">
              <EKBIcon name="#EDico-plus-default" className="oper mr-8" />
            </Tooltip>
          ) : (
            <EKBIcon
              name="#EDico-plus-default"
              className="oper mr-8"
              onClick={() => onChange({ type: changeType.create, index })}
            />
          )}
          {value?.length === 1 ? (
            <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
              <EKBIcon name="#EDico-scan-b" className="oper" />
            </Tooltip>
          ) : (
            <EKBIcon
              name="#EDico-scan-b"
              className="oper"
              onClick={() => onChange({ type: changeType.delete, index })}
            />
          )}
        </div>
      </div>
    ))

  return <div>{renderList(value)}</div>
}
export default OrderList
