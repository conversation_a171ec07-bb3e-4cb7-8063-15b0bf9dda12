/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { DatePicker as AntdDatePicker } from 'antd'
import { DatePicker as EUIDatePicker, ConfigProvider } from '@hose/eui'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
import fnPredefine4DateRange from '../utils/fnPredefine4DateRange'
import { formatWithTime, formatDateTime, getShowTime } from '../utils/fnPredefine4Date'
import styles from './Date.module.less'
import classNames from 'classnames'
import { getEUILocale } from '../consts'

@EnhanceField({
  descriptor: {
    type: 'dateRange'
  },
  initialValue(props) {
    let { field } = props
    return fnPredefine4DateRange(field)
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class DateRange extends PureComponent {
  constructor(props) {
    super()
    moment.locale('zh-cn')
  }

  componentDidMount = () => {
    this.props.bus.on('assign:date:range:value', this.assignDateRangeValue)
  }

  componentWillUnmount = () => {
    this.props.bus.un('assign:date:range:value', this.assignDateRangeValue)
  }

  assignDateRangeValue = daterange => {
    const { onChange, field } = this.props
    const { defaultValue } = field
    if (defaultValue?.type === 'predefine' && defaultValue?.value === 'submit.travelPlan') {
      let start = formatWithTime(field.withTime, daterange[0], field.dateTimeType)
      let end = formatWithTime(field.withTime, daterange[1], field.dateTimeType)
      if (moment(daterange[1]).format('HH:mm:ss') === moment(daterange[1]).format('00:00:00')) {
        end = moment(moment(daterange[1]).format('YYYY-MM-DD 23:59:59')).valueOf()
      }
      onChange && onChange({ start, end })
    }
  }

  handleChange = date => {
    const { onChange, field, bus } = this.props
    const files = {}
    let dateValue = []
    if (date && Array.isArray(date)) {
      dateValue = date.map(item => !!item ? item : Date.now())
    }
    if (dateValue.length === 0) {
      //清空数据
      files[field?.field] = undefined
      onChange && onChange(undefined)
      bus && bus.emit('dateRange:changed:value', files)
    } else {
      let start = formatWithTime(field.withTime, dateValue[0], field.dateTimeType)
      let end = formatWithTime(field.withTime, dateValue[1], field.dateTimeType)
      files[field?.field] = { start, end }
      onChange && onChange({ start, end })
      bus && bus.emit('dateRange:changed:value', files)
    }
  }
  render() {
    let { value, field, useEUI } = this.props
    let { withTime, dateTimeType } = field
    let date = void 0

    if (value) {
      let start = moment(value.start)
      let end = moment(value.end)
      date = [start, end]
    }
    let disabled = isDisable(this.props)
    const dateTime = formatDateTime(withTime, dateTimeType)
    const dateType = dateTime === 'YYYY-MM' ? 'month' : 'date'
    return (
      useEUI ? (
        <ConfigProvider locale={getEUILocale()}>
          <EUIDatePicker.RangePicker
            style={{ width: '100%' }}
            value={date}
            onChange={this.handleChange}
            format={dateTime}
            disabled={disabled}
            allowClear
            showTime={getShowTime(withTime, dateTimeType)}
            className={classNames({ [styles['date-auto-disabled']]: disabled })}
            getPopupContainer={triggerNode => triggerNode.parentNode}
          />
        </ConfigProvider>
      ) : (
        <AntdDatePicker.RangePicker
          style={{ width: '100%' }}
          value={date}
          onChange={this.handleChange}
          onPanelChange={this.handleChange}
          format={dateTime}
          disabled={disabled}
          size="large"
          showTime={getShowTime(withTime, dateTimeType)}
          mode={[dateType, dateType]}
          className={classNames({ [styles['date-auto-disabled']]: disabled })}
        />
      )
    )
  }
}
