import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from 'antd'
import styles from './LoanRuleType.module.less'
import { wrapper } from '../layout/FormWrapper'

const RadioGroup = Radio.Group

interface Props {
  field: any
  value: any
  onChange: Function
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'loan-rule-type'
  },
  wrapper: wrapper()
})
export default class LoanRuleType extends PureComponent<Props, State> {
  componentDidMount() {
    const {
      onChange,
      value,
      field: { defaultValueType }
    } = this.props
    if (!value) {
      onChange && onChange(defaultValueType)
    }
  }

  handleChange = (e: any) => {
    const val = e.target.value
    const { onChange } = this.props
    onChange && onChange(val)
  }

  render() {
    const { field, value } = this.props
    const { tags, defaultValueType } = field
    return (
      <div className={styles['loan-rule-type-wrapper']}>
        <RadioGroup onChange={this.handleChange} value={value || defaultValueType}>
          {tags.map(v => {
            return (
              <Radio key={v.value} value={v.value}>
                {v.label}
              </Radio>
            )
          })}
        </RadioGroup>
      </div>
    )
  }
}
