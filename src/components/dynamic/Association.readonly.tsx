/*
 * @Description: 场景群插件
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-12-10 16:08:04
 */
import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { Switch, Button, Checkbox } from 'antd'
import styles from './Association.module.less'
import { wrapper } from '../layout/FormWrapper'
import icon from './../../images/dingtalk-group.svg'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { Resource } from '@ekuaibao/fetch'
import { isObject, isArray, isString } from 'lodash'
import qs from 'qs'

const associationGroupData = new Resource('/api/v1/association/group/list')
const groupRelation = new Resource('/api/v2/flow/group/relation')
const createGroupRelation = new Resource('/api/v1/association/group/createSceneGroup')
const joinDtGroup = new Resource('/api/v1/association/group/joinDtGroup')
// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'isOpenAssociation'
  },
  wrapper: wrapper(true, { wrapperCol: { span: 14 } })
})
@EnhanceConnect(state => {
  return {
    userInfo: state['@common'].userinfo.data
  }
})
export default class Association extends Component<any> {
  constructor(props) {
    super(props)
    this.state = {
      componentLabel: '',
      associationSpecification: null,
      hasGroupRelation: true,
      groupRelationData: {},
      loading: false,
      loadingUnBind: false,
      rolesMap: {},
      staffList: [],
      staffSet: new Set(),
      selectSet: new Set()
    }
  }

  componentDidMount() {
    this.checkGroupRelation()
  }
  getBillAndPlanStaff = async () => {
    const { rolesMap, staffList, selectSet, staffSet, associationSpecification, componentLabel } = this.state
    const staff = this?.props?.userInfo?.staff
    rolesMap[staff?.id] = i18n.get('我')
    selectSet?.add(staff?.id)
    staffSet?.add(staff?.id)
    staffList?.push(staff)
    const template = this?.props?.template
    const tag = this?.props?.tag?.tag
    const plan = this?.props?.tag?.plan
    const nodes = plan?.nodes
    const submitter = tag?.['submitterId']
    if (!rolesMap?.[submitter?.id]) {
      rolesMap[submitter?.id] = i18n.get('提交人')
    }
    selectSet?.add(submitter?.id)
    if (!staffSet?.has(submitter?.id)) {
      staffSet?.add(submitter?.id)
      staffList?.push(submitter)
    }
    nodes?.forEach(it => {
      if (it?.type !== 'ebot' && it?.type !== 'recalculate' && it?.type !== 'invoicingApplication') {
        if (it?.type === 'countersign') {
          it?.counterSigners?.forEach(itt => {
            if (itt?.signerId) {
              const value = itt?.signerId
              if (!rolesMap[value?.id]) {
                rolesMap[value?.id] = i18n.get('审批人')
              }
              selectSet?.add(value?.id)
              if (!staffSet?.has(value?.id)) {
                staffSet?.add(value?.id)
                staffList?.push(value)
              }
            }
          })
        } else {
          if (it?.approverId) {
            const value = it?.approverId
            selectSet?.add(value?.id)
            if (!rolesMap[value?.id]) {
              rolesMap[value?.id] = i18n.get('审批人')
            }
            if (!staffSet?.has(value?.id)) {
              staffSet?.add(value?.id)
              staffList?.push(value)
            }
          }
        }
      }
    })
    // 过滤出单子的人员数据
    let fieldArr = []
    template?.forEach(it => {
      if (isArray(it)) {
        it?.forEach(item => {
          if (
            item?.dataType?.entity === 'organization.Staff' ||
            item?.dataType?.elemType?.entity === 'organization.Staff'
          ) {
            fieldArr?.push(item?.field)
          }
        })
      } else {
        const item = it
        if (
          item?.dataType?.entity === 'organization.Staff' ||
          item?.dataType?.elemType?.entity === 'organization.Staff'
        ) {
          fieldArr?.push(item?.field)
        }
      }
    })
    fieldArr?.forEach(async it => {
      const value = tag?.[it]
      const isConfigField = it === associationSpecification?.fields?.[0]?.fieldName
      if (value) {
        if (isArray(value)) {
          let dataArr = value
          if (isString(value[0])) {
            // 如果是 id 需要去把数据拉回来
            const res = await app.invokeService('@common:get:staff:by:ids', { ids: value })
            dataArr = res?.items || []
          }
          dataArr?.forEach(it => {
            if (isConfigField && !rolesMap[it?.id]) {
              rolesMap[it?.id] = componentLabel
            }
            selectSet?.add(it?.id)
            if (!staffSet?.has(it?.id)) {
              staffSet?.add(it?.id)
              staffList?.push(it)
            }
          })
          // 这个地方异步需要自己再 setState
          this.setState({
            rolesMap,
            staffList,
            selectSet,
            staffSet
          })
        } else {
          if (isConfigField && !rolesMap[value?.id]) {
            rolesMap[value?.id] = componentLabel
          }
          selectSet?.add(value?.id)
          if (!staffSet?.has(value?.id)) {
            staffSet?.add(value?.id)
            staffList?.push(value)
          }
        }
      }
    })
    this.setState({
      rolesMap,
      staffList,
      selectSet,
      staffSet
    })
  }
  handleAddStaff = () => {
    const { staffSet = [], staffList, selectSet } = this.state
    app.emit('@vendor:select-multiple-user', {
      checkedKeys: Array?.from(staffSet),
      callback: staffs => {
        staffs?.forEach(it => {
          const id = it?.id
          if (!staffSet?.has(id)) {
            staffSet?.add(id)
            staffList?.push(it)
            selectSet?.add(id)
          }
        })
        this.setState({ staffSet, staffList, selectSet })
      }
    })
  }
  getFlowId = () => {
    const { flowId, tag } = this.props
    return tag?.tag?.linkRequisitionInfo?.id || tag?.tag?.expenseLink?.id || flowId
  }
  checkGroupRelation = () => {
    const { flowId, submitterId, userInfo, template, billSpecification } = this.props
    this.setState({ loading: true }, async () => {
      try {
        const assiciationGroupData = await associationGroupData.GET('')
        const associationSpecification = assiciationGroupData?.value?.specificationConfigs?.find(
          item =>
            item?.specificationId === billSpecification?.id ||
            item?.specificationId === billSpecification?.originalId ||
            item?.specificationId === billSpecification?.originalId?.id
        )
        let componentLabel = template
          ?.flat()
          ?.find(item => item?.field === associationSpecification?.fields?.[0]?.fieldName)?.cnLabel
        const flowIdNew = this.getFlowId()
        const res = await groupRelation.GET('', { flowId: flowIdNew })
        if (res?.value) {
          this.setState({
            hasGroupRelation: true,
            groupRelationData: res?.value,
            componentLabel,
            loading: false,
            associationSpecification
          })
          window?.TRACK?.('Ungroup_pv', {
            actionName: '解除群关联卡片的pv'
          })
          return
        } else {
          this.setState(
            { hasGroupRelation: false, componentLabel, loading: false, associationSpecification },
            this.getBillAndPlanStaff
          )
          window?.TRACK?.('Create_group_pv', {
            actionName: '单据查看态下、立即创建群的页面pv'
          })
        }
      } catch (error) {
        showMessage.error(error?.message || error)
        this.setState({ loading: false })
      }
    })
  }

  goToDingTalk = async () => {
    this.setState({ loading: true }, async () => {
      const uid = this?.props?.userInfo?.staff?.id
      const { flowId } = this.props
      try {
        const flowIdNew = this.getFlowId()
        const result = await joinDtGroup.POST('/$flowId', { flowId: flowIdNew, userIds: [uid] })
        this.setState({ loading: false })
        if (result?.id) {
          const reg = /([^?]+)$/
          const searchParams = result?.id?.match(reg)?.[1]
          const searchParamsObj = qs.parse(searchParams)
          this.goToDingTalkChat(searchParamsObj?.chatId, searchParamsObj?.corpId)
        } else {
          showModal.confirm({
            title: <div className={styles.title}>{i18n.get('前往钉钉群失败')}</div>,
            content: (
              <div className={styles['modal-content']}>
                <p className={styles['tip-info']}>{i18n.get('请重试或者联系管理员。')}</p>
              </div>
            ),
            okText: i18n.get('重试'),
            onOk: () => {
              this.goToDingTalk()
            },
            cancelText: i18n.get('取消'),
            onCancel: () => {}
          })
        }
      } catch (error) {
        this.setState({ loading: false })
      }
    })
  }
  goToDingTalkChat = (chatId, corpId) => {
    const el = document.createElement('a')
    document.body.appendChild(el)
    el.href = `dingtalk://dingtalkclient/page/conversation?chatId=${chatId}&corpId=${corpId}` //url 是你得到的连接
    el.target = '_new' //指定在新窗口打开
    el.click()
    document.body.removeChild(el)
  }

  handleCreate = async () => {
    window?.TRACK?.('Create_group_click', {
      actionName: '立即创建钉钉群按钮的点击量'
    })
    this.setState({ loading: true }, async () => {
      const { flowId } = this.props
      const { selectSet } = this.state
      if (selectSet?.size < 2) {
        this.setState({ loading: false })
        return showMessage.info(i18n.get('选择的群成员必须大于等于两个人'))
      }
      try {
        const flowIdNew = this.getFlowId()
        const result = await createGroupRelation.POST('/$flowId', {
          flowId: flowIdNew,
          userIds: Array?.from(selectSet)
        })
        this.setState({ loading: false })
        return new Promise((resolve, reject) => {
          if (result?.id) {
            const reg = /([^?]+)$/
            const searchParams = result?.id?.match(reg)?.[1]
            const searchParamsObj = qs.parse(searchParams)
            this.setState({
              hasGroupRelation: true,
              groupRelationData: { groupTitle: searchParamsObj?.groupTitle }
            })
            window?.TRACK?.('Successfully_created _group_pv', {
              actionName: '创建群成功卡片的pv'
            })
            showModal.confirm({
              title: <div className={styles.title}>{i18n.get('创建群成功')}</div>,
              content: (
                <div className={styles['modal-content']}>
                  <p className={styles['tip-info']}>{i18n.get('钉钉关联群已成功创建！')}</p>
                  <p className={styles['tip-info']}>{i18n.get('单据动态将实时同步至群中。')}</p>
                </div>
              ),
              okText: i18n.get('打开钉钉群'),
              onOk: () => {
                this.goToDingTalkChat(searchParamsObj?.chatId, searchParamsObj?.corpId)
                window?.TRACK?.('Open_Dingding_Group_click', {
                  actionName: '打开钉钉群按钮的点击量'
                })
                return resolve()
              },
              cancelText: i18n.get('知道了'),
              onCancel: () => {
                return reject('cancel')
              }
            })
          } else {
            showModal.confirm({
              title: <div className={styles.title}>{i18n.get('创建群失败')}</div>,
              content: (
                <div className={styles['modal-content']}>
                  <p className={styles['tip-info']}>{i18n.get('创建钉钉关联群失败！')}</p>
                  <p className={styles['tip-info']}>{i18n.get('请重试或者联系管理员。')}</p>
                </div>
              ),
              okText: i18n.get('重试'),
              onOk: () => {
                this.handleCreate()
                return resolve()
              },
              cancelText: i18n.get('取消'),
              onCancel: () => {
                return reject('cancel')
              }
            })
          }
        })
      } catch (error) {
        this.setState({ loading: false })
      }
    })
  }

  handleUnBind = async () => {
    const { flowId } = this.props

    window?.TRACK?.('Ungroup_click', {
      actionName: '解除群关联按钮的点击量'
    })
    this.setState({ loadingUnBind: true }, () => {
      return new Promise((resolve, reject) => {
        showModal.confirm({
          title: <div className={styles.title}>{i18n.get('解除群关联')}</div>,
          content: (
            <div className={styles['modal-content']}>
              <p className={styles['tip-info']}>{i18n.get('您确定要解除群关联吗？')}</p>
              <p className={styles['tip-info']}>{i18n.get('解除后可再次创建新群。')}</p>
            </div>
          ),
          okText: i18n.get('确定解除'),
          onOk: () => {
            window?.TRACK?.('Ungroup_confirm_click', {
              actionName: '解除群关联后确定解除按钮的点击量'
            })
            const flowIdNew = this.getFlowId()
            groupRelation
              .DELETE('', { flowId: flowIdNew })
              .then(res => {
                this.setState({ loadingUnBind: false })
                if (res?.value) {
                  this.setState({ hasGroupRelation: false, groupRelationData: {} })
                  showMessage.success(i18n.get('群关联解除成功'))
                } else {
                  showMessage.error(i18n.get('群关联解除失败'))
                }
                return resolve()
              })
              .catch(err => {
                this.setState({ loadingUnBind: false })
              })
          },
          cancelText: i18n.get('取消'),
          onCancel: () => {
            this.setState({ loadingUnBind: false })
            return reject('cancel')
          }
        })
      })
    })
  }

  render() {
    const {
      hasGroupRelation,
      groupRelationData,
      loading,
      loadingUnBind,
      rolesMap,
      staffList,
      selectSet,
      componentLabel
    } = this.state

    return (
      <div className={`ant-col-14 ${styles['association-wrapper']}`}>
        {hasGroupRelation ? (
          <>
            <div className="top_view">
              <div className="bind-title">{i18n.get('已关联钉钉群：')}</div>
              <div className="group-name">
                <img src={icon} alt="" />
                {groupRelationData?.groupTitle}
              </div>
            </div>
            <div className="bottom_view">
              <Button loading={loading} disabled={loadingUnBind} className="btn-go-dd" onClick={this.goToDingTalk}>
                {i18n.get('前往钉钉群')}
              </Button>
              <Button loading={loadingUnBind} disabled={loading} className="btn-unlink" onClick={this.handleUnBind}>
                {i18n.get('解除群关联')}
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="top_view">
              <div className="">
                {i18n.get('可根据{__k0}和审批人创建钉钉群', { __k0: componentLabel || i18n.get('相关人') })}
              </div>
              <div className="sub-title">{i18n.get('单据动态将实时同步至群内')}</div>
            </div>
            <div className="item add" onClick={this.handleAddStaff}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="11.5" stroke="currentColor" />
                <rect x="7" y="11" width="10" height="2" fill="currentColor" />
                <rect x="11" y="17" width="10" height="2" transform="rotate(-90 11 17)" fill="currentColor" />
              </svg>
              <div className="add-staff">{i18n.get('添加人员')}</div>
            </div>
            <div className="content-view">
              {staffList?.map(it => {
                return (
                  <div className="item data" key={it?.id}>
                    <div className="left">
                      <Checkbox
                        checked={selectSet?.has(it?.id)}
                        disabled={it?.id === this?.props?.userInfo?.staff?.id}
                        onChange={() => {
                          if (selectSet?.has(it?.id)) {
                            selectSet?.delete(it?.id)
                          } else {
                            selectSet?.add(it?.id)
                          }
                          this.setState({ selectSet })
                        }}
                      />
                      <div className="name">{it?.name}</div>
                    </div>
                    <div className="right">{rolesMap?.[it?.id] || i18n.get('其他')}</div>
                  </div>
                )
              })}
            </div>
            <div className="bottom_view">
              <Button loading={loading} type="primary" onClick={this.handleCreate}>
                {i18n.get('立即创建')}
              </Button>
            </div>
          </>
        )}
      </div>
    )
  }
}
