import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { IComponentDef, IField } from '@ekuaibao/template/src/Cellar'
import { app as api } from '@ekuaibao/whispered'
import styles from './ReconcileResultForm.module.less'
interface IProps {
  field: IField
  value: {
    subTaskId: string
    subTaskName: string
    sourceName: string
    // 对账源总条数
    sourceTotalCount: number
    // 对账源匹配总条数
    sourceMatchCount: number
    // 匹配源名称
    refName: string
    // 匹配源总条数
    refTotalCount: number
    // 匹配源匹配总条数
    refMatchCount: number
    operatorId: string
  }
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'reconcileResultForm'
  },
  wrapper: wrapper(false, {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 }
  })
} as IComponentDef)
export default class ReconcileResultForm extends Component<IProps> {
  handleOpenModal = reconcileResultForm => {
    const { code } = reconcileResultForm
    api.open('@business-to-business:ReconciliationTaskDetailDrawer', {
      inDrawer: true,
      id: code
    })
  }
  render() {
    const reconcileResultForm = this.props.value
    if (!reconcileResultForm) {
      return <div>{i18n.get('无')}</div>
    }
    const { sourceTotalCount = 0, sourceMatchCount = 0 } = reconcileResultForm
    return (
      <div className={styles.reconcileResultFormWrapper} onClick={() => this.handleOpenModal(reconcileResultForm)}>
        <div className={styles.left}>
          <span className={styles.number}>{sourceTotalCount || 0}</span>
          <span className={styles.val}>{i18n.get('对账总数')}</span>
        </div>
        <div className={styles.line} />
        <div className={styles.right}>
          <span className={styles.number}>{sourceMatchCount || 0}</span>
          <span className={styles.val}>{i18n.get('已匹配')}</span>
        </div>
      </div>
    )
  }
}
