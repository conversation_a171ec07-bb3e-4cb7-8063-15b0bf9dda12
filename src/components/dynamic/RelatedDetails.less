@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/web-theme-variables/styles/colors';

.relatelist-wrapper {
  .title {
    color: @color-black-1;
    .font-weight-3;
    .font-size-3;
    margin-bottom: 12px;
  }
  .relatelist {
    .list-wrapper {
      margin-bottom: @icon-size-1;
      padding: 0 24px 0 0;
      .item-header {
        margin-bottom: 16px;
        .apply-header {
          display: flex;
          justify-content: space-between;
          padding-bottom: @space-5;
          border-bottom: 1px dashed rgba(29, 43, 61, 0.15);
          position: relative;
          .header-left {
            display: flex;
            align-items: center;
            .left-description {
              .left-title {
                .font-size-3;
                .font-weight-3;
                color: @color-black-1;
              }
              .left-content {
                display: flex;
                align-items: center;
                .font-size-2;
                .font-weight-2;
                color: @color-black-3;
                span {
                  display: inline-block;
                }
                .line {
                  width: 1px;
                  height: 14px;
                  background: rgba(29, 43, 61, 0.15);
                  margin: 0 @space-4;
                }
              }
            }
          }
          .header-right {
            .apply-all-money {
              .font-size-2;
              .font-weight-2;
              color: @color-black-3;
            }
            .right-money {
              .font-size-3;
              font-weight: bold;
              color: @color-black-1;
              display: flex;
              justify-content: flex-end;
            }
            .right-check-wrap {
              .check-apply-money,
              .right-check-money {
                .font-size-2;
                .font-weight-2;
                text-align: right;
                cursor: pointer;
              }
              .check-apply-money {
                color: @color-black-3;
              }
              .right-check-money {
                display: none;
                color: @color-inform;
              }
            }
            .right-check-wrap:hover {
              .check-apply-money {
                display: none;
              }
              .right-check-money {
                display: block;
              }
            }
          }
          .delete-header {
            display: none;
            position: absolute;
            right: 0;
            top: 0;
            width: @space-7;
            height: @space-7;
            line-height: 28px;
            text-align: center;
            background: @color-error;
            cursor: pointer;
            .icon-delete-header {
              font-size: @space-6;
              color: @color-white-1;
            }
          }
        }
      }
      .item-header:hover {
        .delete-header {
          display: block;
        }
      }
      .list-item:hover {
        background: rgba(29, 43, 61, 0.03);
      }
    }
    .btn-addrelate {
      display: inline;
      color: @color-brand;
      border-radius: @space-2;
      border: 1px solid @color-brand;
      padding: @space-3 @space-5;
      cursor: pointer;
      .font-size-2;
      .font-weight-3;
    }
  }
}
