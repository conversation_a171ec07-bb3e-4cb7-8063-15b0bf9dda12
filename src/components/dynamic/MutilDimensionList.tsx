import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import RefView from '../../elements/puppet/Ref'
import { get } from 'lodash'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { isObject, isArray } from '@ekuaibao/helpers'
import {
  constantValue,
  lastSelectValue,
  formatTreeToArray,
  isAllowCancelDependenceClearValue
} from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import styles from './MutilDimensionList.module.less'
import { getRecordLink } from './helpers/getRecordLink'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { getBoolVariation } from '../../lib/featbit'
import MutilDimensionListV2 from './CustomDimension/MutilDimensionListV2Refactored'
import { enableRecordOptimization } from '../../lib/featbit'
import { mutilDimensionListHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'

// @ts-ignore
@EnhanceField({
  descriptor: {
    test({ type = '' }) {
      return type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select')
    }
  },
  initialValue(props) {
    const { field = {}, value, lastChoice } = props
    if (!value) {
      const constValue = constantValue(field)
      if (constValue) {
        return [constValue]
      }
      const lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) {
        return lastVal.split(',')
      }
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class MutilDimensionList extends React.Component<any, any> {
  isFlow = () => {
    const { businessType } = this.props
    return ['FLOW', 'DETAILS'].includes(businessType)
  }

  render() {
    if (this.isFlow() && enableRecordOptimization()) {
      return <MutilDimensionListV2 {...this.props} />
    }
    return <MutilDimensionListOld {...this.props} />
  }
}


@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo
  }),
  {
    getRecordLink: getRecordLink
  }
)
class MutilDimensionListOld extends React.Component<any, any> {
  initState = () => {
    const { field } = this.props
    const { dependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    return {
      dimensionList: [],
      dependenceList: [],
      isDependence,
      useDependenceData: false,
      dependenceListOnLoading: false,
      dependenceMap
    }
  }

  state: any = this.initState()
  hasEmitHandleDependenceChange: boolean = false

  componentDidMount() {
    const { onComponentLoadFinished, field } = this.props
    if (onComponentLoadFinished && !this.hasEmitHandleDependenceChange) {
      onComponentLoadFinished(field)
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
  }
  
  currentIdx = null
  handleDependenceChange = async ({ key, id, dependenceFeeType = false },options = {}) => {
    this.hasEmitHandleDependenceChange = true
    if (useNewAutomaticAssignment()) {
      const result = await mutilDimensionListHandleDenpence({ key, id, dependenceFeeType }, options, this.state, this.props)
      result && this.setState(result)
      return
    }
    const { getRecordLink, field, value = [], bus, flowId, isModify, billState, form, detailId, isDetail} = this.props
    const { dependence, dataType, selectRange,dependenceCondition, allowCancelDependence } = field

    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = (billState !== 'new' && Boolean(!isDetail) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据
    if (dependence && dependence?.length) {
      const isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (Boolean(isNeedFetch)) {
        const { dependenceMap } = this.state
        const list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          if (dependence[i]?.dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        this.currentIdx = id // 标明promise的执行顺序
        this.setState({ dependenceListOnLoading: true }, () => {
          getRecordLink({
            recordSearch: list,
            entity: get(dataType, 'elemType.entity'),
            defaultValue: isObject(value) ? value.id : value,
            range: selectRange,
            dependenceFeeType,
            flowId: isModify ? flowId : '',
            dependenceCondition
          }).then(action => {
            const { matchDefaultValue, items, leafItems } = action.payload
            const spreadArray = formatTreeToArray(items)
            const itemMap = spreadArray?.map(v => v?.id)
            let newValue = undefined

            if (this.currentIdx === id) {
              if(isInitLoad){
                const value = form.getFieldsValue()
                // 初始化进来 当前value 值赋值
                newValue = value[field?.field] ?? undefined
                this.onChange(newValue)
              }else if (!matchDefaultValue) {
                bus.setValidateLevel(1)
                if (leafItems) {
                  newValue = [leafItems.id]
                  this.onChange(newValue)
                } else if (value.length) {
                  newValue = value.filter(v => itemMap.includes(v))
                  if(isAllowCancelDependenceClearValue(allowCancelDependence, newValue?.length, value)){
                    this.onChange(newValue)
                  }
                } else if(isAllowCancelDependenceClearValue(allowCancelDependence, true, value)){
                  this.onChange(undefined)
                }
              }
              // TODO: 档案关系埋点
              const oldValue = value
              let { billData, billSpecification, feeType, dataSource } = this.props
              let newBillData = billData
              let message = '单据上的档案关系赋值'
              if (feeType) {
                message = '明细上的档案关系赋值'
              } else {
                newBillData = dataSource
              }
              api?.logger?.info(message, {
                specificationId: billSpecification?.id,
                specificationName: billSpecification?.name,
                flowId: newBillData?.flowId || newBillData?.id,
                code: newBillData?.code || newBillData?.form?.code,
                sceneName: '档案关系',
                feeTypeId: feeType?.id,
                feeTypeName: feeType?.name,
                field: field?.field,
                dependField: key,
                oldValue,
                newValue
              })
              this.setState({
                dependenceListOnLoading: false,
                dependenceList: items
              })
            }
          })
        })
      }
    }
  }


  handleDimensionList = dimensionList => {
    this.setState({ dimensionList })
  }
  onChange = (value: any[]) => {
    const { onChange } = this.props
    onChange(value)
  }
  render() {
    const { dimensionList, isDependence, dependenceList, dependenceListOnLoading } = this.state
    const { flowId, field, value = [], submitterId, form } = this.props
    let { useEUI } = this.props
    if (getBoolVariation('use_old_dimension_select')) {
      useEUI = false
    }
    const { multiple, optional, selectRange, hideCode } = field
    let placeholder = getPlaceholder(field)
    const disabled = isDisable(this.props)
    const data = {
      multiple,
      onChange: this.onChange,
      id: isArray(value) ? value.map((el: any) => (isObject(el) ? el.id : el)) : value?.id,
      placeholder: placeholder,
      disabled: disabled,
      optional: optional,
      hideCode
    }
    const param = { name: get(field, 'dataType.elemType.entity'), flowId }
    return (
      <div className={styles['dimensionList']}>
        <RefView
          useTreeSelectRC={!useEUI}
          useEUI={useEUI}
          submitterId={submitterId?.id}
          onlyLeafCanBeSelected={'all' !== selectRange}
          onDimensionChange={this.handleDimensionList}
          data={data}
          allowCancelDependence={field?.allowCancelDependence}
          param={param}
          dimensionList={dimensionList}
          dependenceList={dependenceList}
          isDependence={isDependence}
          dependenceListOnLoading={dependenceListOnLoading}
          form={form}
          field={field}
        />
      </div>
    )
  }
}
