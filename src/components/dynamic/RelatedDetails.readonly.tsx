import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import './RelatedDetails.less'
import FeeDetailViewItem from '../../elements/feeDetailViewList/feeDetailViewItem'
import { app as api } from '@ekuaibao/whispered'
import { reduce, get } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
import RelateTitleView from '../../elements/feeDetailViewList/relateTitleView'

interface Props {
  value: any
  onChange: Function
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'linkDetailEntities'
  },
  wrapper: wrapper()
})
export default class RelatedDetailsReadOnly extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
  }

  componentDidMount() {
    this.fnGetValue()
  }

  componentDidUpdate() {
    const { value } = this.props
    if (value.length && !value[0].title) {
      this.fnGetValue()
    }
  }

  fnGetValue = () => {
    const { value, onChange } = this.props
    if (!value) {
      return
    }
    const ids = value.map((line: any) => line.flowId)
    api.invokeService('@bills:get:ExpenseLink:list', ids).then((result: any) => {
      const flows: any = {}
      let flowList = []
      result?.items?.forEach((item: any) => {
        if (item?.details?.length) {
          flowList = flowList.concat(item.details)
        }
      })
      flowList.forEach(item => (flows[item.id] = item))
      const cValue = value.map((line: any) => {
        // const flowObj: any = flows[line.flowId]
        const flowObj: any = flows[line.dataList[0].linkId]
        const {
          form: { code, title, requisitionMoney, specificationId, submitterId }
        } = flowObj
        return { ...line, title, code, money: requisitionMoney, ownerId: submitterId, specificationId }
      })
      cValue.map(line => this.fnAddUseMoney(line))
      onChange && onChange(cValue)
    })
  }

  fnAddUseMoney = (data: any) => {
    const moneys = data.dataList.map((item: any) => item.modifyValue)
    data.useTotalMoney = reduce(
      moneys,
      function(sum, n) {
        return new MoneyMath(sum).add(n).value
      },
      0
    )
  }

  render() {
    const { value } = this.props
    return (
      <div className="relatelist-wrapper">
        <div className="title">{i18n.get('关联明细')}</div>
        <div className="relatelist">
          <div className="list-wrapper">
            {!!value.length &&
              value.map((line, index) => {
                const { dataList = [] } = line
                const requisitionConfig = get(line, 'specificationId.configs', []).find(
                  (line: any) => line.ability === 'requisition'
                )
                const isManual = get(requisitionConfig, 'applyContentRule', '') === 'manual'
                return (
                  !!dataList.length && (
                    <div key={index} className="item-header">
                      <RelateTitleView item={line} isShowDelete={false} />
                      {!isManual &&
                        dataList.map((item, index) => {
                          return (
                            <div className="list-item" key={index}>
                              <FeeDetailViewItem data={item} canDelete={true} readOnly={true} />
                            </div>
                          )
                        })}
                    </div>
                  )
                )
              })}
          </div>
        </div>
      </div>
    )
  }
}
