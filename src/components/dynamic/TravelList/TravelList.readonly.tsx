import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './TravelList.module.less'
import { wrapper } from '../../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import TravelItem from '../../../elements/new-travel-plan-item'
import TravelClosedLoop from "../TravelClosedLoop"
import { getInitialTemplate, getTmcStatus } from './travel-util'
import { Resource, Fetch } from '@ekuaibao/fetch'
import { getFlowId } from '../../../plugins/bills/util/intent'
const getCustomCondition = new Resource('/api/v1/flow/customCondition')
const TYPE_MAP = {
  用车: 'TAXI',
  飞机: 'FLIGHT',
  火车: 'TRAIN',
  酒店: 'HOTEL',
  餐饮: 'FOOD',
  通用: 'COMMON'
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'travel'
  },
  wrapper: wrapper(true)
})
@EnhanceConnect(state => ({
  powersList: state['@common'].powers.powersList || []
}))
export default class TravelList extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      templateList: [],
      config: {},
      loading: true,
      limitOrder: false
    }
  }
  componentWillMount = async () => {
    const { field, billSpecification, flowId } = this.props
    const limitTravelTypeIds = field?.limitTravelTypeIds || []
    const initialTemplate = await getInitialTemplate(limitTravelTypeIds)
    //看单据模板里是否设置了限制订购条件，如果设置了，请求条件结果并保存
    const ability = billSpecification?.configs.find((line) => line?.ability === 'tripPlatform')
    let limitCondition
    if (!!ability?.isConditionChecked) {
      limitCondition = await getCustomCondition.GET('/$flowId', { flowId })
    }
    this.setState({
      templateList: initialTemplate,
      loading: false,
      limitOrder: limitCondition?.value
    })
  }

  handleOrderClick = async line => {
    const enType = TYPE_MAP[line.type]
    const sources = [`BUTTON_${enType}_ORDER`]
    const result = await Promise.all([
      api.invokeService('@custom-triptype:get:all:travel:intent', { sources }),
      api.invokeService('@auth-check:get:mall:auth:query', { type: sources[0] }),
    ])
    const items = result[0]?.items || []
    const authQuery = result[1] || ''
    const { flowId } = this.props
    if (flowId) {
      await getFlowId({}, items, flowId)
    }
    items.forEach((i: any) => {
      const type = /\?/.test(i.source) ? '&' : '?'
      if (!i.source.includes('token')) {
        i.source = i.source + `${type}${authQuery}`
      }
    })
    sources.forEach(type => {
      api.thirdResources.deleteByType(type)
    })
    api.thirdResources.add(items)
    api.request({
      type: `BUTTON_${enType}_ORDER` || 'BUTTON_TRAVEL_ORDER'
    }, { openHoseMallInEKB: true })
  }

  handleShowDetail = (line: any, index: number) => {
    const { value = [], submitterId } = this.props
    const { templateList } = this.state
    const travelerId = submitterId?.id
    api.open('@bills:AddTravelPlanModal', {
      templateList: templateList,
      editable: false,
      value: value?.length ? value[index] : {},
      isSingle: true,
      travelerId,
      submitterId,
      isDetail: true,
      readOnly: true,
    })
  }

  render() {
    const { value, state, isDetail } = this.props
    const { loading } = this.state

    if (loading) {
      return null
    }

    let prevItem = null
    const hoseState = getTmcStatus('120209') || getTmcStatus('170093') || getTmcStatus('170041')
    if (isDetail && !value?.length) {
      return '-'
    }
    return (
      <div className={styles.travelList}>
        <TravelClosedLoop value={value} />
        {value?.map((line, index) => {
          const isNewGroup = prevItem?.startDate !== line.startDate
          const showOrderButton =
            ['paid', 'archived'].includes(state) && ['飞机', '酒店', '火车'].includes(line.type) && hoseState
          const comp = (
            <TravelItem
              key={index}
              isNewGroup={isNewGroup}
              line={line}
              readOnly={true}
              onOrderClick={() => this.handleOrderClick(line)}
              showOrderButton={showOrderButton}
              onShowDetail={() => this.handleShowDetail(line, index)}
            />
          )
          prevItem = line
          return comp
        })}
        <div id="map-container"></div>
      </div>
    )
  }
}
