@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';
.mt {
  margin-top: @space-5;
}
.travelList {
  width: 100%;
  :global {
    .ant-btn {
      margin-right: @space-4;
      border: 1px solid #b9e6eb;
    }
    .trip-select-wrapper {
      margin-top: @space-5;
      .add-button-wrapper {
        display: flex;
        .add-button {
          width: 168px;
          height: 38px;
          color: @color-brand;
          border-radius: @radius-1;
          background-color: rgba(237, 249, 251, 1);
          line-height: 38px;
          text-align: center;
          cursor: pointer;
          font-size: 14px;
          margin-right: 8px;
        }
      }
    }
    .trips-list-wrapper {
      .trips-item {
        position: relative;
        border-radius: @radius-2;
        &:hover {
          cursor: pointer;
          background: rgba(29, 43, 61, 0.06) !important;
        }
        .icon-wrapper {
          &::before,
          &::after {
            position: absolute;
            left: 50%;
            display: block;
            content: '';
            width: 1px;
            height: 20px;
            background: rgba(29, 43, 61, 0.09);
          }
          &::before {
            top: -60%;
          }
          &::after {
            top: 100%;
          }
        }
        .delete-wrapper {
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          width: 24px;
          height: 24px;
          background: #f4526b;
          img {
            transform: translate(50%, -30%);
          }
        }
        &:first-child .icon-wrapper::before,
        &:last-child .icon-wrapper::after {
          display: none;
        }
      }
    }
  }
}

.data-link-red {
  .font-size-2;
  .font-weight-2;
  color: @color-error;
}
