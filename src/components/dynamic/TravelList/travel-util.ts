import { app as api } from '@ekuaibao/whispered'
import { sortBy, cloneDeep, uniqBy } from 'lodash'
import { message } from 'antd'
const TYPE_MAP = {
    "用车": "TAXI",
    "飞机": "FLIGHT",
    "火车": "TRAIN",
    "酒店": "HOTEL",
    "餐饮": "FOOD",
    "通用": "COMMON",
}


let INTENTS = []

const fnGetMallSensorParams = () => {
    const staff = api.getState()['@common'].userinfo?.staff
    const isTraveler = api.getState()['@common'].mallRole?.mallRole === '0'
    const param = {
        category: isTraveler ? i18n.get('散客') : i18n.get('企业'),
        staffId: staff?.userId,
        staffName: staff?.name,
        corName: staff?.corporation?.name,
        corpId: staff?.corporation?.id
    }
    return param
}


const sensorTrack = async type => {
    switch (type) {
        case 'FLIGHT':
            fnMallSensorTrack('pcPlanning_planeApplication_price_click')
            return
        case 'TRAIN':
            fnMallSensorTrack('pcPlanning_trainApplication_price_click')
            return
        case 'HOTEL':
            fnMallSensorTrack('pcPlanning_hotelApplication_price_click')
            return
        default:
            return
    }
}

const fnMallSensorTrack = trackName => {
    window.TRACK && window.TRACK(trackName, fnGetMallSensorParams())
}

const getIntent = async () => {
    const sources = [`BUTTON_HOTEL_MALL_PRICE_IN_TIME`, 'BUTTON_FLIGHT_MALL_PRICE_IN_TIME', 'BUTTON_TRAIN_MALL_PRICE_IN_TIME']
    const result = await api.invokeService('@custom-triptype:get:all:travel:intent', { sources })
    if (result?.items) {
        INTENTS = result.items.map(i => i)
    }
}

const getCitysAjax = trip => {
    let cityajax = []
    let startCity = JSON.parse(trip.travelFromCity || {})
    if (startCity?.length > 0) {
        if (startCity[0]?.type == 'cityGroup') {
            cityajax.push(
                api.invokeService('@custom-triptype:get:getCityGroupId', { id: startCity[0].key, type: TYPE_MAP[trip.type] })
            )
        } else {
            cityajax.push(
                api.invokeService('@custom-triptype:get:getCityParentId', {
                    ids: startCity.map(i => i.key).join(','),
                    type: TYPE_MAP[trip.type]
                })
            )
        }
    }
    let endCity = ''
    let two = trip.travelToCity && trip.travelFromCity
    if (two) {
        endCity = JSON.parse(trip.travelToCity || {})
        if (endCity?.length > 0) {
            if (endCity[0]?.type == 'cityGroup') {
                cityajax.push(
                    api.invokeService('@custom-triptype:get:getCityGroupId', { id: endCity[0].key, type: trip.tripType })
                )
            } else {
                cityajax.push(
                    api.invokeService('@custom-triptype:get:getCityParentId', {
                        ids: endCity.map(i => i.key).join(','),
                        type: TYPE_MAP[trip.type]
                    })
                )
            }
        }
    }
    return { cityajax, startCity, endCity, two }
}

const getCitys = (city, tripType, data) => {
    let result = { citys: [] }
    if (city && city.length > 0) {
        if (city[0]?.type == 'cityGroup') {
            result.cityName = city[0].label
        }
        if (tripType != 'HOTEL' && tripType != 'TRAIN') {
            result.citys = uniqBy(data?.items || [], 'code').filter(i => i.haveFlight)
        } else {
            result.citys = uniqBy(data?.items || [], 'code')
        }
    }
    return result
}

export const getPrice = async (trip) => {
    const { type } = trip
    const enType = TYPE_MAP[type]
    const TK = `BUTTON_${enType}_MALL_PRICE_IN_TIME`
    let { cityajax, startCity, endCity, two } = getCitysAjax(trip)
    sensorTrack(enType)
    if (!INTENTS?.length) {
        await getIntent()
    }
    const result = await Promise.all([...cityajax, api.invokeService('@auth-check:get:mall:auth:query', { type: 'BUTTON_HOTEL_MALL_PRICE_IN_TIME' })])
    const authQuery = result[result.length - 1] || ''
    const data: any = {
        tripType: enType,
        isPc: true,
        startTime: trip.startDate,
        endTime: trip.endDate
    }
    let scity = getCitys(startCity, enType, result[0])
    data.startCity = scity
    let ecity: any = {}
    if (two) {
        ecity = getCitys(endCity, enType, result[1])
        data.endCity = ecity
    }
    if (scity.citys.length == 0) {
        message.error(i18n.get('出发城市没有') + i18n.get(enType == 'TRAIN' ? '火车站' : '机场'))
        return
    }
    if (two && ecity.citys.length == 0) {
        message.error(i18n.get('到达城市没有') + i18n.get(enType == 'TRAIN' ? '火车站' : '机场'))
        return
    }
    const items = []
    INTENTS?.forEach(i => {
        const item = cloneDeep(i)
        item.data = data
        let type = /\?/.test(item.source) ? '&' : '?'
        if (!item.source.includes('token')) {
            item.source = item.source + `${type}${authQuery}`
        }
        items.push(item)
    })
    api.thirdResources.deleteByType(TK)
    api.thirdResources.add(items)
    const res: any = await api.request({ type: TK })
    return res?.money
}


export const getTravelerId = async (bus) => {
    let travelerId = ''
    if (api.has('get:bills:value')) {
        const billsValue = await api.invoke('get:bills:value')
        const value = await bus.getFieldsValue()
        const { travelers } = value

        if (!travelers || travelers.length === 0) {
            travelerId = billsValue.values.submitterId.id
        } else if (travelers.length === 1) {
            travelerId = travelers[0].id
        }
    }
    return travelerId
}

export const sortList = tripsList => {
    const newDataList = sortBy(tripsList, item => {
        const { startDate } = item
        return startDate
    })
    return newDataList
}

export const getTmcStatus = (code) => {
    const powersList = api.getState('@common').powers.powersList || []
    const tmcStatus = powersList.find(i => i.powerCode == code)
    return tmcStatus?.state === 'using'
}




export const getInitialTemplate = async (limitTravelTypeIds) => {
    const cityGroup = await api.invokeService('@tpp-v2:get:travelManagementConfig', { type: 'cityGroup' })
    const templateObj = await api.invokeService('@tpp-v2:get:getTravelManagement')
    await api.invokeService('@tpp-v2:get:travelManagementConfig', { type: 'tripBlackList' })
    const sortList = ["通用", "餐饮", "用车", "火车", "酒店", "飞机"]
    const activeTemp = templateObj.items.filter(v => {
        if (limitTravelTypeIds.length) {
            return v.active && limitTravelTypeIds.includes(v.id)
        }
        return v.active
    }).sort((a, b) => {
        return sortList.findIndex(i => i === b.name) - sortList.findIndex(i => i === a.name)
    })
    const cityConfigMap = {}
    cityGroup?.value?.contextDetail?.forEach(item => {
        item.travelTypes.forEach(key => {
            cityConfigMap[key] = item
        })
    })
    activeTemp.forEach(v => {
        const enKey = TYPE_MAP[v.name]
        v.multiple = cityConfigMap[enKey]?.multiple || false
    })
    return activeTemp

}