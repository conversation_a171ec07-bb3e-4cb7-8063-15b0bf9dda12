@import '~@ekuaibao/eui-styles/less/token.less';


.credential-wrapper {
    width: 100%;

    :global {
        // .ant-form-item {
        //     margin-bottom: 0;
        // }

        .form-wrap .ant-form-item {
            margin-bottom: 0;
        }

        .credential-title {
            font-size: 14px;
            font-weight: 500;
            margin: 0;
            display: flex;
            align-items: center;
            color: var(--eui-text-title);
            >span{
                font-size: 20px;
                margin-right: 4px;
                color: var(--eui-text-title);
                font-weight: 500;
            }
        }

        .credential-title-required::before {
            display: inline-block;
            margin-right: 4px;
            content: "*";
            line-height: 1;
            font-size: 12px;
            color: #ff7c7c;
        }

        .credential-type {
            width: 100%;
            display: flex;
            justify-content: space-between;
            // align-items:center;

            .credential-longterm {
                width: 200px;
                padding: 4px 7px;
                height: 28px;
                background-color: #f7f7f7;
                border-radius: 4px;
            }
        }

        .credential-add-list {
            width: 100%;
            overflow: hidden;

            .credential-list-item{
                width: 100%;
                border: 1px solid var(--eui-line-divider-default);
                border-radius: 6px;
                margin-bottom: 12px;
            }
            .credential-list-header {
                width: 100%;
                height: 36px;
                border-radius: 6px 6px 0 0;
                background: var(--eui-bg-body-overlay);
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;

                >div:last-child{
                    >span{
                        font-size: 20px;
                        cursor: pointer;
                        color: var(--eui-icon-n2);
                        &:hover{
                            color: var(--eui-primary-pri-500);
                        }
                    }
                }
            }

            .credential-list-content{
                width: 100%;
                padding: 12px 12px 0;
                overflow: hidden;

                .credential-base-wrapper{
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    justify-content: space-between;
                    >div{
                        flex: 1;
                    }
                }
                .credential-inner-wrapper{
                    display: flex;
                    width: 100%;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    >div{
                        width: calc(50% - 6px);
                    }
                }
            }

            .credential-list-ml {
                margin-left: 35px;
            }
        }


        .credential-add {
            width: 100%;
            display: flex;
        }

    }
}
