/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 06/07/2017 13:09.
 **************************************************/
import React, { PureComponent } from 'react'
import styles from './StaffCredential.module.less'
import { app } from '@ekuaibao/whispered'
import { Select, Input, Form, DatePicker, Menu, Dropdown, Row, Col, Switch, Tooltip } from 'antd'
import { Button} from '@hose/eui'
import { cloneDeep, differenceBy } from 'lodash'
import moment from 'moment'
import { OutlinedGeneralCredentials, OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons'
const EKBIcon = app.require<any>('@elements/ekbIcon')

const Delete_icon = require('../../images/icon-delete.svg')
const { Item: FormItem, create } = Form;
const { Option } = Select

// 证件类型必填属性
const requiredProperty = {
    ID_CARD: ['number', 'birthDate', 'gender'], // 身份证
    PASSPORT: ['number', 'validDate', 'nationality', 'issuancePlace', 'lastEngName', 'firstEngName'], // 护照
    // HOUSEHOLD: ['number'], // 户口本
    // BIRTH_CERTIFICATE: ['number'], // 出生证明
    SERVICEMAN_CARD: ['number', 'validDate', 'lastEngName', 'firstEngName'], // 军人证
    HONG_KONG_AND_MACAO_GO: ['number', 'validDate', 'lastEngName', 'firstEngName'], // 港澳居民来往内地通行证
    TAIWAN_GO: ['number', 'validDate', 'lastEngName', 'firstEngName'], // 台湾居民来往内地通行证
    FOREIGNERS_LIVE: ['number', 'validDate', 'nationality', 'lastEngName', 'firstEngName'], // 外国人永久居留身份证
    HONG_KONG_MACAO_TAIWAN: ['number', 'validDate', 'nationality'], // 港澳台居民居住证
    MAINLAND_TO_TAIWAN: ['number', 'validDate', 'lastEngName', 'firstEngName'], // 大陆居民来往台湾通行证
    HONG_KONG_AND_MACAO_PASS: ['number', 'validDate', 'lastEngName', 'firstEngName'], // 港澳通行证
    OTHER: ['number', 'validDate'] //其他
}

// 户口本/出生证明-->其他
const credentialOrigins = [
    {
        type: 'ID_CARD',
        label: "中国大陆居民身份证",
        name: "中国大陆居民身份证",
    },
    {
        type: 'PASSPORT',
        label: "护照",
        name: "护照"
    },
    {
        type: 'SERVICEMAN_CARD',
        label: "军人证",
        name: "军人证"
    },
    {
        type: 'HONG_KONG_AND_MACAO_GO',
        label: "港澳居民来往内地通行证",
        name: "港澳居民来往内地通行证"
    },
    {
        type: 'TAIWAN_GO',
        label: "台湾居民来往内地通行证",
        name: "台湾居民来往内地通行证"
    },
    {
        type: 'FOREIGNERS_LIVE',
        label: "外国人永久居留身份证",
        name: "外国人永久居留身份证"
    },
    {
        type: 'HONG_KONG_MACAO_TAIWAN',
        label: "港澳台居民居住证",
        name: "港澳台居民居住证"
    },
    {
        type: 'MAINLAND_TO_TAIWAN',
        label: "大陆居民来往台湾通行证",
        name: "大陆居民来往台湾通行证"
    },
    {
        type: 'HONG_KONG_AND_MACAO_PASS',
        label: "港澳通行证",
        name: "港澳通行证"
    },
    {
        type: 'OTHER',
        label: "其他",
        name: "其他"
    }
]

interface IProps {
    form: any
    credentialList: any[]
    countryList: any[]
    optional?: boolean
    readOnly?: false
    onRef?: any
    maxModifyCount: any
}
interface Istate {
    selectedOptions: any
    credentialOptions: any[]
    selectedList: any[]
    IDcardCode: string | number
    showFormItem: boolean
    hasCdtList: boolean
    typeOptions: any[]
    typeInitValueRequired: boolean
    longTermDate: boolean
    forece: boolean
    showAllowClear: boolean
    // countryList: any[]
    credentialList: any[]
}

const initData = (credentialList) => {
    let data = []
    let setOptionsMap = {}
    let hasCdtList = false
    let typeInitValueRequired = false
    if (!credentialList || credentialList?.length <= 0) {
        data = [{}]
    } else {
        typeInitValueRequired = true
        hasCdtList = true

        data = credentialList.map(e => {
            const findLabel = credentialOrigins.find(v => e.type === v.type)
            setOptionsMap[e.type] = findLabel
            e.label = findLabel.label
            // 已经添加的证件不允许删除
            e.forbidEdit = true
            return e
        })
    }
    return { selectedList: data, hasCdtList, typeInitValueRequired, selectedOptions: setOptionsMap, }
}
class StaffCredentialView extends PureComponent<IProps, Istate> {

    constructor(props) {
        super(props)
        this.state = {
            selectedOptions: {},
            IDcardCode: null,
            showFormItem: false,
            hasCdtList: false, //  默认 没有选择过证件
            selectedList: [],
            typeOptions: [],
            // countryList: [],
            credentialOptions: credentialOrigins,
            typeInitValueRequired: false,
            longTermDate: false,
            forece: false,
            credentialList: props.credentialList,
            showAllowClear: false
        }
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        if (
          nextProps.credentialList !== prevState.credentialList
        ) {
            return {...initData(nextProps.credentialList), credentialList: nextProps.credentialList}
        }
        return null
    }

        get computedMenu() {
            const { credentialOptions, selectedList, selectedOptions } = this.state
            const addCredentialTypes = differenceBy(credentialOrigins, selectedList, 'type')
            console.log(addCredentialTypes, selectedOptions, selectedList)
            const addBtnDisabled = !(!!addCredentialTypes.length || !Object.keys(selectedOptions)?.length)
            const menu =
                <Menu onClick={(e) => this.handleAddCredential(e)}>
                    {
                        addCredentialTypes.map(e => (
                            <Menu.Item key={e.type}>
                                <span>{i18n.get(e.name)}</span>
                            </Menu.Item>
                        ))
                    }
                </Menu>
            return { menu, addCredentialTypes, addBtnDisabled }
        }

    get computedShowFormItem() {
        const { selectedOptions } = this.state
        const { credentialList } = this.props
        return credentialList?.length || selectedOptions
    }

    async componentDidMount() {
        this.props?.onRef?.(this)

        const data = this.initCredentialData()
    }

    componentWillUnmount(): void {
        window.removeEventListener('click', () => {

        })
    }

    initCredentialData = () => {
        const state = initData(this.props.credentialList)
        this.setState(state)
    }

    handleChangeBirthday = (date, dateString) => {

    }

    handleChangeSex = value => {
        console.log(value)
    }

    handleSelectAllType = (value, cardType, index, addCredentialTypes) => {
        const { setFieldsValue } = this.props.form
        const { selectedList, credentialOptions, typeInitValueRequired } = this.state
        const copySelectedList = cloneDeep(selectedList)
        const findThis = credentialOrigins.find(e => value === e.type)
        copySelectedList.splice(index, 1, findThis)
        const setTypeInitValueRequired = true
        const number = `${cardType}@number`
        const gender = `${cardType}@gender`
        const birthDate = `${cardType}@birthDate`
        const validDate = `${cardType}@validDate`
        const extended = `${cardType}@extended`
        const nationality = `${cardType}@nationality`
        const issuancePlace = `${cardType}@issuancePlace`
        const lastEngName = `${cardType}@lastEngName`
        const firstEngName = `${cardType}@firstEngName`
        setFieldsValue({
            [number]: undefined,
            [gender]: undefined,
            [birthDate]: undefined,
            [birthDate]: undefined,
            [validDate]: undefined,
            [extended]: false,
            [nationality]: undefined,
            [issuancePlace]: undefined,
            [lastEngName]: undefined,
            [firstEngName]: undefined,
        })

        const setOptionsMap = this.setSelectedOpstions(value, findThis)
        this.setState({
            selectedList: copySelectedList,
            selectedOptions: setOptionsMap,
            typeInitValueRequired: setTypeInitValueRequired
        })
    }

    setSelectedOpstions = (type, target) => {
        const { selectedOptions } = this.state
        selectedOptions[type] = target
        return selectedOptions
    }

    // 添加证件
    handleAddCredential = (item) => {
        const { setFieldsValue } = this.props.form
        const { selectedList, credentialOptions, typeInitValueRequired } = this.state
        const copySelectedList = cloneDeep(selectedList)
        const exitCredential = copySelectedList.find(e => e.type === item.key)
        const setTypeInitValueRequired = true
        if (!exitCredential) {
            const addItem = credentialOrigins.find(e => e.type === item.key)
            copySelectedList.push(addItem)
            const setOptionsMap = this.setSelectedOpstions(item.key, addItem)

            this.setState({ selectedList: copySelectedList, typeInitValueRequired: setTypeInitValueRequired }, () => {
                const type = `${item.key}@type`
                setFieldsValue({
                    [type]: item.key,
                    selectedOptions: setOptionsMap
                })
            })
        }
    }

    // 删除证件
    handleDelete = (cardType, index) => {
        const { selectedList, selectedOptions } = this.state
        const copySelectedList = cloneDeep(selectedList)
        if (copySelectedList?.[index]?.type === selectedOptions?.[cardType]?.type) {
            const optionsMap = { ...selectedOptions }
            delete optionsMap?.[cardType]
            this.setState({ selectedOptions: optionsMap })
        }
        copySelectedList.splice(index, 1)
        this.setState({ selectedList: copySelectedList })
    }

    //
    handleResetCerdential = (cardType?) => {
        const { setFieldsValue } = this.props.form
        const type = `${cardType}@type`

        if ('ID_CARD' === cardType) {
            setFieldsValue({
                [type]: undefined
            })
        }

        this.setState({ selectedList: [{}], selectedOptions: {}, typeInitValueRequired: false, showAllowClear: false })
    }

    checkBirthDate = (type) => {
        return type === 'ID_CARD' && new RegExp(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, "g")
    }

    // 身份证截取生日和性别
    getBirthdayByIdNO = (IdCode) => {
        let birthday = "";
        if (IdCode.length == 18) {
            birthday = IdCode.substr(6, 8);
            return birthday.replace(/(.{4})(.{2})/, "$1-$2-");
        } else if (IdCode.length == 15) {
            birthday = "19" + IdCode.substr(6, 6);
            return birthday.replace(/(.{4})(.{2})/, "$1-$2-");
        } else {
            return "";
        }
    }

    getSexByIdNO = (IdCode) => {
        if (IdCode.length == 18) {
            return IdCode.charAt(16) % 2 == 0 ? "FEMALE" : "MALE";
        } else if (IdCode.length == 15) {
            return IdCode.charAt(14) % 2 == 0 ? "FEMALE" : "MALE";
        } else {
            return "";
        }
    }

    handleBlurIdCode = (e) => {
        const exp = this.checkBirthDate('ID_CARD')
        const { setFieldsValue, getFieldValue } = this.props.form
        const { selectedOptions } = this.state
        const getIDcardCode = getFieldValue('ID_CARD@number')
        let birthday
        let sex

        if (getIDcardCode && selectedOptions?.['ID_CARD']?.type === 'ID_CARD') {
            birthday = this.getBirthdayByIdNO(getIDcardCode)
            sex = this.getSexByIdNO(getIDcardCode)
            exp.test(getIDcardCode) && setFieldsValue({
                'ID_CARD@birthDate': moment(birthday), 'ID_CARD@gender': sex
            })
        }
    }

    handleBlurLower2Upper = (cardType, name, value) => {
        const { setFieldsValue, getFieldValue } = this.props.form
        const key = `${cardType}@${name}`
        const getValue = getFieldValue(key)
        setFieldsValue({
            [key]: getValue?.toUpperCase()
        })
    }

    // 判断必填项及显示的证件信息项目
    fillFormRequiredAndName = (cardType, name) => {
        const { selectedOptions } = this.state
        const requiredList = requiredProperty?.[cardType] || []
        let required = requiredList.includes(name)
        const iptName = cardType + "@" + name
        return { name: iptName, required: required && selectedOptions?.[cardType], showCdtItem: required }
    }

    formatDate = (date, extended?, cardType?) => {
        const dateFormat = 'YYYY-MM-DD';
        const d = date ? moment(date).format(dateFormat) : undefined
        let endingValue = undefined
        if (cardType === 'validDate' && extended) {
            endingValue = 0
        }
        return d ? moment(d, dateFormat) : endingValue
    }

    handleChangeEffectiveDate = (date, dateString, cardType) => {
        const { setFieldsValue } = this.props.form
        const extended = `${cardType}@extended`
        setFieldsValue({
            [extended]: undefined
        })
    }

    hendleSelectedLongTerm = (e, cardType) => {
        const { setFieldsValue } = this.props.form
        const selected = e.target.checked
        const validDate = `${cardType}@validDate`
        const extended = `${cardType}@extended`
        if (selected) {
            setFieldsValue({
                [validDate]: undefined,
                [extended]: e.target.checked
            })
        }
    }

    getExtendedStatus = (cardType) => {
        const { setFieldsValue, getFieldValue } = this.props.form
        const key = `${cardType}@extended`
        const getValue = getFieldValue(key)
        return { extendedChecked: getValue }
    }

    handleShowAllowClear = (e, cardType) => {
        const { getFieldValue } = this.props.form
        const key = `${cardType}@type`
        const value = getFieldValue(key)
        if (value) {
            this.setState({ showAllowClear: true })
        }
    }

    handleHideAllowClear = (e) => {
        this.setState({ showAllowClear: false })
    }

    renderCdtBase = ({ cardType, credentialList, currentCrential, readOnly, addCredentialTypes, selectedOptions, index }) => {
        const { form: { getFieldDecorator }, maxModifyCount } = this.props
        let { typeOptions, selectedList, credentialOptions, typeInitValueRequired, showAllowClear } = this.state
        // 当剩下一个证件
        const onlyOneCdt = selectedList?.length === 1
        // 判断当前类型选项
        const notCurr = selectedList.filter(e => e.type !== cardType)
        typeOptions = differenceBy(credentialOrigins, notCurr, 'type')

        return (
            <div className='credential-type credential-base-wrapper'>
                <div className='credential-type-selecte' onMouseLeave={e => this.handleHideAllowClear(e)}>
                    <FormItem label={i18n.get("证件类型")} className='credential-type-selecte-idcard-title'>
                        {/* <span className='credential-title'>证件类型及证件号码:</span> */}
                        {
                            getFieldDecorator(this.fillFormRequiredAndName(cardType, 'type').name, {
                                initialValue: typeInitValueRequired ? currentCrential?.type : undefined,
                                rules: [{
                                    required: this.fillFormRequiredAndName(cardType, 'type').required,
                                    message: i18n.get('请选择证件类型')
                                }],
                            })(
                                <Select
                                    style={{ width: '100%' }}
                                    dropdownClassName='dropdownClassName'
                                    placeholder={i18n.get("请选择证件类型")}
                                    disabled={readOnly || currentCrential.forbidEdit}
                                    // allowClear={selectedList?.length === 1 ? true : false}
                                    // labelInValue
                                    onMouseEnter={e => this.handleShowAllowClear(e, cardType)}
                                    onSelect={(value) => this.handleSelectAllType(value, cardType, index, addCredentialTypes)}
                                // onDeselect={value => this.handleCancelSelect(value, cardType, index, addCredentialTypes)}
                                >
                                    {typeOptions.map((e, i) => (
                                        <Option title={i18n.get(e.label)} key={e.type} value={e.type}>
                                            {i18n.get(e.label)}
                                        </Option>
                                    ))}
                                </Select>
                            )
                        }
                        {!currentCrential.forbidEdit && showAllowClear && onlyOneCdt && <EKBIcon onClick={() => this.handleResetCerdential(cardType)} name='#EDico-shanchu' key={index} style={{ width: '16px', height: '16px', position: 'absolute', top: '0', right: '11px' }} />}
                    </FormItem>
                </div>
                    <FormItem label={i18n.get("证件号码")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'number').name, {
                            initialValue: currentCrential?.number,
                            rules: [
                                {
                                    required: this.fillFormRequiredAndName(cardType, 'number').required,
                                    message: i18n.get("请输入证件号码")
                                },
                                {
                                    max: 50,
                                    pattern: this.checkBirthDate(currentCrential?.type),
                                    message: `${this.checkBirthDate(currentCrential?.type) ? "证件号码格式不正确" : "最多输入50字符"}`
                                }
                            ],
                        })(
                            <Input disabled={readOnly || currentCrential.modifyCount >= maxModifyCount[currentCrential.type]} onBlur={this.handleBlurIdCode} placeholder={i18n.get("请输入证件号码")} />
                        )}
                    </FormItem>
            </div>
        )
    }

    renderCdtExtraChild = ({ cardType, credentialList, currentCrential, readOnly, countryList }) => {
        const { form: { getFieldDecorator }, maxModifyCount } = this.props
        const { extendedChecked } = this.getExtendedStatus(cardType)

        const forbidModify = currentCrential.modifyCount >= maxModifyCount[currentCrential.type]
        return (
            <div>
                <div className='credential-type credential-base-wrapper' >
                        {
                            this.fillFormRequiredAndName(cardType, 'validDate').showCdtItem &&
                            <FormItem label={i18n.get("证件有效期")} style={{ width: '100%' }}>
                                {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'validDate').name, {
                                    initialValue: this.formatDate(currentCrential.validDate, currentCrential?.extended, 'validDate'),
                                    rules: [{
                                        required: extendedChecked ? (extendedChecked !== undefined && !extendedChecked) : this.fillFormRequiredAndName(cardType, 'validDate').required,
                                        message: i18n.get('请输入证件有效期')
                                    }],
                                })(
                                    <DatePicker
                                        style={{ width: '100%' }}
                                        format="YYYY-MM-DD"
                                        disabled={readOnly || forbidModify}
                                        onChange={(date, dateString) => this.handleChangeEffectiveDate(date, dateString, cardType)} />
                                )}
                            </FormItem>
                        }
                        {
                            this.fillFormRequiredAndName(cardType, 'validDate').showCdtItem &&
                            <FormItem label={i18n.get("证件是否长期有效")}>
                                {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'extended').name, {
                                    initialValue: currentCrential.extended
                                })(
                                    <Switch
                                        disabled={readOnly || forbidModify}
                                        onChange={value => this.hendleSelectedLongTerm(value, cardType)}
                                    >
                                        {i18n.get('长期有效')}
                                    </Switch>
                                )}
                            </FormItem>
                        }
                </div>
                <div className="credential-inner-wrapper">
                {
                    this.fillFormRequiredAndName(cardType, 'birthDate').showCdtItem &&
                    <FormItem label={i18n.get("出生日期")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'birthDate').name, {
                            initialValue: this.formatDate(currentCrential.birthDate),
                            rules: [{ required: this.fillFormRequiredAndName(cardType, 'birthDate').required, message: i18n.get('请输入出生日期') }],
                        })(
                            <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" showToday={false} disabled={readOnly || forbidModify} onChange={this.handleChangeBirthday} />
                        )}
                    </FormItem>
                }
                {
                    this.fillFormRequiredAndName(cardType, 'gender').showCdtItem &&
                    <FormItem label={i18n.get("性别")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'gender').name, {
                            initialValue: currentCrential?.gender,
                            rules: [{
                                required: this.fillFormRequiredAndName(cardType, 'gender').required,
                                // pattern: new RegExp("[/^男$|^女&/]"),
                                message: i18n.get('请选择性别')
                            }],
                        })(
                            <Select disabled={readOnly || forbidModify} placeholder={i18n.get('请选择性别')}>
                                <Option value="MALE">男</Option>
                                <Option value="FEMALE">女</Option>
                            </Select>
                        )}
                    </FormItem>
                }

                {
                    this.fillFormRequiredAndName(cardType, 'nationality').showCdtItem &&
                    <FormItem label={i18n.get("国籍")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'nationality').name, {
                            initialValue: currentCrential?.nationality,
                            rules: [{
                                required: this.fillFormRequiredAndName(cardType, 'nationality').required,
                                message: i18n.get('请选择国籍')
                            }],
                        })(
                            <Select
                                style={{ width: '100%' }}
                                placeholder={i18n.get("请选择国籍")}
                                disabled={readOnly || forbidModify}
                            // labelInValue
                            >
                                {countryList.map(e => (
                                    <Option key={cardType + e.id} value={e.id}>{e.name}</Option>
                                ))}
                            </Select>
                        )}
                    </FormItem>
                }
                {
                    this.fillFormRequiredAndName(cardType, 'issuancePlace').showCdtItem &&
                    <FormItem label={i18n.get("签发地")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'issuancePlace').name, {
                            initialValue: currentCrential?.issuancePlace,
                            rules: [{
                                required: this.fillFormRequiredAndName(cardType, 'issuancePlace').required,
                                message: i18n.get('请选择签发地')
                            }],
                        })(
                            <Select
                                style={{ width: '100%' }}
                                placeholder={i18n.get("请选择签发地")}
                                disabled={readOnly || forbidModify}
                            // labelInValue
                            >
                                {countryList.map(e => (
                                    <Option key={cardType + e.id} value={e.id}>{e.name}</Option>
                                ))}
                            </Select>
                        )}
                    </FormItem>
                }
                {
                    this.fillFormRequiredAndName(cardType, 'lastEngName').showCdtItem &&
                    <FormItem label={i18n.get("英文姓(拼音)")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'lastEngName').name, {
                            initialValue: currentCrential?.lastEngName,
                            rules: [{
                                required: this.fillFormRequiredAndName(cardType, 'lastEngName').required,
                                message: i18n.get('请输入英文姓'),
                                pattern: new RegExp(/^[A-Za-z]+$/)
                            },
                            {
                                max: 50,
                                message: i18n.get('最多50字符'),
                            }
                            ],
                        })(
                            <Input disabled={readOnly || forbidModify} onBlur={(e) => this.handleBlurLower2Upper(cardType, 'lastEngName', e)} placeholder={i18n.get("请输入英文姓")} />
                        )}
                    </FormItem>
                }
                {
                    this.fillFormRequiredAndName(cardType, 'firstEngName').showCdtItem &&
                    <FormItem label={i18n.get("英文名(拼音)")}>
                        {getFieldDecorator(this.fillFormRequiredAndName(cardType, 'firstEngName').name, {
                            initialValue: currentCrential?.firstEngName,
                            rules: [{
                                required: this.fillFormRequiredAndName(cardType, 'firstEngName').required,
                                message: i18n.get('请输入英文名'),
                                pattern: new RegExp(/^[A-Za-z]+$/)
                            },
                            {
                                max: 50,
                                message: i18n.get('最多50字符'),
                            }],
                        })(
                            <Input disabled={readOnly || forbidModify} onBlur={(e) => this.handleBlurLower2Upper(cardType, 'firstEngName', e)} placeholder={i18n.get("请输入英文名")} />
                        )}
                    </FormItem>
                }
                </div>
            </div>
        )
    }

    renderCredentialDetail = ({ cardType, currentCrential, index, addCredentialTypes, typeOptions }: any) => {
        const { readOnly, countryList = [], credentialList = [], optional = false } = this.props;
        const { selectedOptions, selectedList } = this.state
        const credentialTitle = currentCrential.label || '证件一'

        return (
            <div className='credential-list-item' key={cardType || 'unSelected'}>
                <div className='credential-list-header'>

                {
                    <h2 className={`credential-title ${optional ? 'credential-title-required' : ''}`}>
                        <OutlinedGeneralCredentials />
                        {i18n.get(credentialTitle)}
                    </h2>
                }
                {((index !== 0 || selectedList?.length >= 2) && !currentCrential.forbidEdit) && (
                  <div onClick={() => this.handleDelete(cardType, index)}>
                      <OutlinedEditDeleteTrash />
                  </div>
                )}
                </div>
                <div className='credential-list-content'>
                    {this.renderCdtBase({ cardType, credentialList, currentCrential, readOnly, addCredentialTypes, selectedOptions, index })}
                    {this.renderCdtExtraChild({ cardType, credentialList, currentCrential, readOnly, countryList })}
                </div>
            </div >
        )
    }

    render() {
        const { readOnly } = this.props;
        const { selectedList, forece } = this.state
        const { menu, addCredentialTypes, addBtnDisabled } = this.computedMenu

        return (
            <>
                <div className={styles['credential-wrapper']}>

                    {/* {this.renderCredentialDetail({ showTitle: false, index: 3 })} */}
                    <div className='credential-add-list'>
                        {
                            selectedList.length > 0 && selectedList.map((e, i) => this.renderCredentialDetail({ cardType: e.type, currentCrential: e, index: i, addCredentialTypes }))
                        }
                    </div>

                    {/* 添加证件 */}
                    {
                        !readOnly && <div className="credential-add ">
                            <Dropdown disabled={addBtnDisabled} overlay={menu} trigger={['hover']} placement='topCenter'>
                                <Button size="small" category="text" theme="highlight" disabled={addBtnDisabled}>
                                    <OutlinedTipsAdd /> {i18n.get('添加证件')}
                                </Button>
                            </Dropdown>
                        </div>
                    }

                </div>
            </>
        )
    }
}

// const StaffCredential = create()(StaffCredentialView)
export default StaffCredentialView
