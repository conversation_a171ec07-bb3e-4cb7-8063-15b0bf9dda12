/**************************************************
 * Created by nanyuanting<PERSON> on 06/07/2017 13:09.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Select } from 'antd'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
@EnhanceField({
  descriptor: {
    type: 'select'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class SelectBox extends React.PureComponent {
  onChange = value => {
    let { onChange, field, bus, fromSupplier } = this.props
    onChange && onChange(value)
    if (fromSupplier && field?.name === 'settlementType') {
      bus.emit('update:supplier:value', value)
    }
    if (field?.name === 'tripTypeName') {
      bus.emit('update:tripTypeName:value', value)
    }
  }

  fnBuildItems() {
    let { tag, field = {} } = this.props
    let children = []
    tag = tag || field.tags || []
    tag.forEach(line => {
      let { value, label, disabled = false } = line
      children.push(
        <Select.Option disabled={disabled} key={value} value={value}>
          {label}
        </Select.Option>
      )
    })
    return children
  }

  render() {
    let { value, field, type, fromSupplier, isEdit } = this.props
    let children = this.fnBuildItems()
    let { placeholder, name, mode ,optional=true} = field
    let disabled = isDisable(this.props) || ((name === 'supplierId' || name === 'supplierType') && type === 'edit')
    return (
      <>
        <Select
          style={{ width: '100%' }}
          disabled={disabled}
          mode={mode}
          placeholder={placeholder}
          onChange={this.onChange}
          value={value}
          size="large"
          allowClear={ optional }
        >
          {children}
        </Select>
        {!isEdit && fromSupplier && /settlementType$/.test(name) && (
          <div style={{ color: 'rgba(250,150,42,1)' }}>{i18n.get('创建后不可修改')}</div>
        )}
      </>
    )
  }
}
