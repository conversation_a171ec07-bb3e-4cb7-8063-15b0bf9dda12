/**************************************************
 * Created by <PERSON><PERSON>xing<PERSON> on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import AttachmentWrapper from '../../elements/puppet/attachment'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { required } from '../validator/validator'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { isDisable } from '../utils/fnDisableComponent'
import { FetchAttachment } from '../utils/FetchAttachment'
import classNames from 'classnames'

const download = api.invokeServiceAsLazyValue('@bills:file:download')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')

@EnhanceField({
  descriptor: {
    type: 'attachments'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@FetchAttachment
export default class Attachment extends PureComponent {
  constructor(props) {
    super(props)

    this.state = {
      uploaderFileList: [],
    }
  }

  handleFinish = fileList => {
    let { onChange, bus } = this.props
    this.setState({ uploaderFileList: [] })
    onChange && onChange(fileList)
    if (bus.has('savebtn:state:change')) {
      bus.emit('savebtn:state:change', { disabled: false })
    }
  }

  handleStart = () => {
    const { bus } = this.props
    if (bus.has('savebtn:state:change')) {
      bus.emit('savebtn:state:change', { disabled: true })
    }
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleError = error => {
    const { bus } = this.props
    if (bus.has('savebtn:state:change')) {
      bus.emit('savebtn:state:change', { disabled: false })
    }
    console.log(error)
  }

  handleRemoveAttachment = line => {
    let { value, onChange } = this.props
    let fileList = fnFormatAttachment(value)
    let cloneList = fileList.slice(0)
    let imgIndex = cloneList.findIndex(v => v.fileId === line.fileId)
    cloneList.splice(imgIndex, 1)

    onChange && onChange(cloneList)
  }

  handleFileDownload = line => {
    download()(line)
  }

  handleFilePreview = line => {
    const { value } = this.props
    preview()({ value, line })
  }

  render() {
    let { value, field } = this.props
    let { uploaderFileList } = this.state
    let disable = isDisable(this.props)
    let style = disable ? { opacity: 0.4 } : null
    return (
      <div style={style} className={classNames('attachment', { 'attachment-disabled': disable })}>
        <AttachmentWrapper
          value={value}
          field={field}
          disable={disable}
          uploaderFileList={uploaderFileList}
          handleStart={this.handleStart}
          handleChange={this.handleChange}
          handleFinish={this.handleFinish}
          handleRemoveAttachment={this.handleRemoveAttachment}
          onFileDownload={this.handleFileDownload}
          onFilePreview={this.handleFilePreview}
          onError={this.handleError}
          canSelectDP={true}
          suffixesPath="BILL"
          suffixesFiled={this.props?.field?.name || this.props.filed?.type}
          fileMaxSize={field?.fileMaxSize}
        />
      </div>
    )
  }
}
