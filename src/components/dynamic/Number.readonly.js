/***z<PERSON> x<PERSON><PERSON> 17/07/2017*****/
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './Number.readonly.module.less'

@EnhanceField({
  descriptor: {
    type: 'number'
  },
  wrapper: wrapper(true)
})
export default class Number extends PureComponent {
  constructor(props) {
    super(props)
  }

  render() {
    let { value, field } = this.props
    if (value && value.hasOwnProperty('label')) {
      value = value.label
    }
    let { unit = '' } = field.dataType || {}

    const { field: fieldName, defaultValue = {}, unit: unitField = '' } = field
    if (unitField) {
      unit = unitField
    }

    return (
      <div className={styles['element-wrapper']}>
        <div className="money-wrapper">{value !== undefined ? <span>{value + ' ' + unit}</span> : i18n.get('无')}</div>
      </div>
    )
  }
}
