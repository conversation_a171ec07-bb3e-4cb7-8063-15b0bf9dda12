/***************************************************
 * Created by nanyuantingfeng on 2020/4/21 15:42. *
 ***************************************************/
import { Resource } from '@ekuaibao/fetch'
const recordLink = new Resource('/api/v1/record/recordLink/search')

const recordLinkV2 = new Resource('/api/flow/v2/referables')

function getStaffWithPageSize(data = {}) {
  let items = []
  let pages = 1
  let pageSize = 2999
  let params = {
    start: 0,
    count: pageSize,
    ...data
  }
  let getStaffs = () => {
    return recordLink.POST('', params).then(resp => {
      items = items.concat(resp.items)
      if (resp.count !== items.length) {
        params.start = pages * pageSize
        pages++
        return getStaffs()
      }
      return { items }
    })
  }
  return getStaffs()
}

export function getRecordLink(params) {
  // if (!params.dependenceId) {
  //   return {
  //     type: '@bills/GET_RECORDLINK',
  //     payload: Promise.resolve({ matchDefaultValue: false, leafItems: undefined, items: [] })
  //   }
  // }
  return {
    type: '@bills/GET_RECORDLINK',
    payload: params.entity === 'organization.Staff' ? getStaffWithPageSize(params) : recordLink.POST('', params)
  }
}

export function getRecordLinkV2(params) {
  return recordLinkV2
    .POST('/search', params)
    .then(resp => resp.value)
    .then(value => {
      const { items } = value
      return {
        ...value,
        items: items || []
      }
    })
}

export function getByIds(params) {
  return recordLinkV2.POST('/getByIds', params).then(resp => resp.items || [])
}
