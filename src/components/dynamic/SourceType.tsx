import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import { Select } from 'antd'
const { Option } = Select
import { wrapper } from '../layout/FormWrapper'
import styles from './SourceType.module.less'
import { app as api } from '@ekuaibao/whispered'

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'sourceType'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { optional } = field
    if ( !optional && !(value && value.source && value.type)) {
      return callback(i18n.get('对账来源为必填'))
    }
    return callback()
  },
  wrapper: wrapper()
})
export default class SourceType extends PureComponent<any, any> {

  constructor(props) {
    super(props)
  }

  handleSourceChange = val => {
    const { field, onChange, value = {} } = this.props
    onChange && onChange({ ...value, source: val })
  }

  handleTypeChange = val => {
    const { onChange, value = {}, bus } = this.props
    const newVal = { ...value, type: val }
    onChange && onChange(newVal)
    bus.emit('update:supplier:value', newVal)
  }

  render() {
    const { value = {}, field, isEdit } = this.props
    const { tags, editable } = field
    const selectItem = tags.find(el => el.value === value.source)
    const options = selectItem?.children || []
    return (
      <>
        <div className={styles.wrapper}>
          <Select
            className="select"
            disabled={!editable}
            value={value.source}
            placeholder={i18n.get("请选择对账来源")}
            onSelect={this.handleSourceChange}
          >
            {tags.map(v => {
              return <Option value={v.value}>{v.label}</Option>
            })}
          </Select>
          <Select
            className="select ml"
            disabled={!editable}
            value={value.type}
            placeholder={i18n.get("请选择对账来源")}
            onSelect={this.handleTypeChange}
          >
            {options.map(v => {
              return <Option value={v.value}>{v.label}</Option>
            })}
          </Select>
        </div>
        {!isEdit && <div style={{ color: 'rgba(250,150,42,1)' }}>{i18n.get('创建后不可修改')}</div>}
      </>
      
    )
  }
}
