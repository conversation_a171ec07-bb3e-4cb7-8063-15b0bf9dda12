/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 10/07/2017 15:41.
 **************************************************/
import styles from './Label.readonly.module.less'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { AutoLink } from '@ekuaibao/eui-isomorphic'
import { app as api } from '@ekuaibao/whispered'
import classNames from 'classnames'
import { isIE } from '../../lib/misc'
import { formatLinkText } from '../../elements/DataLinkTable/tableUtil'

@EnhanceField({
  descriptor: {
    test({ type }) {
      return !!~['specification', 'text', 'textarea', 'list'].indexOf(type)
    }
  },
  wrapper: wrapper(true)
})
export default class LabelReadonly extends PureComponent {
  handleLink = url => {
    api.emit('@vendor:open:link', url)
  }

  getNewValue = value => {
    const {
      field: { name, type }
    } = this.props
    if (!!~name.indexOf('travelPeople') && Array.isArray(value)) {
      return value.map((item, index) => (
        <div key={index}>
          <AutoLink value={item.name} onClick={this.handleLink} />
        </div>
      ))
    }
    if (type === 'text' || type === 'textarea') {
      const { domValue, isRedirectText } = formatLinkText(value)
      if (isRedirectText) {
        return <div className={classNames({ 'ml-8': isIE() })}>{domValue}</div>
      }
    }
    let reg = /\n/
    return (
      <div className={classNames({ 'ml-8': isIE() })}>
        {new String(value).split(reg).map((item, index) => (
          <div key={index}>
            <AutoLink value={item} onClick={this.handleLink} />
          </div>
        ))}
      </div>
    )
  }

  render() {
    let { value = i18n.get('无') } = this.props
    if (value && value.hasOwnProperty('label')) {
      value = value.label
    }
    let str = this.getNewValue(value)
    return <div className={styles.label_wrap}>{str}</div>
  }
}
