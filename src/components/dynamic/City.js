/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/5.
 */
import React, { PureComponent } from 'react'
import styles from './City.module.less'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { Select, Spin } from 'antd'
import { debounce } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { isDisable } from '../utils/fnDisableComponent'
import { Fetch } from '@ekuaibao/fetch'
import { filterCity } from '../utils/fnTripCityFilter'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const Option = Select.Option

@EnhanceField({
  descriptor: {
    type: 'city'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class City extends PureComponent {
  constructor(props) {
    super(props)
    this.lastFetchId = 0
    this.fetchCity = debounce(this.fetchCity, 800)
    this.state = {
      data: [],
      fetching: false,
      emptyText: ''
    }
  }

  fetchCity = value => {
    this.lastFetchId += 1
    const fetchId = this.lastFetchId
    const { tripTypeId } = this.props

    this.setState({ fetching: true })
    api
      .invokeService('@bills:search:city', value)
      .then(result => {
        if (fetchId !== this.lastFetchId) return // for fetch callback order
        let { items } = result
        let data = items.filter(filterCity(tripTypeId)).map(city => ({
          id: city.id,
          value: city.name,
          enName: city.enName,
          fullName: city.fullName
        }))
        let emptyText = !data.length ? i18n.get('没有查询到城市') : ''
        this.setState({ data, fetching: false, emptyText })
      })
      .catch(error => {
        this.setState({ fetching: false })
      })
  }

  fnParseValue2Show = value => {
    let { field } = this.props
    let { multiple } = field
    let result

    if (multiple) {
      try {
        result = value && JSON.parse(value)
      } catch (err) {}
    } else {
      try {
        let arr = value && JSON.parse(value)
        result = arr[0]
      } catch (err) {}
    }

    return result
  }

  fnParseValue2Save(value) {
    let { field } = this.props
    let { multiple } = field
    let result
    if (multiple) {
      try {
        if (!value.length) {
          result = undefined
        } else {
          result = JSON.stringify(
            value.map(v => ({
              key: v.key,
              label: typeof v.label === 'object' ? this.fnFormatCityLabel(v.label.key) : this.fnFormatCityLabel(v.label)
            }))
          )
        }
      } catch (err) {}
    } else {
      try {
        result = JSON.stringify([
          {
            key: value.key,
            label:
              typeof value.label === 'object'
                ? this.fnFormatCityLabel(value.label.key)
                : this.fnFormatCityLabel(value.label)
          }
        ])
      } catch (err) {}
    }
    return result
  }

  handleChange = value => {
    let { onChange, getExpenseStandardItemsLength, external } = this.props
    this.setState({
      data: [],
      fetching: false
    })

    let result = this.fnParseValue2Save(value)

    onChange && onChange(result)
    emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }

  fnFormatCityLabel(label) {
    return label.replace(/^中国,\s*/, '').replace(/,\s*/g, '/')
  }

  renderOptions(d) {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage

    if (lang === 'en-US') {
      return <Option key={d.id}>{d.enName}</Option>
    }

    return (
      <Option key={d.id}>
        <span key={d.fullName}>
          <span>{d.value}</span>
          <span className="ml-5 color-gray">{d.fullName}</span>
        </span>
      </Option>
    )
  }

  render() {
    let { field, value } = this.props
    let { multiple, placeholder, Component } = field
    let { fetching, data, emptyText } = this.state
    let value2show = this.fnParseValue2Show(value) || []
    let randomNumber = Math.ceil(Math.random() * 10000)
    let cityClassId = field.name + randomNumber || `ekb-city-${randomNumber}`
    let disable = isDisable(this.props)
    return (
      <div id={cityClassId} style={{ position: 'relative' }}>
        <Select
          className={styles['ekb-city']}
          mode={multiple ? 'multiple' : 'default'}
          showSearch={!multiple}
          labelInValue
          allowClear
          filterOption={false}
          value={value2show}
          placeholder={placeholder}
          notFoundContent={fetching ? <Spin size="small" /> : emptyText}
          onSearch={this.fetchCity}
          onChange={this.handleChange}
          style={{ width: '100%' }}
          disabled={disable}
          dropdownClassName={'dropDown_' + cityClassId}
          getPopupContainer={() => document.getElementById(cityClassId)}
        >
          {data.map((d, idx) => this.renderOptions(d))}
        </Select>
        {Component && value2show.length > 0 && <Component totalCount={value2show.length} />}
      </div>
    )
  }
}
