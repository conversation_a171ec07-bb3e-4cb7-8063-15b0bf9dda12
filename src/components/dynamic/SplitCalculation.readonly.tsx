import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import styles from './SplitCalculation.module.less'
import { Fetch } from '@ekuaibao/fetch'
import { Table, Button, Select } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import EmptyBody from '../../plugins/bills/elements/EmptyBody'
import Money from '../../elements/puppet/Money'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { formatSplitDetail } from '../utils/fnFormatSplitDetailTable'

const withLoader = api.require('@elements/data-grid-v2/withLoader')

const SplitTable = withLoader(() => import('../../elements/puppet/SplitTable'))

export type header = { field: string, name: string, show: boolean, type: string }
export type cellValue = { field: string, value: any[], type: any }
type State = { headers?: header[], result?: cellValue[], total?: number, totalAmount?: number, showDetail: boolean, splitGroupList: any[], selectedGroup: any }
// @ts-ignore
@EnhanceField({
    descriptor: {
        type: 'splitCalculation'
    }
})
export default class SplitCalculation extends React.Component<any, State> {

    constructor(props) {
        super(props)
        const { value } = props
        this.state = {
            splitGroupList: [],
            selectedGroup: value?.splitGroupId,
            showDetail: false,
        }
    }
    async componentWillMount() {
        const { field, value } = this.props
        const { selectedGroup } = this.state
        const splitRuleId = field?.splitRuleIds[0]
        const res = await Fetch.GET(`/api/form/v2/customSplitGroup/$${splitRuleId}`)
        const activeItems = res.items.filter(el => el.active)
        const selectedItem =  res.items.find(el => el.id === selectedGroup)
        if (selectedItem && !selectedItem.active) {
            this.setState({ splitGroupList: activeItems, selectedGroup: `${selectedItem.fieldName}(${i18n.get('已删除')})` })
        } else {
            this.setState({ splitGroupList: activeItems })
        }   
        
        if (value && value.splitResultId) {
            const splitResult = await Fetch.GET(`/api/form/v2/customSplitResult/$${value.splitResultId}`)
            if (splitResult.value) {
                this.setState({ ...splitResult.value.splitResult })
            }
        }
    }

    handelShowDetail = () => {
        this.setState({ showDetail: !this.state.showDetail })
    }

    renderTable = () => {
        const { headers, result, total } = this.state
        const { dataSource, columns, secondCols } = formatSplitDetail(headers, result, total)
        return (
            <div className="table-c">
                <SplitTable
                    rowKey="key"
                    bordered
                    pagination={false}
                    secondCols={secondCols}
                    dataSource={dataSource}
                    columns={columns}
                    scroll={{ x: true }}
                />
            </div>

        )
    }
    renderEmpty = () => {
        return <EmptyBody label={i18n.get('当前明细未命中补贴维度或数据为空')} />
    }
    render() {
        const { value, field, billSpecification } = this.props
        const { total, totalAmount, headers, showDetail, splitGroupList, selectedGroup } = this.state
        const selectedGroupItem = splitGroupList.find(el => el.id === selectedGroup)
        return (
            <div className={styles['split-detail']}>
                <div className="split-group-title">{i18n.get('费用拆分明细')}</div>
                <div className="desc">
                    {i18n.get('该明细根据个人费标规则按照「同行人」 自动拆分')}
                </div>
                <div className="split-group-value">
                    <span>{fnGetFieldLabel(field)}</span>
                    { selectedGroup && <span className="ml-8">{selectedGroupItem?.fieldName || selectedGroup}</span>}
                </div>
                <div className="detals-count mt-8">
                    <div>{i18n.get('费用总金额: ')} <Money style={{ display: 'inline-block', fontWeight: 600 }} value={totalAmount || 0} /></div>
                    <div>
                        <span className="detals-total">{i18n.get('总计 {count} 条', { count: total || 0 })}</span>
                        <a onClick={this.handelShowDetail}>{showDetail ? i18n.get('收起') : i18n.get('查看详情')}</a>
                    </div>
                </div>
                {
                  showDetail && (
                      <div className="detail-table">
                          {headers ? this.renderTable() : this.renderEmpty()}
                      </div>
                  )
                }
            </div>
        )
    }
}
