import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import Painter from '@ekuaibao/painter'
import { app as api } from '@ekuaibao/whispered'
import { getBlockUIReadonlyDSL } from '../utils/BlockUI'
import { Icon, Spin } from 'antd'
import { IMG_REG } from '@ekuaibao/lib/lib/enums'
import './BlockUI.less'
import { blockUIactions } from './BlockUI'

@EnhanceField({
  descriptor: {
    type: 'engineBlockUI'
  },
  wrapper: wrapper(true)
})
export default class BlockUI extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { value: undefined, isloading: true }
  }

  async componentDidMount() {
    const me = api.getState()['@common'].userinfo
    const value = await getBlockUIReadonlyDSL(this.props)
    this.setState({ value: !value || value.errorCode ? null : value, isloading: false })
  }

  async componentWillReceiveProps(nextProps) {
    const { field } = nextProps
    const referenceData = field?.dataType?.entity?.split('.').pop()
    if (
      (this.props.submitterId !== nextProps.submitterId || this.props.ownerId !== nextProps.ownerId) &&
      referenceData === 'LOAN_STATISTICS'
    ) {
      const value = await getBlockUIReadonlyDSL(nextProps)
      this.setState({ value, isloading: false })
    }
  }

  async refreshFn() {
    this.setState({ isloading: true })
    const value = await getBlockUIReadonlyDSL(this.props)
    this.setState({ value: !value || value.errorCode ? null : value, isloading: false })
  }

  renderSyncIcon(isWidgetCard) {
    if (isWidgetCard) {
      return <Icon type="sync" className="blockui_icon-sync" onClick={() => this.refreshFn()} />
    }
    return null
  }

  render() {
    const { value, isloading } = this.state
    const isWidgetCard = this.props.field?.dataType?.entity.indexOf('connect.BlockUI.widgetCard') >= 0
    if (isloading || !value) {
      return (
        <div className={`blockui_container bg_f6 ${isWidgetCard ? 'widget-card' : ''}`}>
          {this.renderSyncIcon(isWidgetCard)}
          {isloading ? (
            <Spin tip="加载中..." size="large" className="blockui_loading" />
          ) : (
            <div className="blockui_error">
              <img src={require('../../images/blockui-error.png')} alt="" />
              <p>访问错误，请联系企业管理员进行排查</p>
            </div>
          )}
        </div>
      )
    }

    if (!value.blocks) {
      value.blocks = []
    }
    // block ui 附件的事件设计不合理，这里需要重新设计
    value.blocks = value.blocks.map(block => {
      if (block.type === 'attachment') {
        block.action_id = ['app:attachment:preview', 'app:attachment:download']
      }

      return block
    })

    return (
      <div className={`blockui_container ${isWidgetCard ? 'widget-card' : ''}`}>
        {this.renderSyncIcon(isWidgetCard)}
        <Painter.Painter {...value} actions={blockUIactions}></Painter.Painter>
      </div>
    )
  }
}
