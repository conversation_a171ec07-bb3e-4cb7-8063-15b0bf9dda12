import React, { PureComponent, Fragment } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { Radio } from 'antd'
import classnames from 'classnames'
import styles from './EnterWay.module.less'
const RadioGroup = Radio.Group
const manual = { value: true, label: i18n.get('手动填写') }
const auto = { value: false, label: i18n.get('系统计算') }
interface Props {
  tags?: any[]
  value: string | {}
  field: any
  form: any
  onChange: Function
  defaultValue: {
    value: boolean
  }
}

interface State {}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'enter-way'
  }
})
export default class EnterWay extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
  }
  onChange = (e: { target: { value: string | {} } }) => {
    /**
     * 仅为通知
     */
    let { onChange } = this.props
    let val = e.target.value
    onChange && onChange(val)
    const { form, field } = this.props
    //切换为自动计算的时候，清空城市组相关值
    const hasForbiddenCityGroup = form?.getFieldValue('hasForbiddenCityGroup')
    const cityGroupId = form?.getFieldValue('cityGroupId')
    if (!val && field?.type === 'enter-way' && cityGroupId && hasForbiddenCityGroup) {
      form?.setFieldsValue({ hasForbiddenCityGroup: false, cityGroupId: null })
    }
  }
  render() {
    const { field, value } = this.props
    let tags = [{ value: true, label: i18n.get('手动填写') }, { value: false, label: i18n.get('系统计算') }]
    return (
      <div className={styles.radio} data-cy="enterWay">
        <RadioGroup buttonStyle="solid" value={value} onChange={this.onChange}>
          {tags.map((v: any) => {
            let active = classnames('item', { active: !!value === v.value })
            return (
              <div key={v.value} className={active}>
                <Radio className={'text'} disabled={field.disabled} value={v.value}>
                  {v.label}
                </Radio>
              </div>
            )
          })}
        </RadioGroup>
      </div>
    )
  }
}
