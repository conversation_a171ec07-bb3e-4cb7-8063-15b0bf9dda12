import React from 'react'
import { get, forEach } from 'lodash';
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { isObject } from '@ekuaibao/helpers'
import { formatTreeToArray } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import styles from './MutilStaff.module.less'
import EkbHighLighter from '../../elements/EkbHighLighter'
import { getRecordLink } from './helpers/getRecordLink'
import { Select as AntdSelect } from 'antd'
import { Select as EUISelect, Tooltip } from '@hose/eui';
import { SpecialAllowCancelDependenceUtils } from './types'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers';
import { OutlinedEditSearch } from '@hose/eui-icons'

// @ts-ignore
@EnhanceConnect(
  state => ({
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class MutilDimensionListSearchComp extends React.Component<any, any> {
  state: any = {
    searchText: '',
    selectOption: [],
    data: [],
    dependenceMap: [],
    isDependenceListEmpty: false,
    useDependenceData: true // 是否允许使用 依赖关系筛选数据（因为允许取消筛选）
  }
  timer = undefined
  componentWillMount() {
    const { bus } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
  }
  componentDidMount() {
    const { value = [], field } = this.props
    const { dependence } = field
    const dependenceMap =
      dependence?.map(v => {
        const { direction, roleDefId } = v
        return { direction, roleDefId, dependenceId: '' }
      }) || []
    this.setState({ dependenceMap })
    const ids = value?.map(it => it?.id || it).join(',')
    api.invokeService('@bills:get:dimension', ids).then((res: any) => {
      this.setState({ selectOption: res.items })
    })
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
  }

  currentIdx = null
  handleDependenceChange = ({ key, id, dependenceFeeType = false },options = {}) => {
    const { getRecordLink, field, value = [], bus, flowId, isModify, billState, detailId, isDetail, form} = this.props
    const { dependence, dataType, selectRange, dependenceCondition, allowCancelDependence = false } = field
    const isSpecialCancelDependency = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
     // 非单据新建及费类新建（费类有detailId 则为编辑）
     const isInitLoad = (billState !== 'new' && Boolean(!isDetail) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据

    if (dependence && dependence?.length) {
      const isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        this.currentIdx = id // 标明promise的执行顺序
        const { dependenceMap } = this.state
        const list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        getRecordLink({
          recordSearch: list,
          entity: get(dataType, 'elemType.entity'),
          defaultValue: isObject(value) ? value.id : value,
          range: selectRange,
          dependenceFeeType,
          flowId: isModify ? flowId : '',
          dependenceCondition
        }).then(action => {
          const { matchDefaultValue, items, leafItems, count } = action.payload
          const flatItems = formatTreeToArray(items)
          const itemMap = flatItems.map(v => v.id)
          let newValue = undefined
          if (this.currentIdx === id) {
            if (!matchDefaultValue) {
              bus.setValidateLevel(1)
              if(isInitLoad){
                const fieldsValue = form.getFieldsValue()
                // 初始化进来 当前value 值赋值
                newValue = fieldsValue[field?.field] ?? undefined
                this.formChange(newValue)
              } else if (leafItems) {
                newValue = [leafItems.id]
                this.formChange(newValue)
                api.invokeService('@bills:get:dimension', [leafItems.id].join(',')).then(res => {
                  this.setState({ selectOption: res.items || [] })
                })
              } else if (value.length) {
                newValue = value.filter(v => itemMap.includes(v))
                api.invokeService('@bills:get:dimension', newValue.join(',')).then(res => {
                  this.setState({ selectOption: res.items || [] })
                })
                this.formChange(newValue)
              } else {
                this.formChange(undefined)
              }
              // TODO: 档案关系埋点
              const oldValue = value
              let { billData, billSpecification, feeType, dataSource } = this.props
              let newBillData = billData
              let message = '单据上的档案关系赋值'
              if (feeType) {
                message = '明细上的档案关系赋值'
              } else {
                newBillData = dataSource
              }
              api?.logger?.info(message, {
                specificationId: billSpecification?.id,
                specificationName: billSpecification?.name,
                flowId: newBillData?.flowId || newBillData?.id,
                code: newBillData?.code || newBillData?.form?.code,
                sceneName: '档案关系',
                feeTypeId: feeType?.id,
                feeTypeName: feeType?.name,
                field: field?.field,
                dependField: key,
                oldValue,
                newValue
              })
            }
            if (isSpecialCancelDependency) {
              // 如果是特殊企业，将直接使用全部数据进行搜索
              this.setState({
                isDependenceListEmpty: false,
                data: [],
                useDependenceData: false,
                emptyText: ''
              })
            } else if (count === 0 && allowCancelDependence) {
              // 如果是开启了取消依赖，将可以取消依赖
              this.setState({
                isDependenceListEmpty: true,
                data: [],
                useDependenceData: true,
                emptyText: (
                  <div className="cancel-dependence">
                    {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
                    <a href="javascript:void(0)" onClick={this.handleCancelDependency.bind(this)}>
                      {i18n.get('查看全量数据')}
                    </a>
                  </div>
                )
              })
            } else {
              // 其他情况，重置 state
              this.setState({
                data: [],
                isDependenceListEmpty: false,
                useDependenceData: true,
                emptyText: ''
              })
            }
          }
        })
      }
    }
  }

  handleCancelDependency() {
    this.setState(
      {
        useDependenceData: false
      },
      () => {
        this.getDimenstionById(this._currentValue)
      }
    )
  }

  _onChange = (value, option) => {
    this.formChange(value)
    const selectObj = option.map(item => item.props.obj)
    this.setState({ selectOption: selectObj }, () => {
      this.initStateData(this.state.data)
    })
  }

  formatValue = value => {
    return value?.map(i => i?.id || i)
  }

  render() {
    const { data = [], emptyText = '', searchText, selectOption = [] } = this.state
    const dataIds = data.map(item => item.id)
    const { field, value, useEUI } = this.props
    const placeholder = getPlaceholder(field)
    const disabled = isDisable(this.props)
    const Select = useEUI ? EUISelect : AntdSelect
    const Option = Select.Option
    const size = useEUI ? 'default' : 'large'

    const children = data
      .map(d => {
        const name = getContentByLocale(d, 'name')
        const showText = !field?.hideCode ? i18n.get(`{__k0}({__k1})`, { __k0: name, __k1: d.code }) : name
        return (
          <Option key={d.id} value={d.id} name={showText} obj={d}>
            {useEUI ? (
              showText
            ) : (
              <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={showText} />
            )}
          </Option>
        )
      })
      .concat(
        selectOption
          .filter(v => !dataIds.includes(v.id))
          .map(item => {
            const name = getContentByLocale(item, 'name')
            return <Option
              style={{ display: 'none' }}
              name={!field?.hideCode ? i18n.get(`{__k0}({__k1})`, { __k0: name, __k1: item.code }) : name}
              key={item.id}
              value={item.id}
              obj={item}
            ></Option>
          })
      )
    return (
      <div>
        <Select
          className={styles['ekb-select']}
          mode="multiple"
          optionLabelProp={'name'}
          style={{ width: '100%' }}
          placeholder={placeholder}
          value={this.formatValue(value)}
          showSearch={true}
          size={size}
          allowClear
          disabled={disabled}
          onChange={this._onChange}
          onSearch={this.onSearch}
          filterOption={false}
          notFoundContent={emptyText}
          showArrow
          getPopupContainer={triggerNode => triggerNode.parentNode}
          suffixIcon={<OutlinedEditSearch />}
        >
          {children}
        </Select>
      </div>
    )
  }
  // 用来确定 Option 数量
  initStateData = searchData => {
    this.setState({ data: searchData })
  }

  formChange = fromValue => {
    const { onChange } = this.props
    onChange && onChange(fromValue)
  }

  _currentValue = ''
  onSearch = currentValue => {
    this.setState({ searchText: currentValue })
    if (currentValue.trim().length) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this._currentValue = currentValue.trim()
      this.timer = setTimeout(() => this.getDimenstionById(currentValue.trim()), 1000)
    } else {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this._currentValue = ''
      this.setState({
        data: [],
        emptyText: ''
      })
    }
  }

  // 搜索
  getDimenstionById(searchValue) {
    const { field, bus, getRecordLink, useEUI } = this.props
    const { selectRange, dependence, dependenceCondition, allowCancelDependence = false } = field
    const { useDependenceData, isDependenceListEmpty } = this.state
    const isSpecialCancelDependency = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence

    const entity = get(field, 'dataType.entity') || get(field, 'dataType.elemType.entity')
    if (dependence && dependence.length && !isSpecialCancelDependency && useDependenceData) {
      if (isDependenceListEmpty) {
        return
      }
      bus.getFieldsValue().then(v => {
        let isNeedFetch = false
        let recordSearch = dependence.map((el, i) => {
          const { use, ...rest } = el
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const depVal = v[el.dependenceId]
          if (depVal && depVal.id) {
            isNeedFetch = true
            return { ...rest, dependenceId: depVal.id }
          }
          return rest
        })
        if (isNeedFetch) {
          getRecordLink({
            recordSearch,
            entity: entity,
            isSearch: true,
            range: selectRange,
            keyWord: searchValue,
            dependenceCondition
          }).then(action => {
            const items = action.payload.items
            const { searchText } = this.state
            if (!searchText) {
              this.setState({ data: [], emptyText: '' })
            } else {
              this.setState({ data: items, emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
            }
          })
        } else {
          const { searchText } = this.state
          if (!searchText) {
            this.setState({ data: [], emptyText: '' })
          } else {
            this.setState({ data: [], emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
          }
        }
      })
    } else {
      api
        .invokeService('@common:get:dimensionById', { keyWord: searchValue, range: selectRange, size: 100, id: entity })
        .then(data => {
          const { searchText } = this.state
          if (!searchText) {
            this.setState({ data: [], emptyText: '' })
          } else {
            this.setState({ data: data, emptyText: useEUI ? undefined : i18n.get('无匹配结果') })
          }
        })
    }
  }
}
