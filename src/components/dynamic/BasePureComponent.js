import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { IMG_REG } from '@ekuaibao/lib/lib/enums'
import { get } from 'lodash'

import { getV } from '@ekuaibao/lib/lib/help'

const IMG_REG_PRE = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|png|raw|tga)$/i

export class BasePureComponent extends PureComponent {
  handleFileDownload = line => {
    const url = getV(line, 'url', '')
    const fileName = getV(line, 'fileName', '')
    api.emit('@vendor:download', url, fileName)
  }

  handleFilePreview = line => {
    const { value } = this.props
    const fileList = get(value, 'attachments') ? get(value, 'attachments') : value
    const fn = api.invokeService('@bills:file:preview')
    fn({ value: fileList, line })
  }

  handleModalClose = () => {
    this.setState({ imageVisible: false })
  }
}
