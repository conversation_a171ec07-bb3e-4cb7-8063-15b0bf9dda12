import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { Switch } from '@hose/eui'
import { Col } from 'antd'
import { wrapper } from '../layout/FormWrapper'

@EnhanceField({
  descriptor: {
    type: 'switcher'
  },
  wrapper: wrapper(true),
})
export default class SwitcherWrapper extends PureComponent {
  render() {
    const { value } = this.props
    return (
      <Col>
        <Switch disabled checked={value} />
      </Col>
    )
  }
}
