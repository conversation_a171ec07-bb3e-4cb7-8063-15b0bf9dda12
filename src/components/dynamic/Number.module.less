/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/8/16 下午6:08
 */
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';
.number_wrap {
  :global {
    .number {
      display: flex;
      .number-input {
        flex: 1;
        &.with-unit {
          border-radius: 2px 0 0 2px;
        }
      }
      .unit-text {
        padding: 0 3px;
        min-width: 32px;
        max-width: 100px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 12px;
        border-radius: 0 2px 2px 0;
        border: 1px solid @border-color-base;
        background-color: @gray-3;
        border-left: none;
      }
    }
    .formula_wrapper {
      word-wrap: break-word;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
    .ekb-number-disabled{
      color: @color-black-2;
    }
  }
}
