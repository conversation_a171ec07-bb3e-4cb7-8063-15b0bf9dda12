import React, { PureComponent } from 'react'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
import styles from './StaffSelectTags.module.less'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import StaffShow from '../../elements/puppet/staff-select-heavy'

interface Props {
  field: any
  value: any
  departmentTree: any
  staffs: any
  roles: any
  onChange: Function
  bus: any
}

interface State {}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'staff-select-tags'
  },
  // validator: (field: any) => (rule: any, value: any, callback: any) => {
  // return callback(undefined)
  // },
  wrapper: wrapper()
})
export default class StaffSelectTags extends PureComponent<Props, State> {
  componentWillMount() {
    api.invokeService('@common:get:staffs:roleList:department')
    const { onChange, value } = this.props
    if (!value) {
      onChange && onChange({})
    }
  }
  handleValueChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    const { field, value } = this.props
    const { description, placeholder } = field
    return (
      <div className={styles['staff_tags']}>
        <StaffShow
          placeholder={placeholder}
          onValueChange={this.handleValueChange}
          value={value}
          showIncludeChildren={true}
          includeSubWrapperStyle={{ marginTop: -15, display: 'flex', alignItems: 'center' }}
        />
        {description && <div className="description">{description}</div>}
      </div>
    )
  }
}
