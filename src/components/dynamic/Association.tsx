/*
 * @Description: 场景群插件
 * @Creator: chencangshan
 * @Date: 2021-12-10 16:08:04
 */
import styles from './Association.module.less'

import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceField } from '@ekuaibao/template'
import { Switch } from 'antd'
import { wrapper } from '../layout/FormWrapper'

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'isOpenAssociation'
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    assiciationGroupData: state['@bills'].assiciationGroupData,
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }
})
export default class Association extends PureComponent<any> {
  constructor(props) {
    super(props)
    this.state = {
      componentLabel: ''
    }
  }

  componentDidMount() {
    const { value, onChange, baseDataPropertiesMap, billSpecification, isCopy, template, originalValue } = this.props

    if (baseDataPropertiesMap?.isOpenAssociation?.active) {
      app.invokeService('@bills:get:association:group:data').then(assiciationGroupData => {
        if (!isCopy) {
          onChange(assiciationGroupData?.value?.isCreateGroup)
        } else {
          // 复制单据保留原单据值
          onChange(originalValue?.isOpenAssociation)
        }

        const associationSpecification = assiciationGroupData?.value?.specificationConfigs?.find(
          item =>
            item.specificationId === billSpecification.id ||
            item.specificationId === billSpecification.originalId ||
            item.specificationId === billSpecification.originalId.id
        )

        this.setState({
          componentLabel: template
            ?.flat()
            ?.find(item => item?.field === associationSpecification?.fields?.[0]?.fieldName)?.cnLabel
        })
      })
    }
  }

  render() {
    const { componentLabel } = this.state
    const { value, field, onChange } = this.props

    return (
      <div className={styles['association-wrapper']}>
        <div className="left">
          <div className="">{i18n.get('根据{__k0}自动创建钉钉群', { __k0: componentLabel || i18n.get('相关人') })}</div>
          <div className="sub-title">{i18n.get('单据动态将实时同步至群内')}</div>
        </div>
        <Switch
          checked={value}
          disabled={false}
          onChange={checked => {
            onChange(checked)
          }}
        />
      </div>
    )
  }
}
