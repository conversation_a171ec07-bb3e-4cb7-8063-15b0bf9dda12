/**
 *  Created by <PERSON><PERSON> on 2018/8/15 下午12:48.
 */

import React, { PureComponent } from 'react'
import { Input } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import { required, checkPhone } from '../validator/validator'
import { wrapper } from '../layout/FormWrapper'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'phone'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    const phoneError = checkPhone(field, value)
    if (phoneError) {
      callback(phoneError)
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class Phone extends PureComponent {
  onChange = e => {
    const { onChange } = this.props
    const { value } = e.target
    onChange && onChange(value)
  }

  render() {
    const { value, field } = this.props
    let { placeholder, optional } = field

    if (optional) {
      placeholder = i18n.get('（选填）') + placeholder
    }

    const disabled = isDisable(this.props)

    return <Input value={value} size="large" disabled={disabled} placeholder={placeholder} onChange={this.onChange} />
  }
}
