/**************************************************
 * Created by kaili on 2017/8/18 上午12:26.
 **************************************************/
import { app as api } from '@ekuaibao/whispered'
import { getMoney } from '../../lib/misc'
import { get, forIn } from 'lodash'
import { isObject } from '@ekuaibao/helpers'
import { checkIsRemuneration } from '../../lib/lib-util'
import { getBoolVariation } from '../../lib/featbit'

export function required(field, value) {
  let { optional, label, inputVisible, dataType = {} } = field
  if (inputVisible === false) {
    return undefined
  }
  if (value && typeof value === 'string') {
    value = value.trim()
  }
  if (!value && typeof value === 'boolean') {
    return undefined
  }

  if (
    dataType.type === 'ref' &&
    value &&
    isObject(value) &&
    !Array.isArray(value) &&
    !value.id &&
    !value.multiplePayeesMode
  ) {
    value = ''
  }

  if (!optional && (value === null || value === undefined || value.length === 0 || value[0]?.checkedKeys?.length === 0)) {
    return i18n.get('not-empty', { label: i18n.get(label) })
  }

  return undefined
}
export function dataLinkEditValidator(field, rule, value, callback, ignore = false) {
  const { importMode, fieldBus, optional, showType } = field
  if (importMode === 'SINGLE' && value && !ignore) {
    let val = value.length > 0 && value[0].dataLinkForm ? Object.values(value[0].dataLinkForm).find(i => !!i) : 0
    if (fieldBus.getValueWithValidate && (!optional || (val && optional))) {
      fieldBus.setValidateLevel(0)
      return fieldBus
        .getValueWithValidate(rule.level)
        .then(_ => {
          callback()
        })
        .catch(_ => {
          callback(' ')
        })
    }
    callback(requiredDataLinkEdit(field, value))
  } else {
    if (!value?.length) {
      return callback(required(field, value))
    }
    const dataLinkEntity = api.getState()['@bills'].dataLinkEntity
    const hasEmptyData = []
    value.forEach((it, index) => {
      const { dataLinkTemplateId, dataLinkForm } = it
      const currDataLinkEntity = dataLinkEntity.find(it => it.templateId === dataLinkTemplateId)
      // const currEmptyData = []
      if (currDataLinkEntity) {
        let components = currDataLinkEntity.components
        if (showType === 'TABLE') {
          components = currDataLinkEntity?.tableTemplate?.components
        }
        components = components?.filter(i => i.type !== 'switcher')
        // const entityName = currDataLinkEntity.entity?.name
        components.some(oo => {
          const { field, optional } = oo
          const val = dataLinkForm[field]
          if (!optional && (!val || val?.length === 0)) {
            hasEmptyData.push(index + 1)
            return true
          }
        })
      }
    })
    if (hasEmptyData.length > 0) {
      return callback(`第${hasEmptyData.join()}信息填写不完整,请填写`)
    }
    return callback()
  }
}
export function requiredDataLinkEdit(field, value) {
  let { optional, label, inputVisible, dataType = {} } = field
  if (inputVisible === false) {
    return undefined
  }
  if (value && typeof value === 'string') {
    value = value.trim()
  }

  if (dataType.type === 'ref' && value && isObject(value) && !value.id) {
    value = ''
  }

  if (!optional) {
    if (!value || value.length === 0 || (value && value[0] && !value[0].dataLinkTemplateId) || value === '')
      return i18n.get('not-empty', { label: i18n.get(label) })
  }

  return undefined
}

export function tripDataLinkEditValidator(field, value) {
  let errorMsg = undefined
  if (!value || value.length === 0) {
    if (!field.optional) {
      errorMsg = i18n.get('not-empty', { label: i18n.get(field.label) })
    }
  } else {
    field.child && field.child.handleSaveTrip()
    const { dataLinkForm } = value[0]
    forIn(dataLinkForm, function (value, key) {
      const label = key?.split('_')?.[2]
      if ((!key?.includes('场景')) && value === '' || value == undefined || value === '[]') {
        if (!field.optional) {
          errorMsg = i18n.get('not-empty', { label: i18n.get(label) })
        }
      }
    })
  }
  return errorMsg
}

export async function tripDataLinkSceneValidator(field, props) {
  const sceneRequired = await api.invoke('checkSceneRequired')
  console.log('check_sceneRequired', sceneRequired)
  if (sceneRequired === 'invalid') {
    const sceneRequiredEnsured = await api.invoke('checkSceneRequired')
    if (sceneRequiredEnsured === 'invalid') {
      return '缺失场景信息'
    }
  }
  return undefined
}

export function fnCheckCompleted({
  value = [],
  isTrip = false,
  billSpecification = {},
  multiplePayeesMode = false,
  payPlanMode = false,
  currentNode,
  isModify,
  dataSource
}) {
  const billType = billSpecification?.type
  let label
  const hasDraftResult = value?.filter(v => v?.feeTypeForm?.detailStatus === 'draft')
  if (hasDraftResult?.length) {
    hasDraftResult.forEach(v => {
      v.errorMsg = v.errorMsg || {}
      v.errorMsg['completed'] = i18n.get('该明细为草稿状态，提交单据前，请先保存该明细')
    })
    return
  }
  if (isTrip) {
    label = i18n.get('行程记录')
  } else {
    switch (billType) {
      case 'requisition':
        label = i18n.get('申请明细')
        break
      case 'expense':
        label = i18n.get('费用明细')
        break
      case 'reconciliation':
        label = i18n.get('对账明细')
        break
      case 'settlement':
        label = i18n.get('结算明细')
        break
      case 'corpPayment':
        label = i18n.get('付款明细')
        break
      default:
        label = i18n.get('报销明细')
    }
  }
  const obj = get(billSpecification, 'components', []).find(item => item.type === 'details')
  const isHideBillDetail = get(obj, 'hide', false)
  const isRequired = get(obj, 'optional', false)
  if (isHideBillDetail) return
  const isRemuneration = checkIsRemuneration(billSpecification)
  if (isRemuneration) return
  if (!value.length && !isRequired) {
    return i18n.get(`{__k0}不能为空`, { __k0: label })
  }
  if (!isTrip) {
    let error = undefined
    let approtionError = undefined
    let totalAmount = 0
    const payeeInfo = billSpecification?.components.find(item => item.field === 'payeeId')
    value.forEach(v => {
      error = v?.errorMsg?.completed || undefined // 初始化赋值错误，场景：单据在外面通过自动计算有必填字段未校验
      v.errorMsg = v.errorMsg || {}
      totalAmount = new Big(totalAmount).add(getMoney(v.feeTypeForm.amount))
      let { feeTypeForm, specificationId = {} } = v
      let { components } = specificationId
      let cc = components
      if (
        multiplePayeesMode &&
        !payPlanMode &&
        cc.findIndex(item => item.type === 'payeeInfo') <= -1 &&
        !feeTypeForm.feeDetailPayeeId
      ) {
        if (payeeInfo) {
          components.push(payeeInfo)
        }
      } else if (!multiplePayeesMode || (multiplePayeesMode && payPlanMode)) {
        // 切回单收款人模式时去掉明细里的收款信息的校验
        cc = components.filter(item => item.field !== 'payeeId' && item.field !== 'feeDetailPayeeId')
      }
      const pay = billSpecification.configs.find(v => v.ability === 'pay')
      const { isLimitFieldRequired, limitFieldRequireds } = currentNode?.config || {}
      const isModifyForceValidation = dataSource?.billFeeForceValidation && isModify // 立即修改及保存的时候触发强校验
      const requiredComponents = cc.filter(c => {
        const { field, configs, checkDetailInvoiceType, type } = c
        if (isModifyForceValidation && limitFieldRequireds?.includes(field) && isLimitFieldRequired) {
          c.optional = false
        }
        // 大概率是：自动计算的属性，默认用户在打开单据的时候就已经做了校验，所以在保存时就过滤掉自动计算属性
        const noConfigs = !Array.isArray(configs) || (Array.isArray(configs) && !configs.length)
        const required = c.optional === false && noConfigs
        if (pay) {
          if (field === 'attachments' || type === 'attachments' || type === 'aiAttachments') {
            return checkDetailInvoiceType ? false : required
          }
          return required && field !== 'feeDetailPayeeId'
        } else {
          return required
        }
      })
      const result = requiredComponents.filter(c => {
        const fieldValue = feeTypeForm[c.field]
        return (
          fieldValue === undefined ||
          fieldValue === '' ||
          fieldValue === null ||
          (Array.isArray(fieldValue) && !fieldValue.length) ||
          (c.type === 'payeeInfo' && isHavePayeeInfo(feeTypeForm, c)) ||
          taxInvalid(fieldValue, c) ||
          fnInvoiceValid(fieldValue, c)
        )
      })
      if (result.length) {
        error = isModify ? i18n.get('该明细有必填字段未填，请填写') : i18n.get('消费明细填写不完整；')
        v.errorMsg['completed'] = error
      } else if (feeTypeForm.apportions?.length) {
        let hasDisactiveDimension = false
        let hasDisactiveDataLink = false
        const baseDataPropertiesMap = api.getState('@common.globalFields.baseDataPropertiesMap') || {}
        const hasDisactive =
          feeTypeForm.apportions.filter(line => {
            const componentsMap =
              line?.specificationId?.components?.reduce((acc, cur) => {
                acc[cur.field] = cur
                return acc
              }, {}) || {}
            return (
              Object.keys(line.apportionForm).filter(key => {
                if (line.apportionForm[key] && line.apportionForm[key].active === false) {
                  const globalField = baseDataPropertiesMap[key]
                  //不校验自定义档案
                  if (globalField?.dataType?.entity?.includes('basedata.Dimension')) {
                    return false
                  }
                  const component = componentsMap[key]
                  const allowUseDeactivateDepartment = component?.allowUseDeactivateDepartment
                  // allowUseDeactivateDepartment allowUseDeactivateDepartment, 不要提示，所以hasDisactiveDimension为false
                  if (line.apportionForm[key].entityId) {
                    hasDisactiveDataLink = true
                  } else {
                    hasDisactiveDimension = !allowUseDeactivateDepartment
                  }
                  return !allowUseDeactivateDepartment
                }
                return false
              }).length > 0
            )
          }).length > 0

        let errorText = '分摊中存在已停用的'
        if (hasDisactiveDimension) {
          errorText += '自定义档案'
        }
        if (hasDisactiveDataLink) {
          errorText += hasDisactiveDimension ? '、业务对象；' : '业务对象；'
        }
        error = hasDisactive ? errorText : v?.errorMsg?.isCheckCalAttr ? v?.errorMsg?.completed : undefined
        v.errorMsg['completed'] = error
        //校验联查未赋值
        if (!error && api.getState()['@common'].powers.customizeQuery) {
          const dataLinkComponentFields = []
          feeTypeForm.apportions[0]?.specificationId?.components?.forEach(comp => {
            if (comp.defaultValue.type === 'customizeQuery') {
              dataLinkComponentFields.push(comp.field)
            }
          })
          if (dataLinkComponentFields.length) {
            const emptyItem = feeTypeForm.apportions.find(el => {
              let hasEmptyField = false
              dataLinkComponentFields.forEach(fieldKey => {
                if (!hasEmptyField && !el.apportionForm[fieldKey]) {
                  hasEmptyField = true
                  return true
                }
              })
              return hasEmptyField
            })
            if (emptyItem) {
              approtionError = i18n.get('分摊明细不完整')
              v.errorMsg['completed'] = approtionError
            } else {
              v.errorMsg['completed'] = ''
            }
          }
        }
        //检查分摊模板其它配置
        const hasOtherConfigErr = fnCheckApportionOtherConfig(feeTypeForm.apportions)
        if (hasOtherConfigErr) {
          v.errorMsg['completed'] = hasOtherConfigErr
        }
      } else if (v.shouldSaveFeetype) {
        v.errorMsg['completed'] = i18n.get('币种汇率发生变化，请点击费用明细中重新保存后提交')
      } else if (v.errorMsg['isCheckCalAttr'] && v.errorMsg['completed']) {
      } else {
        v.errorMsg['completed'] = ''
      }
    })

    if (billSpecification) {
      const { type, configs } = billSpecification
      if (
        type !== 'requisition' &&
        type !== 'permit' &&
        type !== 'receipt' &&
        type !== 'reimbursement' &&
        !configs.find(v => v.ability === 'chargeAgainst') &&
        new Big(totalAmount).lte(0)
      ) {
        //冲销允许金额为负
        return i18n.get('消费记录总额不能小于0')
      }
    }

    return error || approtionError
  }
}
export function fnCheckApportionOtherConfig(apportions) {
  //检查分摊其它配置
  if (apportions && apportions.length) {
    const apportionConfig = get(apportions[0], 'specificationId.configs')
    const otherConfig = apportionConfig.find(item => item.ability === 'apportionOtherConfig')
    const { apportionDetailsAtLeastTwo, apportionSingleItemNotEqualOne } = otherConfig || {}
    if (apportionDetailsAtLeastTwo && apportions.length < 2) {
      return i18n.get('分摊明细数量至少2条')
    } else if (apportionSingleItemNotEqualOne) {
      const hasOne = apportions.some(apportion => {
        const { apportionPercent } = apportion.apportionForm
        return apportionPercent == 100
      })
      if (hasOne) {
        return i18n.get('单条分摊比例不可等于100%')
      }
    }
  }
}
// 兼容账户的老数据 (不是做了数据升级了吗,咋还有老数据)
function isHavePayeeInfo(feeTypeForm, c) {
  return feeTypeForm[c.field].hasOwnProperty('asPayee')
    ? feeTypeForm[c.field].sort !== 'OTHER' && feeTypeForm[c.field].sort !== 'WEIXIN' && !feeTypeForm[c.field].accountNo
    : !feeTypeForm[c.field].cardNo
}

function taxInvalid(value, component) {
  return get(component, 'defaultValue.type') === 'invoiceSum' && (value === undefined || value === null)
}

export const fnInvoiceValid = (value, component) => {
  // 补充发票批次如果存在了，说明也有发票了
  if (value?.type === 'exist' && !!value?.supplementInvoiceBatchId?.length) {
    return false
  }
  return value?.type === 'exist' && !value?.invoices?.length && !value?.attachments?.length
}

export function validatorMoney(value, max, min, callback, field) {
  const { editable } = field
  let {
    standard,
    standardStrCode,
    standardScale,
    rate,
    foreign,
    foreignStrCode,
    foreignScale,
    budget,
    budgetStrCode,
    budgetScale,
    budgetRate
  } = value

  if (standardStrCode) {
    const { result, commond } = _regExpMoneyFormat(standard, standardStrCode, standardScale)
    if (!result) {
      return callback(commond)
    }
  }

  const maxLimit = 1000000000000000
  const foreignMinLimit = -100000000000
  if (foreignStrCode) {
    const { result, commond } = _regExpMoneyFormat(foreign, foreignStrCode, foreignScale)
    if (!result) {
      return callback(commond)
    }
    if (foreign * 1 > maxLimit) {
      return callback(i18n.get('amount-cannot-be-greater', { standardStrCode: foreignStrCode, max: maxLimit }))
    }
    if (foreign * 1 < foreignMinLimit * 1) {
      return callback(i18n.get('amount-cannot-be-less', { standardStrCode: foreignStrCode, min: foreignMinLimit }))
    }
    if (editable && foreign * 1 > max * 1) {
      return callback(i18n.get('amount-cannot-be-greater', { standardStrCode: foreignStrCode, max }))
    }
    if (editable && foreign * 1 < min * 1) {
      return callback(i18n.get('amount-cannot-be-less', { standardStrCode: foreignStrCode, min }))
    }
    if (rate <= 0) {
      return callback(i18n.get('输入的汇率必须大于0'))
    }
    let re = /^([1-9]\d*(\.\d+)?$)|^0(\.\d+)$/
    if (!re.test(rate)) {
      return callback(i18n.get('输入的汇率的格式不正确'))
    }
  }

  if (budgetStrCode) {
    const { result, commond } = _regExpMoneyFormat(budget, budgetStrCode, budgetScale)
    if (!result) {
      return callback(commond)
    }
    if (budget * 1 > maxLimit) {
      return callback(i18n.get('amount-cannot-be-greater', { standardStrCode: budgetStrCode, max: maxLimit }))
    }
    if (budget * 1 < -1000000000 * 1) {
      return callback(i18n.get('amount-cannot-be-less', { standardStrCode: budgetStrCode, min: -1000000000 }))
    }
    if (editable && budget * 1 > max * 1) {
      return callback(i18n.get('amount-cannot-be-greater', { standardStrCode: budgetStrCode, max }))
    }
    if (editable && budget * 1 < min * 1) {
      return callback(i18n.get('amount-cannot-be-less', { standardStrCode: budgetStrCode, min }))
    }
  }
  if (!editable && standard * 1 > maxLimit) {
    return callback(i18n.get('amount-cannot-be-greater', { standardStrCode, max: maxLimit }))
  }
  if (!editable && standard * 1 < -1000000000 * 1) {
    return callback(i18n.get('amount-cannot-be-less', { standardStrCode, min: -1000000000 }))
  }
  if (editable && standard * 1 > max * 1) {
    return callback(i18n.get('amount-cannot-be-greater', { standardStrCode, max }))
  }
  if (editable && standard * 1 < min * 1) {
    return callback(i18n.get('amount-cannot-be-less', { standardStrCode, min }))
  }
}

export function checkPhone(field, value) {
  if (!value || /^((\+?86)|(\(\+86\)))?\d{2,15}$/.test(value)) {
    return undefined
  } else {
    return i18n.get('手机格式不正确')
  }
}

function _regExpMoneyFormat(money, moneyStrCode, moneyScale) {
  if (money === undefined || money === '') {
    return { result: false, commond: i18n.get('请输入金额') }
  }
  let moneyRe = new RegExp(`^(-?)(([1-9]\\d*)|0)(\\.\\d{0,${moneyScale}})?$`)
  let numberFormatFlag = moneyRe.test(money)
  if (!numberFormatFlag) {
    return { result: false, commond: _commondStr(moneyStrCode, moneyScale) }
  }
  return { result: true }
}

function _commondStr(strCode, scale) {
  if (scale === 0) {
    return i18n.get('strCode', { strCode })
  }
  return i18n.get('strCode-scale', { strCode, scale })
}

export function checkedEmail(value) {
  let reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
  if (!value) {
    return i18n.get('邮箱不能为空')
  } else if (!reg.test(value)) {
    return i18n.get('请输入正确格式的邮箱地址')
  }
}

export function checkedAddress(value) {
  let reg = /^[0-9_.-]+$/
  if (!value) {
    return i18n.get('地址不能为空')
  } else if (reg.test(value)) {
    return i18n.get('请输入正确格式的地址')
  }
}

export const validatorCheckBoxValue = (field, value, props) => {
  if (field.name === 'allowExternalStaff') {
    const externalStaffPower = api.getState('@common').powers.ExternalAuth
    if (!externalStaffPower) {
      return undefined
    }
    const { form } = props
    const formValue = form.getFieldsValue()
    if (!formValue.allowInteriorStaff && !formValue.allowExternalStaff) {
      return i18n.get('至少选择一项')
    }
  }
  return undefined
}

// 校验每一项item 中的apportionMoney 和 apportionPercent 是否成正比，且两者都不为0的情况下
export const fnValidateMoneyItemEqual = (items, apportionTotalMoney) => {
  items = items || []
  const totalExpected = parseFloat(apportionTotalMoney || 0)
  if (!getBoolVariation('apportion-item-validate')) {
    return {
      zeroPass: true,
      percentMoneyPass: true,
      apportionMoneyZeroPass: true
    }
  }

  for (const item of items) {
    const { apportionMoney, apportionPercent } = item.apportionForm
    const moneyValue = parseFloat(apportionMoney.standard)
    const percentValue = parseFloat(apportionPercent)

    if (moneyValue === 0 && percentValue === 0) {
      return {
        zeroPass: false,
        percentMoneyPass: true,
        apportionMoneyZeroPass: false
      }
    }

    if (moneyValue === 0) {
      // 分摊金额为0
      return {
        zeroPass: true,
        percentMoneyPass: true,
        apportionMoneyZeroPass: false
      }
    }

    // 检查条件：费用金额 * 分摊比例 = 分摊金额
    const calculatedFromPercent = new Big(totalExpected * percentValue)
      .div(100)
      .toFixed(apportionTotalMoney?.standardScale || 2)
    // 检查条件：分摊金额 / 费用金额 = 分摊比例
    const calculatedFromMoney =
      Number(totalExpected) === 0
        ? percentValue
        : new Big(moneyValue / (totalExpected || 1)).times(100).toFixed(apportionTotalMoney?.standardScale || 2)

    // 分摊金额误差是1，百分比误差是0.1
    const isPercentEqual = isDifferenceRange(calculatedFromPercent, moneyValue, 1)
    const isMoneyEqual = isDifferenceRange(calculatedFromMoney, percentValue, 0.1)
    if (
      !isPercentEqual &&
      !isMoneyEqual &&
      items.indexOf(item) < items.length - 2 // 最后两项不校验
    ) {
      return {
        percentMoneyPass: false,
        zeroPass: true,
        apportionMoneyZeroPass: true
      }
    }
    if (items.length <= 2 && !isPercentEqual && !isMoneyEqual) {
      // 如果只有两项，同时有不相等的就需要校验
      return {
        percentMoneyPass: false,
        zeroPass: true,
        apportionMoneyZeroPass: true
      }
    }
  }
  return {
    zeroPass: true,
    percentMoneyPass: true,
    apportionMoneyZeroPass: true
  }
}

/**
 * diff 误差范围
 * @returns
 */
export function isDifferenceRange(num1, num2, range) {
  return Math.abs(Number(num1) - Number(num2)) <= range
}
