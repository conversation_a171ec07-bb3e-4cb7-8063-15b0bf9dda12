{"RES_ASYNC": ["elements/puppet/data-interconnection-config/DataConfig", "elements/ekbIcon", "ekb-components/business/role-selected/tags", "elements/transfer/SortableTransferTable", "elements/transfer/SortableTransfer", "elements/search-dropdown", "ekb-components/base/puppet/EKBCheckBox", "ekb-components/base/puppet/EKBInput", "ekb-components/base/puppet/EKBSelect", "ekb-components/base/form/form-item", "elements/puppet/entity-detail/entity-detail-fieldview", "elements/radio-group/EKBRadioGroup", "elements/DataLinkTable/DataLinkTableWrapper", "elements/ETabs", "elements/puppet/KeelSingleViewHeader", "elements/puppet/KeelViewBody", "ekb-components/base/puppet/EKBInput", "ekb-components/base/puppet/EKBSelect", "ekb-components/base/form/form-item", "ekb-components/business/ekbsteps", "ekb-components/base/puppet/EKBSelect", "ekb-components/base/puppet/EKBTreeSelect", "ekb-components/business/card/data-interconnection", "ekb-components/base/form/form-text", "ekb-components/business/breadcrumb/Title"], "RES_SYNC": ["elements/enhance/enhance-form-create", "components/utils/fnEntityDataParse", "elements/data-grid-v2/withLoader", "components/index.interconnectal", "components/utils/utilFunctionsParams", "components/interconnectal/checkboxLabel", "components/interconnectal/CustomDimensionFilter.isValidCustomDimensions", "components/validator/validator", "components/layout/FormWrapper", "elements/DataLinkTable/tableUtil", "components/index.interconnectal", "components/index.entitydetail"], "RES_IMG": ["images/avatar.svg", "images/platform-default-icon.png", "images/role.svg", "images/department.svg", "images/taxi.svg", "images/eleme.svg", "images/tongyong.svg", "images/clyh.svg", "images/xc-travel.png"]}