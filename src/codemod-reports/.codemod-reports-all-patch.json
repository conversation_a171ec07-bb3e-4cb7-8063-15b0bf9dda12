{"RES_ASYNC": ["elements/feetype-tree/feetype-tree-search-modal", "elements/modal/sync-staffs-modal", "elements/export-excel/export-excel-modal", "elements/async-export-modal", "elements/city/SelectCityModal", "elements/transfer/TransferModal", "elements/del-confirm-modal", "elements/staff-selector/staff-selector-modal", "elements/modal/sync-staffs-modal", "elements/select-member-modal", "elements/select-tree-modal", "elements/CarouselInvoiceReviewer/CarouselInvoiceReviewer", "elements/budget-detail/budget-detail", "elements/payee-account/account-change-log", "elements/print-modal", "elements/dept-select-modal", "elements/phone-verification/verification-phone", "elements/flow-allow-modal", "components/layout/ExceedStandardRiskForField", "elements/payPlan/table/EditRow", "elements/payPlan/table/EditCell"], "RES_SYNC": ["elements/feetype-icons", "elements/payPlan/table/table.store", "elements/payPlan/types", "elements/payPlan/helper/formatTableData", "elements/payPlan/table/ColumnFormat", "elements/payPlan/helper/tableHelper", "elements/data-grid-v2/withLoader", "elements/ekbc-basic/layout/Box", "components/reports", "components/index.editable", "components/index.entitydetail", "components/index.interconnectal", "components/index.internal", "components/index.readonly", "components/index.trips.readonly"], "RES_IMG": ["images/brand"]}