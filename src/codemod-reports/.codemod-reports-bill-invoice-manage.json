{"RES_ASYNC": ["elements/ETabs", "elements/puppet/Money", "elements/InvoiceCard/OpenBillInfoCard", "ekb-components/business/breadcrumb", "elements/puppet/KeelViewBody", "elements/ekbIcon", "components/dynamic/CityPickerComponent", "elements/ImmersiveToolBar", "elements/invoice-form/RefInvoice", "elements/InvoiceCard/InvoiceItem", "ekb-components/business/breadcrumb/index", "elements/feetype-icon", "elements/data-grid/EKBDataGridWrapper", "elements/data-grid-v2/LightingMode"], "RES_SYNC": ["components/validator/validator", "components/layout/FormWrapper", "elements/data-grid-v2/withLoader", "elements/enhance/enhance-form-create", "elements/InvoiceCard/FormatInvoiceData", "elements/data-grid/columnsUtil", "elements/data-grid/columnOthers", "elements/data-grid/fetchFixer", "components/index.editable"], "RES_IMG": ["images/refresh.svg"]}