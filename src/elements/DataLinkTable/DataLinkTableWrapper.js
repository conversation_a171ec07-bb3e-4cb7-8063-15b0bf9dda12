import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { parseFields, parseSorters, parseFilter, parseData } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { parseColumns, parseOptions } from './tableUtil'
import { cloneDeep, get, uniqBy } from 'lodash'
import { filterMultiStaff } from '../../lib/mutil-staff-fetch'
import * as DataGrid from '@ekuaibao/datagrid'
import styles from './DataLinkTableWrapper.module.less'
import { Switch, Button } from 'antd'
import { Input, Pagination } from '@hose/eui'
import { getBoolVariation } from '../../lib/featbit'
const { Search } = Input
const newOrderManagementFeatbitFlag = getBoolVariation('fkrd-3906-new-order-management')

const SCROLL_PAGE_SIZE = 10
@EnhanceConnect(state => ({
  trip: state['@tpp-v2'].trip,
  userinfo: state['@common'].userinfo.staff
}))
export default class DataLinkTableWrapper extends React.Component {
  state = {
    columns: [],
    dataSource: [],
    activeSceneIndex: '',
    rolesList: [],
    isShowTripFilter: false,
    formdataQuery: {},
    templateData: {}
  }

  fieldMap = {}
  pathMap = {}

  initFilterParams = {
    page: { currentPage: 1, pageSize: SCROLL_PAGE_SIZE },
    pageMode: 'pagination',
    sorters: '',
    filters: '',
    searchText: ''
  }

  getStatus = () => {
    let id = this.props?.entityInfo?.id
    let status = [true, false]
    if (id && localStorage.getItem(id)) {
      try {
        const bool = localStorage.getItem(id)
        if (bool === 'true') {
          status = [true, false]
        } else {
          status = [true]
        }
      } catch (e) {
        console.error(e)
      }
    }
    return status
  }

  filterParams = cloneDeep(this.initFilterParams)

  async componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    const { bus, type } = this.props
    if (type === 'TRAVEL_MANAGEMENT') {
      await api.invokeService('@tpp-v2:get:tripDataLink', { type })
      await api.invokeService(`@common:refresh:userinfo`)
    }
    await api.dataLoader('@common.allCurrencyRates').load()
    bus.on('table:row:useCount:click', this.handleUseCountClick)
    this.formatColumns(this.props)
    bus.reload = this.updateData
    bus.getTemplateData = this.getTemplateData
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('table:row:useCount:click', this.handleUseCountClick)
  }

  componentWillReceiveProps(nextProps) {
    if (
      this.props.SRDMap !== nextProps.SRDMap ||
      this.props.showEdit !== nextProps.showEdit ||
      this.props.entityInfo !== nextProps.entityInfo ||
      this.props.entityInfoMap !== nextProps.entityInfoMap
    ) {
      this.filterParams = cloneDeep(this.initFilterParams)
      this.formatColumns(nextProps)
    }
    if (
      (this.props.linkType === 'CHANPAY' && this.props.editFlag !== nextProps.editFlag && nextProps.editFlag) ||
      this.props.isBatchEdit !== nextProps.isBatchEdit
    ) {
      this.formatColumns(nextProps)
    }

    const { userinfo, type, parentEntityInfo } = nextProps
    if (type === 'TRAVEL_MANAGEMENT' && parentEntityInfo?.type === 'TRIP') {
      const rolesList = this.getRolesList(userinfo.roles?.values)
      this.setState({
        isShowTripFilter: true,
        rolesList
      })
    }
  }

  getTemplateData = () => {
    return this.state.templateData
  }

  formatColumns = (props = this.props) => {
    const {
      SRDMap,
      showEdit,
      bus,
      type,
      action,
      showNameAndCode,
      entityInfo,
      entityInfoMap,
      linkType,
      isManage,
      formatBatchCellColumns,
      isBatchEdit
    } = props

    this.fetchData(undefined, props).then(data => {
      const { template, path, ...others } = data

      const showUseCount = type !== 'PRIVATE_CAR'
      let fields = parseFields({ res: template, SRDMap, showEdit, type, showUseCount, showNameAndCode, entityInfo })
      fields = fields.map(field => ({ ...field, isManage }))
      // 批量编辑，一些自定义的按钮要disabled
      bus.isBatchEdit = isBatchEdit
      let { columns, fieldMap } = parseColumns({
        entityInfoMap,
        fields,
        bus,
        action,
        path,
        platformType: type,
        entityInfo,
        linkType
      })
      columns = typeof formatBatchCellColumns == 'function' ? formatBatchCellColumns(columns) : columns
      this.setCurrentList(data?.dataSource || [], columns || [])
      this.fieldMap = fieldMap
      this.pathMap = { ...path, entityId: 'entityId' }
      this.setState({ columns, ...others })
    })
  }

  setCurrentList = (data, columns) => {
    let list = []
    let key = ''
    data?.forEach(i => {
      let dataLink = i?.dataLink || {}
      if (!key) {
        key = Object.keys(dataLink).find(i => i?.includes('system_default_currency'))
      }
      if (dataLink[key]?.name) {
        list.push({
          label: dataLink[key]?.name,
          value: dataLink[key]?.numCode
        })
      }
    })
    columns?.forEach(i => {
      if (i?.property?.entity === 'basedata.Enum.currency' && i?.lookup && i?.filterDataSource) {
        if (i?.filterDataSource?.length) {
          list.push(...i?.filterDataSource)
        }
        list = uniqBy(list, 'value')
        i.lookup.dataSource = list
        i.filterDataSource = list
      }
    })
    this.setState({ columns })
  }

  fetchData = async (options = { page: { currentPage: 0, pageSize: SCROLL_PAGE_SIZE } }, props = this.props) => {
    let { fetch, isSingleSelect, bus, getLocal, queryChange, checkingBillId = null } = this.props
    const { formdataQuery } = this.state
    const { entityInfo, type, orderManagement, orderBy, showStop } = props
    let { id } = entityInfo
    let flag = getLocal(id)
    const { filters, ...others } = this.filterParams
    const formatFilters = this.formatFilters(filters)
    const o = { ...options, ...others, filters: formatFilters }
    let that = this
    if (fetch) {
      return fetch(o, this.fieldMap)
    } else {
      // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
      const __options = cloneDeep(o)
      __options.sorters = parseSorters(o.sorters, this.pathMap)
      __options.filters = parseFilter(o.filters, this.pathMap)
      __options.orderManagement = orderManagement
      __options.formdataQuery = formdataQuery

      //缓存获取停用状态
      if (__options?.filters && !__options?.filters?.active && showStop) {
        __options.filters.active = this.getStatus()
      }
      await filterMultiStaff(__options, this.fieldMap)
      const query = parseOptions({
        options: __options,
        isSingleSelect,
        entityInfo,
        type,
        fieldMap: this.fieldMap,
        isIgnoreSearchTextUpperOrLower: true // 获取搜索的大小写
      })

      if (!query.orderBy && orderBy) {
        query.orderBy = orderBy
      }

      queryChange?.(query)
      if (!flag) {
        const entityId = encodeURIComponent(entityInfo.id)
        api.invokeService('@third-party-manage:get:dataLink:import:status', { entityId }).then(res => {
          const {
            value: { state, urls }
          } = res
          const url = urls && urls[0]
          const step = state === 'PENDING' ? 2 : state === 'SUCCESS' ? 3 : state === 'FAILURE' ? 4 : 1
          bus.emit('refresh:status', { step, url })
        })
      }
      let params = this.getParams(query)
      let deferKey = '@third-party-manage:search:platform:datalink'
      if (checkingBillId) {
        if (!params?.entityId) return
        params.checkingBillId = checkingBillId
        deferKey = '@third-party-manage:search:order:match:order'
      }
      const supplierPortalConfig = api.getState('@common').supplierPortalConfig
      if (supplierPortalConfig?.dataLinkRules?.length) {
        const dataLinkRule = supplierPortalConfig.dataLinkRules.find(rule => rule.dataLinkEntityId === entityInfo?.id)
        if (dataLinkRule) {
          deferKey = '@third-party-manage:search:datalink:by:entityId'
          params.filterId = dataLinkRule.filterId
          params.type = 'TABLE'
          params.allActive = false // 获取停启用的数据
          params.form = {}
        }
      }

      // // 如果 deferKey 是 @third-party-manage:search:platform:datalink
      // // 需要删除 params 中的 query中的 orderBy
      // // 说明：业务对象排序规则由配置的排序规则决定，不需要前端传递
      // if (deferKey === '@third-party-manage:search:platform:datalink') {
      //   delete params.query.orderBy
      // }

      return api.invokeService(deferKey, params).then(rep => {
        const { template, total, path } = rep.items
        const data = parseData(rep.items.data)
        const { type, parentEntityInfo } = this.props
        if (type === 'TRAVEL_MANAGEMENT' && parentEntityInfo?.type === 'TRIP') {
          const ids = data.map(item => item.dataLink.id)
          //如果是行程管理里面的行程，请求日志接口
          return api.invokeService('@third-party-manage:get:travel:manage:logs', ids).then(result => {
            const newData = data.map(item => {
              return { ...item, logs: result?.items?.find(o => o.dataLinkId === item.id)?.logs }
            })
            return { dataSource: newData, template, path, total: total ? total : 1 }
          })
        }
        that.setCurrentList(data || [], that.state.columns || [])
        this.setState({
          templateData: {
            ...rep.items,
            dataSource: data
          }
        })
        return { dataSource: data, template, path, total: total ? total : 1 }
      })
    }
  }

  getParams = query => {
    const { type, entityInfo, category, parentEntityInfo } = this.props
    const visibility = query?.orderBy?.find(item => item.value === 'visibility')
    if (visibility) {
      //参与人排序
      query.orderBy = [
        {
          value: 'visibility.fullVisible',
          order: visibility.order
        },
        {
          value: 'visibility.staffs',
          order: visibility.order
        },
        {
          value: 'visibility.departments',
          order: visibility.order
        },
        {
          value: 'visibility.roles',
          order: visibility.order
        },
        {
          value: 'visibility',
          order: visibility.order
        }
      ]
    }
    this.isTripManagement = type === 'TRAVEL_MANAGEMENT' && parentEntityInfo?.type === 'TRIP'
    const params = {
      entityId: entityInfo.id,
      category,
      query
    }
    if (this.isTripManagement) {
      const { activeSceneIndex } = this.state
      const { trip, userinfo } = this.props
      const adminParticipantIds = get(trip, 'properties.adminParticipantIds') || []
      const adminIds = trip.adminIds || []
      const roles = get(userinfo, 'roles.values') || []
      let roleDefIds = null
      const isProperties = adminParticipantIds.includes(userinfo.id)
      const isAdmin = adminIds.includes(userinfo.id)
      if (!isAdmin && isProperties) {
        roleDefIds = roles.map(i => i.roleDefId)
      }
      params.roleDefIds = activeSceneIndex ? [activeSceneIndex] : roleDefIds
    }
    return params
  }

  formatFilters = filters => {
    if (!filters) return ''
    try {
      const result = {}
      Object.keys(filters).forEach(key => {
        let value = filters[key]
        if (Array.isArray(value) && !!value.length) {
          let v = {}
          if (value.length === 2 && (value[0]._isAMomentObject || value[1]._isAMomentObject)) {
            v.start = this.parseMoment(value[0])
            v.end = this.parseMoment(value[1])
          } else {
            v = value.map(item => this.parseMoment(item))
          }
          result[key] = v
        } else {
          result[key] = this.parseMoment(value)
        }
      })
      return result
    } catch (e) {
      return ''
    }
  }

  parseMoment = value => {
    if (value._isAMomentObject) {
      return value.format('x')
    } else {
      return value
    }
  }

  updateData = async params => {
    this.filterParams = {
      ...this.filterParams,
      ...params
    }
    const data = await this.fetchData()
    this.setState({ ...data })
  }

  handleUseCountClick = line => {
    const { id } = line.dataLink
    api.open('@bills:ArchivedStackerModal', { viewKey: 'DataLinkList', instanceId: id })
  }

  handlePageChange = (pagination, pageMode) => {
    this.updateData({ page: { currentPage: pagination.current, pageSize: pagination.size }, pageMode })
  }

  onEuiPaginationChange = (current, size) => {
    this.updateData({ page: { currentPage: current, pageSize: size }, pageMode: 'pagination' })
  }

  handleFilterChange = filters => {
    this.updateData({ filters, page: { currentPage: 1, pageSize: this.filterParams.page.pageSize } })
  }

  handleSearch = value => {
    this.updateData({ searchText: value, page: { currentPage: 1, pageSize: this.filterParams.page.pageSize } })
  }

  handleClearSearch = () => {
    this.updateData({ searchText: '', page: { currentPage: 1, pageSize: this.filterParams.page.pageSize } })
  }

  handleSorterChange = sorters => {
    this.updateData({ sorters })
  }
  handleRowClick = record => {
    const { type, entityInfo } = this.props
    if (type === 'PRIVATE_CAR') {
      api
        .open('@bills:BillStackerModal', {
          viewKey: 'MyCarBusinessInfo',
          referenceData: entityInfo,
          data: { form: record.dataLink },
          fromHistoryLog: true,
          needEdit: true
        })
        .then(_ => {
          api.open('@third-party-manage:ModifyPrivateCarRecordPopup', { dataSource: record.dataLink }, true).then(_ => {
            this.fetchData().then(data => {
              this.setState({ ...data })
            })
          })
        })
    }
  }
  getRolesList = (roles = []) => {
    const rolesList = [{ text: i18n.get('全部'), sceneIndex: '' }]
    roles.forEach(i => {
      if (!i?.roleDef) {
        return null
      }
      const { name, id } = i.roleDef
      const hasRole = rolesList.find(i => i.sceneIndex === id)
      if (!hasRole) {
        rolesList.push({
          text: name,
          sceneIndex: id
        })
      }
    })

    return rolesList.filter(v => v)
  }
  renderTripFilter = () => {
    const { activeSceneIndex, rolesList } = this.state
    return <DataGrid.Scenes scenes={rolesList} activeScene={activeSceneIndex} onChangeScene={this.handleSceneChange} />
  }
  handleSceneChange = activeSceneIndex => {
    this.setState({
      activeSceneIndex
    })
    this.updateData({ searchText: '', page: { currentPage: 1, pageSize: this.filterParams.page.pageSize } })
  }

  handleSeachMultiAssist = values => {
    const formdataQuery = this.constructorSearchMoreFormdata(values)
    this.setState({ formdataQuery }, async () => {
      this.filterParams = {
        ...this.filterParams
      }
      const data = await this.fetchData()
      this.setState({ ...data })
    })
  }
  // 构造select FORM
  constructorSearchMoreFormdata = formdata => {
    const {
      entityInfo: { parentId }
    } = this.props
    let map = {}
    Object.keys(formdata).forEach(e => {
      // 过滤空的条件，订单管理导出查询时
      if (formdata[e]) {
        map[`form.E_${parentId}_${e}`] = formdata[e] || ''
      }
    })
    return map
  }

  get computedToolMarginStyle() {
    const { orderManagement, travelManagement } = this.props
    return orderManagement || travelManagement
  }

  renderSwitchActive = () => {
    const { showStop } = this.props
    if (!showStop) {
      return null
    }
    const arr = this.getStatus()
    // active filters 的数据格式为 [true, false] ｜ [true]
    const checked = arr?.length === 2

    return (
      <div className="show_switch">
        {i18n.get('显示已停用：')}
        <Switch size="small" checked={checked} onChange={this.handleActive} />
      </div>
    )
  }

  handleActive = checked => {
    const filters = this.filterParams?.filters || {}
    let id = this.props?.entityInfo?.id
    if (checked) {
      id && localStorage.setItem(id, true)
      filters['dataLink.active'] = [true, false]
    } else {
      id && localStorage.setItem(id, false)
      filters['dataLink.active'] = [true]
    }

    this.updateData({
      filters
    })
  }

  render() {
    let { columns, dataSource, total, isShowTripFilter } = this.state
    if (!columns.length) return null
    const { page, pageMode, filters } = this.filterParams
    let {
      size,
      bus,
      isSingleSelect = false,
      rowKey = 'dataLink.id',
      linkType,
      hiddenSearch,
      toolExtraChildren,
      isMultiSelect = true,
      showStop,
      toolExtraStyle = {},
      isBatchEdit,
      onCancelBatchCell,
      onSubmitBatchCell,
      onSelectedChange,
      selectedRowKeys,
      orderManagement,
      travelManagement,
      ...others
    } = this.props
    const { type, isManage } = this.props
    //isManage 代表从应用管理里面过来的，暂时只改了这个位置的样式（主要改动为表头固定），后续别的页面有需求逐步改动
    const paginationParams = {
      totalLength: total || 0,
      pagination: { size: page.pageSize, current: page.currentPage },
      scrollPagination: { size: page.pageSize, current: page.currentPage },
      pageMode: pageMode,
      onChange: this.handlePageChange,
      disabledScroll: true
    }

    return (
      <div
        className={styles[isManage ? 'simpleContainer_dataLink_manage' : 'simpleContainer']}
        style={this.props.styles}
      >
        {isShowTripFilter && this.renderTripFilter()}

        {linkType !== 'CHANPAY' && !hiddenSearch && (
          <div className="header" style={{ marginBottom: this.computedToolMarginStyle ? '11px' : '' }}>
            {newOrderManagementFeatbitFlag && (orderManagement || travelManagement) ? (
              <Search
                style={{ width: 200 }}
                allowClear
                onSearch={this.handleSearch}
                onClear={this.handleClearSearch}
                placeholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
              />
            ) : (
              <DataGrid.Search
                className="search-wrapper"
                placeholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
                onSearch={this.handleSearch}
                onClear={this.handleClearSearch}
              />
            )}

            {/***订单管理工具栏右侧添加配置入口... */}
            <div className="header-extra-wrapper" style={toolExtraStyle}>
              {toolExtraChildren}
              {Boolean(isBatchEdit) && (
                <div>
                  <Button className="mr-12 ml-12" onClick={onCancelBatchCell}>
                    取消
                  </Button>
                  <Button type="primary" onClick={onSubmitBatchCell}>
                    保存
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="body">
          <DataGrid.TableWrapper
            {...others}
            bus={bus}
            rowKey={rowKey}
            selectedRowKeys={selectedRowKeys ?? []}
            onSelectedChange={onSelectedChange ?? (() => void 0)}
            filters={filters}
            scrolling={{ mode: 'virtual' }}
            standard={Boolean(isBatchEdit)} // 批量编辑态不能滚动加载了，不然会重绘编辑内容
            isMultiSelect={isMultiSelect}
            className={isManage ? styles.tableWrapper : styles['table-min-height']}
            onFilterChange={this.handleFilterChange}
            onSorterChange={this.handleSorterChange}
            onRowClick={this.handleRowClick}
            columns={columns}
            searchPlaceholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
            dataSource={dataSource}
            disabledSwitcher={true}
            isSingleSelect={isSingleSelect}
            columnMinWidth={160}
            allowColumnReordering
            allowColumnResizing
            fixedSelect
            rowClassName={row => {
              const rowKeyValue = get(dataSource[row], rowKey)
              const topDate = get(dataSource[row], 'dataLink.topDate')
              if (window.PLATFORMINFO?.dataTopFunctionActive && !isManage && !!topDate) {
                //我的首页下 业务对象表格增加置顶样式
                if (isMultiSelect) {
                  return i18n?.currentLocale === 'en-US'
                    ? 'hascheck_table_row_set_top_en'
                    : 'hascheck_table_row_set_top'
                } else {
                  return i18n?.currentLocale === 'en-US' ? 'table_row_set_top_en' : 'table_row_set_top'
                }
              }
              return 'table_row_tr_item' + rowKeyValue
            }}
          />
        </div>
        <div className="pagination">
          {this.renderSwitchActive()}
          {newOrderManagementFeatbitFlag && orderManagement ? (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Pagination
                showSizeChanger
                showQuickJumper
                total={paginationParams?.totalLength}
                showTotal={total => i18n.get('共 {__k0} 条', { __k0: total })}
                pageSize={paginationParams?.pagination?.size}
                current={paginationParams?.pagination?.current}
                onChange={this.onEuiPaginationChange}
              />
            </div>
          ) : (
            <DataGrid.Pagination {...paginationParams} />
          )}
        </div>
      </div>
    )
  }
}
