.simpleContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 100%;

  :global {
    .body {
      flex: 1;
      overflow: hidden;
      height: 100%;
      position: relative;
      border-top: 1px solid #e6e6e6;
      z-index: 1;

      .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-rowsview tr.dx-row.dx-selection td {
        background-color: var(--eui-primary-pri-50);
      }

      .dx-scrollable-wrapper {
        .hascheck_table_row_set_top {
          position: relative;

          td:nth-child(2) {
            padding-left: 34px !important;
          }

          td:nth-child(2)::before {
            content: '置顶';
            background: #f5f5f5;
            padding: 1px 2px;
            font-size: 12px;
            position: absolute;
            left: 2px;
            margin-right: 10px;
            border-radius: 4px;
            opacity: 0.6;
          }
        }

        .hascheck_table_row_set_top_en {
          position: relative;

          td:nth-child(2) {
            padding-left: 34px !important;
          }

          td:nth-child(2)::before {
            content: 'Top';
            background: #f5f5f5;
            padding: 1px 2px;
            font-size: 12px;
            position: absolute;
            left: 2px;
            margin-right: 10px;
            border-radius: 4px;
            opacity: 0.6;
          }
        }

        .table_row_set_top {
          position: relative;

          td:nth-child(1) {
            padding-left: 34px !important;
          }

          td:nth-child(1)::before {
            content: '置顶';
            background: #f5f5f5;
            padding: 1px 2px;
            font-size: 12px;
            position: absolute;
            left: 2px;
            margin-right: 10px;
            border-radius: 4px;
            opacity: 0.6;
          }
        }

        .table_row_set_top_en {
          position: relative;

          td:nth-child(1) {
            padding-left: 34px !important;
          }

          td:nth-child(1)::before {
            content: 'Top';
            background: #f5f5f5;
            padding: 1px 2px;
            font-size: 12px;
            position: absolute;
            left: 2px;
            margin-right: 10px;
            border-radius: 4px;
            opacity: 0.6;
          }
        }
      }
    }

    .header {
      :global {
        .search-wrapper {
          right: 297px;
          top: 5px;
        }

        .header-extra-wrapper {
          position: absolute;
          right: 0;
          display: flex;
          top: -4px;
        }

        .show_switch {
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
      }
    }

    .pagination {
      height: 32px;
      flex-shrink: 0;
      padding: 0 18px;

      .show_switch {
        float: left;
      }
    }
  }
}

.simpleContainer_dataLink_manage {
  display: flex;
  width: 100%;
  flex: 1;
  flex-direction: column;
  overflow: hidden;

  :global {
    .body {
      flex: 1;
      overflow: hidden;
      position: relative;
      border-top: 1px solid #e6e6e6;
      z-index: 1;
    }

    .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-rowsview tr.dx-row.dx-selection td{
      background-color: var(--eui-primary-pri-50);
    }

    .header {
      position: relative;

      :global {
        .search-wrapper {
          left: 0;
          right: auto;
          height: 28px;
          top: -6px;

          &>div {
            height: auto;
            top: 14px;
          }
        }

        .header-extra-wrapper {
          position: absolute;
          align-items: center;
          right: 0;
          display: flex;
          top: -4px;
        }

        .show_switch {
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
      }

      height: 40px;
      line-height: 40px;
    }

    .pagination {
      height: 20px;
      flex-shrink: 0;
      padding: 0 18px;

      .show_switch {
        float: left;
      }
    }
  }
}

.tableWrapper {
  height: calc(100% - 16px);

  :global {
    .dx-datagrid-rowsview.dx-scrollable {
      overflow: auto;
    }

    .ant-form-item-label {
      display: none;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .table_row_has_error {
      background-color: var(--eui-function-danger-50);
    }
  }
}

.table-min-height {
  min-height: 300px;
}