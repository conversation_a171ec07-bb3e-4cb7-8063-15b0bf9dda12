/**
 *  Created by <PERSON><PERSON> on 2018/5/30 下午3:01.
 */
import { get } from 'lodash'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { getNumberColor } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { EntityFieldIF } from '@ekuaibao/lib/lib/entityUtil/EntityTypes'
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select'
import { fixBaseData, fixMoney, fixPayeeInfo, fixSwitch, SwitcherData, parseColumnFilter } from '../data-grid/columnOthers'
import fetchFixer from '../data-grid/fetchFixer'
import PersonnelList from '../puppet/personnelList/PersonnelList'
import { Icon } from 'antd'
import { Tooltip } from '@hose/eui'
import { fnShouldShowWarning } from '../../components/utils/fnFormartDatalinkData'
import DataLinkUrlText from './DataLinkUrlText'
import DataLinkCode from './DataLinkCode'
import { getDisplayName, getStaffShowByConfig } from "../utilFn";
import { fixEntityObj } from '../../lib/utils'
import { isString } from '@ekuaibao/helpers'
import { T } from '@ekuaibao/i18n'
import StaffFilter from '../data-grid/StaffFilter'
import moment from 'moment'

interface ParseColumnIF {
  fields: any
  bus?: any
  action?: any
  otherColumns?: Function
  path: PathIF
  useCountCanLink?: boolean
  platformType?: string
  entityInfoMap: any
  entityInfo: any
  linkType?: string
  from?: string
  moneyTextAlign?: string //money类型组件展示在左边还是右边
  useNewStyle?: boolean //text是否显示一行...
}

interface PathIF {
  [key: string]: string
}

export function parseColumns(param: ParseColumnIF) {
  const {
    bus,
    action,
    fields,
    otherColumns,
    path,
    useCountCanLink,
    platformType,
    entityInfoMap,
    entityInfo,
    linkType,
    from,
    useNewStyle,
    moneyTextAlign
  } = param
  const newFields: EntityFieldIF[] = []
  let fieldMap = {}
  if (fields && path) {
    fieldMap = parseFieldMap(fields, path)
  }
  fields.forEach((f: EntityFieldIF) => {
    if (!(f.type === 'list' && platformType === 'PRIVATE_CAR')) {
      newFields.push(f)
    }
  })

  if (platformType === 'TRAVEL_MANAGEMENT') {
    const activeField = {
      name: "active",
      label: "可用状态",
      type: "switcher",
      source: "dataLink",
      optional: false,
      defaultValue: null,
      formula: false,
      index: false,
      systemField: false,
      childrenOptional: {},
      calculation: {
        dependencies: [],
        dependenciesBy: [],
        order: -1
      }
    }
    const idx = fields.findIndex(i => i.label === '订购状态')
    fields.splice(idx, 0, activeField)
  }
  const columns = parseToColumn({
    fields: fields,
    fixedCount: 0,
    otherColumns: otherColumns || undefined,
    action: action ? action : undefined,
    bus: bus,
    entityInfoMap: entityInfoMap,
    useCountCanLink,
    platformType,
    entityInfo,
    linkType,
    from,
    useNewStyle,
    moneyTextAlign
  })
  return { columns, fieldMap }
}

export function parseFieldMap(fields: EntityFieldIF[], path: any) {
  const fieldMap = {} as any
  fields.forEach((f: EntityFieldIF) => {
    if (f.name === 'entityId') {
      fieldMap[f.name] = {
        ...f,
        dataType: { type: f.type }
      }
    } else {
      fieldMap[`${path[f.name]}`] = {
        ...f,
        dataType: { type: f.type }
      }
    }
  })
  return fieldMap
}

function parseToColumn({
  fields,
  fixedCount,
  otherColumns,
  action,
  bus,
  useCountCanLink,
  platformType,
  entityInfoMap,
  entityInfo,
  linkType,
  from,
  useNewStyle,
  moneyTextAlign
}) {
  const columns = createColumns({
    fields,
    fixedCount,
    bus,
    useCountCanLink,
    platformType,
    entityInfoMap,
    entityInfo,
    linkType,
    from,
    useNewStyle,
    moneyTextAlign
  })
  if (otherColumns) {
    columns.push(otherColumns(bus, columns.length))
  }
  if (action) {
    columns.push(action(bus, columns.length))
  }
  return columns
}

export function createColumns({
  fields,
  fixedCount,
  bus,
  useCountCanLink,
  platformType,
  entityInfoMap,
  entityInfo,
  linkType,
  from,
  useNewStyle,
  moneyTextAlign
}) {
  return fields.map((line, index, arr) => {
    return parseColumn({
      property: line,
      index,
      fixedCount,
      bus,
      length: arr.length,
      isSingleSelect: false,
      useCountCanLink,
      platformType,
      entityInfoMap,
      entityInfo,
      linkType,
      from,
      useNewStyle,
      moneyTextAlign
    })
  })
}

export function parseData(dataSource) {
  return dataSource.map(line => {
    return {
      ...line,
      key: line.id,
      id: line.id,
      platformId: line.platformId,
      active: line.active,
      dataLinkCount: { useCount: line.useCount, totalCount: line.totalCount }
    }
  })
}

const hanldeOnClick = (e, value, entityInfo) => {
  e && e.stopPropagation && e.stopPropagation()
  e && e.preventDefault && e.preventDefault()
  if (value) {
    const type = get(value, 'data.dataLink.entity.type', '')
    if (type === 'CORPORATEBANKING_CONTRAT') {
      // 合同的通过 keel 打开
      const data = get(value, 'data', {})
      // api.emit('open:ContractDetail:by:keel', dataLink)
      api.open('@business-to-business:ContractDetailDrawer', { data })
    } else {
      const id = get(value, 'data.dataLink.id')
      api.open('@bills:DataLinkDetailModal', {
        entityInfo: { dataLink: { id: id } },
        showClose: true,
        viewKey: 'DataLinkDetailModal'
      })
    }
  }
}
function parseColumn({
  property,
  index,
  fixedCount,
  bus,
  isSingleSelect,
  useCountCanLink,
  length,
  platformType,
  entityInfoMap,
  entityInfo,
  linkType,
  from,
  useNewStyle,
  moneyTextAlign
}: any) {
  const { label, enLabel, type, fixed, entity } = property

  const dataIndex = parseDataIndex(property)
  const filterOptions = parseColumnFilter({ dataType: { entity, type } })
  const others = parseMeta2ColumnOthers({
    type,
    property,
    bus,
    isSingleSelect,
    useCountCanLink,
    platformType,
    entityInfoMap,
    entityInfo,
    linkType,
    from,
    useNewStyle,
    moneyTextAlign
  }) as Object
  const mfixed = length <= 4 ? false : index < fixedCount
  const width = length <= 4 ? {} : { width: 200 }
  // 对于多语言bug 临时处理
  // 在 多语言文档中 有类似  "code": "代码：{code}", 这样的词条，如果词条名中code 会产生展示bug
  const EXCLUDE_I18N = ['code']
  const realLabel = (i18n.currentLocale === 'en-US' && enLabel) ? enLabel : label
  const showLabel = EXCLUDE_I18N.includes(realLabel) ? realLabel : i18n.get(realLabel)
  return {
    title: showLabel || label,
    dataIndex,
    dataType: type,
    filterType: entity?.startsWith('basedata.Enum') ? false : type,
    key: dataIndex,
    sorter: true,
    label: showLabel || label,
    value: dataIndex,
    property,
    ...width,
    className: 'fs-14',
    fixed: !!fixed ? fixed : mfixed,
    ...others,
    ...filterOptions
  }
}

function parseDataIndex(property) {
  const { source = 'form', name } = property
  if (source === 'planned') {
    const array = name.split('_')
    const suffix = array[array.length - 1].toUpperCase()
    return `${source}.${name}.${suffix}`
  }
  return `${source}.${name}`
}

export function parseMeta2ColumnOthers({
  type,
  property,
  bus,
  isSingleSelect,
  useCountCanLink,
  platformType,
  entityInfoMap,
  entityInfo,
  linkType,
  from,
  moneyTextAlign = 'right', //money类型对齐方式，之前用的是右对齐，但是有的时候显示左对齐，加一个参数控制吧
  useNewStyle //这个地方主要控制的是超长...，true的话就会显示出...因为之前默认是显示五行还是几行...的，新表格一般都是一行...
}: any) {
  if (type === 'text' && property.name.endsWith('原始单据')) {
    // 原始单据实际类型为text类型，存的是单据的id，但是实际返回的是单据的对象，单独处理一下
    return fixBill()
  }
  if (type === 'ref' && property?.entity === 'basedata.Enum.currency') {
    return fixCurrency(property.name)
  }
  if (type === 'text') {
    return fixText({ ...property, from, entityInfo, useNewStyle })
  }

  if (type === 'money') {
    return fixMoney({ valueSize: 14, canFilter: property.source !== 'planned', moneyTextAlign })
  }

  if (type === 'active' || property.name === 'active') {
    return fixActive(property, isSingleSelect, platformType)
  }

  if (type === 'switcher') {
    return fixSwitch(SwitcherData)
  }

  if (type === 'dataLinkCount' || property.name === 'useCount') {
    return fixDataLinkCount(property, bus, isSingleSelect, useCountCanLink)
  }

  if (type === 'location') {
    return fixLocation()
  }
  if (type === 'duration') {
    return fixDuration()
  }
  // TODO staffComponent
  if (type === 'staffFZ' || type === 'staffCYR') {
    return fixStaff(type, bus, property, platformType)
  }
  if (type === 'list' && get(property, 'elemType.entity', '').startsWith('datalink.DataLinkEntity')) {
    return fixDataLinks(entityInfo)
  }
  // 收款信息(多选)
  if (type === 'list' && property.elemType && property.elemType.entity === 'pay.PayeeInfo') {
    return fixMutilPayeeInfo(bus, platformType)
  }
  if (type === 'list') {
    return fixList(property)
  }
  if (type === 'number') {
    return fixNumber(property)
  }
  if (type === 'dataLinkType') {
    return fixDataLinkType(entityInfoMap, from)
  }
  if (type === 'settlementOpportunity') {
    return fixSettlementOpportunity()
  }
  if (type === 'settlementPeriod') {
    return fixSettlementPeriod()
  }
  if (type === 'date') {
    return fixDate(property)
  }
  if (type === 'dateRange') {
    return fixDateRange(property)
  }

  // 收款信息
  if (type === 'ref') {
    if (property.entity.startsWith('pay.PayeeInfo')) {
      return fixPayeeInfo()
    } else if (property.entity.startsWith('organization.Staff')) {
      return fixStaffName()
    } else if (property.entity.startsWith('datalink.DataLinkEntity')) {
      // 业务对象
      return fixDataLink(entityInfo)
    } else if (property.entity.startsWith('basedata.Dimension')) {
      // 自定义档案
      return fixBaseData()
    } else if (property.entity.startsWith('basedata.city')) {
      // 城市
      return fixCity()
    } else {
      return fixRefOther() //其余的走这个地方
    }
  }

  if (property.label === '单号' && linkType === 'CHANPAY') {
    // @i18n-ignore
    // 针对银企联流水表格中的「单号」
    return fixDocumentNo(property.label)
  }

  return {
    render: (value: string) => {
      return (
        <Tooltip placement="topLeft" title={value}>
          <span>{value}</span>
        </Tooltip>
      )
    }
  }
}

const opportunityFn = api.invokeServiceAsLazyValue('@supplier-file:get:supplier:enums')
function fixSettlementOpportunity() {
  const { opportunity } = opportunityFn()
  return {
    render(val: string) {
      const settleType = opportunity.find(el => el.name === val)
      return settleType ? settleType.label : '-'
    },
    filterType: 'list',
    lookup: {
      dataSource: opportunity,
      displayExpr: 'label',
      valueExpr: 'name'
    }
  }
}

const periodFn = api.invokeServiceAsLazyValue('@supplier-file:get:supplier:enums')
function fixSettlementPeriod() {
  const { period } = periodFn()
  return {
    render(val: string) {
      const settleTime = period.find(el => el.name === val)
      return settleTime ? settleTime.label : '-'
    },
    filterType: 'list',
    lookup: {
      dataSource: period,
      displayExpr: 'label',
      valueExpr: 'name'
    }
  }
}

function fixMutilPayeeInfo(bus, platformType) {
  return {
    width: 350,
    render(payeeInfo, record) {
      if (!(Array.isArray(payeeInfo) && payeeInfo.length)) {
        return <NullCell />
      }
      const item = payeeInfo[0]
      const { name, bank, branch, cardNo, icon, type } = item
      const bankName = bank || branch
      const accountType = type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')
      const cardNoLable = ` **** ${cardNo.substring(cardNo.length - 4, cardNo.length)}`
      return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div
            className="bank-card"
            style={{
              background: '#fff',
              border: '1px solid rgba(29,43,61,0.09)',
              borderRadius: '4px',
              padding: '8px'
            }}
          >
            <div
              className="bank-title"
              style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}
            >
              <div
                className="text-nowrap-ellipsis"
                style={{ fontWeight: 600, color: 'rgba(29,43,61,1)', width: '160px' }}
              >
                {name}
              </div>
              <div style={{ fontSize: '12px', color: 'rgba(29,43,61,0.4)' }}>{accountType}</div>
            </div>
            <div className="bank-body" style={{ display: 'flex' }}>
              {icon && <img className="bank-icon-img" src={icon} alt="" />}
              <div className="bank-name text-nowrap-ellipsis" style={{ width: '150px' }}>
                {bankName}
              </div>
              <div className="bank-cardNo">{cardNoLable}</div>
            </div>
          </div>
          <span
            className="bank-more"
            style={{ color: 'rgba(0,107,224,1)', cursor: 'pointer', padding: '8px 16px' }}
            onClick={() =>
              bus.emit('edit:table:supplier', platformType === 'SUPPLIER' ? record : get(record, 'dataLink'))
            }
          >
            {i18n.get('更多')}
          </span>
        </div>
      )
    }
  }
}
export function fixDate(props) {
  return {
    render(value) {
      if (!value) {
        return <NullCell />
      }
      const withTime = props?.withTime
      const format = withTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD'
      value = moment(value).format(format)
      if (value === 'Invalid date') {
        value = '-'
      }
      return <span>{value}</span>
    }
  }
}

export function fixDateRange(props: any) {
  return {
    render(value: any) {
      if (!value || (typeof value === 'string' && !value.length)) {
        return <NullCell />
      }
      let start: any = 0
      let end: any = 0
      if (Array.isArray(value)) {
        start = value[0]
        end = value[1]
      } else if (typeof value === 'object') {
        start = value.start
        end = value.end
      }

      if (!start || !end) {
        return <NullCell />
      }
      const withTime = props?.withTime
      const format = withTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD'
      start = moment(start).format(format)
      end = moment(end).format(format)
      const text = `${start} ~ ${end}`
      return (
        <Tooltip placement="topLeft" title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    }
  }
}
function fixDataLinkType(entityInfoMap, from) {
  return {
    filterType: undefined,
    render(text: string, record: any) {
      if (!text) {
        return <span>{'-'}</span>
      }
      let value = entityInfoMap[text]
      if (from) {
        value = value || get(record, 'dataLink.entity.name')
      }
      return <span>{value ? value : '-'}</span>
    }
  }
}
function getTitleByTem(value: any) {
  if (!value) {
    return { title: '-' }
  }
  const dataLink = value?.data?.dataLink ?? {}
  let title = ''
  let code = ''
  Object.keys(dataLink).forEach(key => {
    if (key.endsWith('_name')) {
      title = dataLink[key]
    }
    if (key.endsWith('_code')) {
      code = dataLink[key]
    }
  })
  //title 有值时返回值，没有返回-
  return title === '-' || code === '-' || !title || !code ? { title: title || '-', code: '-' } : { title, code }
}

export function fixDataLink(entityInfo) {
  return {
    render(value: any) {
      const data: any = getTitleByTem(value)
      const { title, code } = data
      return title === '-' || code === '-' ? (
        <span>{title}</span>
      ) : (
        <Tooltip placement="topLeft" title={code}>
          <a onClick={(e: any) => hanldeOnClick(e, value, entityInfo)}>{title}</a>
        </Tooltip>
      )
    }
  }
}
export function fixDataLinks(entityInfo) {
  return {
    render(value: any) {
      if (value && Array.isArray(value)) {
        const maps = value.map((item: any, index: any) => {
          const data: any = getTitleByTem(item)
          const { title, code } = data
          return title === '-' || code === '-' ? (
            <span>{title}</span>
          ) : (
            <Tooltip key={index} placement="topLeft" title={code}>
              <a style={{ marginRight: 8 }} onClick={(e: any) => hanldeOnClick(e, item, entityInfo)}>
                {title}
              </a>
            </Tooltip>
          )
        })
        return <div style={{ whiteSpace: 'normal', wordBreak: 'normal' }}>{maps}</div> //这个业务对象多选一直都是换行的，没有办法...展示，因为每个业务对象上面都有一个Tooltip
      } else {
        return <NullCell />
      }
    }
  }
}
export function fixCity() {
  return {
    render(text) {
      if (!text) {
        return <span>{'-'}</span>
      }
      const arr = JSON.parse(text)
      const label = arr.map(line => i18n.currentLocale === 'en-US' ? line?.enLabel || line?.label : line?.label).join(',')
      return (
        <Tooltip placement="topLeft" title={label}>
          <span>{label}</span>
        </Tooltip>
      )
    }
  }
}
export function fixRefOther() {
  return {
    render(value) {
      if (!value) {
        return <NullCell />
      }
      if (typeof value === 'object') {
        value = getDisplayName(value)
      }
      if (value) {
        return (
          <Tooltip placement="topLeft" title={value}>
            <span>{value}</span>
          </Tooltip>
        )
      }
      return <NullCell />
    }
  }
}
export function fixList(property) {
  const {
    elemType: { type, entity }
  } = property
  if (type === 'ref' && entity === 'organization.Staff') {
    return {
      filterType: 'ref',
      render(value) {
        const staffs = value
          ?.filter(i => i)
          ?.map(item => getStaffShowByConfig(item))
          .join(',')
        if (staffs) {
          return (
            <Tooltip placement="topLeft" title={staffs}>
              <span>{staffs}</span>
            </Tooltip>
          )
        }
        return <NullCell />
      }
    }
  } else if (type === 'ref' && entity.startsWith('basedata.Dimension.')) {
    return {
      filterType: 'ref',
      render(value) {
        const dimension = value
          ?.filter(i => i)
          ?.map(item => `${item.name}(${item.code})`)
          ?.join(',')
        if (dimension) {
          return (
            <Tooltip placement="topLeft" title={dimension}>
              <span>{dimension}</span>
            </Tooltip>
          )
        }
        return <NullCell />
      }
    }
  } else if (type === 'location') {
    return {
      render(value) {
        if (value && value.length) {
          const text = value
            .filter(i => i)
            .map(v => v.name ? `${v.name}(${v.address})` : v.address)
            .join('/')
          return (
            <Tooltip placement="topLeft" title={text}>
              <span>{text}</span>
            </Tooltip>
          )
        }
        return <NullCell />
      }
    }
  } else {
    return {
      render(value) {
        return <span>{i18n.get('无法解析')}</span>
      }
    }
  }
}

export function parseOptions({
  options,
  isSingleSelect,
  entityInfo = {},
  fieldMap,
  dataLinkList,
  isRemuneration,
  flowId,
  isSelectOwner,
  type = 'DATALINK',
  expenseCodeList,
  otherParams,
  newFilter = true,
  isIgnoreSearchTextUpperOrLower = false
}: any) {
  let param = Object.assign({}, options)
  param = fetchFixer(param, { ...fieldMap, 'form.dataLinkCount': { name: 'form.dataLinkCount' } }, newFilter)

  let { filters = {}, orderManagement, formdataQuery } = param

  const { searchText } = param
  if (isSingleSelect && param.filters && !param.filters.active) {
    filters = { ...filters, active: true }
  }
  param.filters = filters
  const { fields, type: entityInfoType } = entityInfo as any
  let columns = []
  if (searchText && searchText.length) {
    // 业务对象模糊查询，只查name，code
    if (isIgnoreSearchTextUpperOrLower) {
      columns = ['name', 'code']
    } else {
      if (isRemuneration) {
        // @i18n-ignore
        columns = fields?.filter((f: any) => f?.name?.indexOf('_name') > -1 || f?.name?.indexOf('_工号证件号') > -1)
      } else {
        columns = fields?.slice().filter((f: any) => f?.name?.indexOf('_name') > -1 || f?.name?.indexOf('_code') > -1)
      }
      columns = columns?.map((f: any) => `form.${f?.name}`)
    }

  }

  param.ignoreUpperOrLower = isIgnoreSearchTextUpperOrLower
  let query = parseQuery2Select(param, undefined, columns, dataLinkList, flowId, fieldMap)
  if (type === 'PRIVATE_CAR' || type === 'TRAVEL_MANAGEMENT') {
    query.orderBy('createTime', 'DESC')
  }
  if (orderManagement) {
    formdataQuery && constructorOrderManageQuery(query, formdataQuery)
  }
  if (entityInfoType === 'ORDER' && type === 'TRAVEL_MANAGEMENT') {
    const entityId = entityInfo.id
    const filterCode =
      expenseCodeList && expenseCodeList.length
        ? //@i18n-ignore
        expenseCodeList.map((item: string) => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item}")`).join(' || ')
        : ''
    if (filterCode) {
      query.filterBy(filterCode)
    }
  }
  if (isSelectOwner) {
    query = query.select(`ownerId(...),...`)
  }
  if (otherParams?.filters?.length) {
    if (Array.isArray(otherParams.filters)) {
      otherParams.filters.forEach(filter => {
        query.filterBy(filter)
      })
    } else if (isString(otherParams?.filters)) {
      query.filterBy(otherParams?.filters)
    }
  }
  query = query.value()
  return query
}

const constructorOrderManageQuery = (query, formdataQuery) => {
  formdataQuery &&
    Object.keys(formdataQuery).forEach(e => {
      if (e && e != undefined) {
        query.filterBy(`${e}.containsIgnoreCase("${formdataQuery[e]}")`)
      }
    })
}

const filterDataSource = [
  { label: <T name="已启用" />, value: 'true' },
  { label: <T name="已停用" />, value: 'false' }
]

const filterDataSourceT = [
  { label: <T name="可用" />, value: 'true' },
  { label: <T name="不可用" />, value: 'false' }
]

const activeLookupT = {
  dataSource: filterDataSourceT,
  displayExpr: 'label',
  valueExpr: 'value'
}

const activeLookup = {
  dataSource: filterDataSource,
  displayExpr: 'label',
  valueExpr: 'value'
}
function fixActive(_property, isSingleSelect, platformType) {
  return {
    filterType: isSingleSelect ? undefined : 'list',
    filterDataSource: platformType === 'TRAVEL_MANAGEMENT' ? filterDataSourceT : filterDataSource,
    lookup: platformType === 'TRAVEL_MANAGEMENT' ? activeLookupT : activeLookup,
    render(active: boolean) {
      if (platformType === 'TRAVEL_MANAGEMENT') {
        return active ? i18n.get('可用') : i18n.get('不可用')
      }
      const backgroundColor = active ? 'var(--eui-function-info-500)' : 'var(--eui-decorative-neu-500)' //设计老师要求改成这个颜色
      const styles = { width: 6, height: 6, backgroundColor, marginRight: 8, borderRadius: 4 }
      const text = active === undefined ? '-' : i18n.get('已停用')
      return active ? (
        <div className="option-line">
          <span style={styles} />
          {i18n.get('启用中')}
        </div>
      ) : (
        <div className="option-line">
          {active === undefined ? null : <span style={styles} />}
          {text}
        </div>
      )
    }
  }
}

function fixLocation() {
  return {
    filterType: undefined,
    render(location) {
      if (!location) {
        return '-'
      }
      const { name, address } = location as any
      return <span>{name ? `${name}(${address})` : address}</span>
    }
  }
}
function fixBill() {
  return {
    filterType: undefined,
    render(bill) {
      const value = typeof bill === 'object' ? get(bill, 'form.title', '-') : bill ? bill : '-'
      return <span>{value}</span>
    }
  }
}

/**
 * 业务对象文本字段增加链接支持
 * propertyName: 字段名；用来判断是否在业务对象中使用方法
 * @returns textValue: 提供给Tooltip的值; domValue: 用于展示的dom元素}}
 * value中包含【"link"】时，对字符串进行解析
 * 业务对象的name和code不做处理
 * 文本标签取解析结果中的【name】,没有值时取【link】
 * 跳转链接取解析结果中的【link】
 */
export const formatLinkText = (value: string, propertyName?: string) => {
  let linkObj
  let shouldNotOpenLink = !!propertyName
  if (shouldNotOpenLink) {
    const n = String(propertyName).split('_')[2]
    shouldNotOpenLink = ['name', 'code'].includes(n)
  }
  const needOpenLink = !shouldNotOpenLink && String(value).includes('"link"')
  if (needOpenLink) {
    try {
      linkObj = JSON.parse(value)
    } catch (e) {
      // console.log('error:',e)
    }
  }

  let fnOnClick = null
  if (linkObj?.link) {
    fnOnClick = async () => {
      const url = await checkBaiwangPreviewUrl(linkObj.link)
      api.emit('@vendor:open:link', url)
    }
  }

  const textValue = value ? (linkObj && linkObj.link ? (linkObj.name ? linkObj.name : linkObj.link) : value) : '-'

  const domValue = fnOnClick ? DataLinkUrlText(textValue, fnOnClick) : textValue

  return { textValue, domValue, isRedirectText: !!fnOnClick }
}

/**
 * 通过url重新获取百旺预览链接
 */
export const checkBaiwangPreviewUrl = async (url: string) => {
  // 判断是否在Group环境
  const inGroup = checkPlatformIsGroup()
  if (inGroup && url) {
    const res = await api.invokeService('@bills:get:baiwang:previewUrl', url)
    return res?.value
  }
  return url
}

/**
 * 判断是否在Group环境
 */
export const checkPlatformIsGroup = () => {
  const whiteList = ['Group-ekb', 'Group-mc', 'plugin', 'movie']
  return whiteList.includes(window.PLATFORMINFO?.platform)
}

/**
 * 通过EBot节点创建的台账，sourceId为源单据的flowId
 * 为编号字段提供跳转回单据详情的能力
 */
export const renderRedirectCode = (propertyName, data, value) => {
  // 判断是否在Group环境
  const inGroup = checkPlatformIsGroup()
  // 暂对本地化和Group环境提供该功能
  if (!IS_STANDALONE && !inGroup) {
    return null
  }
  const { dataLink, sourceId } = data
  if (sourceId && propertyName === `E_${dataLink.entityId}_code`) {
    return DataLinkCode(sourceId, value)
  }
  return null
}

const renderEveryStatus = (filterDataSourcePayStatus, from?) => {
  if (from === 'MyContract') {
    return { sorter: false, filterType: undefined }
  }
  return {
    filterType: 'list',
    filterDataSource: filterDataSourcePayStatus,
    lookup: {
      dataSource: filterDataSourcePayStatus,
      displayExpr: 'label',
      valueExpr: 'value'
    }
  }
}

enum StatusColor {
  '#A4D76E' = '#A4D76E', // 绿色
  '#CBCBCB' = '#CBCBCB', // 灰色
  'rgba(34, 178, 204, 0.36)' = 'rgba(34, 178, 204, 0.36)', // 浅蓝
  '#22B2CC' = '#22B2CC', // 深蓝
  '#F4664A' = '#F4664A', // 橘红
  '#FAAD14' = '#FAAD14' // 橘黄
}

const renderStatusStyle = value => {
  let backgroundColor
  switch (value) {
    case '已付款':
    case '全部发起':
    case '全部支付':
      backgroundColor = StatusColor['#22B2CC']
      break
    case '部分付款':
    case '部分发起':
    case '部分支付':
      backgroundColor = StatusColor['rgba(34, 178, 204, 0.36)']
      break
    case '未付款':
    case '未发起':
    case '未支付':
      backgroundColor = StatusColor['#CBCBCB']
      break
    case '超额付款':
    case '超额发起':
    case '超额支付':
      backgroundColor = StatusColor['#F4664A']
      break
    default:
      break
  }
  return { width: 6, height: 6, backgroundColor, marginRight: 8, borderRadius: 4 }
}

//  app.less文件在协助链接，企业微信某些情况下加载出现问题，导致不生效，样式错乱，补救一下
function renderDataLinkTableTextStyleNew(lineNumber = '5', useNewStyle: boolean) {
  const style = {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: !useNewStyle ? '-webkit-box' : 'block',
    '-webkitBoxOrient': 'vertical',
    '-webkitLineClamp': lineNumber
  }
  return style
}
const contractStateMap = {
  DRAFT: i18n.get('草稿'),
  REJECTED: i18n.get('驳回'),
  APPROVING: i18n.get('审批中'),
  EXCUTING: i18n.get('执行中'),
  CLOSE: i18n.get('关闭'),
  END: i18n.get('已完成')
}
const settleStateMap = {
  DRAFT: i18n.get('待执行'),
  PAYING: i18n.get('执行中'),
  PAID: i18n.get('已支付')
}
function fixText({ name, label, from, entityInfo, useNewStyle }) {
  const nameArr = name?.split('_') || []
  switch (nameArr[nameArr.length - 1]) {
    case '付款计划发起状态':
      return {
        render(value, record) {
          const styles = renderStatusStyle(value)
          return (
            <div className="option-line">
              <span style={styles} />
              {value ? i18n.get(value) : '-'}
            </div>
          )
        },
        ...renderEveryStatus([
          { label: i18n.get('未发起'), value: i18n.get('未发起') },
          { label: i18n.get('部分发起'), value: i18n.get('部分发起') },
          { label: i18n.get('全部发起'), value: i18n.get('全部发起') },
          { label: i18n.get('超额发起'), value: i18n.get('超额发起') }
        ])
      }
    case 'contractState': // 合同状态
      return {
        render(value, record) {
          return <div className="option-line">{contractStateMap[value] || '-'}</div>
        },
        ...renderEveryStatus(
          [
            { label: i18n.get('草稿'), value: 'DRAFT' },
            { label: i18n.get('驳回'), value: 'REJECTED' },
            { label: i18n.get('审批中'), value: 'APPROVING' },
            { label: i18n.get('执行中'), value: 'EXCUTING' },
            { label: i18n.get('关闭'), value: 'CLOSE' },
            { label: i18n.get('已完成'), value: 'END' }
          ],
          from
        )
      }
    case 'settleState': // 结算状态
      return {
        render(value, record) {
          return <div className="option-line">{settleStateMap[value] || '-'}</div>
        },
        ...renderEveryStatus([
          { label: i18n.get('待执行'), value: 'DRAFT' },
          { label: i18n.get('执行中'), value: 'PAYING' },
          { label: i18n.get('已支付'), value: 'PAID' }
        ])
      }
    case '付款计划支付状态':
      return {
        render(value, record) {
          const styles = renderStatusStyle(value)
          return (
            <div className="option-line">
              <span style={styles} />
              {value ? i18n.get(value) : '-'}
            </div>
          )
        },
        ...renderEveryStatus([
          { label: i18n.get('未支付'), value: i18n.get('未支付') },
          { label: i18n.get('部分支付'), value: i18n.get('部分支付') },
          { label: i18n.get('全部支付'), value: i18n.get('全部支付') },
          { label: i18n.get('超额支付'), value: i18n.get('超额支付') }
        ])
      }
    case '付款状态':
      return {
        render(value, record) {
          const styles = renderStatusStyle(value)
          return (
            <div className="option-line">
              <span style={styles} />
              {value ? i18n.get(value) : '-'}
            </div>
          )
        },
        ...renderEveryStatus([
          { label: i18n.get('未付款'), value: i18n.get('未付款') },
          { label: i18n.get('已付款'), value: i18n.get('已付款') },
          { label: i18n.get('部分付款'), value: i18n.get('部分付款') },
          { label: i18n.get('超额付款'), value: i18n.get('超额付款') }
        ])
      }
    case '是否管理员修改':
      return {
        render(value) {
          return (
            <span>
              {value === '1' ? '是' : '-'}
            </span>
          )
        },
      }
    default:
      return {
        filterType: 'text',
        render(value, line) {
          const data = value ? value : '-'
          if (window.IS_SMG && data && data.indexOf('NCC:') === 0) {
            const url = getSMGNCCTextURL(value)
            return <a onClick={() => api.emit('@vendor:open:link', url)}>查看合同</a>
          }
          const dom = renderRedirectCode(name, line, value)
          if (dom) {
            return dom
          }
          const { textValue, domValue } = formatLinkText(value, name)
          const textValueStyle = renderDataLinkTableTextStyleNew('5', useNewStyle)
          if (entityInfo?.type === 'CORPORATEBANKING_CONTRAT') {
            const dataLink = get(line, 'dataLink', {})
            const dataObj = fixEntityObj(dataLink)
            const contractState = get(dataObj, 'contractState', {})
            if (contractState?.id === 'COR_CONTRACT_REJECTED') {
              return (
                <span className="data-link-table-text-style-new" style={textValueStyle}>
                  {textValue}
                  <span style={{ color: 'var(--eui-function-danger-500)' }}>[{contractState?.name}]</span>
                </span>
              )
            }
          }

          if (value) {
            return (
              <Tooltip placement="topLeft" title={textValue}>
                <span className="data-link-table-text-style-new" style={textValueStyle}>
                  {domValue}
                </span>
              </Tooltip>
            )
          } else {
            return (
              <span className="data-link-table-text-style-new" style={textValueStyle}>
                {textValue}
              </span>
            )
          }
        }
      }
  }
}

function fixCurrency(propertyName) {
  let list = api.getState()['@common'].getCurrencyAll?.slice()

  return {
    filterType: 'list',
    filterDataSource: [],
    lookup: {
      dataSource: [],
      displayExpr: 'label',
      valueExpr: 'value'
    },
    render(value) {
      let name = list?.find(i => value == i?.numCode || value?.id === i?.numCode || value?.numCode === i?.numCode)
      if (name) {
        const text = `${name?.name} (${name?.strCode})`
        return (
          <Tooltip placement="topLeft" title={text}>
            <span>{text}</span>
          </Tooltip>
        )
      } else {
        return <NullCell />
      }
    }
  }
}
export function fixCurrencyList(value) {
  let list = api.getState()['@common'].getCurrencyAll?.slice()

  let name = list?.find(i => value == i?.numCode || value?.id === i?.numCode || value?.numCode === i?.numCode)
  if (name) {
    return `${name?.name} (${name?.strCode})`
  } else {
    return '-'
  }
}

function fixDuration() {
  return {
    filterType: undefined,
    render(duration) {
      const m = Math.ceil((duration % 3600000) / 60000)
      const h = Math.floor(duration / 3600000)
      if (h > 0) {
        return <div>{i18n.get(`{__k0}时{__k1}分`, { __k0: h, __k1: m })}</div>
      }
      return <div>{i18n.get(`{__k0}分`, { __k0: m })}</div>
    }
  }
}

function fixDataLinkCount(property, bus, isSingleSelect, useCountCanLink = true) {
  return {
    filterType: undefined,
    render(obj, line) {
      if (typeof line.dataLink.totalCount !== 'number') {
        return <NullCell />
      }
      return isSingleSelect || !useCountCanLink ? (
        <div className="option-line">
          <span className="ml-5">{`${line.dataLink.useCount} / ${line.dataLink.totalCount}`}</span>
        </div>
      ) : (
        <div className="option-line">
          <a className="ant-dropdown-link mr-5" onClick={e => bus.emit('table:row:useCount:click', line)}>
            {line.dataLink.useCount}
          </a>
          <span>{'/'}</span>
          <span className="ml-5">{line.dataLink.totalCount}</span>
        </div>
      )
    }
  }
}

export function generateList(obj) {
  let arr = []
  let staffList = []
  let roleList = []
  let departmentList = []
  staffList = obj ? obj.staff : []
  roleList = obj ? obj.role : []
  departmentList = obj ? obj.department : []
  staffList.forEach(item => (item.type = 'staff'))
  roleList.forEach(item => (item.type = 'role'))
  departmentList.forEach(item => (item.type = 'department'))
  arr = [...staffList, ...roleList, ...departmentList]
  return arr
}

function fixStaffName() {
  return {
    filterType: 'ref',
    render(obj: any) {
      if (obj) {
        const name = getStaffShowByConfig(obj)
        return (
          <Tooltip placement="topLeft" title={name}>
            <span>{name}</span>
          </Tooltip>
        )
      }
      return <NullCell />
    }
  }
}

function fixStaff(type, bus, property, platformType) {
  const { isHiddenEditBtn = false, isManage } = property

  return {
    filterType: type === "staffFZ" ? "custom" : undefined,
    filterStyles: {
      wrapperStyle: {
        border: 'none',
        backgroundColor: 'transparent'
      },
      bodyStyle: {
        padding: '0'
      }
    },
    hiddenFilterAction: true,
    renderFilter: props => <StaffFilter {...props} />,
    render(obj, line) {
      const { dataLink } = line
      const { ownerId } = dataLink
      let staffList = []
      let flag = false
      let str = ''
      if (type === 'staffCYR') {
        staffList = generateList(obj)
        const CYRStr = !staffList.length ? '-' : obj
        str = obj
          ? obj.fullVisible && platformType != 'supplierFile' && platformType != 'SUPPLIER'
            ? i18n.get('全部人员')
            : CYRStr
          : CYRStr
        flag = obj ? obj.fullVisible || !staffList.length : !staffList.length
      } else {
        staffList = obj ? [obj] : []
        const active = obj && obj.active
        flag = !active
        str = active ? '' : '-'
      }
      let isShowEdit = obj && !obj.fullVisible
      // 没有列的可见性没有ownerId
      const fieldVisibility = ownerId !== undefined

      if (!fieldVisibility || isHiddenEditBtn) {
        isShowEdit = false
      }

      if (platformType === 'supplierFile') {
        isShowEdit = true
      }

      if (type === 'staffFZ' && !isShowEdit && !staffList?.length && !isHiddenEditBtn) {
        isShowEdit = true
      }
      const strAll = i18n.get('全部参与人')
      const domStr = i18n.get('查看所有')

      // 负责人编辑状态
      // - 业务对象负责人是自己的时候，可以编辑负责人
      // - 业务对象负责人是自己的时候，可以编辑参与人
      // - 当可见性为全部的时候，不可编辑参与人
      // - 如果是从管理员入口进入，视为管理权限，当不为是全员可见，可以编辑参与人

      const { staff = {} } = api.getState()['@common'].userinfo

      isShowEdit = ownerId?.id === staff?.id

      if (type === 'staffCYR' && obj?.fullVisible) {
        isShowEdit = false
      }

      if (isManage && !obj?.fullVisible) {
        isShowEdit = true
      }

      if (window.__PLANTFORM__ === 'MC' && isHiddenEditBtn) {
        isShowEdit = false
      }

      return (
        <div className="option-line">
          {/* 批量编辑态，去掉编辑按钮 */}
          {isShowEdit && !bus?.isBatchEdit && bus?.has('table:row:edit:staff') && (
            <div
              style={{
                color: 'var(--brand-base)',
                fontSize: 14,
                flexShrink: 0,
                marginRight: 8,
                cursor: 'pointer'
              }}
              onClick={e => {
                e.stopPropagation()
                e.preventDefault()
                bus?.emit('table:row:edit:staff', { type, obj, line, property })
              }}
            >
              {i18n.get('编辑')}
            </div>
          )}
          <div>
            {flag ? (
              <div>{str}</div>
            ) : (
              <PersonnelList dataSource={staffList} moreLine={false} popUpTitle={strAll} domStr={domStr} />
            )}
          </div>
        </div>
      )
    }
  }
}

function fixNumber(property) {
  const { unit = '', source, name = '' } = property
  const canFilter = source !== 'planned'
  return {
    filterType: canFilter ? 'number' : undefined,
    sorter: canFilter,
    render(value, line) {
      if (!value && value !== 0) {
        return <NullCell />
      }
      const { planned } = line
      const plannedValue = get(planned, property.name)
      let cls
      if (plannedValue) {
        cls = fnShouldShowWarning(value, plannedValue) ? 'data_link_number_red_color' : 'datalink_item_value_color_nor'
      } else {
        cls = getNumberColor(name, value)
      }

      return <span className={cls}>{`${value}${unit}`}</span>
    }
  }
}

function NullCell() {
  return <span>-</span>
}

function fixDocumentNo(label: string) {
  return {
    title: (
      <span>
        {label}
        <Tooltip title={i18n.get('当前仅支持招商银行、建设银行的流水信息匹配单号。如有特殊需要，请联系易快报工作人员')}>
          <Icon style={{ fontSize: '14px', color: '#e39a47' }} type="question-circle-o" />
        </Tooltip>
      </span>
    )
  }
}

export function getActionText(disableStrategy, line) {
  let title = ''
  let text = ''
  switch (disableStrategy) {
    case 'MANUAL_ADMIN':
    case 'MANUAL':
      text = line.dataLink.active === undefined ? '-' : line.dataLink.active ? i18n.get('停用') : i18n.get('启用')
      title = i18n.get('状态')
      break
    case 'LIMIT_COUNT':
      text = line.dataLink.totalCount === undefined ? '-' : i18n.get('修改次数上限')
      title = i18n.get('引用次数')
      break
    case 'WRITTEN_OFF':
      text = i18n.get('核销')
      break
  }
  return { title, text }
}

export function getSMGNCCTextURL(data) {
  const code = api.getState('@common').userinfo?.staff?.code
  const url = data.split('NCC:')[1]
  return url.replace('%empcode%', code)
}