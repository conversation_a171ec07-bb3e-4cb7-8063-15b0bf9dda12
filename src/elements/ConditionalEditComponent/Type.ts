/**
 *  Created by gym on 2019-07-30 14:51.
 */

interface DataLinkFilterProps {
  layer: any
  value?: any[]
  entityInfo: EntityInfoInterface
  entityName: string
  sourceTypeFieldName: string
  templateFields: TempFieldsInterface[]
}

interface DataLinkFilterState {
  templateFieldList: TempFieldsInterface[] | []
}

interface DataLinkConditionalProps {
  value?: any[]
  onChange: (args?: any) => void
  entityInfo: EntityInfoInterface
  entityName: string
  templateFields: TempFieldsInterface[]
  sourceTypeFieldName: string
  globalfields?: any
  onUpdateValues: (values: any) => void
  blackList?: string[]
}

interface DataLinkConditionalState {
  conditionalData: any[]
  entityInfoList: any[]
  templateFieldValue: InterfaceMap
  dateSource: operatorValue[]
  baseSource: operatorValue[]
}

interface FieldInterface {
  hiddenLabel: boolean
  label: string
  name: string
  size: string
  type: string
  entityId?: string
  entityInfo: EntityInfoInterface
  entityName?: string
  templateFields?: TempFieldsInterface[]
}

interface operatorMap {
  [key: string]: operatorValue[]
}

interface operatorValue {
  label: string
  value: string
}

interface groupDateInterface {
  left: string
  operator: string
  right: groupInterface
}

interface groupInterface {
  type: string
  value: string
}

interface TempFieldsInterface {
  dataType: DataTypeInterface
  defaultValue: DataTypeInterface
  dependence: null
  editable: boolean
  field: string
  id: string
  label: string
  multiple: boolean
  optional: boolean
  placeholder: string
  priority: []
  selectRange: string
  showInDetails: boolean
  type: string
  children?: any
  copyType?: string
  name?: string
}

interface DataTypeInterface {
  type?: string
  entity?: string
}

interface EntityInfoInterface {
  fields?: FieldsInterface[]
  planned?: PlannedInterface[]
  id?: string
}

interface FieldsInterface {
  name: string
  label: string
  active: boolean
  ability?: string
  canAsDimension?: boolean
  dataType: DataTypeInterface
  id?: string
}

interface PlannedInterface {
  id: string
  version: number
  active: boolean
  createTime: number
  updateTime: number
  corporationId: string
  staffId: string
  dataLinkEntityId: string
  name: string
  showProgressBar: boolean
  plannedMoneyField: string
  occupyMoneyField: string
  controlType: string
}

interface PlanListInterface extends PlannedInterface {
  value: string
  label: string
  dataType: DataTypeInterface
  children: operatorValue[]
}

interface FormatFieldInterface {
  version?: number
  createTime?: number
  updateTime?: number
  corporationId?: string
  staffId?: string
  dataLinkEntityId?: string
  showProgressBar?: boolean
  plannedMoneyField?: string
  occupyMoneyField?: string
  controlType?: string
  value: string
  children?: operatorValue[]
  id?: string
  name: string
  label: string
  active?: boolean
  ability?: string
  canAsDimension?: boolean
  dataType: DataTypeInterface
  type?: string
}

interface InterfaceMap {
  [key: string]: any
}

interface InterfaceMate {
  active: boolean
  components: FormatFieldInterface
  configs: any[]
  createTime: number
  id: string
  name: string
  type: string
  updateTime: number
  version: number
  visibility: any
}

enum DataSourceType {
  CONSTANT = 'CONSTANT', // 给定值
  DATALINK_FIELD = 'DATALINK_FIELD',
  BILL_FIELD = 'BILL_FIELD',
  DYNAMIC = 'DYNAMIC', //动态范围
  RELATIVE_TODAY = 'RELATIVE_TODAY', //相对今天
  BILL_DETAIL_FIELD = 'BILL_DETAIL_FIELD'
}

const TupleInvoiceType = () => [
  {
    label: i18n.get('电子发票文件'),
    value: 'UPLOAD'
  },
  {
    label: i18n.get('手工录入'),
    value: 'QUERY'
  },
  {
    label: i18n.get('扫一扫录入'),
    value: 'SCAN'
  },
  {
    label: i18n.get('智能识票'),
    value: 'OCR'
  },
  {
    label: i18n.get('微信卡包导入'),
    value: 'WECHAT_CARD'
  },
  {
    label: i18n.get('支付宝卡包导入'),
    value: 'ALIPAY_CARD'
  },
  {
    label: i18n.get('爱发票导入'),
    value: 'AIFAPIAO'
  },
  {
    label: i18n.get('票池选择'),
    value: 'OUTPUT_OPEN_API'
  },
  {
    label: i18n.get('海外票据识别'),
    value: 'OVERSEAS_INVOICE'
  }
]

enum isNull {
  null = 'null',
  notNull = 'not null'
}

export {
  DataLinkFilterProps,
  DataLinkFilterState,
  DataLinkConditionalProps,
  DataLinkConditionalState,
  FieldInterface,
  EntityInfoInterface,
  operatorMap,
  operatorValue,
  groupDateInterface,
  TempFieldsInterface,
  FieldsInterface,
  PlannedInterface,
  PlanListInterface,
  FormatFieldInterface,
  DataTypeInterface,
  InterfaceMap,
  InterfaceMate,
  DataSourceType,
  TupleInvoiceType,
  isNull
}
