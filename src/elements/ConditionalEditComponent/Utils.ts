/**
 *  Created by gym on 2019-07-31 16:30.
 */
import {
  DataTypeInterface,
  EntityInfoInterface,
  FieldsInterface,
  FormatFieldInterface,
  InterfaceMap,
  operatorMap,
  PlanListInterface,
  PlannedInterface,
  TempFieldsInterface,
  isNull
} from './Type'
import { get, groupBy, flattenDeep, cloneDeep, flatMapDepth } from 'lodash'

const textFields = [
  { label: i18n.get('包含'), value: 'contains' },
  { label: i18n.get('不包含'), value: 'not contains' },
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('为空'), value: 'null' },
  { label: i18n.get('不为空'), value: 'not null' }
]

const numberFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('大于'), value: '>' },
  { label: i18n.get('小于'), value: '<' },
  { label: i18n.get('大于等于'), value: '>=' },
  { label: i18n.get('小于等于'), value: '<=' }
]

const dateFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('早于'), value: '<' },
  { label: i18n.get('不早于'), value: '>=' },
  { label: i18n.get('晚于'), value: '>' },
  { label: i18n.get('不晚于'), value: '<=' },
  { label: i18n.get('为空'), value: 'null' },
  { label: i18n.get('不为空'), value: 'not null' }
]

const dateRangeFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('早于'), value: '<' },
  { label: i18n.get('不早于'), value: '>=' },
  { label: i18n.get('晚于'), value: '>' },
  { label: i18n.get('不晚于'), value: '<=' },
  { label: i18n.get('属于'), value: 'in' },
  { label: i18n.get('不属于'), value: 'not in' },
  { label: i18n.get('为空'), value: 'null' },
  { label: i18n.get('不为空'), value: 'not null' }
]

export const dateRange = [
  { label: i18n.get('本周'), value: 'THIS_WEEK' },
  { label: i18n.get('上周'), value: 'LAST_WEEK' },
  { label: i18n.get('本月'), value: 'THIS_MONTH' },
  { label: i18n.get('上月'), value: 'LAST_MONTH' },
  { label: i18n.get('本年'), value: 'THIS_YEAR' },
  { label: i18n.get('去年'), value: 'LAST_YEAR' },
  { label: i18n.get('为空'), value: 'null' },
  { label: i18n.get('不为空'), value: 'not null' }
]

const switcherFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' }
]

const baseChildren = [
  { label: i18n.get('名称'), value: 'name' },
  { label: i18n.get('编码'), value: 'code' }
]

const moneyChildren = [{ label: i18n.get('本位币'), value: 'standard' }]

const plannedChildren = [
  { label: i18n.get('百分比'), value: 'percentage' },
  { label: i18n.get('余额'), value: 'balance' }
]

const staffChildren = [
  { label: i18n.get('姓名'), value: 'name' },
  { label: i18n.get('工号'), value: 'code' },
  { label: i18n.get('邮箱'), value: 'email' },
  { label: i18n.get('手机号'), value: 'cellphone' }
]

const payeeInfoChildren = [
  { label: i18n.get('开户行'), value: 'bank' },
  { label: i18n.get('开户网点'), value: 'branch' },
  { label: i18n.get('户名'), value: 'name' },
  { label: i18n.get('账号'), value: 'accountNo' }
]

export function getBaseSource(entityName: string, sourceTypeFieldName: string) {
  if (!sourceTypeFieldName) {
    return [
      { label: i18n.get('给定值'), value: 'CONSTANT' }
      // { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' }
    ]
  }
  return [
    { label: i18n.get('给定值'), value: 'CONSTANT' },
    { label: sourceTypeFieldName, value: 'BILL_FIELD' },
    { label: '明细字段', value: 'BILL_DETAIL_FIELD' }

    // { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' }
  ]
}

export function getDateSource(entityName: string, sourceTypeFieldName: string, blackList: string[]) {
  let sourceList = [
    { label: i18n.get('给定值'), value: 'CONSTANT' },
    { label: sourceTypeFieldName, value: 'BILL_FIELD' },
    // { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' },
    { label: '明细字段', value: 'BILL_DETAIL_FIELD' },
    { label: i18n.get('动态范围'), value: 'DYNAMIC' },
    { label: i18n.get('相对今天'), value: 'RELATIVE_TODAY' }
  ]
  if(blackList && blackList.length){
    sourceList = sourceList.filter(item => !blackList.includes(item.value))
  }
  return sourceList
}

export function initData(data, dataSource) {
  data.forEach(item => {
    if (item.children && !isMoneyItem(item.children)) {
      initData(item.children, dataSource)
    } else {
      dataSource.push(item)
    }
  })
}

function isMoneyItem(item) {
  const data = cloneDeep(item)
  if (data.length !== 1) {
    return false
  }
  const valueObj = data.pop()
  if (valueObj.value === 'standard') {
    return true
  }
  return false
}

export function getFieldsType(subjectData: FormatFieldInterface[], key: string): any {
  if (!key || !subjectData.length) {
    return ''
  }
  const arr = key.split('.')
  let keyStr = arr.pop()
  if (keyStr === 'standard') {
    keyStr = arr.pop()
  }
  const dataSource = []
  initData(cloneDeep(subjectData), dataSource)
  const field = dataSource.find(item => item.value === keyStr)
  let type = getType(field)
  if (type === 'money') {
    type = 'number'
  } else if (type === 'ref') {
    const entity = getDataEntity(field)
    type = entity.startsWith('basedata.Dimension.') || entity.startsWith('organization.Department') ? 'text' : type
  } else if (type === 'switcher') {
    return 'boolean'
  }
  return type
}

export const operatorDataMap: operatorMap = {
  text: textFields,
  autoNumber: textFields,
  number: numberFields,
  date: dateFields,
  dateRange: dateRangeFields,
  boolean: switcherFields
}

export function formatEntityList(data: EntityInfoInterface, isCascader: boolean) {
  if (!data) return []
  const { fields = [], planned = [] } = data
  let fieldList = fields.filter(
    oo => getDataEntity(oo) !== 'organization.Staff' && getDataEntity(oo) !== 'basedata.city'
  )
  if (planned && planned.length) {
    let planList: PlanListInterface[] | any = planned.map((line: PlannedInterface) => {
      return formatPlanned(line, isCascader)
    })
    fieldList = fieldList.concat(planList)
  }
  formatFieldList(fieldList)
  return fieldList
}

function formatFieldList(fieldList) {
  return fieldList.forEach((item, index) => {
    if (item.children) {
      formatFieldList(item.children)
    } else {
      fieldList[index] = formatFieldType(item)
    }
  })
}

function formatPlanned(line: PlannedInterface, isCascader: boolean) {
  let plannedField: FormatFieldInterface = {
    ...line,
    value: line.id,
    name: line.id,
    label: line.name,
    dataType: { type: 'number' }
  }
  plannedField.children = plannedChildren
  return plannedField
}

function formatFieldType(item: FieldsInterface) {
  let result: FormatFieldInterface = {
    ...item,
    value: item.name,
    label: item.label
  }
  const entity = getDataEntity(item)
  if (getType(item) && (entity.startsWith('basedata.Dimension.') || entity.startsWith('organization.Department'))) {
    result.children = baseChildren
  }
  if (getType(item) === 'money') {
    result.children = moneyChildren
  }
  return result
}

export function formatTempFieldValue(data: TempFieldsInterface[]): InterfaceMap {
  if (!data || !data.length) return []
  let fields = data
    .filter(item => item.type !== 'separator')
    .map(line => {
      let result: FormatFieldInterface | any = {
        ...line,
        value: line.field || line.name
      }
      if (getType(line) === 'ref' && !getDataEntity(line).startsWith('datalink.DataLinkEntity')) {
        result.children = getResultChildren(getDataEntity(line))
      }
      if (getType(line) === 'money') {
        result.children = moneyChildren
      }
      return result
    })

  let dataLinkList = fields.filter(line => isDataLnikField(line))
  let arr: TempFieldsInterface[] = []
  getNewTemFieldValue(dataLinkList, arr)

  let fieldList = fields.filter(line => !isDataLnikField(line)).concat(arr)
  return templateGroup(fieldList)
}

function getNewTemFieldValue(dataLinkList: TempFieldsInterface[], arr: TempFieldsInterface[]) {
  dataLinkList.forEach(item => {
    let childrenGroup = groupBy(item.children, oo => {
      //money类型.本位币当做数字类型处理
      if (oo.children && getType(oo) === 'money') {
        return 'number'
      } else if (oo.children && getDataEntity(oo).startsWith('basedata.Dimension')) {
        return 'text'
      } else {
        return getType(oo)
      }
    })
    Object.keys(childrenGroup).forEach((vv: string) => {
      let str = { ...item }
      str['copyType'] = vv
      str.children = childrenGroup[vv]
      arr.push(str)
    })
  })
}

function templateGroup(fieldList: TempFieldsInterface[]) {
  return groupBy(fieldList, line => {
    if (line.children) {
      if (getType(line) === 'money') {
        return 'number'
      } else if (line.copyType) {
        return line.copyType
      } else {
        return 'text'
      }
    } else {
      return getType(line)
    }
  })
}

export function getResultChildren(entity: string) {
  switch (entity) {
    case 'organization.Department':
      return baseChildren
    case 'organization.Staff':
      return staffChildren
    case 'pay.PayeeInfo':
      return payeeInfoChildren
    default:
      return baseChildren
  }
}

export function getEntityList(arr: TempFieldsInterface[], type: string) {
  let moneyField: TempFieldsInterface[] = []
  let dimensionField: TempFieldsInterface[] = []
  if (type === 'number') {
    moneyField = arr.filter(line => getType(line) === 'money')
  }
  if (type === 'text') {
    dimensionField = arr.filter(line => {
      const entity = getDataEntity(line)
      return entity.startsWith('basedata.Dimension') || entity.startsWith('organization.Department')
    })
  }
  let list: any[] = arr
    .filter(line => {
      const lineType = getType(line)
      if (['text', 'autoNumber'].includes(type)) {
        return ['text', 'autoNumber'].includes(lineType)
      } else {
        return getType(line) === type
      }
    })
    .concat(moneyField)
    .concat(dimensionField)
  //特殊处理: 金额字段、计划字段和档案字段  展示二级联查
  list.forEach(item => {
    const type = getType(item)
    const entity = getDataEntity(item)
    if (
      item.children &&
      type !== 'money' &&
      !item.controlType &&
      !entity.startsWith('basedata.Dimension') &&
      !entity.startsWith('organization.Department')
    ) {
      delete item.children
    }
  })
  return list
}

export function getDataEntityIds<T>(data: T[] = []) {
  return data
    .filter(item => getType(item) === 'ref' && getDataEntity(item).startsWith('datalink.DataLinkEntity'))
    .map((oo: any) => (oo.id = getDataEntity(oo).split('.')[2]))
    .filter((oo: string) => !!oo)
}

export function getType<T>(item: T) {
  return get(item, 'dataType.type') || get(item, 'type')
}

export function getDataEntity<T>(item: T) {
  return get(item, 'dataType.entity', '') || get(item, 'entity', '')
}

export function isDataLnikField<T>(item: T) {
  return getType(item) === 'ref' && getDataEntity(item).startsWith('datalink.DataLinkEntity')
}

export function getCascaderOptions(entityInfoList: TempFieldsInterface[]) {
  let entityArr = entityInfoList.filter(oo => getType(oo) !== 'dateRange')
  entityArr.forEach(vv => {
    if (isDataLnikField(vv)) {
      vv.children = vv?.children?.filter((oo: any) => getType(oo) !== 'dateRange')
    }
  })
  return entityArr
}

export function isNullOperate(operator) {
  if (operator === isNull.null || operator === isNull.notNull) {
    return true
  }
  return false
}
