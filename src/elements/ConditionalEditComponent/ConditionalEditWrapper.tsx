import React, { PureComponent } from 'react'
import styles from './DataLinkConditional.module.less'
import { Cascader, DatePicker, Popconfirm, Select, Tooltip } from 'antd'
import {
  DataLinkConditionalProps,
  DataLinkConditionalState,
  EntityInfoInterface,
  groupDateInterface,
  operatorValue,
  DataSourceType,
  TupleInvoiceType,
  isNull
} from './Type'
import {
  dateRange,
  formatEntityList,
  getEntityList,
  getFieldsType,
  operatorDataMap,
  formatTempFieldValue,
  getDataEntityIds,
  getType,
  getDataEntity,
  getDateSource,
  getBaseSource,
  getCascaderOptions,
  isNullOperate
} from './Utils'
import { cloneDeep } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
const ConditionalInput = api.require<any>('@custom-flow/elements/conditional/conditionaI-input')
import moment from 'moment'
import 'moment/locale/zh-cn'
import { getV } from '@ekuaibao/lib/lib/help'
import EkbIcon from '../ekbIcon'

const SVG_DELETE = require('./images/delete.svg')
// const SVG_DELETE_AND = require('./images/delete2.svg')
// const SVG_ADD = require('./images/add.svg')
const pleaseSelect = i18n.get('请选择')
const { Option } = Select
const { RangePicker } = DatePicker

export default class ConditionalEditWrapper extends PureComponent<DataLinkConditionalProps, DataLinkConditionalState> {
  constructor(props: DataLinkConditionalProps) {
    super(props)
    moment.locale('zh-cn')
    this.state = {
      conditionalData: props.value || [[{}]],
      entityInfoList: [],
      templateFieldValue: {},
      dateSource: getDateSource(props.entityName, props.sourceTypeFieldName, props.blackList),
      baseSource: getBaseSource(props.entityName, props.sourceTypeFieldName)
    }
  }

  async componentDidMount() {
    const id = this.fnGetEntityId()
    let resultObj = {}
    if (id.length > 0) {
      const result = await api.invokeService('@custom-specification:get:datalink:permission:fields:byIds', id)
      resultObj = result && result.value
    }
    const entityInfoList = this.fnFormatEntityInfoList(resultObj)
    const templateFieldValue = this.fnFormatTemplateFieldValue(resultObj)
    this.setState({ entityInfoList, templateFieldValue })
  }

  componentWillReceiveProps(nextProps) {
    const { value } = nextProps
    if (this.state.conditionalData !== value) {
      this.setState({ conditionalData: value })
    }
  }

  private fnFormatEntityInfoList = resultObj => {
    const { entityInfo } = this.props
    const entityInfoId = getV(entityInfo, 'id')
    let entityInfoList = []
    if (entityInfoId) {
      const permissionEntityInfo = getV(resultObj, `${entityInfoId}`, '')
      entityInfoList = formatEntityList(cloneDeep(permissionEntityInfo) as EntityInfoInterface, true)
    } else {
      entityInfoList = formatEntityList(cloneDeep(entityInfo), true)
    }
    Object.keys(resultObj).forEach((vv: string) => {
      entityInfoList.forEach(oo => {
        if (getType(oo) === 'ref' && getDataEntity(oo).endsWith(vv)) {
          oo.children = formatEntityList(cloneDeep(resultObj[vv]), false)
        }
      })
    })
    return entityInfoList
  }

  private fnFormatTemplateFieldValue = resultObj => {
    const { templateFields } = this.props
    const templateFieldList = cloneDeep(templateFields)
    Object.keys(resultObj).forEach((vv: string) => {
      templateFieldList.forEach(item => {
        if (getType(item) === 'ref' && getDataEntity(item).endsWith(vv)) {
          item.children = formatEntityList(cloneDeep(resultObj[vv]), false)
        }
      })
    })
    const templateFieldValue = formatTempFieldValue(templateFieldList)
    return templateFieldValue
  }

  private updateData = data => {
    const { onChange } = this.props
    this.setState({ conditionalData: data }, () => {
      onChange && onChange(data)
    })
  }

  fnGetEntityId = () => {
    const { entityInfo, templateFields } = this.props
    const templateFieldList = cloneDeep(templateFields)
    const tempEntityId =
      templateFieldList.length > 0 ? getDataEntityIds(templateFieldList) : entityInfo.id ? [entityInfo.id] : []
    const entityId = entityInfo.fields && getDataEntityIds(entityInfo.fields)
    // const entityInfoList = formatEntityList(cloneDeep(entityInfo), true)
    const id = tempEntityId.concat(entityId)
    return id
  }

  // 添加或条件组
  handleAddOr = () => {
    const { conditionalData } = this.state
    this.updateData([...conditionalData, [{}]])
  }

  // 删除或条件组
  handleDelOr = (index: number) => {
    const { conditionalData } = this.state
    const cData = conditionalData.slice()
    cData.splice(index, 1)
    this.updateData(cData)
  }

  // 添加且条件
  handleAddAnd(parentIndex: number, index: number) {
    const { conditionalData } = this.state
    const cData = conditionalData.slice()
    const group = cData[parentIndex]
    group.splice(index + 1, 0, {})
    this.updateData(cData)
  }

  // 删除且条件
  handleDelAnd = (parentIndex: number, index: number) => {
    const { conditionalData } = this.state
    const cData = conditionalData.slice()
    const group = cData[parentIndex]
    group.splice(index, 1)
    if (group.length === 0) {
      cData.splice(parentIndex, 1)
    }
    this.updateData(cData)
  }

  // 左值
  onChangeSubject(parentIndex: number, index: number, value: string[]) {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.left = value[1] ? value.join('.') : value[0] // value[1]存在说明是二级联动
    obj.operator && delete obj.operator
    obj.right = {}
    this.updateData(cData)
  }

  // 比较符
  onChangePredicate(parentIndex: number, index: number, types: string, value: string) {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.operator = value
    if ((types === 'date' || types === 'dateRange') && obj.right) {
      obj.right = {}
    }
    if (isNullOperate(value)) {
      delete obj.right
    } else {
      obj.right = {}
    }
    this.updateData(cData)
  }

  // 右值来源
  onChangeRightSource(parentIndex: number, index: number, value: string) {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.right = { type: value }
    this.updateData(cData)
  }

  // 右值
  onChangeRightValue(parentIndex: number, index: number, value: string[]) {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.right = { ...obj.right, value: Array.isArray(value) ? (value[1] ? value.join('.') : value[0]) : value }

    this.updateData(cData)
  }
  // 右值为时间
  handleChange = (parentIndex: number, index: number, isDateRange: boolean, date: any) => {
    let dateStr
    if (isDateRange) {
      const start = date[0].valueOf()
      const end = date[1].valueOf()
      dateStr = `${start}-${end}`
    } else {
      dateStr = date.valueOf()
    }

    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.right = { ...obj.right, value: dateStr }
    this.updateData(cData)
  }

  // 右值为'相对今天'
  onChangeInputValue0 = (parentIndex: number, index: number, data: string) => {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.right = { ...obj.right }
    obj.right.value = obj.right.value ? this.formatRelativeTotay(obj.right.value, data, true) : `${data}_`
    this.updateData(cData)
  }

  formatRelativeTotay = (value: string, data: string, isLeft: boolean) => {
    const arr = value.split('_')
    if (isLeft) {
      arr[0] = data
    } else {
      arr[1] = data
    }
    return arr.join('_')
  }

  onChangeInputValue1 = (parentIndex: number, index: number, data: string) => {
    const { cData, obj } = this.publicFn(parentIndex, index)
    obj.right = { ...obj.right }
    obj.right.value = obj.right.value ? this.formatRelativeTotay(obj.right.value, data, false) : `_${data}`
    this.updateData(cData)
  }

  publicFn = (parentIndex: number, index: number) => {
    const { conditionalData } = this.state
    const cData = conditionalData.slice()
    const obj = cData[parentIndex][index]
    return { cData, obj }
  }

  renderDisplayRender = (label: string[], defalutValue: string[]) => {
    const sbujectText = label.map((o: string) => o).join('/')
    if (!sbujectText.length && defalutValue && defalutValue.length) {
      const { onUpdateValues } = this.props
      onUpdateValues && onUpdateValues(defalutValue)
    }
    return <span>{sbujectText}</span>
  }

  renderSelect<T>(args: T | any) {

    const { targetValue, targetArr, parentIndex, index } = args
    return (
      <Select
        className="mr-5 flex-l"
        defaultValue={targetValue}
        value={targetValue}
        placeholder={pleaseSelect}
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
      >
        {targetArr.map((line: any) => {
          return (
            <Option key={line.value} value={line.value}>
              {line.label}
            </Option>
          )
        })}
      </Select>
    )
  }

  renderCascaderSelect<T>(args: T | any) {
    const { targetValue, targetArr = [], parentIndex, index, sourceType, types } = args
    let inputValueStr0, inputValueStr1
    if (sourceType && targetValue && sourceType === DataSourceType.RELATIVE_TODAY) {
      inputValueStr0 = targetValue.split('_')[0]
      inputValueStr1 = targetValue.split('_')[1]
    }
    const cascaderArr = targetValue ? targetValue.split('.') : [] // 二级联动
    return sourceType === DataSourceType.RELATIVE_TODAY ? (
      <div style={{ display: 'flex' }}>
        <ConditionalInput
          onChange={this.onChangeInputValue0.bind(this, parentIndex, index)}
          style={{ width: '88px', marginRight: '4px' }}
          type={types}
          isValidValue={true}
          dataSource={inputValueStr0}
        />
        <ConditionalInput
          onChange={this.onChangeInputValue1.bind(this, parentIndex, index)}
          style={{ width: '88px' }}
          type={types}
          isValidValue={true}
          dataSource={inputValueStr1}
        />
      </div>
    ) : (
      <Cascader
        className="mr-5 flex-l"
        defaultValue={cascaderArr}
        allowClear={false}
        value={cascaderArr}
        notFoundContent={i18n.get('没有匹配结果')}
        options={targetArr}
        placeholder={targetArr.length ? pleaseSelect : i18n.get('没有匹配结果')}
        displayRender={(label: string[]) => this.renderDisplayRender(label, cascaderArr)}
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
        showSearch
      />
    )
  }

  formatDateRange = (value: string) => {
    const arr = value.split('-')
    const start = moment(Number(arr[0]))
    const end = moment(Number(arr[1]))
    return [start, end]
  }

  renderConditional<T>(args: T | any) {
    const { parentIndex, index, types, targetValue, operator, left } = args
    const source = left?.split('.').pop()
    const isDate = types === 'date' || types === 'dateRange'
    const isDateRange = operator === 'in' || operator === 'not in'
    const isShowInvoice = operator === 'contains' || operator === 'not contains' || operator === '=' || operator === '!='
    const isSource = source === 'E_system_发票主体_来源'

    const dateValue: any = isDate
      ? targetValue
        ? isDateRange
          ? this.formatDateRange(targetValue)
          : moment(Number(targetValue))
        : void 0
      : void 0

    return isDate ? (
      isDateRange ? (
        <RangePicker
          style={{ width: '180px' }}
          value={dateValue}
          format="YYYY-MM-DD"
          onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
        />
      ) : (
        <DatePicker
          style={{ width: '180px' }}
          value={dateValue}
          onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
          allowClear={false}
        />
      )
    ) : (
      isSource && isShowInvoice ?
        this.renderSelect({ targetValue, targetArr: TupleInvoiceType(), parentIndex, index })
        :
      <ConditionalInput
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
        type={types}
        style={{ width: '180px' }}
        isValidValue={true}
        isRequired={false}
        dataSource={targetValue}
      />
    )
  }

  renderItem(data: groupDateInterface[], parentIndex: number) {
    return data.map((line, index) => {
      return (
        <div key={index} className="and-wrapper">
          <div className="conditional-and">
            <div className="and"> {index !== 0 ? i18n.get('且') : ''}</div>
            <div className="cascader">
              {this.renderFirstSelect(line, parentIndex, index)}
              {this.renderSecondSelect(line, parentIndex, index)}
              {/* <div style={{ display: 'flex', alignItems: 'center' }}> */}
              {this.renderThirdSelect(line, parentIndex, index)}
              {this.renderForthSelect(line, parentIndex, index)}
              {/* </div> */}
            </div>
            {this.renderOperator(parentIndex, index)}
          </div>
        </div>
      )
    })
  }

  private fnGetTargetArr = (sourceType: DataSourceType, types) => {
    const { entityInfoList, templateFieldValue } = this.state
    let targetArr = []
    if (sourceType === DataSourceType.BILL_FIELD || sourceType === DataSourceType.BILL_DETAIL_FIELD) {
      targetArr = templateFieldValue[types]
      if (sourceType === DataSourceType.BILL_DETAIL_FIELD) {
        const abilityList = ['feeDetail', null, '', undefined]
        targetArr = targetArr.filter(item => abilityList.includes(item?.ability))
      }
    } else if (sourceType === DataSourceType.DYNAMIC) {
      targetArr = dateRange
    } else {
      targetArr = getEntityList(cloneDeep(entityInfoList), types)
    }
    return targetArr
  }

  private fnGetTypes = line => {
    const { entityInfoList } = this.state
    const { left, operator, right } = line
    const type = getFieldsType(entityInfoList, left)
    let types = type && type.trim()
    types = types === 'date' && operator && (operator === 'in' || operator === 'not in') ? 'dateRange' : types
    return types
  }

  private renderForthSelect = (line, parentIndex, index) => {
    const { operator, right, left } = line
    if (isNullOperate(operator)) {
      return <></>
    }
    const types = this.fnGetTypes(line)
    const sourceType: DataSourceType = right && right.type
    const targetValue = right && right.value
    const targetArr = this.fnGetTargetArr(sourceType, types)
    return (
      <>
        {sourceType
          ? sourceType === DataSourceType.CONSTANT
            ? this.renderConditional({ targetValue, parentIndex,left, index, types, operator })
            : this.renderCascaderSelect({ targetValue, targetArr, parentIndex, index, sourceType, types })
          : this.renderSelect({ targetValue, targetArr, parentIndex, index, sourceType })}
      </>
    )
  }

  renderFirstSelect = (line, parentIndex: number, index: number) => {
    const { left } = line
    const { entityInfoList } = this.state
    let subjectArr = left ? left.split('.') : [] //二级联动
    const cascaderOptions = getCascaderOptions(cloneDeep(entityInfoList))
    return (
      <Cascader
        className="mr-5 flex-l"
        defaultValue={subjectArr}
        allowClear={false}
        value={subjectArr}
        notFoundContent={i18n.get('没有匹配结果')}
        options={cascaderOptions}
        placeholder={pleaseSelect}
        displayRender={(label: string[]) => this.renderDisplayRender(label, subjectArr)}
        onChange={this.onChangeSubject.bind(this, parentIndex, index)}
        showSearch
      />
    )
  }

  private renderSecondSelect = (line, parentIndex, index) => {
    const { operator } = line
    const types = this.fnGetTypes(line)
    const operatorData = operatorDataMap[types] || []
    return (
      <Select
        className="mr-5 flex-m"
        defaultValue={operator}
        value={operator}
        placeholder={pleaseSelect}
        onChange={this.onChangePredicate.bind(this, parentIndex, index, types)}
      >
        {operatorData.map((line: operatorValue) => {
          return (
            <Option key={line.value} value={line.value}>
              {line.label}
            </Option>
          )
        })}
      </Select>
    )
  }

  private fnGetRightSourceData = line => {
    const { operator } = line
    const { dateSource, baseSource } = this.state
    const types = this.fnGetTypes(line)
    const rightSourceData = types
      ? types === 'date' || types === 'dateRange'
        ? dateSource
        : baseSource
      : []
    return rightSourceData
  }

  private renderThirdSelect = (line, parentIndex, index) => {
    const { right, operator } = line
    if (isNullOperate(operator)) {
      return <></>
    }
    const sourceType = right && right.type
    const rightSourceData = this.fnGetRightSourceData(line)
    return (
      <Select
        className="mr-5 flex-m"
        defaultValue={sourceType}
        value={sourceType}
        placeholder={pleaseSelect}
        onChange={this.onChangeRightSource.bind(this, parentIndex, index)}
      >
        {rightSourceData.map((line: operatorValue) => {
          return (
            <Option key={line.value} value={line.value}>
              {line.label}
            </Option>
          )
        })}
      </Select>
    )
  }

  renderOperator = (parentIndex: number, index: number) => {
    const { conditionalData } = this.state
    return (
      <div className="operator">
        {conditionalData[parentIndex].length > 9 ? (
          <Tooltip title={i18n.get('请最多添加10个且条件')} trigger="click">
            {/* <img className="oper mr-8" src={SVG_ADD} /> */}
            <EkbIcon name="#EDico-plus-default" className="oper mr-8" />
          </Tooltip>
        ) : (
          <EkbIcon
            name="#EDico-plus-default"
            className="oper mr-8"
            onClick={this.handleAddAnd.bind(this, parentIndex, index)}
          />
          // <img className="oper mr-8" src={SVG_ADD} onClick={this.handleAddAnd.bind(this, parentIndex, index)} />
        )}
        {conditionalData.length < 2 && conditionalData[parentIndex].length < 2 ? (
          <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
            {/* <img className="oper" src={SVG_DELETE_AND} /> */}
            <EkbIcon name="#EDico-scan-b" className="oper" />
          </Tooltip>
        ) : (
          <EkbIcon name="#EDico-scan-b" className="oper" onClick={this.handleDelAnd.bind(this, parentIndex, index)} />
          // <img className="oper" src={SVG_DELETE_AND} onClick={this.handleDelAnd.bind(this, parentIndex, index)} />
        )}
      </div>
    )
  }

  render() {
    const { conditionalData, entityInfoList } = this.state
    if (!entityInfoList.length) {
      return null
    }
    return (
      <div className={styles['dataLink-conditional-content']}>
        <div>
          {conditionalData.map((line, key) => {
            return (
              <div key={key} className="conditional-or">
                {key !== 0 && (
                  <div className="or">
                    <div>{i18n.get('或')}</div>
                  </div>
                )}
                {conditionalData.length !== 1 ? (
                  <div className="or-top">
                    <div>{i18n.get('condition-group', { key: key + 1 })}</div>
                    <Popconfirm title={i18n.get('确定删除此条件组？')} onConfirm={this.handleDelOr.bind(this, key)}>
                      <img src={SVG_DELETE} />
                    </Popconfirm>
                  </div>
                ) : null}
                {this.renderItem(line, key)}
              </div>
            )
          })}
        </div>
        {conditionalData && conditionalData.length < 10 && (
          <div className="add-or" onClick={this.handleAddOr}>
            {i18n.get('添加条件组')}
          </div>
        )}
      </div>
    )
  }
}
