@import '~@ekuaibao/eui-styles/less/token.less';

.dataLink-conditional-content {
  padding-bottom: 8px;
  :global {
    .conditional-or {
      display: flex;
      flex-direction: column;
      background-color: #f7f7f7;
      .and-wrapper {
        font-size: 14px;
        padding: 0 @space-5;
        margin-bottom: 6px;
        .conditional-and {
          display: flex;
          align-items: center;
          margin: @space-4 0;
          .and {
            width: @space-5;
            flex-shrink: 0;
            margin-right: @space-4;
            color: #6c6c6c;
          }
          .cascader {
            flex: 1;
            display: flex;
            // align-items: center;
            margin-right: @space-4;
            .flex-l {
              width: 180px;
            }
            .flex-m {
              width: 116px;
            }
            .ant-form-item {
              margin: 0;
            }
            .cond-input-wrapper {
              margin-bottom: 0;
            }
            #ConditionalInput {
              margin-bottom: -24px;
            }
          }
          .operator {
            flex-shrink: 0;
            margin-right: 3px;
            .oper {
              cursor: pointer;
              color: var(--brand-base);
            }
          }
        }
      }
      .or {
        background: #ffffff;
        padding: @space-4 0;
        div {
          width: 30px;
          height: @space-7;
          border-radius: 2px;
          text-align: center;
          font-size: 14px;
          color: #ffffff;
          background-color: rgba(0, 0, 0, 0.85);
        }
      }
      .or-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        padding: @space-4 @space-5 0 @space-5;
        img {
          width: @space-6;
          height: @space-6;
          cursor: pointer;
        }
      }
    }
    .add-or {
      width: 80px;
      font-size: 14px;
      color: var(--brand-base);
      cursor: pointer;
      margin-top: @space-4;
    }
  }
}
