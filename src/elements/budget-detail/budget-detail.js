import styles from './budget-detail.module.less'
import { find, debounce, get } from 'lodash'
import moment from 'moment'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import Big from 'big.js'
import BudgetDetailTitle from './elements/Title'
import BudgetDetailInfo from './elements/DetailInfo'
import BudgetChildList from './elements/ChildList'
import NewBudgetDetailView from './elements/NewBudgetDetailView'
import Money from '../puppet/Money'
import { Fetch } from '@ekuaibao/fetch'

const PERIODS = {
  SEASON: i18n.get('季度'),
  MONTH: i18n.get('月份')
}
export default class BudgetDetailView extends React.Component {
  start = 10
  count = 10
  searchText = ''
  constructor(props) {
    super(props)
    this.preItems = get(props, 'data.budgetDetails.items') || []
    this.state = {
      periodTimeStr: '',
      isCustom: '',
      data: {},
      addMoreVisible: this.preItems.length === this.count + 1,
      inLoading: false
    }
  }

  componentWillMount() {
    this.initData(this.props)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.data !== nextProps.data) {
      this.start = 10
      this.searchText = ''
      this.preItems = get(nextProps, 'data.budgetDetails.items') || []
      this.initData(nextProps)
    }
  }

  initData(props) {
    const startDate = new Date().getTime()
    let { budgetDetails, periodTime } = props.data
    let budgetInfo = budgetDetails.info
    let period = periodTime + PERIODS[budgetInfo.period.period]
    if (budgetInfo.isCustom) {
      let sDateStr = moment(budgetInfo.period.startTime).format('YYYY-MM-DD')
      let eDateStr = moment(budgetInfo.period.endTime).format('YYYY-MM-DD')
      period = sDateStr + '~' + eDateStr
    } else {
      let currentMoney = {
        periodStartTime: budgetInfo.period?.startTime,
        periodEndTime: budgetInfo.period?.endTime
      }
      if (budgetInfo.isFiscalYear) {
        const parentNodeId = budgetInfo.nodeId
        const budgetAllData = budgetDetails.items
        const currentDetail = budgetAllData.find(line => line.nodeId === parentNodeId) || {}
        const periodTimeNumber = Number(props.data.periodTime) - 1 || 0
        currentMoney = currentDetail?.moneys?.[periodTimeNumber]
      }
      period = this.handleComputePeriod(period, budgetInfo.period, props.data.periodTime, budgetInfo, currentMoney, periodTime)
    }

    let dataSource = props.data
    let data = this.handleData(dataSource)
    this.setState(
      {
        periodTimeStr: period,
        isCustom: budgetInfo.isCustom,
        dataSource,
        data
      },
      () => {
        const endDate = new Date().getTime()
        const time = endDate - startDate
        this.fnTrack(time)
      }
    )
  }

  fnTrack = time => {
    const { pageTime } = this.props
    if (pageTime) {
      const mPageTime = time + pageTime
      window.TRACK &&
        window.TRACK('BudgetDetail', {
          corpId: Fetch.ekbCorpId,
          budgetDetailTime: mPageTime
        })
    }
  }

  render() {
    let { data, dataSource } = this.state
    let expenseFlag = !!dataSource.expenseId
    if (!data) return null

    // 修正金额为负数释放预算的显示错误
    if (data.used < 0) {
      data.used = 0
    }
    let percent = this.handleComputPer(data.used, data.total)
    let overControllerRate = data.currentDetail.overControllerRate ? data.currentDetail.overControllerRate : 100
    let control = data.currentDetail.control && data.currentDetail.control === 'IGNORED'
    const { keel, stackerManager } = this.props
    let { flowMoney, expensePer } = this.props.data
    let stackerValues
    if (keel) {
      stackerValues = (this.keel && this.keel.dataset) || []
    } else if (stackerManager) {
      stackerValues = this.props.stackerManager.values() || []
    }
    let { periodTimeStr } = this.state
    let currentTimeStr = this.isRollCalc ? periodTimeStr + i18n.get('累计') : periodTimeStr
    if (window.isNewHome) {
      let unUserdPercent = this.handleComputPer(data.newUnUsed, data.total)
      const titleProps = {
        isCustom: this.state.isCustom,
        currentTimeStr,
        expenseFlag,
        keel,
        stackerManager,
        handleTitleTipsData: this.handleTitleTipsData.call(this, data),
        handleFilterItemData: this.handleFilterItemData,
        hanldeOnClickFilter: this.hanldeOnClickFilter,
        othersProps: this.props.othersProps
      }
      const detailsInfoProps = {
        usedPercent: percent,
        overControllerRate,
        control,
        expenseFlag,
        unUserdPercent,
        isRollCalc: this.isRollCalc,
        handleTitleTipsData: this.handleTitleTipsData.call(this, data),
        handleOnClickUsedDetail: this.handleOnClickUsedDetail
      }
      const childListProps = {
        handleBudgetChildList: this.handleBudgetChildList.call(this, data),
        expenseFlag,
        handleBudgetChildItem: this.handleBudgetChildItem,
        handleOnClickDetailItem: this.handleOnClickDetailItem,
        onAddMore: this.handleMore,
        onSearch: this.handleSearch,
        addMoreVisible: this.state.addMoreVisible,
        inLoading: this.state.inLoading
      }
      return (
        <NewBudgetDetailView
          data={data}
          titleProps={titleProps}
          detailsInfoProps={detailsInfoProps}
          childListProps={childListProps}
        />
      )
    }
    return (
      <div className={styles['budget-detail-wrapper']}>
        {expenseFlag && flowMoney && expensePer && (
          <div className="expense-alert">
            {i18n.get('该单据占用')}
            &nbsp;
            <Money style={{ display: 'inline-block' }} value={flowMoney} />
            {i18n.get('，')}
            {i18n.get('占比')}
            &nbsp;
            {expensePer}
            %(
            {i18n.get('超标比例')}
            &nbsp;
            {overControllerRate}
            %)
          </div>
        )}
        <div className="budget-detail-content">
          <BudgetDetailTitle
            dataSource={data}
            isCustom={this.state.isCustom}
            periodTimeStr={currentTimeStr}
            expenseFlag={expenseFlag}
            isRollcalc={this.isRollCalc}
            titleTipsData={this.handleTitleTipsData.call(this, data)}
            fnfilterItemData={this.handleFilterItemData}
            onClickFilterItem={this.hanldeOnClickFilter}
          />
          <BudgetDetailInfo
            dataSource={data}
            expenseFlag={expenseFlag}
            stackerValues={stackerValues}
            percent={percent}
            overControllerRate={overControllerRate}
            control={control}
            clickUsedDetail={this.handleOnClickUsedDetail}
          />
          <BudgetChildList
            dataSource={this.handleBudgetChildList.call(this, data)}
            expenseFlag={expenseFlag}
            fnchildItem={this.handleBudgetChildItem}
            onClickItem={debounce(this.handleOnClickDetailItem, 300, false)}
          />
        </div>
      </div>
    )
  }

  handleOnClickUsedDetail = data => {
    const rootNodeId = get(this.props, 'data.rootNodeId', '')
    api.open('@report-budget-chart:BudgetDetailModal', { budgetInfo: data, data: { rootNodeId } })
  }

  handleComputPer(used, total) {
    let mtotal = total === 0 ? 1 : total
    let isZero = total === 0 && used === 0
    return isZero
      ? new Big(100).toFixed(2)
      : new Big(used)
          .div(mtotal)
          .times(100)
          .toFixed(2)
  }

  handleData(data) {
    let { budgetDetails, periodTime, expensePer } = data
    let budgetInfo = budgetDetails.info

    let budgetAllData = budgetDetails.items || []
    let parentNodeId = budgetInfo.nodeId
    let parentNodes = [],
      childNodes = []

    budgetAllData.forEach(line => {
      if (line.nodeId === parentNodeId) {
        parentNodes.push(line)
      } else {
        childNodes.push(line)
      }
    })

    let currentDetail = parentNodes.length > 0 ? parentNodes[0] : {}
    let moneys = currentDetail.moneys
    let parentData = find(moneys, line => line.periodTime === periodTime)
    let isRollCalc = currentDetail.isRollCalc
    this.isRollCalc = isRollCalc
    let confirmedMoney = isRollCalc ? parentData.confirmedMoneyRoll : parentData.confirmedMoney
    let occupiedMoney = isRollCalc ? parentData.occupiedMoneyRoll : parentData.occupiedMoney
    let budgetMoney = isRollCalc ? parentData.budgetMoneyRoll : parentData.budgetMoney
    let used = confirmedMoney + occupiedMoney
    let unUsed = budgetMoney - (confirmedMoney + occupiedMoney)
    const symbol = currentDetail.symbol
    const newUnUsed = unUsed
    unUsed = unUsed < 0 ? 0 : unUsed
    return {
      currentDetail,
      budgetInfo,
      parentData,
      childNodes,
      used,
      unUsed,
      periodTime,
      total: budgetMoney,
      newUnUsed,
      expensePer,
      symbol
    }
  }

  handleBudgetChildList(data) {
    let { childNodes, periodTime } = data
    let periodChildNodes = []
    for (let j = 0; j < childNodes.length; j++) {
      let childNode = childNodes[j]
      for (let k = 0; k < childNode.moneys.length; k++) {
        if (childNode.moneys[k].periodTime === periodTime) {
          childNode.moneys[k].name = childNode.name
          childNode.moneys[k].flowMoney = childNode.flowMoney
          let smoney = childNode.moneys[k]
          smoney.overControllerRate = childNode.overControllerRate
          smoney.control = childNode.control
          smoney.symbol = data.symbol
          periodChildNodes = periodChildNodes.concat(smoney)
        }
      }
    }

    return periodChildNodes
  }

  handleBudgetChildItem = (line, expenseFlag) => {
    let confirmedMoney = this.isRollCalc ? line.confirmedMoneyRoll : line.confirmedMoney
    let occupiedMoney = this.isRollCalc ? line.occupiedMoneyRoll : line.occupiedMoney
    let budgetMoney = this.isRollCalc ? line.budgetMoneyRoll : line.budgetMoney
    let used = confirmedMoney + occupiedMoney
    let percent = this.handleComputPer(used, budgetMoney)
    let expensePercent
    if (expenseFlag && line.flowMoney) {
      expensePercent = this.handleComputPer(line.flowMoney, budgetMoney)
    }
    let unUsed = budgetMoney - used
    line.name = line.name ? line.name : i18n.get('notFound')
    const unUsedPercent = this.handleComputPer(unUsed, budgetMoney)
    return { percent, expensePercent, unUsed, unUsedPercent, used }
  }

  handleOnClickDetailItem = (itemData, percent) => {
    let { expenseId } = this.state.dataSource
    let { nodeId, budgetId, periodTime } = itemData
    if (expenseId) {
      api
        .invokeService('@bills:get:budget', {
          id: expenseId,
          budgetId,
          nodeId,
          periodTime,
          start: 0,
          count: this.count,
          name: ''
        })
        .then(budgetData => {
          let params = { budgetData, itemData, percent }
          this.handleChildBudgetData(params)
        })
    } else {
      api
        .invokeService('@report-budget-chart:get:budget', {
          budgetId,
          nodeId,
          start: 0,
          count: this.count,
          name: ''
        })
        .then(budgetData => {
          let params = { budgetData, itemData, percent }
          this.handleChildBudgetData(params)
        })
    }
  }

  handleMore = () => {
    this.fnGetBudgetData(true)
  }

  handleSearch = value => {
    this.searchText = value
    this.start = 0
    this.preItems = []
    this.fnGetBudgetData()
  }

  fnGetBudgetData = isAddMore => {
    if (isAddMore && this.state.inLoading) {
      return
    }
    isAddMore ? this.setState({ inLoading: true }) : this.setState({ addMoreVisible: false })
    let { expenseId, periodTime } = this.state.dataSource
    let { nodeId, budgetId, fromNodeId, expenseItem } = this.props.data
    if (expenseId) {
      api
        .invokeService('@bills:get:budget', {
          id: expenseId,
          budgetId,
          nodeId: nodeId || expenseItem?.nodeId || fromNodeId,
          periodTime,
          start: this.start,
          count: this.count,
          name: this.searchText
        })
        .then(budgetData => {
          this.fnInitData(budgetData)
        })
        .catch(() => {
          this.setState({ inLoading: false })
        })
    } else {
      api
        .invokeService('@report-budget-chart:get:budget', {
          budgetId,
          nodeId,
          start: this.start,
          count: this.count,
          name: this.searchText
        })
        .then(budgetData => {
          this.fnInitData(budgetData)
        })
        .catch(() => {
          this.setState({ inLoading: false })
        })
    }
  }

  fnInitData = budgetData => {
    const { info, items } = budgetData
    this.start += this.count
    this.setState({
      addMoreVisible: items && items.length === this.count + 1,
      inLoading: false
    })
    const data = {
      ...this.props.data,
      ...this.state.dataSource,
      budgetDetails: { info, items: [...this.preItems, ...items] }
    }
    this.initData({ data })
    this.preItems = [...this.preItems, ...items]
  }

  handleChildBudgetData(params) {
    let { budgetData, itemData, percent } = params
    const items = get(budgetData, 'items', [])
    this.setState({ addMoreVisible: items.length === this.count + 1 })
    let { flowMoney, periodTime, name, nodeId } = itemData
    let { expenseId, budgetId } = this.state.dataSource
    const rootNodeId = get(this.props, 'data.rootNodeId', '')
    const { keel, stackerManager } = this.props
    if (keel) {
      keel.open('BudgetChart', {
        data: {
          budgetDetails: budgetData,
          periodTime: periodTime,
          title: name,
          budgetId,
          expenseId,
          rootNodeId,
          nodeId,
          expensePer: percent,
          flowMoney: flowMoney
        },
        title: name
      })
    } else if (stackerManager) {
      stackerManager.push('BudgetChart', {
        data: {
          budgetDetails: budgetData,
          periodTime: periodTime,
          title: name,
          budgetId,
          expenseId,
          rootNodeId,
          nodeId,
          expensePer: percent,
          flowMoney: flowMoney
        }
      })
    }
  }

  handleTitleTipsData(data) {
    let { currentDetail, budgetInfo } = data
    let infos = []
    for (let k in currentDetail.dimensions) {
      infos.push(k)
    }

    let period = budgetInfo.period
    let year = ''
    if (budgetInfo.isCustom) {
      //表示区间
      let sDateStr = moment(period.startTime).format('YYYY-MM-DD')
      let eDateStr = moment(period.endTime).format('YYYY-MM-DD')
      year = sDateStr + '~' + eDateStr
    } else {
      year = period.annual
    }

    return { currentDetail, infos, year }
  }

  handleFilterItemData = (line, budgetInfo, period, index) => {
    let confirmedMoney = this.isRollCalc ? line.confirmedMoneyRoll : line.confirmedMoney
    let occupiedMoney = this.isRollCalc ? line.occupiedMoneyRoll : line.occupiedMoney
    let budgetMoney = this.isRollCalc ? line.budgetMoneyRoll : line.budgetMoney
    let used = confirmedMoney + occupiedMoney
    let percent = this.handleComputPer(used, budgetMoney)
    let periodStr = line.periodTime + period
    periodStr = this.handleComputePeriod(periodStr, budgetInfo.period, line.periodTime, budgetInfo, line, index)
    let periodSectionStr = ''
    if (getPeriodControlStr(budgetInfo?.period?.periodControl).length) {
      periodSectionStr = this.handleGetAdditionalStr(line, budgetInfo)
      line.periodSectionStr = periodSectionStr
    }
    line.periodStr = periodStr
    if (line.checked === undefined && this.state.dataSource.periodTime === line.periodTime) {
      line.checked = true
    }

    periodStr = this.isRollCalc ? periodStr + i18n.get('累计') : periodStr
    return { percent, periodStr, periodSectionStr }
  }

  handleComputePeriod(periodStr, period, periodTime, budgetInfo, money, index) {
    if (budgetInfo.isFiscalYear) {
      const periodControlStr = getPeriodControlStr(budgetInfo?.period?.periodControl)
      if (!periodControlStr.length) {
        return this.handleGetAdditionalStr(money, budgetInfo)
      }
      periodStr = `第${index}${periodControlStr}`
    } else if (period.period === 'YEAR') {
      periodStr = i18n.get('year', { year: period.annual })
    } else if (period.period === 'HALF_YEAR') {
      if (periodTime == '1') {
        periodStr = i18n.get('上半年')
      } else {
        periodStr = i18n.get('下半年')
      }
    }

    return periodStr
  }

  handleGetAdditionalStr = (money, budgetInfo) => {
    if (budgetInfo.isFiscalYear) {
      const format = 'YYYY/MM/DD'
      const startStr = moment(money.periodStartTime).format(format)
      const endStr = moment(money.periodEndTime).format(format)
      return `${startStr}-${endStr}`
    }
    return ''
  }

  hanldeOnClickFilter = (line, moneys) => {
    for (let i = 0; i < moneys.length; i++) {
      let item = moneys[i]
      item.checked = line.id === item.id
    }

    let { data } = this.props
    let paramData = { ...data, periodTime: line.periodTime }
    let parseData = this.handleData(paramData)
    this.preItems = get(paramData, 'budgetDetails.items') || []
    this.start = 10
    this.setState({
      addMoreVisible: this.preItems.length === this.count + 1,
      data: parseData,
      periodTimeStr: line.periodStr,
      dataSource: paramData
    })
  }
}

export function getPeriodControlStr(periodControl) {
  if (periodControl === 'NATURAL_HALF_YEAR' || periodControl === 'NOT_NATURAL_HALF_YEAR') {
    return i18n.get('个半年')
  }
  if (periodControl === 'NATURAL_SEASON' || periodControl === 'NOT_NATURAL_SEASON') {
    return i18n.get('季度')
  }
  if (periodControl === 'NATURAL_MONTH') {
    return i18n.get('月')
  }
  return ''
}
