@import '~@ekuaibao/web-theme-variables/styles/default';

.budget-detail-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  overflow-y: auto;
  padding-bottom: 16px;
  :global {
    .expense-alert {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      background-color: #fffbe2;
      border-bottom: solid 1px #eaeaea;
      font-size: 12px;
      color: @text-color;
      height: 28px;
    }
    .budget-detail-content {
      flex: 1;
      overflow-y: auto;
      .detail-title {
        position: relative;
        margin-top: 27px;
        color: @text-color;
        .detail-info {
          position: absolute;
          left: 50px;
          display: flex;
          font-size: 12px;
          height: 24px;
          text-align: center;
          align-items: center;
          .detail-img {
            width: 14px;
            height: 14px;
            margin-left: 5px;
          }
        }
        .select-label {
          display: flex;
          justify-content: center;
          .label {
            display: flex;
            align-items: center;
            height: 24px;
            border-radius: 100px;
            background-color: #ffffff;
            border: solid 1px #dcdcdc;
            padding: 0 10px;
            .icon {
              margin-left: 7px;
              color: #dcdcdc;
            }
          }
          .select_width {
            min-width: 110px;
            justify-content: center;
            cursor: pointer;
          }
        }
        .scroll-budget {
          position: absolute;
          right: 50px;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .scroll-tag {
            height: 22px;
            color: #9e9e9e;
            font-size: 12px;
            padding: 2px 5px;
            margin-right: 3px;
          }
        }
      }

      .budget-percentage {
        display: flex;
        justify-content: space-between;
        .percentage-left {
          display: flex;
          flex-direction: column;
          margin-left: 147px;
          margin-top: 75px;
          font-size: 13px;
          .title {
            width: 52px;
            margin-left: 9px;
          }
          .used {
            color: @text-color;
            .green-circle {
              display: inline-table;
              width: 9px;
              height: 9px;
              background-color: #a1dc63;
              border-radius: 9px;
            }
            .used-detail {
              white-space: nowrap;
              font-size: 13px;
              color: var(--brand-base);
              margin-left: 16px;
              cursor: pointer;
            }
          }
          .confirm {
            color: #9c9c9c;
            padding-left: 9px;
            margin-top: 12px;
          }
          .unused {
            margin-top: 19px;
            color: #54595b;
            .gray-circle {
              display: inline-table;
              width: 9px;
              height: 9px;
              background-color: #dddddd;
              border-radius: 9px;
            }
          }
          .line {
            height: 1px;
            width: 100%;
            background-color: #d8d8d8;
            margin-top: 22px;
          }
          .total-money {
            margin-top: 14px;
            font-size: 13px;
            color: @text-color;
            .money {
              font-size: 14px;
              color: #54595b;
            }
          }
        }
        .percentage-right {
          margin-right: 130px;
          width: 180px;
          height: 180px;
          margin-top: 36px;
        }
      }
      .budget-info {
        margin-left: 120px;
        margin-right: 120px;
        .info-title {
          margin-top: 60px;
          padding-top: 19px;
          padding-bottom: 19px;
          border-top: solid 1px #dcdcdc;
        }
        .info-item {
          display: flex;
          justify-content: space-between;
          height: 60px;
          border: solid 1px #dcdcdc;
          align-items: center;
          margin-bottom: 10px;
          cursor: pointer;
          &:hover {
            background-color: #f3f5f7 !important;
          }
          .item-left {
            font-size: 13px;
            color: @text-color;
            margin-left: 20px;
          }
          .info {
            font-size: 12px;
            color: #9c9c9c;
            margin-top: 7px;
          }
          .item-right {
            font-size: 13px;
            color: #76be2b;
            margin-right: 20px;
          }
        }
      }
    }
  }
}

.ant-popover-title {
  min-width: 350px;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.3px;
  color: #54595b;
  height: 40px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  background: #f7f7f7;
}

.detail-popover {
  background-color: #00a2ae;
  .ant-popover-title {
    min-width: 350px;
    background-color: #f7f7f7;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0.3px;
    color: #54595b;
    border-bottom: 0 !important;
  }
}

.popover-wrapper {
  .popover-item {
    margin-bottom: 10px;
    font-size: 12px;
    color: #9c9c9c;
    .value {
      color: #54595b;
    }
  }
}

.filter-item {
  min-height: 58px;
  width: 377px;
  border-bottom: 1px solid #e6e6e6;
  cursor: pointer;
  padding-top: 8px;
  padding-bottom: 8px;
  &:hover {
    background-color: #fcfcfc !important;
  }
  :global {
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5px;
      .left {
        display: flex;
        flex-direction: column;
        .left-row {
          display: flex;
          align-items: center;
          .circle {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 6px;
            background-color: var(--brand-base);
            flex-shrink: 0;
            margin-right: 5px;
          }
          .month {
            font-size: 13px;
            color: #54595b;
          }
        }
        .period-section {
          margin-top: 2px;
          font-size: 13px;
          color: #54595b;
        }
      }
      .used {
        font-size: 13px;
        color: #a2abaf;
        flex-shrink: 0;
        font {
          font-size: 13px;
          color: #79c030;
          margin-left: 10px;
        }
      }
    }

    .ant-progress-line {
      .ant-progress-bg {
        background-color: #a1dc63;
      }
      &.ant-progress-status-exception .ant-progress-bg {
        background-color: #f17b7b;
      }
    }
  }
}

.filter-time {
  display: flex;
  justify-content: center;
  .popover-filter {
    background-color: #00a854;
  }
}

.scroll-view {
  .ant-popover-content {
    .ant-popover-inner {
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}
