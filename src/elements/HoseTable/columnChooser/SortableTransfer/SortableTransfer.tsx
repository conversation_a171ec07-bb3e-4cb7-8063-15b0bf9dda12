/*!
 * Copyright 2019 yangjunbao <yangjun<PERSON>@shimo.im>. All rights reserved.
 * @since 2019-05-06 17:20:20
 */

import React, { PureComponent } from 'react'
import { message, Icon } from 'antd'
import { flatten } from 'lodash'
//@ts-ignore
import ReactGridLayout, { ItemCallback, WidthProvider } from 'react-grid-layout'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'
import '../../styles/SortableTransfer.less'
import ImagesArrowLeft from '../../styles/images/arrow-left.svg'
import ImagesArrowRight from '../../styles/images/arrow-right.svg'
import ImagesEmpty from '../../styles/images/empty.svg'
import ImagesSearch from '../../styles/images/search.svg'

const GridLayout = WidthProvider(ReactGridLayout)

export type Key<T> = T extends object ? undefined : T
export type Value<T, K extends Key<T>> = K extends keyof T ? T[K] : T

export interface SelectOptions {
  value: string
  label: string
}

export interface SortableTransferProps<D> {
  items: D[]
  used: any[]
  onChange(used: any[], items: D[]): void
  keyKey?: string
  displayKey?: string
  display?(item: D): string | number
  row?: number
  name?: string
  i18nTextMap: any
  canSelectName: string
  selectedName: string
}

export interface SortableTransferState {
  itemsSearch: string
  usedSearch: string
  used: any[]
  selected: { items: any[]; used: any[] }
}

export default class SortableTransfer<D> extends PureComponent<
  SortableTransferProps<D>,
  SortableTransferState
> {
  private itemsMap: { [P in string | number]: D }

  constructor(props: SortableTransferProps<D>) {
    super(props)
    this.map(this.props.items)
    this.state = {
      itemsSearch: '',
      usedSearch: '',
      selected: {
        items: [],
        used: []
      },
      used: props.used
    }
  }

  private display(item: D) {
    if (this.props.display) {
      return this.props.display(item)
    }
    if (item && this.props.displayKey) {
      return (item as any)[this.props.displayKey]
    }
    return item
  }

  private key(item: D): any {
    if (this.props.keyKey) {
      return (item as any)[this.props.keyKey]
    }
    return item as any
  }

  private map(items: D[]) {
    this.itemsMap = items.reduce((map, item) => {
      map[this.key(item)] = item
      return map
    }, {} as any)
  }

  private buildRe(str: string) {
    return [
      new RegExp(`^${str}$`),
      new RegExp(`^${str}$`, 'i'),
      new RegExp(`^${str}`),
      new RegExp(`^${str}`, 'i'),
      new RegExp(`${str}`),
      new RegExp(`${str}`, 'i'),
      new RegExp(str.split('').join('.*')),
      new RegExp(str.split('').join('.*'), 'i')
    ]
  }

  private getItems() {
    if (!this.state.itemsSearch) {
      return this.props.items
    }
    const res = this.buildRe(this.state.itemsSearch)
    const filtered: D[][] = res.map(() => [])
    for (const item of this.props.items) {
      const disp = this.display(item)
      for (let i = 0; i < res.length; i++) {
        if (res[i].test(disp)) {
          filtered[i].push(item)
          break
        }
      }
    }
    return flatten(filtered)
  }

  private getUsed() {
    if (!this.state.usedSearch) {
      return this.state.used.filter(el => this.itemsMap[el])
    }
    const res = this.buildRe(this.state.usedSearch)
    const filtered: any[][] = res.map(() => [])
    for (const key of this.state.used) {
      const disp = this.display(this.itemsMap[key as any])
      for (let i = 0; i < res.length; i++) {
        if (res[i].test(disp)) {
          filtered[i].push(key)
          break
        }
      }
    }
    return flatten(filtered)
  }

  private isMoved = false

  private handleDragStart: ItemCallback = (
    layout,
    oldItem,
    newItem,
    placeholder,
    event,
    element
  ) => {
    this.isMoved = false
  }

  private handleDragMove = () => {
    this.isMoved = true
  }

  private handleDragStop: ItemCallback = (
    layout1,
    oldItem,
    newItem,
    placeholder,
    event1,
    element
  ) => {
    element.classList.remove('dragging')
    const used = this.state.used.slice()
    const oldKey = used[oldItem.y]
    used.splice(oldItem.y, 1)
    used.splice(newItem.y, 0, oldKey)
    this.props.onChange(used, this.props.items)
  }

  private handleSearchChange(kind: 'items' | 'used') {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.currentTarget.value
      const key = kind + 'Search'
      this.setState({ [key]: value } as any)
    }
  }

  private handleSelect(key: any, state: string) {
    return () => {
      if (state === 'used' && this.isMoved) return
      const { selected } = this.state
      let data: any[] = state === 'used' ? selected.used : selected.items
      if (data.includes(key)) {
        data = data.filter(v => v != key)
      } else {
        data.push(key)
      }
      this.setState({ selected: { ...selected, [state]: data } })
    }
  }

  private handleTransfer(state: any) {
    return () => {
      const { items, onChange, i18nTextMap } = this.props
      const { used, selected } = this.state
      let data: any[] = state === 'used' ? selected.used : selected.items
      if (!data.length) {
        return message.error(i18nTextMap.transforEmpty)
      }
      if (state === 'used') {
        data = used.filter(v => !data.includes(v))
      } else {
        data = used.concat(data)
      }
      onChange(data, items)
      this.setState({ selected: { ...selected, [state]: [] } })
    }
  }

  private handleAll = () => {
    const used = this.state.used.slice()
    this.props.items.forEach(item => {
      const key = this.key(item)
      if (used.indexOf(key) === -1) {
        used.push(key)
      }
    })
    this.props.onChange && this.props.onChange(used, this.props.items)
  }

  private handleClear = () => {
    this.props.onChange && this.props.onChange([], this.props.items)
  }

  componentWillReceiveProps(
    nextProps: Readonly<SortableTransferProps<D>>,
    nextContext: any
  ): void {
    if (nextProps.items !== this.props.items) {
      this.map(nextProps.items)
    }
    if (nextProps.used !== this.state.used) {
      this.setState({ used: nextProps.used })
    }
  }

  render() {
    const { i18nTextMap } = this.props
    const items = this.getItems().filter(item => {
      return this.state.used.indexOf(this.key(item)) === -1
    })
    const used = this.getUsed()
    const selected = this.state.selected
    return (
      <div className="sortable-transfer">
        <div className="transfer-panel items">
          <div className="title">{this.props.canSelectName}</div>
          <div className="content">
            <div className="header">
              <span onClick={this.handleAll}>{i18nTextMap.selectAll}</span>
            </div>
            <div className="search">
              <div className="input">
                <input onChange={this.handleSearchChange('items')} />
                <img src={ImagesSearch} />
              </div>
            </div>
            <div className="list list-overflow">
              {items.length > 0 ? (
                items.map((item, index) => (
                  <div
                    className="item"
                    key={index}
                    onClick={this.handleSelect(this.key(item), 'items')}
                  >
                    <div className="flex">{this.display(item)}</div>
                    {selected.items.includes(this.key(item)) && (
                      <Icon type="check" />
                    )}
                  </div>
                ))
              ) : (
                <div className="empty">
                  <img className="empty" src={ImagesEmpty} />
                  <span>{i18nTextMap.noData}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="arrows">
          <span className="item" onClick={this.handleTransfer('items')}>
            <img src={ImagesArrowRight} />
          </span>
          <span className="item" onClick={this.handleTransfer('used')}>
            <img src={ImagesArrowLeft} />
          </span>
        </div>
        <div className="transfer-panel used">
          <div className="title">{`${this.props.selectedName}(${i18nTextMap.DragOrderAble})`}</div>
          <div className="content">
            <div className="header">
              <span onClick={this.handleClear}>{i18nTextMap.cancelAll}</span>
            </div>
            <div className="list list-overflow">
              {used.length ? (
                <GridLayout
                  cols={1}
                  margin={[0, 0]}
                  containerPadding={[0, 0]}
                  rowHeight={38}
                  isResizable={false}
                  isDraggable={true}
                  onDragStart={this.handleDragStart}
                  onDrag={this.handleDragMove}
                  onDragStop={this.handleDragStop}
                  maxRows={used.length}
                  autoSize={false}
                  verticalCompact={true}
                >
                  {used.map(key => (
                    <div
                      className="item sortable"
                      key={key}
                      onClick={this.handleSelect(key, 'used')}
                    >
                      <div className="flex">
                        {this.display(this.itemsMap[key])}
                      </div>
                      {selected.used.includes(key) && <Icon type="check" />}
                    </div>
                  ))}
                </GridLayout>
              ) : (
                <div className="empty">
                  <img className="empty" src={ImagesEmpty} />
                  <span>{i18nTextMap.selectInLeftBox}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }
}
