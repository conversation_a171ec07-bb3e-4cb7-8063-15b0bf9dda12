/**
 * @description 待选择的列选字段
 * <AUTHOR>
 * @since 2021-02-02
 */
import React, { useState, useEffect } from 'react'
import { message, Icon } from 'antd'
import ReactGridLayout, {
  ItemCallback,
  Layout,
  WidthProvider
} from 'react-grid-layout'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'
import '../../styles/SortableTransfer.less'
const GridLayout = WidthProvider(ReactGridLayout)
export default function UnusedItemsWrapper(props: any) {
  const {
    list,
    used,
    fnChangeIsMoved,
    onChange,
    selected,
    handleSelect,
    KindMap,
    type,
    index,
    handleDragChange,
    fnChangeSorter,
    fnSetItemUsedData
  } = props

  const fnGetUsed = (): [] => {
    return used.filter((el: React.ReactText) => !!list[el])
  }

  const [layouts, setLayouts] = useState<Layout[]>([])
  const [data, setData] = useState(fnGetUsed())
  useEffect(() => {
    const data = fnGetUsed()
    fnSetItemUsedData(data, index)
    setData(data)
    setTimeout(() => {
      setLayouts([])
    }, 0)
  }, [used.length, used])

  if (!list || !data.length) return <></>
  const display = (item: any) => {
    if (props.display) {
      return props.display(item)
    }
    if (item && props.displayKey) {
      return (item as any)[props.displayKey]
    }
    return item
  }

  const handleDragStart = () => {
    console.log('move start')
    fnChangeIsMoved(false)
  }

  const handleDragMove = () => {
    console.log('move ing')
    fnChangeIsMoved(true)
  }

  const handleLayoutChange = (
    layout: React.SetStateAction<ReactGridLayout.Layout[]>
  ) => {
    setLayouts(layout)
  }

  const handleDragStop: ItemCallback = (
    layout1,
    oldItem,
    newItem,
    placeholder,
    event1,
    element
  ) => {
    element.classList.remove('dragging')
    const used = data.slice()
    const oldKey = used[oldItem.y]
    console.log(data)
    used.splice(oldItem.y, 1)
    used.splice(newItem.y, 0, oldKey)
    handleDragChange(used, index)
  }
  return (
    <div className="list" style={{ height: (data.length + 1) * 38 }}>
      <div className="title">
        <div>{KindMap[type]}</div>
        <div>
          <span onClick={() => fnChangeSorter(index, 'up')}>↑</span>
          <span onClick={() => fnChangeSorter(index, 'down')}>↓</span>
        </div>
      </div>
      <GridLayout
        cols={1}
        margin={[0, 0]}
        containerPadding={[0, 0]}
        rowHeight={38}
        isResizable={false}
        isDraggable={true}
        onDragStart={handleDragStart}
        onDrag={handleDragMove}
        onDragStop={handleDragStop}
        maxRows={data.length}
        autoSize={true}
        verticalCompact={false}
        layout={layouts}
        onLayoutChange={layout => handleLayoutChange(layout)}
      >
        {data.map((key: string) => (
          <div
            className="item sortable"
            key={key}
            onClick={handleSelect.bind(null, key, 'used')}
          >
            <div className="flex">{display(list[key])}</div>
            {selected.used.includes(key) && <Icon type="check" />}
          </div>
        ))}
      </GridLayout>
    </div>
  )
}
