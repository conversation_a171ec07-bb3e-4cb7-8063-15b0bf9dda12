/**************************************
 * Created By LinK On 2019/11/7 16:05.
 **************************************/
import React, { PureComponent } from 'react'
import SortableTransfer from './SortableTransfer'
import SortableGroupTransfet from './SortableGroupTransfet'
import { Modal } from 'antd'
import { Tooltip } from '@hose/eui'
import classnames from 'classnames'
import { Column } from '../types/column'
// @ts-ignore
import styles from '../ColumnChooser.module.less'
import '../../styles/SortableTransfer.less'
import Icon from '../iconfont/Icon'
import { TooltipPlacement } from 'antd/lib/tooltip'

interface Props {
  columns: Column[]
  className?: string
  fnHandleChange: (visibleColumns: string[]) => void
  fnHandleSave: (visibleColumns: string[]) => void
  visibleColumns: string[]
  i18nTextMap: any
  isGroup?: boolean
  KindMap: any
  placement?: TooltipPlacement
  renderChildren?: () => JSX.Element
  maxColumnCount?: number
  onMaxColumnCount?: (count: number) => void
}

interface State {
  visible: boolean
  value: string[]
}

export default class SortableTransferBtn extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { visible: false, value: props.visibleColumns }
  }

  showModal = () => {
    this.setState({
      visible: true
    })
  }

  fnHandleChangeValue = (value: string[]) => {
    this.setState({ value })
  }

  handleOk = (e: any) => {
    const { value } = this.state
    const { fnHandleSave, maxColumnCount, onMaxColumnCount } = this.props
    if (maxColumnCount !== undefined && value?.length > maxColumnCount) {
      onMaxColumnCount && onMaxColumnCount(value?.length)
      return
    }
    fnHandleSave(value)
    this.setState({
      visible: false
    })
  }

  handleCancel = (e: any) => {
    const { visibleColumns } = this.props
    this.setState({
      visible: false,
      value: visibleColumns
    })
  }

  public componentDidUpdate(prevProps: Props) {
    if (this.props.visibleColumns !== prevProps.visibleColumns) {
      this.setState({
        value: this.props.visibleColumns
      })
    }
  }

  render() {
    const {
      className,
      i18nTextMap,
      columns,
      isGroup = false,
      KindMap = {},
      renderChildren,
      placement = 'left'
    } = this.props
    const { visible, value } = this.state
    // @TEST
    const MyComponent = !isGroup ? SortableTransfer : SortableGroupTransfet
    // const MyComponent = SortableGroupTransfet
    return (
      <>
        <div className={classnames(styles.button, className)}>
          <Tooltip placement={placement} title={i18nTextMap.columnChooserTip}>
            {/* <div
              className={classnames(styles.icon, {
                [styles.activeIcon]: visible
              })}
              onClick={this.showModal}
            /> */}
            <span onClick={this.showModal}>
              {renderChildren ? (
                renderChildren()
              ) : (
                <Icon
                  name="#EDico-column-active1"
                  className={classnames(styles.icon, {
                    [styles.activeIcon]: visible
                  })}
                />
              )}
            </span>
          </Tooltip>
        </div>
        <Modal
          className="sortableTransferModal"
          visible={visible}
          onOk={this.handleOk}
          okText={i18nTextMap.submit}
          cancelText={i18nTextMap.cancelRowChanges}
          title={i18nTextMap.columnChooserTitle}
          okType="primary"
          onCancel={this.handleCancel}
        >
          <MyComponent
            items={columns}
            used={value}
            KindMap={KindMap}
            i18nTextMap={i18nTextMap}
            onChange={this.fnHandleChangeValue}
            keyKey="dataIndex"
            displayKey="label"
            canSelectName={i18nTextMap.canSelectField}
            selectedName={i18nTextMap.selectedField}
          />
        </Modal>
      </>
    )
  }
}
