/**
 * @description 待选择的列选字段
 * <AUTHOR>
 * @since 2021-02-02
 */
import React, { useState, useEffect } from 'react'
import { Icon } from 'antd'
export default function UnusedItemsWrapper(props: any) {
  const {
    list,
    displayKey,
    selected,
    fnKey,
    handleSelect,
    type,
    KindMap
  } = props

  if (!list.length) {
    return <></>
  }

  const display = (item: any) => {
    if (props.display) {
      return props.display(item)
    }
    if (item && props.displayKey) {
      return (item as any)[props.displayKey]
    }
    return item
  }
  return (
    <div>
      <div className="title">{KindMap[type]}</div>
      {list.map((item: any, index: number) => (
        <div
          className="item"
          key={index}
          onClick={handleSelect.bind(null, fnKey(item), 'items')}
        >
          <div className="flex">{display(item)}</div>
          {selected.items.includes(fnKey(item)) && <Icon type="check" />}
        </div>
      ))}
    </div>
  )
}
