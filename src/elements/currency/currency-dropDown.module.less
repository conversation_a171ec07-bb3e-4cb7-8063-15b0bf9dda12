.currency-dropdown-item {
  padding: 14px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  :global {
    .item-right {
      display: flex;
      flex-direction: row;
      .item-img {
        width: 42px;
        height: 42px;
        border-radius: 21px;
      }
      .item-content {
        margin-left: 14px;
        display: flex;
        flex-direction: column;
        .item-currency {
          font-size: 14px;
          font-weight: 600;
          line-height: 1.5;
          text-align: left;
          color: rgba(0, 0, 0, 0.65);
        }
        .item-code {
          margin-top: 4px;
          font-size: 12px;
          line-height: 1.5;
          text-align: left;
          color: #6c6c6c;
        }
      }
    }

    .item-action {
      width: 48px;
      height: 22px;
      border-radius: 2px;
      background-color: #ffffff;
      border: solid 1px var(--brand-base);
      font-size: 12px;
      line-height: 20px;
      color: var(--brand-base);
      text-align: center;
      cursor: pointer;
    }
    .item-action-disable {
      font-size: 12px;
      line-height: 1.5;
      text-align: left;
      color: #9e9e9e;
    }
  }
}

.currency-dropdown {
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  :global {
    .ant-menu-vertical>.ant-menu-item {
      height: 71px;
      line-height: 71px;
    }
  }
}

.currency-dropdown-add {
  width: 92px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  font-size: 12px;
  color: var(--brand-base);
  border-radius: 2px;
  background-color: #ffffff;
  border: solid 1px var(--brand-base);
  cursor: pointer;
}

.currency-dropdown-menu {
  padding: 6px;
  min-width: 350px;
}

.eui-currency-dropdown-item {
  padding: 8px 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  :global {
    .item-right {
      display: flex;
      flex-direction: row;
      .item-img {
        width: 42px;
        height: 42px;
        border-radius: 21px;
      }
      .item-content {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        text-align: left;
        font: var(--eui-font-body-r1);
        .item-currency {
          color: var(--eui-text-title);
        }
        .item-code {
          color: var(--eui-text-placeholder);
        }
      }
    }

    .item-action {
      width: 48px;
      height: 22px;
      border-radius: 2px;
      background-color: #ffffff;
      border: solid 1px var(--brand-base);
      font-size: 12px;
      line-height: 20px;
      color: var(--brand-base);
      text-align: center;
      cursor: pointer;
    }
    .item-action-disable {
      font-size: 12px;
      line-height: 1.5;
      text-align: left;
      color: #9e9e9e;
    }
  }
}

.eui-currency-dropdown {
  box-shadow: var(--eui-shadow-down-3);
  border-radius: 6px;
  :global {
    .eui-dropdown-menu {
      width: 334px;
      padding: 8px;
      border-radius: 0 0 6px 6px;
      box-shadow: none;
    }
    .eui-dropdown-menu-item {
      margin: 0;
      padding: 0;
      border-radius: 8px;
    }
    .empty-text {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-placeholder);
      >div {
        text-align: center;
        line-height: 20px;
      }
    }
    .eui-currency-dropdown-search {
      padding: 6px;
      background-color: var(--eui-bg-float);
      border-radius: 6px 6px 0 0;
      width: 100%;
      .eui-input-affix-wrapper {
        border-color: var(--eui-line-border-component) !important;
        &:hover {
          border-color: var(--eui-primary-pri-500) !important;
        }
      }
    }
  }
}

.eui-currency-dropdown-menu {
  margin-bottom: 8px;
  padding: 0;
  :global {
    .empty-menu-item:hover {
      background-color: var(--eui-bg-float);
    }
  }
}

.eui-currency-dropdown-item-checked {
  background-color: var(--eui-fill-active);
  border-radius: 8px;
}

