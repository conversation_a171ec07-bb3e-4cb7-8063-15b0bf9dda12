import React from 'react'

export function permissionName(key) {
  let config = {
    SYS_ADMIN: i18n.get('系统管理'),
    REPORT_VIEW: i18n.get('费用报表管理'),
    BUDGET_MANAGE: i18n.get('预算管理'),
    LOAN_MANAGE: i18n.get('借款管理'),
    EXPENSE_MANAGE: i18n.get('报销单管理'),
    REQUISITION_MANAGE: i18n.get('申请管理'),
    CUSTOM_REPORT_VIEW: i18n.get('高级报表'),
    SETTLEMENT_MANAGE: i18n.get('结算方式管理'),
    TRIP_MANAGE: i18n.get('差旅管理'),
    CORP_WALLET_MANAGE: i18n.get('企业钱包管理'),
    VIRTUAL_CARD_MANAGE: i18n.get('易商卡管理'),
    INVOICE_MANAGE: i18n.get('票据管理'),
    SUPPLEMENT_INVOICE: i18n.get('补充发票'),
    SMART_REPORT: i18n.get('智能报表管理'),
    AUDIT_ADMIN: i18n.get('审计日志'),
    BANK_ACCOUNT_MANAGE: i18n.get('账户管理'),
    RECEIPT_ACCOUNT: i18n.get('账户管理-收款账户'),
    PAYMENT_ACCOUNT: i18n.get('账户管理-付款账户'),
    PAYMENT_WORKBENCH: i18n.get('支付数据管理'),
    CREDIT_MANAGE: i18n.get('信用管理'),
    MALL_MANAGE: i18n.get('商城管理'),
    INVOICE_REVIEW: i18n.get('发票复核管理'),
    INVOICE_DISCOUNT: i18n.get('进项抵扣管理'),
    CONSUMER_MATTERS: i18n.get('自动报销管理'),
    RECEIPT_MANAGE: i18n.get('收款单管理'),
    CHECKING_BILL_MANAGE: i18n.get('对账结算管理'),
    RESEARCHERS_ACTIVATE: i18n.get('人员激活管理'),
    CORP_PAYMENT_MANAGE: i18n.get('对公付款管理'),
    CONTACTS_MANAGE: i18n.get('通讯录管理'),
    KA_TOBACCO_CHECKING_MANAGER: i18n.get('部门或科室对账'),
    EXTEND_DIMENSION: i18n.get('扩展档案管理'),
    CORP_AGENCY_EXPENSE: i18n.get('企业代报销'),
    PAYMENT_MANAGEMENT: i18n.get('企业支付管理'),
    FLOW_ADMIN: i18n.get('审批流管理'),
    AI_APPROVAL_NODE: i18n.get('AI 智能审批管理'),
    ROLE_ADMIN: i18n.get('角色管理'),
    INVOICE_REVIEW_QUICK_EXPENSE: i18n.get('发票复核管理（费用明细）'),
    HOME_PAGE_ADMIN: i18n.get('移动端首页配置'),
    BI_REPORT_VIEW: i18n.get('BI管理'),
    CORPORATE_BANKING: i18n.get('付款管理'),
    RECORD_LINK_ADMIN: i18n.get('档案关系管理'),
    COST_CONTROL_ADMIN: i18n.get('费用标准管理'),
    FORM_SPEC_ADMIN: i18n.get('单据模板管理'),
    FEE_TYPE_ADMIN: i18n.get('费用类型管理'),
    INVOICE_USAGE_NORM: i18n.get('发票使用规范管理'),
    INVOICE_ACCOUNTING: i18n.get('发票入账'),
    INVOICE_MATCH_RULE: i18n.get('发票匹配规则'),
    INVOICE_IMPORT_SET: i18n.get('发票导入设置管理'),
    INVOICE_PRICETAX_SEP: i18n.get('发票价税分离管理'),
    OUTPUT_TAX_MANAGE: i18n.get('销项开票管理'),
    UKEY_APPLY: i18n.get('UKEY申请'),
    CORPORATE_RECEIPT: i18n.get('收款管理'),
    VIRTUAL_TOKEN_MANAGE: i18n.get('易商卡管理'),
    BI_REPORT_CONFIG: i18n.get('BI报表配置'),
    REQUISITION_CONFIG_MANAGE: i18n.get('申请事项管理'),
    HOSE_TRIP_INVOICE_HUB: i18n.get('合思商旅开票申请'),
  }

  return config[key]
}

export function permissionDescribe(key) {
  let config = {
    SYS_ADMIN: <div>{i18n.get('permissionDescribe')}</div>,
    REPORT_VIEW: <div>{i18n.get('REPORT_VIEW')}</div>,
    BUDGET_MANAGE: <div>{i18n.get('BUDGET_MANAGE')}</div>,
    LOAN_MANAGE: <div>{i18n.get('LOAN_MANAGE')}</div>,
    EXPENSE_MANAGE: <div>{i18n.get('EXPENSE_MANAGE')}</div>,
    REQUISITION_MANAGE: <div>{i18n.get('REQUISITION_MANAGE')}</div>,
    CUSTOM_REPORT_VIEW: <div>{i18n.get('CUSTOM_REPORT_VIEW')}</div>,
    SETTLEMENT_MANAGE: <div>{i18n.get('可维护企业的结算方式。')}</div>,
    TRIP_MANAGE: <div>{i18n.get('可以管理企业差旅钱包，查看差旅交易流水 ，进行差旅相关配置。')}</div>,
    CORP_WALLET_MANAGE: <div>{i18n.get('管理并查看企业账户的信息及交易数据，操作充值')}</div>,
    VIRTUAL_CARD_MANAGE: <div>{i18n.get('管理并查看企业成员的易商卡信息及数据，配置消费标准')}</div>,
    INVOICE_MANAGE: <div>{i18n.get('INVOICE_MANAGE')}</div>,
    SUPPLEMENT_INVOICE: <div>{i18n.get('SUPPLEMENT_INVOICE')}</div>,
    SMART_REPORT: <div>{i18n.get('可管理智能报表后台')}</div>,
    AUDIT_ADMIN: <div>{i18n.get('可查看企业审计日志，与系统管理权限互斥。')}</div>,
    BANK_ACCOUNT_MANAGE: <div>{i18n.get('可管理企业付款账户、收款账户。')}</div>,
    RECEIPT_ACCOUNT: <div>{i18n.get('可管理企业收款账户。')}</div>,
    PAYMENT_ACCOUNT: <div>{i18n.get('可管理企业付款账户。')}</div>,
    PAYMENT_WORKBENCH: <div>{i18n.get('可查看支付相关数据。')}</div>,
    CREDIT_MANAGE: <div>{i18n.get('设置和发布信用模型，管理加减分规则，管理信用数据。')}</div>,
    MALL_MANAGE: <div>{i18n.get('可拥有查看合思商旅后台的权限。')}</div>,
    INVOICE_REVIEW: <div>{i18n.get('具有发票复核的操作与查看风险的权限')}</div>,
    INVOICE_DISCOUNT: <div>{i18n.get('具有进项抵扣管理的操作权限')}</div>,
    CONSUMER_MATTERS: <div>{i18n.get('可配置消费事项、费用生成、自动提报的相关规则')}</div>,
    RECEIPT_MANAGE: <div>{i18n.get('可查看和管理企业中所有收款单，并将其导出为Excel。')}</div>,
    CHECKING_BILL_MANAGE: <div>{i18n.get('可拥有对账结算中心的操作权限')}</div>,
    RESEARCHERS_ACTIVATE: <div>{i18n.get('可以激活所在部门的全部员工，系统管理员默认有激活所有员工的权限')}</div>,
    CORP_PAYMENT_MANAGE: <div>{i18n.get('对公付款管理')}</div>,
    KA_TOBACCO_CHECKING_MANAGER: <div>{i18n.get('部门或科室对账')}</div>,
    CONTACTS_MANAGE: <div>{i18n.get('可用于管理企业员工信息')}</div>,
    EXTEND_DIMENSION: <div>{i18n.get('可管理企业内的扩展档案')}</div>,
    PAYMENT_MANAGEMENT: <div>{i18n.get('可操作支付复核及配置复核规则')}</div>,
    CORP_AGENCY_EXPENSE: <div>{i18n.get('企业代报销')}</div>,
    FLOW_ADMIN: <div>{i18n.get('可管理企业审批流，对审批流程进行维护')}</div>,
    AI_APPROVAL_NODE: <div>{i18n.get('可管理企业审批相关的 AI 智能体、规则制度、测试任务、日志等')}</div>,
    ROLE_ADMIN: <div>{i18n.get('可管理企业角色，维护角色对应人员')}</div>,
    INVOICE_REVIEW_QUICK_EXPENSE: <div>{i18n.get('发票复核管理（费用明细）')}</div>,
    HOME_PAGE_ADMIN: <div>{i18n.get('可配置app端首页模版下发给员工使用')}</div>,
    BI_REPORT_VIEW: (
      <div>
        {i18n.get(
          '请选择“主管理员”角色及其他用户或角色，主管理员同时也将成为该模块的管理员，该模块的详细权限由主管理员在【BI管理】模块中详细配置。'
        )}
      </div>
    ),
    CORPORATE_BANKING: <div>{i18n.get('可进行付款管理中的权限配置')}</div>,
    RECORD_LINK_ADMIN: <div>{i18n.get('可查看和设置档案关系')}</div>,
    COST_CONTROL_ADMIN: <div>{i18n.get('可查看和设置费用标准')}</div>,
    FORM_SPEC_ADMIN: <div>{i18n.get('可查看和设置单据模板')}</div>,
    FEE_TYPE_ADMIN: <div>{i18n.get('可查看和设置费用类型')}</div>,
    INVOICE_USAGE_NORM: <div>{i18n.get('可查看和设置发票使用规范')}</div>,
    INVOICE_ACCOUNTING: <div>{i18n.get('可查看并操作发票入账')}</div>,
    INVOICE_MATCH_RULE: <div>{i18n.get('查看并操作发票&费用规则配置')}</div>,
    INVOICE_IMPORT_SET: <div>{i18n.get('可查看和设置发票导入设置')}</div>,
    INVOICE_PRICETAX_SEP: <div>{i18n.get('可查看和设置发票价税分离')}</div>,
    OUTPUT_TAX_MANAGE: <div>{i18n.get('可查看并使用销项开票功能模块')}</div>,
    UKEY_APPLY: <div>{i18n.get('可管理UKEY申请。')}</div>,
    CORPORATE_RECEIPT: <div>{i18n.get('可进行收款管理中的权限配置')}</div>,
    BI_REPORT_CONFIG: <div>{i18n.get('可管理BI报表看板，看板及用户权限可在[BI报表配置]中详细配置。')}</div>,
    VIRTUAL_TOKEN_MANAGE: <div>{i18n.get('管理并查看企业成员的易商卡信息及数据，配置消费标准')}</div>,
    REQUISITION_CONFIG_MANAGE: <div>{i18n.get('可查看【申请事项管理】菜单，并对申请事项额度管控进行配置')}</div>,
    HOSE_TRIP_INVOICE_HUB: <div>{i18n.get('可对合思商旅消费在线提交开票申请')}</div>,
  }
  /*
   系统管理员权限：用于维护企业信息及数据，可为企业成员配置权限和进行角色组管理，对消费类型、模板、审批流、项目进行自定义。
   费用报表查看权限：可查看企业费用报表。
   借款管理权限：可查看管理企业借款情况，对成员还款进行确认。
   报销单管理权限：可查看企业中所有报销单，并将其导出为Excel。
   申请单管理权限：查看企业中的所有申请单以及它们的报销情况。
   高级报表：可查看并管理高级报表。
   发票管理：可查看并管理所有导入的发票及项目明细，可集中维护「统一开票」、「待开发票」的单据。
   补充发票：可在「待办」列表中，快速查找「待开发票」的单据；可在「首页」、「待办」各列表中进行「补充发票」操作。
   */
  return config[key]
}

export const allPermissions = [
  'SYS_ADMIN',
  'REPORT_VIEW',
  'EXPENSE_MANAGE',
  'LOAN_MANAGE',
  'BUDGET_MANAGE',
  'REQUISITION_MANAGE',
  'CUSTOM_REPORT_VIEW',
  'SETTLEMENT_MANAGE',
  'TRIP_MANAGE',
  'CORP_WALLET_MANAGE',
  'VIRTUAL_CARD_MANAGE',
  'VIRTUAL_TOKEN_MANAGE',
  'INVOICE_MANAGE',
  'SUPPLEMENT_INVOICE',
  'SMART_REPORT',
  'AUDIT_ADMIN',
  'BANK_ACCOUNT_MANAGE',
  'RECEIPT_ACCOUNT',
  'PAYMENT_ACCOUNT',
  'PAYMENT_WORKBENCH',
  'newMall',
  'CREDIT_MANAGE',
  'RESEARCHERS_ACTIVATE',
  'INVOICE_REVIEW_QUICK_EXPENSE'
]
