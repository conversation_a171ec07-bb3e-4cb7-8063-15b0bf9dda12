/**
 * 数据来源：单据详情适配方案
 * https://wiki.hosecloud.com/pages/viewpage.action?pageId=287899892#单据详情适配方案
*/

// 单据详情抽屉宽度配置
export const BILL_DRAWER_DEFAULT_WIDTH = 1030 as const
export const BILL_DRAWER_MIN_WIDTH_IN_CN = 680 as const
export const BILL_DRAWER_MIN_WIDTH_IN_EN = 778 as const
const BILL_DRAWER_WIDTH_STORAGE_KEY = 'billDrawerWidthKey'

// 单据详情内容部分宽度配置（368 + 16）
export const BILL_CONTENT_PART_MIN_WIDTH = 384 as const

// 抽屉内审批流宽度配置
const FLOW_PART_MIN_RESIZE_WIDTH = 280 as const
const FLOW_PART_MAX_RESIZE_WIDTH = 500 as const
const FLOW_PART_LIST_MODE_DEFAULT_WIDTH = 280 as const
const FLOW_PART_TABLE_MODE_DEFAULT_WIDTH = 320 as const

export const listenerToDrawerResize = (callback?: (width: number) => void) => {

  const handleDrawerWidthResize = (e: CustomEvent) => {
    const width = e.detail.width
    billDrawerConfig.width = width
    callback?.(width)
  }

  window.addEventListener('drawerWidthResize', handleDrawerWidthResize)

  return () => {
    window.removeEventListener('drawerWidthResize', handleDrawerWidthResize)
  }
}


export const billDrawerConfig = {
  get minWidth() {
    return i18n.currentLocale === 'zh-CN' ? BILL_DRAWER_MIN_WIDTH_IN_CN : BILL_DRAWER_MIN_WIDTH_IN_EN
  },
  set width(width: number) {
    if (typeof width === 'number' && width !== Number.NaN) {
      localStorage.setItem(BILL_DRAWER_WIDTH_STORAGE_KEY, width.toString())
    }
  },
  get width() {
    const width = localStorage.getItem(BILL_DRAWER_WIDTH_STORAGE_KEY)
    return width ? parseInt(width) : BILL_DRAWER_DEFAULT_WIDTH
  }
}


export const flowPartDrawerConfig = {
  minWidth: FLOW_PART_MIN_RESIZE_WIDTH,
  maxWidth: FLOW_PART_MAX_RESIZE_WIDTH,
  listModeDefaultWidth: FLOW_PART_LIST_MODE_DEFAULT_WIDTH,
  tableModeDefaultWidth: FLOW_PART_TABLE_MODE_DEFAULT_WIDTH,
  collapseWidth: i18n.currentLocale === 'zh-CN' ? 48 : 72
}

/**
 * 在预览以及下载时，会触发a的点击事件来下载或者预览的场景
 * 并且a元素会被append到 #layout-wrapper中，就会触发该顶层元素的点击事件，导致抽屉被关闭，所以需要针对该元素做特殊处理
 */
export const ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER = ['ekb-node-openlink', 'ai-approval-copilot-detail-drawer']