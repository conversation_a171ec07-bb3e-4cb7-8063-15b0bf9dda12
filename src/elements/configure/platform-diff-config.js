export const SYNC_URL = (isRole = false, isDep = false, isStaffByDep = false, accessToken, depIds = [], plateForm) => {
  if (plateForm === 'DT') { // 钉钉
    if (isRole) {
      return '/api/dingtalk/v2/syncRole/sync'
    } else if (isDep) {
      return '/api/dingtalk/v2/sync/syncDepartment'
    } else if (isStaffByDep) {
      return `/api/dingtalk/v2/sync/syncStaffByDeptIds/${JSON.stringify(depIds)?.replace?.(/"|'/g, '')}`
    } else {
      return '/api/dingtalk/v2/sync/progressive'
    }
  }
  if (plateForm === 'QYWX') {
    return '/api/qyweixin/v2/sync/progressive'
  }

  if (plateForm === 'KdCloud') {
    return `/api/kdcloud/v2/sync/progressive?accessToken=${accessToken}`
  }
  if (plateForm === 'HW') {
    return '/api/huawei/sync/v1/progressive'
  }
  if (plateForm === 'FEISHU') {
    return isRole ? '/api/feishu/v2/role/sync' : '/api/feishu/sync/v2/progressive'
  }
  if (plateForm === 'DEBUGGER') {
    return ''
  }
}
