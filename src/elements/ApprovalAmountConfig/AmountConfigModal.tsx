import React, { useCallback, useEffect, useState } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import EnhanceFormCreate from '../enhance/enhance-form-create'
import { Button, Icon, Switch } from 'antd'
import { Tooltip } from '@hose/eui'
import { switchConfig } from './form'
import RuleSelect from './RuleSelect'
import { EnhanceConnect } from '@ekuaibao/store'
import './AmountConfigModal.less'
import {
  getApproveDisplayConfig,
  saveApproveDisplayConfig,
  updateApproveDisplayConfig
} from '../../plugins/bills/bills.action'
const renderSwitch = ({ checked, onChange, label }) => {
  return (
    <div className="switch_item" key={label}>
      <div className="switch_label">{i18n.get(label)}</div>
      <Switch checked={checked} onChange={onChange} />
    </div>
  )
}
/**
 * 审批同意、驳回时右上角金额展示配置弹窗
 */
const AmountConfigModal = props => {
  const [switchVal, setSwitch] = useState({})
  const [ruleVal, setRule] = useState([])
  const [id, setID] = useState('')
  const { staff = {} } = props
  const handleModalClose = () => props.layer.emitCancel()
  const handleModalOK = () => {
    const fn = !!!id ? saveApproveDisplayConfig : updateApproveDisplayConfig
    fn({
      name: '',
      type: 'APPROVEDISPLAY',
      configDetail: {
        riskWarningCfg: switchVal,
        moneyCaliberCfg: ruleVal.filter(it => it.name), // 为空校验
        staffId: staff.id
      },
      id: id
    })
    props.layer.emitOk({})
  }
  const getInitData = async () => {
    const { value } = await getApproveDisplayConfig()
    const { configDetail = {}, id = '' } = value
    setID(id)
    setSwitch(configDetail?.riskWarningCfg)
    setRule(configDetail?.moneyCaliberCfg)
  }
  useEffect(() => {
    getInitData()
  }, [])
  const handleSwitchChange = useCallback(
    (name, e) => {
      setSwitch({ ...switchVal, [name]: e })
    },
    [switchVal]
  )
  return (
    <div className="amount-config-modal">
      <div className="modal-header">
        <div className="flex">{i18n.get('审批布局设置')}</div>
        <Icon className="cross-icon" type="cross" onClick={handleModalClose} />
      </div>
      <div className="content p-20 scrollable" style={{ maxHeight: 400 }}>
        <div className="content_title">{i18n.get('风险展示设置')}</div>
        <div className="switch_content">
          {switchConfig.map(it => {
            const { label, name } = it
            return renderSwitch({
              label,
              checked: switchVal[name],
              onChange: handleSwitchChange.bind(null, name)
            })
          })}
        </div>
        <div className="content_title">
          {i18n.get('审批金额口径设置')}
          <Tooltip
            placement="bottom"
            overlayInnerStyle={{ width: 282 }}
            title={i18n.get('可自定义审批金额字段，若字段值为空，则默认不展示')}
          >
            <Icon type="question-circle-o" style={{ marginLeft: 4 }} />
          </Tooltip>
        </div>
        <div className="rule_content">
          <RuleSelect value={ruleVal} onChange={setRule}></RuleSelect>
        </div>
      </div>
      <div className="modal-footer">
        <Button className="btn-ml" onClick={handleModalClose}>
          {i18n.get('取消')}
        </Button>
        <Button type="primary" className="btn-ml" onClick={handleModalOK}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})(
  EnhanceFormCreate()(
    EnhanceConnect(state => {
      return {
        staff: state['@common'].userinfo?.staff
      }
    })(AmountConfigModal)
  )
)
