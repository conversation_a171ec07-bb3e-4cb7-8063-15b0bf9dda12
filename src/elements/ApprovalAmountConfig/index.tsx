import { Button } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import React, { useCallback } from 'react'
import { OutlinedGeneralSetting } from '@hose/eui-icons'

const ApprovalAmountConfig = props => {
  const { fn } = props
  const handlerClick = useCallback(() => {
    app.open('@bills:AmountConfigModal', {}).then(fn)
  }, [])
  return (
    <Button category="text" className="fs-14" onClick={handlerClick}>
      <OutlinedGeneralSetting fontSize={14} /> {i18n.get('设置')}
    </Button>
  )
}

export default ApprovalAmountConfig
