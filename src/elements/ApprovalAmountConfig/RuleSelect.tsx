import { app } from '@ekuaibao/whispered'
import { T } from '@ekuaibao/i18n'
import { OrderSelect } from '../../components/dynamic/OrderConfigRules/OrderSelect'
import { EnhanceConnect } from '@ekuaibao/store'
import React, { useCallback, useState } from 'react'
import './RuleSelect.less'
import { showMessage } from '@ekuaibao/show-util'
import { filterMoney } from './utils'
import { currencySelect } from './form'
const DragSort = app.require<any>('@lib/dragSort')
const EKBIcon = app.require<any>('@elements/ekbIcon')
const maxLen = 20
const RuleSelect = props => {
  const { value, onChange, baseDataProperties } = props
  const moneyProperties = filterMoney(baseDataProperties, value)
  const [curMoveItem, setCurMove] = useState(null)
  const handlerChange = useCallback(
    (type: string, i?: number, key?: string, val?: any) => {
      const _v = value.slice()
      switch (type) {
        case 'delete':
          _v.splice(i, 1)
          break
        case 'create':
          if (value?.length > maxLen) return showMessage.error(`金额字段不得超过${maxLen}个`)
          _v.push({ currency: 'currency' })
          break
        case 'change':
          _v[i][key] = val
          break
      }
      onChange(_v)
    },
    [value]
  )
  const handleDragEnd = () => {
    setCurMove(null)
  }
  const handleDragMove = (data, from, to) => {
    setCurMove(to)
    onChange(data)
  }

  return (
    <div className="ruleList">
      <DragSort onDragEnd={handleDragEnd} onChange={handleDragMove} data={value}>
        {value.map((it, index) => {
          const { name, currency } = it
          const classStyle = curMoveItem === index ? ` active` : ''
          return (
            <div className={'ruleItem' + classStyle} key={name}>
              <OrderSelect
                options={moneyProperties}
                style={{ width: 500 }}
                value={name}
                onChange={val => handlerChange('change', index, 'name', val)}
                valueKey="name"
                labelKey="label"
              ></OrderSelect>
              <OrderSelect
                valueKey="name"
                labelKey="label"
                style={{ width: 130, margin: '0 10px' }}
                value={currency}
                options={currencySelect()}
                onChange={_ => {}}
                disabled
              ></OrderSelect>
              <div className="operation">
                <EKBIcon name="#EDico-delete" className="oper" onClick={() => handlerChange('delete', index)} />
              </div>
            </div>
          )
        })}
      </DragSort>
      <div className="add-group-wrapper" onClick={() => handlerChange('create')}>
        <EKBIcon className={'add-group-wrapper-icon'} name="#EDico-b_add" />
        <div className={'add-group-wrapper-text'}>
          <T name={'添加分组'} />
        </div>
      </div>
    </div>
  )
}

export default EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data
}))(RuleSelect) as typeof RuleSelect
