import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import { formatInvoiceData } from '../../InvoiceCard/FormatInvoiceData'
import moment from 'moment'
import { OriginalInvoiceItem, PhotoInvoiceItem, InvoiceItem, InvoiceType, DetailItem } from '../type'
import { getTitleByInvoiceMark } from "../../InvoiceCard/InvoiceItem";

type InvoiceInfoFunc = (
  item: OriginalInvoiceItem | PhotoInvoiceItem
) => Pick<InvoiceItem, 'title' | 'typeName' | 'allMoney' | 'dateOrInfo' | 'isDeduction'>

export const invoiceInfoFuncMap: { [key in InvoiceType]: InvoiceInfoFunc } = {
  system_发票主体: getAddInvoiceInfo,
  system_出租车票: getTaxiInvoiceInfo,
  system_过路费发票: getTollInvoiceInfo,
  system_定额发票: getQuotaInvoiceInfo,
  system_火车票: getTrainInvoiceInfo,
  system_航空运输电子客票行程单: getPlaneInvoiceInfo,
  system_客运汽车发票: getBusInvoiceInfo,
  system_其他: getOtherInvoiceInfo,
  system_机打发票: getMachineInvoiceInfo,
  system_医疗发票: getMedicalInvoiceInfo,
  system_非税收入类票据: getNontaxInvoiceInfo,
  system_消费小票: getShoppingInvoiceInfo,
  system_海外发票: getOverseasInvoiceInfo,
  invoicePhoto: getPhotoInvoiceInfo
}

export const ReviewedInvoiceTypes = Object.keys(invoiceInfoFuncMap)

function getAddInvoiceInfo(item: OriginalInvoiceItem) {
  const { invoiceInfo = {} }: any = formatInvoiceData({ data: item, dateFormat: i18n.get('YYYY年MM月DD日') } as any)
  const { masterItems, type: invoiceType, invamt: totalAmount, detailItems: details } = invoiceInfo
  const title = getTitleByInvoiceMark(item?.master?.form, invoiceInfo)
  const { E_税额, E_是否抵扣 } = item.master.form
  return {
    typeName: INVOICE_TYPE()[invoiceType],
    title: title,
    allMoney: totalAmount,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    details,
    dateOrInfo: masterItems.filter((i: any) => i.type === 'date')[0].value
  }
}

function getTaxiInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_出租车票_发票所在地,
    E_system_出租车票_上车时间,
    E_system_出租车票_下车时间,
    E_system_出租车票_里程,
    E_system_出租车票_金额
  } = item.master.form
  const taxi_date = `${moment(E_system_出租车票_上车时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))}-${moment(
    E_system_出租车票_下车时间
  ).format('HH:mm')}`
  const km = E_system_出租车票_里程 ? `${E_system_出租车票_里程}km` : ''
  return {
    // @i18n-ignore
    title: E_system_出租车票_发票所在地 ? `出租车发票 (${E_system_出租车票_发票所在地})` : '',
    allMoney: E_system_出租车票_金额 ? E_system_出租车票_金额 : '',
    typeName: i18n.get('出租车票'),
    dateOrInfo: `${taxi_date} ${km}`
  }
}

function getTollInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_过路费发票_时间,
    E_system_过路费发票_入口,
    E_system_过路费发票_出口,
    E_system_过路费发票_金额
  } = item.master.form
  const start = E_system_过路费发票_入口 ? E_system_过路费发票_入口 : ''
  const end = E_system_过路费发票_出口 ? E_system_过路费发票_出口 : ''
  return {
    title: `${start} -- ${end}`,
    allMoney: E_system_过路费发票_金额,
    typeName: i18n.get('过路费票'),
    dateOrInfo: moment(E_system_过路费发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  }
}

function getQuotaInvoiceInfo(item: OriginalInvoiceItem) {
  const { E_system_定额发票_金额 } = item.master.form
  return {
    title: i18n.get('定额发票'),
    allMoney: E_system_定额发票_金额 ? E_system_定额发票_金额 : '',
    typeName: i18n.get('定额发票'),
    dateOrInfo: ''
  }
}

function getTrainInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_火车票_乘车时间,
    E_system_火车票_上车车站,
    E_system_火车票_下车车站,
    E_system_火车票_金额,
    E_税额,
    E_是否抵扣
  } = item.master.form
  const trainDate = moment(E_system_火车票_乘车时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const start = E_system_火车票_上车车站 ? E_system_火车票_上车车站 : ''
  const end = E_system_火车票_下车车站 ? E_system_火车票_下车车站 : ''
  return {
    title: `${start} -- ${end}`,
    allMoney: E_system_火车票_金额,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    typeName: i18n.get('铁路客票'),
    dateOrInfo: trainDate
  }
}

function getPlaneInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_航空运输电子客票行程单_乘机时间,
    E_system_航空运输电子客票行程单_出发站,
    E_system_航空运输电子客票行程单_到达站,
    E_system_航空运输电子客票行程单_金额,
    E_税额,
    E_是否抵扣
  } = item.master.form
  const planeDate = moment(E_system_航空运输电子客票行程单_乘机时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const start = E_system_航空运输电子客票行程单_出发站 ? E_system_航空运输电子客票行程单_出发站 : ''
  const end = E_system_航空运输电子客票行程单_到达站 ? E_system_航空运输电子客票行程单_到达站 : ''
  return {
    title: `${start} -- ${end}`,
    allMoney: E_system_航空运输电子客票行程单_金额,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    typeName: i18n.get('机票行程单'),
    dateOrInfo: planeDate
  }
}

function getBusInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_客运汽车发票_时间,
    E_system_客运汽车发票_出发车站,
    E_system_客运汽车发票_达到车站,
    E_system_客运汽车发票_金额,
    E_税额,
    E_是否抵扣
  } = item.master.form
  const start = E_system_客运汽车发票_出发车站 ? E_system_客运汽车发票_出发车站 : ''
  const end = E_system_客运汽车发票_达到车站 ? E_system_客运汽车发票_达到车站 : ''
  return {
    title: `${start} -- ${end}`,
    allMoney: E_system_客运汽车发票_金额,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    typeName: i18n.get('客运汽车票'),
    dateOrInfo: `${moment(E_system_客运汽车发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))}`
  }
}

function getMedicalInvoiceInfo(item: OriginalInvoiceItem) {
  const { E_system_医疗发票_开票日期, E_system_医疗发票_金额合计, E_system_医疗发票_发票种类 } = item.master.form
  return {
    title: `${E_system_医疗发票_发票种类}`,
    allMoney: E_system_医疗发票_金额合计,
    typeName: i18n.get('医疗发票'),
    dateOrInfo: moment(E_system_医疗发票_开票日期).format(i18n.get('YYYY年MM月DD日'))
  }
}

function getNontaxInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_非税收入类票据_开票日期,
    E_system_非税收入类票据_金额合计,
    E_system_非税收入类票据_发票种类,
    E_system_非税收入类票据_发票类别,
    E_system_非税收入类票据_收款单位
  } = item.master.form
  return {
    title: E_system_非税收入类票据_发票种类 || E_system_非税收入类票据_收款单位,
    allMoney: E_system_非税收入类票据_金额合计,
    typeName: E_system_非税收入类票据_发票类别,
    dateOrInfo: moment(E_system_非税收入类票据_开票日期).format(i18n.get('YYYY年MM月DD日'))
  }
}

function getMachineInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_机打发票_时间,
    E_system_机打发票_金额,
    E_system_机打发票_销售方名称,
    E_税额,
    E_是否抵扣
  } = item.master.form
  return {
    title: `${E_system_机打发票_销售方名称}`,
    allMoney: E_system_机打发票_金额,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    typeName: i18n.get('机打发票'),
    dateOrInfo: moment(E_system_机打发票_时间).format(i18n.get('YYYY年MM月DD日'))
  }
}

function getOtherInvoiceInfo(item: OriginalInvoiceItem) {
  const { E_system_其他_日期, E_system_其他_金额 } = item.master.form
  return {
    title: i18n.get('其他票据'),
    allMoney: E_system_其他_金额,
    typeName: i18n.get('其他票据'),
    dateOrInfo: moment(E_system_其他_日期).format(i18n.get('YYYY年MM月DD日'))
  }
}

function getOverseasInvoiceInfo(item: OriginalInvoiceItem) {
  const { E_system_海外发票_日期, E_system_海外发票_金额, E_system_海外发票_票据类型 } = item.master.form
  return {
    title: i18n.get('海外票据'),
    allMoney: E_system_海外发票_金额,
    typeName: E_system_海外发票_票据类型,
    dateOrInfo: moment(E_system_海外发票_日期).format(i18n.get('YYYY年MM月DD日'))
  }
}

function getShoppingInvoiceInfo(item: OriginalInvoiceItem) {
  const {
    E_system_消费小票_时间,
    E_system_消费小票_金额,
    E_system_消费小票_店名,
    E_税额,
    E_是否抵扣
  } = item.master.form
  return {
    title: `${E_system_消费小票_店名}`,
    allMoney: E_system_消费小票_金额,
    taxAmount: E_税额,
    isDeduction: E_是否抵扣,
    typeName: i18n.get('消费小票'),
    dateOrInfo: moment(E_system_消费小票_时间).format(i18n.get('YYYY年MM月DD日'))
  }
}

export function getPhotoInvoiceInfo(item: PhotoInvoiceItem, details?: any[]) {
  const entity = item.type
  const photoInvoiceInfo = {
    id: '',
    title: item.fileName,
    allMoney: '',
    typeName: i18n.get('发票照片'),
    dateOrInfo: '',
    imageSrc: item.url,
    hasPDF: false,
    formData: {},
    entity
  }
  const detailItemIndex = details?.findIndex(detail => {
    const attachments = detail.feeTypeForm.invoiceForm?.attachments || []
    return !!attachments.find(el => item.fileId === el.fileId?.id)
  })
  const detailItem = details[detailItemIndex]
  if (detailItem) {
    photoInvoiceInfo['feeDetailAmount'] = detailItem.feeTypeForm.amount
    photoInvoiceInfo['feeDetailName'] = detailItem.feeTypeId.fullname
    photoInvoiceInfo['detailNo'] = detailItemIndex
  }
  return photoInvoiceInfo
}

// 此处为何要前端进行计算？
export function calculateTotalMoneyOfDetails(items: DetailItem[]) {
  let totalAmount = 0
  let totalTaxAmount = 0
  items.forEach(v => {
    if (v.amount) {
      if (typeof v.amount === 'number' || typeof v.amount === 'string') {
        totalAmount += Number(v.amount)
      } else {
        totalAmount += Number(v.amount.standard)
      }
    }
    if (v.tax) {
      if (typeof v.tax === 'number' || typeof v.tax === 'string') {
        totalTaxAmount += Number(v.tax)
      } else {
        totalTaxAmount += Number(v.tax.standard)
      }
    }
  })

  return { totalAmount, totalTaxAmount }
}
