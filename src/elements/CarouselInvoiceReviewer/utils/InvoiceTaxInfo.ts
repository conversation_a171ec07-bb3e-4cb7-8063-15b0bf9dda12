import { get } from 'lodash'

interface SystemType {
  system_发票主体: string
  system_火车票: string
  system_客运汽车发票: string
  system_航空运输电子客票行程单: string
  system_其他: string
  system_过路费发票: string
  system_出租车票: string
  system_定额发票: string
}
export default class InvoiceTaxInfo {
  private get canEditList() {
    return ['system_发票主体', 'system_火车票', 'system_客运汽车发票', 'system_航空运输电子客票行程单', 'system_其他', 'system_过路费发票', 'system_机打发票'] // @i18n-ignore
  }
  public fnCanEditType = (type: keyof SystemType): boolean => {
    return this.canEditList.includes(type)
  }

  private get canState() {
    return ['paid', 'archived']
  }

  public fnCanState = (billState) => {
    return !this.canState.includes(billState)
  }

  private getInvoiceTaxAmount = (item, fromBatchInvoice?: boolean) => {
    const { master: { form }, taxAmount, details } = item
    const active = form['E_是否抵扣'] // @i18n-ignore
    if (taxAmount) {
      return typeof taxAmount === 'object' ? Number(taxAmount.standard) : Number(taxAmount)
    }
    if (!active) {
      return 0
    }
    if (!details.length || fromBatchInvoice) {
      // 兼容没有details 的情况，直接取form中的税额字段
      return Number(form['E_税额']?.standard || 0)
    }

    const total = details.reduce((cur, next) => {
      let num = get(next, 'form.E_system_发票明细_税额.standard') || 0 // @i18n-ignore
      let standardScale = get(next, 'form.E_system_发票明细_税额.standardScale') || 2 // @i18n-ignore
      return new Big(cur).plus(new Big(num)).toFixed(Number(standardScale))
    }, 0)
    return Number(total)
  }

  private getTicketTaxAmount = (item) => {
    const { master: { form }, taxAmount } = item
    if (taxAmount) {
      return typeof taxAmount === 'object' ? Number(taxAmount.standard) : Number(taxAmount)
    }
    return typeof form['E_税额'] === 'object' && form['E_税额'] !== null ? Number(form['E_税额'].standard) : 0 // @i18n-ignore
  }

  public getTaxAmount = (item, fromBatchInvoice?: boolean) => {
    const { master: { entityId } } = item
    if (entityId === 'system_发票主体') { // @i18n-ignore
      return this.getInvoiceTaxAmount(item, fromBatchInvoice)
    } else {
      return this.getTicketTaxAmount(item)
    }
  }

  public getTaxRate = (item) => {
    const { master: { entityId, form }, taxRate } = item
    if (taxRate !== undefined) {
      return Number(taxRate)
    } else {
      if (entityId !== 'system_发票主体') { // @i18n-ignore
        return !!form['E_税率'] ? Number(form['E_税率']) : 0 // @i18n-ignore
      }
      return 0
    }
  }

  public getAllMoney = (details) => {
    return details.reduce((cur, next) => {
      const { form } = next || { form: {} }
      const money = typeof form['E_system_发票明细_金额'] === 'object' && form['E_system_发票明细_金额'] !== null ? form['E_system_发票明细_金额'].standard : form['E_system_发票明细_金额'] || 0 // @i18n-ignore
      const tax = typeof form['E_system_发票明细_税额'] === 'object' && form['E_system_发票明细_税额'] !== null ? form['E_system_发票明细_税额'].standard : form['E_system_发票明细_税额'] || 0 // @i18n-ignore
      const standardScale = typeof form['E_system_发票明细_金额'] === 'object' && form['E_system_发票明细_金额'] !== null ? form['E_system_发票明细_金额'].standardScale : 2 // @i18n-ignore
      const num = new Big(Number(money)).plus(new Big(Number(tax)))
      return new Big(Number(cur)).plus(new Big(Number(num))).toFixed(Number(standardScale))
    }, 0)
  }
}
