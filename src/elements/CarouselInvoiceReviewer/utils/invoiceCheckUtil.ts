import SVG_GREEN_SHIELD from '../../../images/invoice-green-shield.svg'
import SVG_WAR from '../../../images/invoice-payerno-warning.svg'
import SVG_ERROR from '../../../images/invoice-payerno-error.png'
import { InvoiceItem, Payer, CheckStatus } from '../type'
import { InvoiceInfoCheckProps } from '../InvoiceInfoCheck'
import { toCDB } from '../../../lib/lib-util'

type StatusMap = {
  [key in CheckStatus]: {
    nameTip: string
    numberTip: string
    icon: string
  }
}
const checkedStatusMap: StatusMap = {
  SUCCESS: {
    nameTip: i18n.get('校验正确，和公司抬头结果一致'),
    numberTip: i18n.get('校验正确，和公司纳税人识别号一致'),
    icon: SVG_GREEN_SHIELD
  },
  NO_VISIBLE: {
    nameTip: i18n.get('校验失败，不属于可用的公司抬头'),
    numberTip: i18n.get('校验失败，不属于可用的纳税人识别号'),
    icon: SVG_WAR
  },
  NO_RESULT: {
    nameTip: '',
    numberTip: '',
    icon: SVG_ERROR
  }
}

const defaultCheckRes = { nameTip: '', numberTip: '', icon: '' }

export function getCurrentPayerInfo(item: InvoiceItem): Payer {
  const { entity, formData } = item
  return {
    name: formData[i18n.get(`E_{__k0}_购买方名称`, { __k0: entity })] as string,
    payerNo: formData[i18n.get(`E_{__k0}_购买方纳税人识别号`, { __k0: entity })] as string,
    date: formData[`E_${entity}_invdate`] as number
  }
}

export function getCurrentSellerInfo(item: InvoiceItem): Payer {
  const { entity, formData } = item
  return {
    name: formData[i18n.get(`E_{__k0}_销售方名称`, { __k0: entity })] as string,
    payerNo: formData[i18n.get(`E_{__k0}_销售方纳税人识别号`, { __k0: entity })] as string,
    date: formData[`E_${entity}_invdate`] as number
  }
}

export function checkInvoicePayerName(
  currentPayer: Payer,
  addInfo: {
    payersPool: Payer[]
    checkStatus: CheckStatus
    checkMessage: string
  }
): InvoiceInfoCheckProps {
  const { name, payerNo, date } = currentPayer
  const isPersonal = name === '个人' // @i18n-ignore
  const { payersPool, checkStatus, checkMessage } = addInfo
  const nameMatchedPayer = findNameMatchedPayer(currentPayer, payersPool)
  const numberMatchedPayer = findNumberMatchedPayer(currentPayer, payersPool)
  const isBefore20170701 = date < 1498838400000
  const hasPayerNo = !!payerNo
  // 当前发票号与用名称匹配到的发票号一致 或者没有名称匹配时发票号在信息池中存在
  const isNumberPassed = !hasPayerNo
    ? false
    : nameMatchedPayer
    ? payerNo === nameMatchedPayer.payerNo
    : !!numberMatchedPayer

  // 前端根据当前信息与后端的状态计算最终校验态
  const checkedRes = checkedStatusMap[checkStatus] || defaultCheckRes
  const title = name
  const icon = isPersonal || !checkStatus ? '' : !isBefore20170701 && !hasPayerNo ? SVG_WAR : checkedRes.icon
  const tooltip = isPersonal
    ? ''
    : !isBefore20170701 && !hasPayerNo
    ? i18n.get('开票信息不符')
    : checkedRes.nameTip || checkMessage
  // 开票信息不在信息池中，名称标红
  const className = isPersonal || nameMatchedPayer ? '' : 'red'
  // 2017年7月11号的发票如果校验出来的税号缺失或有误，则用附加信息标识出来
  const warningTitle = isPersonal || isBefore20170701 || !nameMatchedPayer || isNumberPassed ? '' : i18n.get('对应税号')
  const warningText =
    isPersonal || isBefore20170701 || !nameMatchedPayer || isNumberPassed ? '' : nameMatchedPayer.payerNo
  return {
    title,
    icon,
    tooltip,
    className,
    warningTitle,
    warningText
  }
}

export function checkInvoicePayerNumber(
  currentPayer: Payer,
  addInfo: {
    payersPool: Payer[]
    checkStatus: CheckStatus
    checkMessage: string
  }
): InvoiceInfoCheckProps {
  const { name, payerNo, date } = currentPayer
  const isPersonal = name === '个人' // @i18n-ignore
  const { payersPool, checkStatus, checkMessage } = addInfo
  const nameMatchedPayer = findNameMatchedPayer(currentPayer, payersPool)
  const numberMatchedPayer = findNumberMatchedPayer(currentPayer, payersPool)
  const isBefore20170701 = date < 1498838400000
  const hasPayerNo = !!payerNo
  // 当前发票号与用名称匹配到的发票号一致 或者没有名称匹配时发票号在信息池中存在
  const isNumberPassed = !hasPayerNo
    ? false
    : nameMatchedPayer
    ? payerNo === nameMatchedPayer.payerNo
    : !!numberMatchedPayer

  // 前端根据当前信息与后端的状态计算最终校验态
  const checkedRes = checkedStatusMap[checkStatus] || defaultCheckRes
  const title = hasPayerNo ? payerNo : i18n.get('无法获取')
  const icon = isPersonal || isBefore20170701 || !checkStatus ? '' : !hasPayerNo ? SVG_WAR : checkedRes.icon
  const tooltip =
    isPersonal || isBefore20170701
      ? ''
      : !hasPayerNo
      ? i18n.get('该发票上纳税人识别号信息缺失')
      : checkedRes.numberTip || checkMessage
  const className = isPersonal || isBefore20170701 || isNumberPassed ? '' : 'red'
  // 当且 1. 没有名称匹配到的发票 2.存在号码匹配发票时
  const warningTitle =
    isPersonal || isBefore20170701 || !isNumberPassed || nameMatchedPayer ? '' : i18n.get('对应购买方')
  const warningText =
    isPersonal || isBefore20170701 || !isNumberPassed || nameMatchedPayer ? '' : numberMatchedPayer.name
  return {
    title,
    icon,
    tooltip,
    className,
    warningTitle,
    warningText
  }
}

function findNameMatchedPayer(currentPayer: Payer, payersPool: Payer[]) {
  const { name: currentPayerName } = currentPayer
  return payersPool.find(payer => {
    let invoicePayerName = currentPayerName
      .replace(/（/g, '(')
      .replace(/）/g, ')')
      .replace(/\s+/g, '')

    invoicePayerName = toCDB(invoicePayerName)
    let companyPayerName = payer.name
      .replace(/（/g, '(')
      .replace(/）/g, ')')
      .replace(/\s+/g, '')
    companyPayerName = toCDB(companyPayerName)
    return invoicePayerName === companyPayerName
  })
}

function findNumberMatchedPayer(currentPayer: Payer, payersPool: Payer[]) {
  const { payerNo: currentPayerNumber } = currentPayer
  return payersPool.find(payer => payer.payerNo === (currentPayerNumber && currentPayerNumber.toUpperCase()))
}

export function getTaxAmount(detail: any = {}) {
  const { entityId, form = {} } = detail
  const tags = form[`E_${entityId}_价税合计`]
  // @i18n-ignore
  return new Big(getMoney(tags)).toFixed(2)
}
export function getMoney(money) {
  return money ? (typeof money === 'object' ? (money.standard ? money.standard : '0.00') : money) : '0.00'
}
