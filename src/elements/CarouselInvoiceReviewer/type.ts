import { MoneyIF } from '@ekuaibao/ekuaibao_types'
import React from "react";

export type Money =
  | number
  | string
  | {
      standard: number
      standardNumCode: string
      standardScale: string
      standardStrCode: string
      standardSymbol: string
      standardUnit: string
      tax?: {
        standard: number
      }
    }

export interface DetailItem {
  name: string
  id: string
  model: string //规格型号
  number: number // 总数量
  amount: Money
  tax: Money
  taxRate: string
}

export interface OriginalInvoiceItem {
  invoiceId: string
  attachment?: OriginalImage
  master: { id: string; entityId?: InvoiceType; form: { [key: string]: any } }
  type: InvoiceType
  details: DetailItem[]
  itemIds:string[]
}

export interface PhotoInvoiceItem {
  fileId: string
  fileName: string
  url: string
  thumbUrl: string
  type: 'invoicePhoto'
}

export interface FieldItem {
  name: string
  label: string | React.ReactNode
  key: string
  type: 'string' | 'number' | 'time' | 'date' | 'money' | 'distance' | 'percent'
  defaultValue?: string
  customRender?(item: InvoiceItem, ...args: any[]): React.ReactNode
}

export interface OriginalImage {
  id: string
  url: string
}

export type FieldList = FieldItem[]

export interface InvoiceItem {
  id: string
  imageSrc: string
  formData: { [key: string]: string | object | number }
  title: string
  allMoney: Money
  taxAmount?: Money // 可抵扣税额
  isDeduction?: boolean // 可抵扣
  dateOrInfo: string
  typeName: string
  fields?: FieldList
  source?: 'OCR' | 'UPLOAD' | 'QUERY' | 'WECHAT_CARD' | 'SCAN' | 'ALIPAY_CARD' | 'AIFAPIAO' | 'invoiceApplication' | 'QXY'
  hasPDF: boolean
  entity: InvoiceType
  entityId?: InvoiceType
  details?: DetailItem[]
  taxRate?: string
  itemIds?: string[]
  image?: any
  master?: {
    entityId: InvoiceType
    form: { [key: string]: string | object | number }
  }
  feeDetailAmount: MoneyIF
  feeDetailName: string
  detailNo: number
}

// @i18n-ignore
export type InvoiceType =
  | 'system_发票主体'
  | 'system_出租车票'
  | 'system_过路费发票'
  | 'system_定额发票'
  | 'system_火车票'
  | 'system_航空运输电子客票行程单'
  | 'system_客运汽车发票'
  | 'system_其他'
  | 'invoicePhoto'
  | 'system_医疗发票'
  | 'system_非税收入类票据'
  | 'system_机打发票'
  | 'system_消费小票'
  | 'system_海外发票'

export type SecondaryInvoiceType = 
 | 'ELECTRONIC_TRAIN_INVOICE'
 | 'ELECTRONIC_AIRCRAFT_INVOICE'

export interface Submitter {
  id: string
}

export interface Payer {
  name: string
  payerNo?: string
  date: number
}

export type CheckStatus = 'SUCCESS' | 'NO_VISIBLE' | 'NO_RESULT'
