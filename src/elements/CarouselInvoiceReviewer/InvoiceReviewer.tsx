import React, { Component } from 'react'
import { DetailViewer } from './DetailViewer'
import { ImageViewer } from './ImageViewer'
import styles from './InvoiceReviewer.module.less'
import { Submitter, InvoiceItem } from './type'
import { Tooltip, Icon } from 'antd'

const feenamefilter = ['waitingBatchBind', 'waitInvoiceManage', 'unifyInvoice']

interface InvoiceReviewerProps {
  visible: boolean
  total: number
  currentIndex: number
  itemData: InvoiceItem
  items: InvoiceItem[]
  submitter: Submitter
  invoiceManageable: boolean
  dataSource: any
  billState: string
  onClose: (detailNo: number) => void
  onPrevious(): void
  onNext(): void
  onNavigate(index: number): void
  onDeduct(id: string, isDeduction: boolean): void
  onEditTaxInfo(item: InvoiceItem): void
  onOpenEditReviewAmount(item: InvoiceItem): void
  riskData?: any
  modifyApproveMoney: boolean
  isFlowEditable: boolean
  isDetail?: boolean
  backLogOwnerId?: string
  source: string
}

interface InvoiceReviewerState {}

export class InvoiceReviewer extends Component<InvoiceReviewerProps, InvoiceReviewerState> {
  handleClose = () => {
    const { itemData, onClose } = this.props
    onClose && onClose(itemData.detailNo)
  }

  render() {
    const {
      visible,
      itemData,
      items,
      currentIndex,
      onNavigate,
      onDeduct,
      invoiceManageable,
      submitter,
      dataSource,
      onEditTaxInfo,
      onOpenEditReviewAmount,
      billState,
      riskData,
      modifyApproveMoney,
      isFlowEditable,
      isDetail,
      backLogOwnerId,
      source
    } = this.props

    if (!visible) {
      return null
    }

    return (
      <div className={styles['invoice-viewer-wrapper']} id="invoice-viewer-mask">
        <div className={styles['invoice-viewer']}>
          <div className={styles['invoice-viewer-title']}>
            <div className={styles['invoice-viewer-titleLeft']}>
              <div className={styles.title}>{i18n.get('发票审阅')}</div>
              {/* 统一开票来的不需要费用名称展示 */}
              {!feenamefilter.includes(source) && itemData?.feeDetailName && (
                <div className={styles['invoice-viewer-center']}>
                  <p className={styles['invoice-viewer-feeDetail']}>
                    {itemData.feeDetailName?.length >= 30 ? (
                      <Tooltip placement="bottom" title={itemData.feeDetailName}>
                        <span className={styles['invoice-viewer-feename']}>{itemData.feeDetailName}</span>
                      </Tooltip>
                    ) : (
                      <span>{itemData.feeDetailName}</span>
                    )}
                  </p>
                  <span className={styles['invoice-viewer-divider']}></span>
                  <span>
                    {i18n.get('费用总额')}:&nbsp;{itemData?.feeDetailAmount?.standardSymbol}
                    {itemData?.feeDetailAmount?.standard}
                  </span>
                </div>
              )}
            </div>
            <Icon className={styles['close-icon']} type="close" onClick={this.handleClose} />
          </div>
          <div className={styles['viewer-layout']}>
            <div className={styles.left}>
              <ImageViewer items={items} currentIndex={currentIndex} onNavigate={onNavigate} />
            </div>
            <div className={styles.right}>
              <DetailViewer
                item={itemData}
                billState={billState}
                dataSource={dataSource}
                onDeduct={onDeduct}
                onEditTaxInfo={onEditTaxInfo}
                onOpenEditReviewAmount={onOpenEditReviewAmount}
                invoiceManageable={invoiceManageable}
                submitter={submitter}
                riskData={riskData}
                modifyApproveMoney={modifyApproveMoney}
                isFlowEditable={isFlowEditable}
                isDetail={isDetail}
                backLogOwnerId={backLogOwnerId}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }
}
