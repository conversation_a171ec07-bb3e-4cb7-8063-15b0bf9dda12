import React, { Component } from 'react'
import { ImagePlayerManager, ImagePlayer, ControlBar, Navigator, Pager } from '@ekuaibao/image-player-manager'
import { get } from 'lodash'
import { Spin, Icon } from 'antd'
import './image-viewer.less'
import { app as api } from '@ekuaibao/whispered'
import ICON_FULLSCREEN from '../../images/icon-fullscreen.svg'
import ICON_FILE from '../../images/icon-file-text.svg'
import ICON_PDF from '../../images/icon-file-pdf.svg'
import ICON_SCAN from '../../images/icon-scan.png'
import SVG_XML from '../../images/SVG_Xml.svg'
import MarkupImage from '@ekuaibao/markup-image'
import '../../elements/InvoiceCard/exif'
import { InvoiceItem } from './type'
import { Fetch } from '@ekuaibao/fetch'
import { geShowInvoiceImg, isInvoiceImage } from '../../plugins/bills/layers/import-bill/util'

interface ImageViewerProps {
  currentIndex: number
  items: InvoiceItem[]
  playterFooterWrapperPosition?: 'top' | 'bottom'
  onNavigate: (index: number) => void
}

interface ImageViewerState {
  images: Array<{
    attachmentUrl?: string
    url?: string
    placeholder?: JSX.Element
    thumbPlaceholder?: JSX.Element
    fileName: string
    exifReady: boolean
    ready: boolean
    region?: number[]
    orientation?: number
    isPDF?: boolean
    imageSrc?: string
    source?: string
    pdfName?: string
  }>
}

export class ImageViewer extends Component<ImageViewerProps, ImageViewerState> {
  constructor(props: ImageViewerProps) {
    super(props)
    const images: ImageViewerState['images'] = []
    this.state = {
      images: images
    }
  }

  async componentDidMount() {
    const images: ImageViewerState['images'] = []
    const { items = [] } = this.props

    await this.fnCheckInvoiceImage()

    items.forEach(item => {
      const isOcrPDF = this.fnIsOcrPdf(item)
      const isOcrXml = this.fnIsOcrXml(item)
      const fileName = get(item, 'formData.E_system_发票主体_图片', '') // @i18n-ignore
      const isImage = isInvoiceImage({ fileName })
      const source = ['OVERSEAS_INVOICE','OCR']
      if (source.includes(item.source) && !!item.imageSrc && isImage) {
        const region = get(item, i18n.get('formData.E_system_发票主体_识别范围'))
        // const ready = !region || !region.length // 为了处理原图拍摄方向所做的一堆处理看起来没什么卵用还受跨域限制
        const ready = true
        images.push({
          attachmentUrl: item.imageSrc,
          url: item.imageSrc,
          fileName: get(item, i18n.get('formData.E_system_发票主体_图片'), ''),
          ready: ready,
          exifReady: ready,
          region: region
        })
        ready || this.resolveExif(images.length - 1, images, region)
      } else if (
        (item.source === 'UPLOAD' && item.hasPDF) ||
        item.source === 'WECHAT_CARD' ||
        item.source === 'ALIPAY_CARD' ||
        (source.includes(item.source) && !item.imageSrc) ||
        (source.includes(item.source) && isOcrPDF) ||
        (source.includes(item.source) && isOcrXml) ||
        item.source === 'AIFAPIAO' ||
        item.source === 'QXY' || // 归集发票
        item.source === 'OUTPUT_OPEN_API'
      ) {
        const imageSrc = isOcrPDF || isOcrXml ? get(item, 'image.url') : item.imageSrc
        const pdfUrl =
          imageSrc &&
          this.getPdfUrl(
            imageSrc,
            get(item, 'image.fileName') || get(item, i18n.get('formData.E_system_发票主体_图片'))
          )
        images.push({
          attachmentUrl: pdfUrl,
          url: imageSrc,
          placeholder: (
            <div className="img-placeholder" onClick={imageSrc ? () => this.handleDownload(pdfUrl) : null}>
              {pdfUrl && <iframe src={pdfUrl} frameBorder="0" width="100%" height="100%" />}
              {item.source === 'AIFAPIAO' && !pdfUrl && (
                <>
                  <img src={ICON_FILE} /> {i18n.get('此发票由爱发票导入')}
                </>
              )}
               {item.source === 'QXY' && !pdfUrl && (
                <>
                  <img src={ICON_FILE} /> {i18n.get('此发票来源不支持预览')}
                </>
              )}
            </div>
          ),
          thumbPlaceholder: (
            <div className="thumb-placeholder">
              <img src={geShowInvoiceImg(item.image)} />
            </div>
          ),
          fileName: `${item.title}.${isOcrXml ? 'xml' : 'pdf'}`,
          exifReady: true,
          ready: true,
          isPDF: true,
          pdfName: item?.image?.fileName
        })
      } else if (item.entity === 'invoicePhoto') {
        images.push({
          attachmentUrl: item.imageSrc,
          url: item.imageSrc,
          fileName: item.title,
          ready: true,
          exifReady: true
        })
      } else if (item.source === 'SCAN') {
        images.push({
          placeholder: (
            <div className="img-placeholder">
              <img src={ICON_SCAN} /> {i18n.get('此发票通过扫一扫录入')}
            </div>
          ),
          thumbPlaceholder: (
            <div className="thumb-placeholder">
              <img src={ICON_SCAN} />
            </div>
          ),
          fileName: '',
          exifReady: true,
          ready: true
        })
      } else if (source.includes(item.source) && !isImage) {
        const imageSrc = isOcrPDF || isOcrXml ? get(item, 'image.url') : item.imageSrc
        images.push({
          attachmentUrl: imageSrc,
          url: imageSrc,
          placeholder: (
            <div className="img-placeholder" onClick={imageSrc ? () => this.handleDownload() : null}>
              <img src={geShowInvoiceImg(item.imageSrc)} />
            </div>
          ),
          thumbPlaceholder: (
            <div className="thumb-placeholder">
              <img src={geShowInvoiceImg(item.imageSrc)} />
            </div>
          ),
          fileName: `${item.title}.${isOcrXml ? 'xml' : 'pdf'}`,
          exifReady: true,
          ready: true
        })
      } else {
        images.push({
          placeholder: (
            <div className="img-placeholder">
              <img src={ICON_FILE} /> {i18n.get('此发票为手录发票')}
            </div>
          ),
          thumbPlaceholder: (
            <div className="thumb-placeholder">
              <img src={ICON_FILE} />
            </div>
          ),
          fileName: '',
          exifReady: true,
          ready: true
        })
      }
    })
    const pdfs = images.filter(item => item?.isPDF && item?.pdfName?.length)
    if (pdfs?.length) {
      const promises = pdfs.map(pdf => {
        return api.invokeService('@bills:get:preview:wps', { corpId: Fetch.ekbCorpId, key: pdf.pdfName })
      })
      const items = await Promise.all(promises)
      pdfs.forEach((pdf, index) => {
        const urlValue = items?.[index]
        const pdfUrl = urlValue?.value?.url
        pdf.attachmentUrl = pdfUrl
        pdf.placeholder = (
          <div className="img-placeholder" onClick={pdf.imageSrc ? () => this.handleDownload(pdfUrl) : null}>
            {pdfUrl && <iframe src={pdfUrl} frameBorder="0" width="100%" height="100%" />}
            {pdf.source === 'AIFAPIAO' && !pdfUrl && (
              <>
                <img src={ICON_FILE} /> {i18n.get('此发票由爱发票导入')}
              </>
            )}
             {pdf.source === 'QXY' && !pdfUrl && (
              <>
                <img src={ICON_FILE} /> {i18n.get('此发票来源不支持预览')}
              </>
            )}
          </div>
        )
      })
    }
    this.setState({ images })
  }

  fnCheckInvoiceImage = async () => {
    const { items = [] } = this.props
    // 如果进入审阅模式比较快，发票的图片在之前的页面没有请求到，可能会导致显示不出来，在此补偿一次请求
    const retryFetchImages: Record<string, any> = {}
    const invoiceIds = []
    items.forEach(item => {
      if (
        (item.source === 'OCR' ||
          (item.source === 'UPLOAD' && item.hasPDF) ||
          item.source === 'WECHAT_CARD' ||
          item.source === 'ALIPAY_CARD' ||
          item.source === 'AIFAPIAO') &&
        !item.imageSrc &&
        !item.image
      ) {
        retryFetchImages[item.id] = item
        invoiceIds.push(item.id)
      }
    })
    if (!!invoiceIds.length) {
      const res = await api.invokeService('@bills:get:invoice:image:by:ids', invoiceIds)
      res?.items?.forEach(item => {
        const invoice = retryFetchImages[item.id]
        if (invoice) {
          invoice.imageSrc = item.url
          invoice.image = item
        }
      })
    }
  }


  render() {
    const { currentIndex, playterFooterWrapperPosition = 'top', onNavigate } = this.props
    const { images } = this.state
    if (!images?.length) {
      return (
        <div className="image-player-wrapper">
          <div className="loading">
            <Spin indicator={<Icon type="loading" style={{ fontSize: 24 }} spin />} tip={i18n.get('加载中...')} />
          </div>
        </div>
      )
    }
    const ready = images.reduce<boolean>((pre, cur) => pre && cur.ready, true)

    const renderPlayerFooterWrapper = () => {
      const { attachmentOperationControl = false } = api.getState('@common.safeSetting')
      return (
        <div
          className="player-footer-wrapper"
          style={playterFooterWrapperPosition === 'top' ? { top: '0px' } : { bottom: '68px' }}
        >
          <Pager />
          {!attachmentOperationControl && <ControlBar onDownload={this.handleDownload} />}
          {!images[currentIndex]?.placeholder && (
            <div className="fullscreen" onClick={this.handleImagePreview} style={{ cursor: 'pointer' }}>
              <img src={ICON_FULLSCREEN} width="22" height="22" style={{ verticalAlign: 'middle' }} />
            </div>
          )}
        </div>
      )
    }

    return (
      <div
        className={`image-player-wrapper ${playterFooterWrapperPosition === 'top' ? 'footer-top' : 'footer-bottom'}`}
      >
        {images.map((image, index) => {
          if (image.ready || !image.exifReady) {
            return null
          }
          return (
            <MarkupImage
              key={index}
              markups={[
                {
                  region: image.region,
                  strokeStyle: 'rgba(255, 0, 0, 0.18)',
                  fillStyle: 'rgba(255, 0, 0, 0.18)',
                  lineWidth: 1
                }
              ]}
              url={image.url}
              orientation={image.orientation}
              onImageLoadFinshed={(src: string) => this.handleImageLoaded(index, src)}
            />
          )
        })}
        {ready ? (
          <ImagePlayerManager images={images} index={currentIndex} onIndexChange={onNavigate}>
            {playterFooterWrapperPosition === 'top' && renderPlayerFooterWrapper()}
            <ImagePlayer />
            {playterFooterWrapperPosition === 'bottom' && renderPlayerFooterWrapper()}
            <Navigator />
          </ImagePlayerManager>
        ) : (
          <div className="image-player-wrapper">
            <div className="loading">
              <Spin indicator={<Icon type="loading" style={{ fontSize: 24 }} spin />} tip={i18n.get('加载中...')} />
            </div>
          </div>
        )}
      </div>
    )
  }

  private handleDownload = (src: string) => {
    api.emit('@vendor:open:link', src)
  }

  private fnIsOcrPdf = item => {
    const img = get(item, 'formData.E_system_发票主体_图片', '') // @i18n-ignore
    if (img == void 0) {
      return false
    }
    return img.toLowerCase().endsWith('.pdf') || img.toLowerCase().endsWith('.ofd')
  }

  private fnIsOcrXml = item => {
    const img = get(item, 'formData.E_system_发票主体_图片', '') // @i18n-ignore
    if (img == void 0) {
      return false
    }
    return img.toLowerCase().endsWith('.xml')
  }

  private getPdfUrl = (src: string, fileName: string) => {
    const isOcrXml = fileName.toLowerCase().endsWith('.xml')
    let resultUrl = `${window.PREVIEW_DOMAIN}/view/url?url=${encodeURIComponent(src)}&name=${fileName ||
      `unnamed.${isOcrXml ? 'xml' : 'pdf'}`}`
    const watermark = api.getState()['@common'].waterMark
    if (watermark && watermark !== '') {
      resultUrl = resultUrl + '&watermark=' + encodeURIComponent(watermark)
    }
    return resultUrl
  }

  private handleImagePreview = () => {
    const { currentIndex } = this.props
    const { images } = this.state
    const image = {
      ...images[currentIndex],
      url: images[currentIndex].url
    }
    api.emit('@vendor:preview:images', [image], image)
  }

  private handleImageLoaded = (index: number, src: string) => {
    const { images } = this.state
    images[index] = {
      ...images[index],
      url: src,
      ready: true
    }
    this.setState({ images })
  }

  private resolveExif = (
    index: number,
    images: ImageViewerState['images'],
    region: [number, number, number, number]
  ) => {
    const image = images[index]
    const img = document.createElement('img')
    img.onload = () => {
      if (region && region[0] === 0 && region[1] === 0 && region[2] === img.width && region[3] === img.height) {
        const nextImages = [...this.state.images]
        nextImages[index] = {
          ...nextImages[index],
          exifReady: false,
          ready: true,
          orientation: 0
        }
        this.setState({
          images: nextImages
        })
        return
      }
      const { EXIF } = window as any
      EXIF.getData(img, () => {
        const Orientation = EXIF.getTag(img, 'Orientation')
        switch (Orientation) {
          case 6:
            this.setOrientation(index, 90)
            break
          case 8:
            this.setOrientation(index, 180)
            break
          case 3:
            this.setOrientation(index, 270)
            break
          case 1:
          default:
            this.setOrientation(index, 0)
        }
      })
    }
    img.src = image.url
  }

  private setOrientation = (index: number, orientation: number) => {
    const { images } = this.state
    const nextImages = [...images]
    nextImages[index] = {
      ...nextImages[index],
      exifReady: true,
      orientation: orientation
    }
    this.setState({
      images: nextImages
    })
  }
}
