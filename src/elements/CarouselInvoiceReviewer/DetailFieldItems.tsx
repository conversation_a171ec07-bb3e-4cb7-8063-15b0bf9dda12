import React from 'react'
import { InvoiceInfoCheck } from './InvoiceInfoCheck'
import {
  FieldList,
  InvoiceItem,
  InvoiceType,
  Payer,
  CheckStatus,
  OriginalInvoiceItem,
  PhotoInvoiceItem,
  OriginalImage,
  SecondaryInvoiceType,
  FieldItem
} from './type'
import {
  checkInvoicePayerName,
  checkInvoicePayerNumber,
  getCurrentPayerInfo,
  getCurrentSellerInfo
} from './utils/invoiceCheckUtil'
import { get } from 'lodash'
import { isElectronicAirAndTrain } from '../../plugins/bills/layers/import-bill/invoice/ImportInvoiceUtils'
import { invoiceInfoFuncMap, getPhotoInvoiceInfo } from './utils/InvoiceDetailUtil'
import { TwoToneLogoAi } from '@hose/eui-icons'

const isShowCheck = (item, isSeller) => {
  const { entity, formData } = item
  const mark = formData[i18n.get(`E_{__k0}_进销项标识`, { __k0: entity })]
  return isShow(isSeller, mark)
}

export const isShow = (isSeller, mark) => {
  if (mark === 'OUTPUT_INVOICE_RECEIPT') {
    return isSeller
  } else {
    return !isSeller
  }
}

const  BuyerName:FieldItem = {
  name: 'buyerName',
  label: i18n.get('购买方名称'),
  type: 'string',
  key: 'E_system_发票主体_购买方名称', // @i18n-ignore
  customRender: (
    item: InvoiceItem,
    additionalInfo: {
      payersPool: Payer[]
      checkStatus: CheckStatus
      checkMessage: string
    }
  ) => {
    const currentPayer = getCurrentPayerInfo(item)
    if (!isShowCheck(item, false)) {
      return currentPayer.name
    }
    const payerNameCheckMsg = checkInvoicePayerName(currentPayer, additionalInfo)
    return <InvoiceInfoCheck {...payerNameCheckMsg} />
  }
}

const BuyerCode:FieldItem = {
  name: 'buyerCode',
  label: i18n.get('纳税人识别号'),
  type: 'string',
  key: 'E_system_发票主体_购买方纳税人识别号',
  customRender: (
    item: InvoiceItem,
    additionalInfo: {
      payersPool: Payer[]
      checkStatus: CheckStatus
      checkMessage: string
    }
  ) => {
    const currentPayer = getCurrentPayerInfo(item)
    if (!isShowCheck(item, false)) {
      return currentPayer.payerNo
    }
    const payerNameCheckMsg = checkInvoicePayerNumber(currentPayer, additionalInfo)
    return <InvoiceInfoCheck {...payerNameCheckMsg} />
  }
}

const Number:FieldItem= { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_发票主体_发票号码' }

const SecondaryInvoiceEntity: {
  [key in SecondaryInvoiceType]: FieldList
} = {
  ELECTRONIC_TRAIN_INVOICE:[
    BuyerName,
    BuyerCode,
    Number,
    { name: 'name', label: i18n.get('乘车人姓名'), type: 'string', key: 'E_system_发票主体_乘车人姓名' },
    { name: 'time', label: i18n.get('乘车时间'), type: 'time', key: 'E_system_发票主体_乘车时间' },
    { name: 'trainNumber', label: i18n.get('车次'), type: 'string', key: 'E_system_发票主体_车次' },
    { name: 'type', label: i18n.get('座位类型'), type: 'string', key: 'E_system_发票主体_座位类型' },
    { name: 'businessType', label: i18n.get('业务类型'), type: 'string', key: 'E_system_发票主体_业务类型' }
  ],
  ELECTRONIC_AIRCRAFT_INVOICE:[
    BuyerName,
    BuyerCode,
    Number,
    { name: 'name', label: i18n.get('乘机人姓名'), type: 'string', key: 'E_system_发票主体_乘机人姓名' },
    { name: 'time', label: i18n.get('乘机时间'), type: 'time', key: 'E_system_发票主体_乘机时间' },
    { name: 'seat', label: i18n.get('电子客票号码'), type: 'string', key: 'E_system_发票主体_电子客票号码' },
    { name: 'flightNumber', label: i18n.get('航班号'), type: 'string', key: 'E_system_发票主体_航班号' },
    { name: 'flight', label: i18n.get('航班'), type: 'string', key: 'E_system_发票主体_航班' },
    { name: 'seatType', label: i18n.get('航班舱型'), type: 'string', key: 'E_system_发票主体_航班舱型' },
  ]
}

export const invoiceFieldMap: {
         [key in InvoiceType]: FieldList
       } = {
         system_发票主体: [
           // @i18n-ignore
           // 购方名称、购方纳税人识别号、销售方名称、发票代码、发票号码、开票日期、价税合计、税额、备注
            BuyerName,
            BuyerCode,
           // @i18n-ignore
           {
             name: 'sellerName',
             label: i18n.get('销售方名称'),
             type: 'string',
             key: 'E_system_发票主体_销售方名称',
             customRender: (
               item: InvoiceItem,
               additionalInfo: {
                 payersPool: Payer[]
                 checkStatus: CheckStatus
                 checkMessage: string
               }
             ) => {
               const currentPayer = getCurrentSellerInfo(item)
               if (!isShowCheck(item, true)) {
                 return currentPayer.name
               }
               const payerNameCheckMsg = checkInvoicePayerName(currentPayer, additionalInfo)
               return <InvoiceInfoCheck {...payerNameCheckMsg} />
             }
           },
           {
             name: 'sellerCode',
             label: i18n.get('纳税人识别号'),
             type: 'string',
             key: 'E_system_发票主体_销售方纳税人识别号',
             customRender: (
               item: InvoiceItem,
               additionalInfo: {
                 payersPool: Payer[]
                 checkStatus: CheckStatus
                 checkMessage: string
               }
             ) => {
               const currentPayer = getCurrentSellerInfo(item)
               if (!isShowCheck(item, true)) {
                 return currentPayer.payerNo
               }
               const payerNameCheckMsg = checkInvoicePayerNumber(currentPayer, additionalInfo)
               return <InvoiceInfoCheck {...payerNameCheckMsg} />
             }
           },
           Number,
          // @i18n-ignore
          { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_发票主体_发票代码' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_发票主体_发票日期' },
           // @i18n-ignore
           { name: 'amountWithTax', label: i18n.get('价税合计'), type: 'money', key: 'E_system_发票主体_价税合计' },
           // @i18n-ignore
           { name: 'tax', label: i18n.get('税额'), type: 'money', key: 'E_system_发票主体_税额' },
           // @i18n-ignore
           {
             name: 'remark',
             label: i18n.get('备注'),
             type: 'string',
             key: 'E_system_发票主体_备注',
             defaultValue: i18n.get('无')
           }
         ],
         system_出租车票: [
           // @i18n-ignore
           // @i18n-ignore
           { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_出租车票_发票代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_出租车票_发票号码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('车牌号码'), type: 'string', key: 'E_system_出租车票_车牌号码' },
           // @i18n-ignore
           { name: 'startTime', label: i18n.get('上车时间'), type: 'time', key: 'E_system_出租车票_上车时间' },
           // @i18n-ignore
           { name: 'endTime', label: i18n.get('下车时间'), type: 'time', key: 'E_system_出租车票_下车时间' },
           // @i18n-ignore
           { name: 'distance', label: i18n.get('里程'), type: 'distance', key: 'E_system_出租车票_里程' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_出租车票_金额' },
           // @i18n-ignore
           { name: 'location', label: i18n.get('所在地'), type: 'string', key: 'E_system_出租车票_发票所在地' }
         ],
         system_过路费发票: [
           // @i18n-ignore
           // @i18n-ignore
           { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_过路费发票_发票代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_过路费发票_发票号码' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_过路费发票_时间' },
           // @i18n-ignore
           { name: 'startLocation', label: i18n.get('入口'), type: 'string', key: 'E_system_过路费发票_入口' },
           // @i18n-ignore
           { name: 'endLocation', label: i18n.get('出口'), type: 'string', key: 'E_system_过路费发票_出口' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_过路费发票_金额' }
         ],
         system_定额发票: [
           // @i18n-ignore
           // @i18n-ignore
           { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_定额发票_发票代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_定额发票_号码' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_定额发票_金额' }
         ],
         system_火车票: [
           // @i18n-ignore
           // 车票号、始发地、目的地、车次、日期时间、价格、席别、姓名
           // @i18n-ignore
           { name: 'number', label: i18n.get('车票号'), type: 'number', key: 'E_system_火车票_号码' },
           // @i18n-ignore
           { name: 'name', label: i18n.get('乘车人'), type: 'string', key: 'E_system_火车票_乘车人姓名' },
           // @i18n-ignore
           { name: 'trainNumber', label: i18n.get('车次'), type: 'string', key: 'E_system_火车票_车次' },
           // @i18n-ignore
           { name: 'seat', label: i18n.get('席别'), type: 'string', key: 'E_system_火车票_座位类型' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('乘车时间'), type: 'time', key: 'E_system_火车票_乘车时间' },
           // @i18n-ignore
           { name: 'startLocation', label: i18n.get('出发站'), type: 'string', key: 'E_system_火车票_上车车站' },
           // @i18n-ignore
           { name: 'endLocation', label: i18n.get('到达站'), type: 'string', key: 'E_system_火车票_下车车站' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_火车票_金额' }
         ],
         system_航空运输电子客票行程单: [
           // @i18n-ignore
           // 乘机人姓名、票价、税费、燃油附加费、民航发展基金、总额、保险费、出发站、到达站、航班号、乘机日期、乘机时间、填开日期、座位等级
           // @i18n-ignore
           {
             name: 'name',
             label: i18n.get('乘机人'),
             type: 'string',
             key: 'E_system_航空运输电子客票行程单_乘机人姓名'
           },
           // @i18n-ignore
           {
             name: 'flightNumber',
             label: i18n.get('航班号'),
             type: 'string',
             key: 'E_system_航空运输电子客票行程单_航班号'
           },
           // @i18n-ignore
           { name: 'seat', label: i18n.get('航班舱型'), type: 'string', key: 'E_system_航空运输电子客票行程单_航班舱型' },
           // @i18n-ignore
           {
             name: 'seat',
             label: i18n.get('电子客票号'),
             type: 'string',
             key: 'E_system_航空运输电子客票行程单_电子客票号码'
           },
           // @i18n-ignore
           { name: 'time', label: i18n.get('乘机时间'), type: 'time', key: 'E_system_航空运输电子客票行程单_乘机时间' },
           // @i18n-ignore
           {
             name: 'issueDate',
             label: i18n.get('填开日期'),
             type: 'date',
             key: 'E_system_航空运输电子客票行程单_填开日期'
           },
           // @i18n-ignore
           {
             name: 'startLocation',
             label: i18n.get('出发站'),
             type: 'string',
             key: 'E_system_航空运输电子客票行程单_出发站'
           },
           // @i18n-ignore
           {
             name: 'endLocation',
             label: i18n.get('到达站'),
             type: 'string',
             key: 'E_system_航空运输电子客票行程单_到达站'
           },
           // @i18n-ignore
           {
             name: 'moneyAmount',
             label: i18n.get('总金额'),
             type: 'money',
             key: 'E_system_航空运输电子客票行程单_金额'
           },
           // @i18n-ignore
           {
             name: 'ticketAmount',
             label: i18n.get('票价'),
             type: 'money',
             key: 'E_system_航空运输电子客票行程单_票价'
           },
           // @i18n-ignore
           { name: 'tax', label: i18n.get('税费'), type: 'money', key: 'E_system_航空运输电子客票行程单_税费' },
           // @i18n-ignore
           {
             name: 'fuel',
             label: i18n.get('燃油附加费'),
             type: 'money',
             key: 'E_system_航空运输电子客票行程单_燃油附加费'
           },
           // @i18n-ignore
           {
             name: 'insurance',
             label: i18n.get('保险费'),
             type: 'money',
             key: 'E_system_航空运输电子客票行程单_保险费'
           },
           // @i18n-ignore
           {
             name: 'fund',
             label: i18n.get('民航发展基金'),
             type: 'money',
             key: 'E_system_航空运输电子客票行程单_民航发展基金'
           }
         ],
         system_医疗发票: [
           // @i18n-ignore
           // 发票代码、发票号码、乘车日期时间、出发车站、达到车站、总金额、姓名
           // @i18n-ignore
           { name: 'code', label: i18n.get('票据代码'), type: 'number', key: 'E_system_医疗发票_票据代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('票据号码'), type: 'number', key: 'E_system_医疗发票_票据号码' },
           // @i18n-ignore
           { name: 'name', label: i18n.get('交款人'), type: 'string', key: 'E_system_医疗发票_交款人' },
           // @i18n-ignore
           { name: 'type', label: i18n.get('发票种类'), type: 'string', key: 'E_system_医疗发票_发票种类' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_医疗发票_开票日期' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('金额合计'), type: 'money', key: 'E_system_医疗发票_金额合计' }
         ],
         system_非税收入类票据: [
           // @i18n-ignore
           { name: 'code', label: i18n.get('票据代码'), type: 'number', key: 'E_system_非税收入类票据_票据代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('票据号码'), type: 'number', key: 'E_system_非税收入类票据_票据号码' },
           // @i18n-ignore
           { name: 'name', label: i18n.get('校验码'), type: 'string', key: 'E_system_非税收入类票据_校验码' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_非税收入类票据_开票日期' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('金额合计'), type: 'money', key: 'E_system_非税收入类票据_金额合计' }
         ],
         system_客运汽车发票: [
           // @i18n-ignore
           // 发票代码、发票号码、乘车日期时间、出发车站、达到车站、总金额、姓名
           // @i18n-ignore
           { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_客运汽车发票_发票代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_客运汽车发票_发票号码' },
           // @i18n-ignore
           { name: 'name', label: i18n.get('乘车人'), type: 'string', key: 'E_system_客运汽车发票_姓名' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('乘车日期'), type: 'date', key: 'E_system_客运汽车发票_时间' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_客运汽车发票_金额' },
           // @i18n-ignore
           { name: 'startLocation', label: i18n.get('出发地'), type: 'string', key: 'E_system_客运汽车发票_出发车站' },
           // @i18n-ignore
           { name: 'endLocation', label: i18n.get('到达地'), type: 'string', key: 'E_system_客运汽车发票_达到车站' }
         ],
         system_机打发票: [
           // @i18n-ignore
           // 发票代码、发票号码、乘车日期时间、出发车站、达到车站、总金额、姓名
           // @i18n-ignore
           { name: 'code', label: i18n.get('发票代码'), type: 'number', key: 'E_system_机打发票_发票代码' },
           // @i18n-ignore
           { name: 'number', label: i18n.get('发票号码'), type: 'number', key: 'E_system_机打发票_发票号码' },
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_机打发票_时间' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_机打发票_金额' },
           // @i18n-ignore
           { name: 'sellerName', label: i18n.get('销售方名称'), type: 'string', key: 'E_system_机打发票_销售方名称' }
         ],
         system_其他: [
           // 金额、日期
           // @i18n-ignore
           { name: 'time', label: i18n.get('开票日期'), type: 'date', key: 'E_system_其他_日期' },
           // @i18n-ignore
           { name: 'moneyAmount', label: i18n.get('总金额'), type: 'money', key: 'E_system_其他_金额' }
         ],
         system_海外发票: [
          {
            name: 'summary',
            label: (
              <div className="invoice-ai-summary">
                <TwoToneLogoAi />
                {i18n.get('AI智能总结')}
              </div>
            ),
            type: 'string',
            key: 'E_system_海外发票_AI智能总结',
            customRender: (item: InvoiceItem) => {
              const summary = get(item, 'formData.E_system_海外发票_AI智能总结', '')
              if (!summary?.length) {
                return <div className="invoice-ai-summary-content">{'-'}</div>
              }
              const summaryTexts = summary.split('\n')
              return (
                <div className="invoice-ai-summary-content">
                  {summaryTexts.map((text: string, index: number) => {
                    return (
                      <div key={index} className="invoice-ai-summary-content-row">
                        {text}
                      </div>
                    )
                  })}
                </div>
              )
            }
          },
          { name: 'name', label: i18n.get('消费标题'), type: 'string', key: 'E_system_海外发票_title' },
          // 金额、日期
          // @i18n-ignore
          { name: 'time', label: i18n.get('日期'), type: 'date', key: 'E_system_海外发票_日期' },
          // @i18n-ignore
          // { name: 'moneyAmount', label: i18n.get('合计金额'), type: 'money', key: 'E_system_海外发票_金额' },
          // { name: 'TaxMoney', label: i18n.get('税额'), type: 'money', key: 'E_system_海外发票_税额' },
          // { name: 'noTaxMoney', label: i18n.get('不计税金额'), type: 'money', key: 'E_system_海外发票_不计税金额' },
          { name: 'rates', label: i18n.get('税率'), type: 'percent', key: 'E_system_海外发票_税率' },
          { name: 'Country', label: i18n.get('消费国家'), type: 'string', key: 'E_system_海外发票_消费国家' },
          { name: 'number', label: i18n.get('票据号码'), type: 'string', key: 'E_system_海外发票_票据号码' },
          { name: 'buyer', label: i18n.get('购买方'), type: 'string', key: 'E_system_海外发票_购买方名称' },
          { name: 'buyerTax', label: i18n.get('纳税人识别号'), type: 'string', key: 'E_system_海外发票_购买方纳税人识别号' },
          { name: 'seller', label: i18n.get('销售方名称'), type: 'string', key: 'E_system_海外发票_销售方名称' },
          { name: 'sellerTax', label: i18n.get('纳税人识别号'), type: 'string', key: 'E_system_海外发票_销售方纳税人识别号' }
        ],
         invoicePhoto: []
       }

export function getInvoiceInfo(
  item: OriginalInvoiceItem | PhotoInvoiceItem,
  imageList: OriginalImage[],
  details?: any
): InvoiceItem {
  if (!(item as any).master) {
    // @ts-ignore
    return getPhotoInvoiceInfo(item as PhotoInvoiceItem, details)
  }
  const { master, attachment, type: entityType, itemIds } = item as OriginalInvoiceItem
  const id = master && master.id
  const entityId = master && master.entityId
  const entity: InvoiceType = entityId || entityType
  const image = attachment ? attachment : imageList.find(image => image?.id === id)
  // @i18n-ignore
  const source = get(item, 'master.form.E_system_发票主体_来源')
  const entityInvoiceType = get(item, 'master.form.E_system_发票主体_发票类别')
  const otherInfo = invoiceInfoFuncMap[entity](item)
  let feeDetailAmount
  let feeDetailName = ''
  let detailNo = 0
  details &&
    details.length !== 0 &&
    details.forEach((detailItem,index) => {
      if(detailItem?.feeTypeForm?.invoiceForm?.invoices) {
        detailItem.feeTypeForm?.invoiceForm?.invoices?.forEach(invoicesItem => {
          const hasItemIds = (invoicesItem?.itemIds || []).some(item => itemIds?.includes(item))
          if(itemIds?.length && hasItemIds ){
             feeDetailAmount = detailItem.feeTypeForm.amount
             feeDetailName = detailItem.feeTypeId.fullname
             detailNo = index
          }else if (!itemIds?.length && invoicesItem.invoiceId === id) {
            feeDetailAmount = detailItem.feeTypeForm.amount
            feeDetailName = detailItem.feeTypeId.fullname
            detailNo = index
          }
        })
      }
    })

  let Entity = invoiceFieldMap[entity]
  if(isElectronicAirAndTrain(master.form)){
    Entity = SecondaryInvoiceEntity[entityInvoiceType]
  }

  return {
    id,
    imageSrc: image && image.thumbUrl ? image.url : '', //ocr识别的pdf image.thumbUrl为空要使用默认的图片
    source,
    hasPDF: source === 'UPLOAD',
    formData: master.form,
    entity,
    image,
    fields: Entity,
    feeDetailAmount,
    feeDetailName,
    detailNo,
    ...otherInfo
  }
}
