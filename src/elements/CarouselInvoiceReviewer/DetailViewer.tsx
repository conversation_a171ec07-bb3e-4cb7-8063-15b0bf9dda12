import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { EnhanceConnect } from '@ekuaibao/store'
import React, { Component } from 'react'
import { Popover, Tooltip } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import SVG_INVOICE_PDF from '../../images/icon-file-pdf.svg'
import SVG_INVOICE_TEXT from '../../images/icon-file-text.svg'
import SVG_WARNING from '../../images/warning.svg'
import Money from '../puppet/Money'
import styles from './InvoiceReviewer.module.less'
import moment from 'moment'
import { calculateTotalMoneyOfDetails } from './utils/InvoiceDetailUtil'
import { InvoiceItem, FieldItem, Payer, CheckStatus, Submitter, Money as MoneyConfig } from './type'
import { get } from 'lodash'
import InvoiceTaxInfo from './utils/InvoiceTaxInfo'
import { getTaxAmount } from './utils/invoiceCheckUtil'
import { TupleInvoiceType } from '../ConditionalEditComponent/Type'
import {
  getInvoiceSignature,
  getInvoiceStatus,
  getIsCheckerInvoice,
  invoiceStatusStr
} from '../InvoiceCard/InvoiceItem'
import { invoiceMetaile } from '@ekuaibao/lib/lib/invoice/invoiceHelper'
import { T } from '@ekuaibao/i18n'
import { geShowInvoiceImg } from '../../plugins/bills/layers/import-bill/util'
import { getBoolVariation } from '../../lib/featbit'
import { isElectronicAirAndTrain } from '../../lib/InvoiceUtil'
const EKBIcon = api.require<any>('@elements/ekbIcon')
import { onlyOneCurrencyHideSeaLocalCurrrency } from '../../lib/featbit/feat-switch'

interface DetailViewerProps {
  item: InvoiceItem
  onDeduct(id: string, isDeduction: boolean): void
  invoiceManageable: boolean
  submitter: Submitter
  payersPool: Payer[]
  dataSource: any
  onEditTaxInfo(item: InvoiceItem): void
  onOpenEditReviewAmount(item: InvoiceItem): void
  billState?: string
  riskData?: any
  modifyApproveMoney?: boolean
  isFlowEditable?: boolean
  isDetail?: boolean
  backLogOwnerId?: string
  invoicePriceAndTaxEditPermission?: boolean,
  OverseasInvoicePower: boolean,
}

interface DetailViewerState {
  checkStatus: CheckStatus
  checkMessage: string
  invoiceComparedData: Record<string, any>
}

@EnhanceConnect((state: any) => ({
  payersPool: state['@common'].payerInfo,
  invoicePriceAndTaxEditPermission: state['@common'].invoicePriceAndTaxEditPermission,
  OverseasInvoicePower:state['@common'].powers.OverseasInvoice,
}))
export class DetailViewer extends Component<DetailViewerProps, DetailViewerState> {
  static defaultProps = {
    payersPool: [] as Payer[]
  }
  state: DetailViewerState = {
    checkStatus: 'SUCCESS',
    checkMessage: '',
    invoiceComparedData: {}
  }

  invoiceTaxInfo = new InvoiceTaxInfo()

  componentDidMount(): void {
    this.checkInvoice()
    this.getInvoiceComparedData()
  }

  componentDidUpdate(prevProps: Readonly<DetailViewerProps>): void {
    if (prevProps.item.id !== this.props.item.id) {
      // 发票切换时，重新校验
      this.checkInvoice()
      this.getInvoiceComparedData()
    }
  }

  render() {
    const { formData } = this.props.item
    const typeInvoice = get(formData, 'E_system_发票主体_发票类别') // @i18n-ignore
    const themeClass = `invoice-${this.getColorTheme(typeInvoice as string)}-theme`
    return (
      <div className={`${styles['invoice-detail-wrapper']} ${styles[themeClass]}`}>
        <div className={styles['invoice-detail']}>
          {this.renderInvoiceCard()}
          {this.renderFormData()}
          {this.renderFullDetail()}
        </div>
      </div>
    )
  }

  private getSRC = (source: string, imageSrc: string, image: any): string => {
    if (image) {
      const { fileName, key, thumbUrl } = image
      const name = fileName || key
      return geShowInvoiceImg({ fileName: name, thumbUrl: thumbUrl })
    }
    if (source === 'QUERY') {
      return SVG_INVOICE_TEXT
    } else if (
      source === 'UPLOAD' ||
      source === 'SCAN' ||
      source === 'WECHAT_CARD' ||
      source === 'ALIPAY_CARD' ||
      source === 'AIFAPIAO'
    ) {
      return SVG_INVOICE_PDF
    } else if (source === 'OCR') {
      return imageSrc ? imageSrc : SVG_INVOICE_PDF
    } else {
      return imageSrc
    }
  }

  private renderInvoiceEdit = (invoiceManageable: boolean) => {
    const { dataSource, onEditTaxInfo, billState, invoicePriceAndTaxEditPermission, isDetail } = this.props
    const { formData } = this.props.item
    const total = this.invoiceTaxInfo.getTaxAmount(dataSource)
    const taxRate = this.invoiceTaxInfo.getTaxRate(dataSource)
    const hasEditPermission = invoiceManageable || invoicePriceAndTaxEditPermission
    return (
      <>
        {
          isElectronicAirAndTrain(formData) && <div className={styles['invoice-total-row']}>
            <div className={styles['text']}>{i18n.get('税率')}</div>
            <div className={styles['money']}>
              {taxRate} {'%'}
            </div>
          </div>
        }
        <div className={styles['invoice-total-row']}>
          <div className={styles['text']}>{i18n.get('可抵扣税额')}</div>
          <Money className={styles['money']} value={total} />
        </div>
        {hasEditPermission && this.invoiceTaxInfo.fnCanState(billState) && (
          <div
            className={`${styles['invoice-total-row']} ${styles['invoice-edit']}`}
            onClick={e => onEditTaxInfo(dataSource)}
          >
            <div>{i18n.get('编辑')}</div>
          </div>
        )}
      </>
    )
  }

  private renderTicketsEdit = invoiceManageable => {
    const { dataSource, onEditTaxInfo, billState, item, invoicePriceAndTaxEditPermission, isDetail } = this.props
    const taxAmount = this.invoiceTaxInfo.getTaxAmount(dataSource)
    const taxRate = this.invoiceTaxInfo.getTaxRate(dataSource)
    const { invoiceComparedData } = this.state
    const changeInvoiceData = invoiceComparedData[item.id] || {}
    const taxRateChangeData = changeInvoiceData['E_税率'] // @i18n-ignore
    const taxAmountChangeData = changeInvoiceData['E_税额'] // @i18n-ignore
    const hasEditPermission = invoiceManageable || invoicePriceAndTaxEditPermission
    return (
      <>
        <div className={styles['row-line']} />
        <div className={styles['invoice-total-row']}>
          <div className={styles['text']}>{'税率'}</div>
          <div className={styles['money']}>
            {taxRate}
            {'%'}
          </div>
          {this.renderHistory(taxRateChangeData, {})}
        </div>
        <div className={styles['invoice-total-row']}>
          <div className={styles['text']}>{i18n.get('可抵扣税额')}</div>
          <Money className={styles['money']} value={taxAmount} />
          {this.renderHistory(taxAmountChangeData, { type: 'money' })}
        </div>
        {hasEditPermission && this.invoiceTaxInfo.fnCanState(billState) && (
          <div
            className={`${styles['invoice-total-row']} ${styles['invoice-edit']}`}
            onClick={e => onEditTaxInfo(dataSource)}
          >
            <div>{i18n.get('编辑')}</div>
          </div>
        )}
      </>
    )
  }

  private renderInvoiceReview = props => {
    const {
      dataSource,
      onOpenEditReviewAmount,
      billState,
      isFlowEditable,
      modifyApproveMoney,
      item: { entity, allMoney }
    } = props
    const showEdit = (billState === 'approving' || billState === 'paying') && isFlowEditable && modifyApproveMoney
    let { approveAmount, comment = '' } = dataSource
    if (!modifyApproveMoney || getBoolVariation('close-approveAmount-validate') ) {
      return <></>
    }
    const type = 'system_发票主体' // @i18n-ignore
    const isCheckerInvoice = getIsCheckerInvoice(dataSource?.master?.form)
    const allMoneyDetail = isCheckerInvoice
      ? this.invoiceTaxInfo.getAllMoney(dataSource.details)
      : getTaxAmount(dataSource.master)
    const amount = entity === type ? allMoneyDetail : allMoney
    approveAmount = approveAmount == void 0 ? amount : approveAmount
    const hasComment = comment ? !!comment.length : false
    const content = <p>{comment}</p>
    return (
      <div className={styles['row-line']}>
        <div className={styles['invoice-total-row']}>
          <div className={styles['text']}>
            {i18n.get('核发金额')}
            {hasComment ? <img className={styles['ml-4']} src={SVG_WARNING} /> : null}
          </div>
          <Money className={styles['money']} value={approveAmount} />
        </div>
        <div className={styles['invoice-total-row']}>
          <div className={styles['text']}>{i18n.get('核发批注')}</div>
          <Popover content={content} title="" placement="topRight">
            <div className={styles['invoice-show-txt text']}>{comment}</div>
          </Popover>
        </div>
        {showEdit && (
          <div
            className={`${styles['invoice-total-row']} ${styles['invoice-edit']}`}
            onClick={e => onOpenEditReviewAmount(dataSource)}
          >
            {i18n.get('编辑')}
          </div>
        )}
      </div>
    )
  }
  // 获取当前发票的提示内容
  private getContent = warningMsg => {
    return (
      <ul>
        {warningMsg.map((v, i) => {
          return <li key={i}>{v}</li>
        })}
      </ul>
    )
  }
  private renderInvoiceCard = () => {
    const { invoiceManageable, dataSource, riskData, modifyApproveMoney = false, OverseasInvoicePower } = this.props
    const {
      source,
      imageSrc,
      title,
      dateOrInfo,
      typeName,
      allMoney,
      entity,
      isDeduction,
      taxAmount,
      formData,
      details,
      image
    } = this.props.item
    const type = 'system_发票主体' // @i18n-ignore
    const typeInvoice = get(formData, 'E_system_发票主体_发票类别') // @i18n-ignore
    const isToken = typeInvoice === 'BLOCK_CHAIN'
    const isFullDigital = ['FULL_DIGITAl_SPECIAL', 'FULL_DIGITAl_NORMAL'].includes(typeInvoice) // 全电票
    const newTypeName = isToken ? i18n.get('区块链电子发票') : typeName
    const useSvgImage = ['UPLOAD', 'QUERY', 'SCAN', 'WECHAT_CARD', 'ALIPAY_CARD', 'AIFAPIAO'].indexOf(source) > -1
    const src = this.getSRC(source, imageSrc, image)
    const isCheckerInvoice = getIsCheckerInvoice(dataSource?.master?.form)
    const allMoneyDetail =
      isCheckerInvoice && Boolean(dataSource.details.length)
        ? this.invoiceTaxInfo.getAllMoney(dataSource.details)
        : getTaxAmount(dataSource.master)
    console.log(this.props, riskData, 'riskDataddd')
    const [className, url] = riskData?.isForbid
      ? ['error', '#EDico-plaint-circle']
      : ['warning', '#EDico-plaint-circle']
    const warningMsg = riskData?.singleInvoiceRiskWarning?.find(v => v.invoiceId === this.props.item?.id)?.riskWarning
    console.log(warningMsg, 'warningMsg')
    // @i18n-ignore
    const isInvoice = dataSource?.master?.entityId === 'system_发票主体'
    const status = getInvoiceStatus(dataSource?.master?.form)
    // @i18n-ignore
    const realTagClass = isCheckerInvoice && status !== 'INVOICE_NULLIFY' ? styles['real-card'] : styles['no-real-card']
    const { metaileText, metaileColor, metaileBgColor } = invoiceMetaile(dataSource?.master)
    const sourceLabel = TupleInvoiceType().find(el => el.value === source)?.label
    const isShowForeign = entity === 'system_海外发票'
    const onlyShowForeign = isShowForeign && OverseasInvoicePower && onlyOneCurrencyHideSeaLocalCurrrency()
    
    return (
      <div className={styles['invoice-card']}>
        <div className={styles['tag-wrapper']}>
          {sourceLabel && <div className={`${styles['source-item']} mr-4`}>{sourceLabel}</div>}
          <div className={`${styles['invoice-type']} ${isShowForeign && 'invoice-purple-tag'}`}>{newTypeName || i18n.get('增值税发票')}</div>
          {isInvoice && (
            <div className={`${realTagClass} ml-4`}>
              <T name={isCheckerInvoice ? invoiceStatusStr(dataSource?.master?.form) : '未验真'} />
            </div>
          )}
          {metaileText?.length ? (
            <div
              style={{ color: metaileColor, backgroundColor: metaileBgColor }}
              className={`${styles['metaile-tag']} ml-4`}
            >
              <T name={metaileText} />
            </div>
          ) : null}
          {getInvoiceSignature(dataSource?.master, `${styles['metaile-tag']} ml-4`)}
        </div>
        <div className={styles['invoice-card-title']}>
          <div className={styles['invoice-image-wrapper']}>
            <img className={useSvgImage ? styles['invoice-svg-image'] : styles['invoice-image']} src={src} />
          </div>
          <div className={styles['invoice-title-texts']}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <div className={styles['invoice-title']}>
                {!isFullDigital && title}
                {/* 每张发票存在问题的话，单独标示出来 */}
                {warningMsg?.length > 0 && (
                  <Popover content={this.getContent(warningMsg)} title={i18n.get('风险详情')} trigger="hover">
                    <svg
                      className={`icon ${className}`}
                      aria-hidden="true"
                      style={{
                        color: '#fa8c16',
                        margin: '0 0 0 14px',
                        width: '14px',
                        height: '14px'
                      }}
                    >
                      <use xlinkHref={url} />
                    </svg>
                  </Popover>
                )}
              </div>
            </div>
            <div className={styles['invoice-sub-title']}>{dateOrInfo}</div>
          </div>
        </div>
        {!dataSource?.isWaitingBatchInvoice && entity !== 'invoicePhoto' && (
          <div className={styles['invoice-total']}>
            <div className={styles['invoice-total-row']}>
              <div className={styles['text']}>{entity === type ? i18n.get('价税合计') : i18n.get('总金额')}</div>
              <Money
                className={styles['money']}
                isShowForeign={isShowForeign}
                onlyForeign={onlyShowForeign && allMoney?.foreignNumCode}
                value={entity === type ? allMoneyDetail : allMoney}
              />
            </div>
            {taxAmount && !isFullDigital && !isToken && !this.invoiceTaxInfo.fnCanEditType(entity) && (
              <div className={styles['invoice-total-row']}>
                <div className={styles['text']}>{i18n.get('可抵扣税额')}</div>
                <Money className={styles['money']} value={taxAmount} />
              </div>
            )}
            {this.invoiceTaxInfo.fnCanEditType(entity) &&
              (entity === type ? this.renderInvoiceEdit(invoiceManageable) : this.renderTicketsEdit(invoiceManageable))}
            {modifyApproveMoney && this.renderInvoiceReview(this.props)}
            {
              isShowForeign && (
                <>
                  <div className={styles['invoice-total-row']}>
                    <div className={styles['text']}>{i18n.get('税额')}</div>
                    <Money className={styles['money']} 
                      isShowForeign={isShowForeign && formData['E_system_海外发票_税额']?.foreignNumCode}
                      onlyForeign={onlyShowForeign && formData['E_system_海外发票_税额']?.foreignNumCode}
                      value={formData['E_system_海外发票_税额']} />
                  </div>
                  <div className={styles['invoice-total-row']}>
                    <div className={styles['text']}>{i18n.get('不计税金额')}</div>
                    <Money 
                      className={styles['money']} 
                      onlyForeign={onlyShowForeign && formData['E_system_海外发票_不计税金额']?.foreignNumCode}
                      isShowForeign={isShowForeign && formData['E_system_海外发票_不计税金额']?.foreignNumCode}
                      value={formData['E_system_海外发票_不计税金额']} />
                  </div>
              </>
              )
            }
          </div>
        )}
      </div>
    )
  }

  private renderFormData = () => {
    const { item } = this.props
    const type = get(item, 'formData.E_system_发票主体_发票类别')
    const isFullDigital = ['FULL_DIGITAl_SPECIAL', 'FULL_DIGITAl_NORMAL'].includes(type)
    let fields = get(item, 'fields', [])
    if (isFullDigital) {
      const blackList = ['发票代码', '购买方地址电话', '购买方开户行及账号']
      fields = fields.filter(el => !blackList.includes(el?.label))
    }
    if (!fields || !fields.length) {
      return null
    }
    const { invoiceComparedData } = this.state
    const changeInvoiceData = invoiceComparedData[item.id] || {}
    return (
      <div className={styles['form-items-wrapper']}>
        {fields.map(field => {
          const { label, key, type } = field
          const changeFieldData = changeInvoiceData[key]
          return (
            <div key={key} className={styles['form-item-row']}>
              <div className={styles['form-title']}>{label}</div>
              <div className={styles['form-value']}>{this.renderFormValue(field)}</div>
              {this.renderHistory(changeFieldData, field)}
            </div>
          )
        })}
      </div>
    )
  }

  private renderHistory = (changeFieldData: any, field) => {
    if (!changeFieldData) {
      return null
    }
    return (
      <Tooltip
        overlayClassName={'modify-invoice-field-wrapper'}
        title={this.renderChangeFieldData(field, changeFieldData)}
        placement="bottom"
      >
        <div className="ml-4">
          <EKBIcon name="#EDico-time1" className="icon" />
        </div>
      </Tooltip>
    )
  }

  private renderChangeFieldData = (field: FieldItem, changeData: Record<string, any>) => {
    const dataBefore = this.renderFormValue(field, changeData.dataBefore)
    const dataAfter = this.renderFormValue(field, changeData.dataAfter)
    const { type } = field
    const cls = type === 'money' ? 'dis-f flex-s-0' : ''
    return (
      <div className={'p-4'}>
        <div className={cls}>
          {`ocr识别的数据为：${type !== 'money' ? dataBefore : ''}`}
          {type === 'money' ? dataBefore : ''}
        </div>
        <div className={cls}>
          <div className={cls}>{`${changeData.modifier}在${moment(changeData.updateTime).format(
            'YYYY年MM月DD日 HH:mm'
          )}修改为：${type !== 'money' ? dataAfter : ''}`}</div>
          {type === 'money' ? dataAfter : ''}
        </div>
      </div>
    )
  }

  private renderFormValue = (field: FieldItem, value?) => {
    const { item, payersPool, OverseasInvoicePower } = this.props
    const onlyShowForeign = item.entity === "system_海外发票" && OverseasInvoicePower && onlyOneCurrencyHideSeaLocalCurrrency()
    const { formData } = item
    const { checkStatus, checkMessage } = this.state
    const { type, customRender, key } = field
    if (customRender) {
      return customRender(item, {
        payersPool,
        checkStatus,
        checkMessage
      })
    }
    const _value = value !== undefined ? value : formData[key]
    return type === 'money' ? <Money value={_value} isShowForeign={onlyShowForeign && _value.foreignNumCode} /> : this.formatFormData(field, _value)
  }

  private renderFullDetail = () => {
    const { details } = this.props.item
    if (!details || !details.length) {
      return null
    }
    const { totalAmount, totalTaxAmount } = calculateTotalMoneyOfDetails(details)
    return (
      <div className={styles['detail-items-wrapper']}>
        <div className={`${styles['detail-item-row']} ${styles['detail-header']}`}>
          <div className={styles['name']}>{i18n.get('项目明细')}</div>
          <div className={styles['number']}>{i18n.get('规格型号')}</div>
          <div className={styles['number']}>{i18n.get('数量')}</div>
          <div className={styles['number']}>{i18n.get('金额')}</div>
          <div className={styles['number']}>{i18n.get('税率')}</div>
          <div className={styles['number']}>{i18n.get('税额')}</div>
        </div>
        <div>
          {details.map(detail => {
            const { name, id, model, number, amount, tax, taxRate } = detail
            return (
              <div key={id} className={styles['detail-item-row']}>
                <div className={styles['name']}>{name}</div>
                <div className={styles['number']}>{model || '--'}</div>
                <div className={styles['number']}>{number}</div>
                <Money className={styles['number']} value={amount} />
                <div className={styles['number']}>{taxRate}</div>
                <Money className={styles['number']} value={tax} />
              </div>
            )
          })}
        </div>
        <div className={`${styles['detail-item-row']} ${styles['detail-footer']}`}>
          <div className={styles['name']}>{i18n.get('合计')}</div>
          <div className={styles['number']} />
          <div className={styles['number']} />
          <Money className={styles['number']} value={totalAmount} />
          <div className={styles['number']} />
          <Money className={styles['number']} value={totalTaxAmount} />
        </div>
      </div>
    )
  }

  private handleDeduct = (e: CheckboxChangeEvent) => {
    this.props.onDeduct(this.props.item.id, e.target.checked)
  }

  private getColorTheme = (typeInvoice: string): 'yellow' | 'blue' | 'grey' | 'green' | 'violet' => {
    const { entity } = this.props.item
    // @i18n-ignore
    if (['system_发票主体', 'system_出租车票', 'system_过路费发票', 'system_定额发票'].indexOf(entity) > -1) {
      return 'yellow'
      // @i18n-ignore
    } else if (['system_火车票', 'system_航空运输电子客票行程单', 'system_客运汽车发票'].indexOf(entity) > -1) {
      return 'blue'
    } else if (['system_机打发票', 'system_医疗发票'].indexOf(entity) > -1) {
      return 'green'
    } else if (['system_非税收入类票据'].indexOf(entity) > -1) {
      return 'violet'
    } else {
      return 'grey'
    }
  }

  private formatFormData = (field: FieldItem, value: any) => {
    const { type, defaultValue } = field
    if (type === 'time' || type === 'date') {
      const dateFormat = type === 'date' ? i18n.get('YYYY年MM月DD日') : i18n.get('YYYY年MM月DD日 HH:mm')
      return value && moment(value) ? moment(value).format(dateFormat) : moment().format(dateFormat)
    } else if (type === 'distance') {
      return value ? `${value}km` : '-'
    } else if(type === 'percent'){
      return value ? `${value}%` : '-'
    }

    return value || defaultValue || '-'
  }

  private checkInvoice = () => {
    const { submitter, item } = this.props
    // @i18n-ignore
    if (!submitter || !item || item.entity !== 'system_发票主体') {
      return
    }
    api.invokeService('@bills:get:invoice:state', item.id, submitter.id).then((res: any) => {
      const { message, status } = res
      this.setState({ checkMessage: message, checkStatus: status })
    })
  }

  private getInvoiceComparedData = async () => {
    const { item } = this.props
    const { invoiceComparedData } = this.state
    const data = invoiceComparedData[item?.id]
    if (!data && item?.id) {
      const items = await api.invokeService('@bills:get:invoice:compared:data', { ids: [item.id] })
      if (items && items.length) {
        const [first] = items
        invoiceComparedData[item.id] = first[item.id]
        this.setState({ invoiceComparedData: { ...invoiceComparedData } })
      }
    }
  }
}
