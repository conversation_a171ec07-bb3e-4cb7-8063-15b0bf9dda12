.image-player-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 54px;
    height: 54px;
    margin: auto;
  }
  .img-placeholder {
    text-align: center;
    color: #fff;
    width: 100%;
    height: 100%;
    background: #979797;
    font-size: 14px;
    img {
      width: 53px;
      display: block;
      margin: 152px auto 27px;
    }
  }
  .fullscreen {
    margin-right: 6px;
    font-size: 12px;
    white-space: nowrap;
    line-height: 50px;
    color: #fff;
  }
  .ekuaibao-image-player {
    .player-footer-wrapper {
      display: flex;
      background-color: #212124;
      padding: 0 10px;
      box-sizing: border-box;
      position: absolute;
      z-index: 1;
      width: 100%;
      left: 0;
    }
    .player-footer {
      background: none;
      width: 290px;
      margin: 0 auto;
    }
    .navigator-wrapper {
      position: absolute;
      bottom: 0;
      z-index: 1;
      .navigator-wrapper-inner
        .navigator-scroller
        .navigator-container
        .navigator-item
        .navigator-item-inner
        .thumb-placeholder {
        img {
          width: 80%;
          height: 80%;
          margin: 10%;
        }
      }
    }
    .image-player {
      box-sizing: border-box;
    }
    .page-wrapper {
      line-height: 50px;
      margin-left: 14px;
    }
  }
  &.footer-top {
    .image-player-container {
      padding: 58px 24px 122px;
    }
  }
  &.footer-bottom {
    .image-player-container {
      padding: 12px 12px 122px;
    }
  }
}
