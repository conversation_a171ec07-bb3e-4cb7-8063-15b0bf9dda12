import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { InvoiceReviewer } from './InvoiceReviewer'
import { getInvoiceInfo } from './DetailFieldItems'
import { MessageCenter } from '@ekuaibao/messagecenter'
import InvoiceTaxInfo from './utils/InvoiceTaxInfo'
import { standardValueMoney } from '../../lib/misc'
import { ReviewedInvoiceTypes } from '../../elements/CarouselInvoiceReviewer/utils/InvoiceDetailUtil'
import { OriginalInvoiceItem, InvoiceItem, OriginalImage, Submitter, Money } from './type'
import { calculateInvoiceTaxRate } from '../../lib/InvoiceUtil'
interface CarouselInvoiceReviewerProps {
  bus?: any
}

interface CarouselInvoiceReviewerState {
  visible: boolean
  index: number
  items: InvoiceItem[]
  invoiceManageable: boolean
  submitter: Submitter | null
  showValue: any
  flowId: string
  billState: string
  taxAmount?: Money | number
  taxRate?: string
  approveAmount?: Money | number
  comment?: string
  bus: MessageCenter
  dataSource?: InvoiceItem[]
  riskData?: any
  invoices: any
  modifyApproveMoney: boolean
  isFlowEditable: boolean
  source: string
  isDetail?: boolean
}

export class CarouselInvoiceReviewer extends Component<CarouselInvoiceReviewerProps, CarouselInvoiceReviewerState> {
  state: CarouselInvoiceReviewerState = {
    visible: false,
    index: 0,
    items: [],
    invoiceManageable: true,
    submitter: null,
    showValue: [],
    flowId: '',
    billState: '',
    bus: new MessageCenter(),
    riskData: {}, // 单张发票的warning
    invoices: [],
    modifyApproveMoney: false,
    isFlowEditable: false,
    source: ''
  }
  private bus = new MessageCenter()
  private invoiceTaxInfo = new InvoiceTaxInfo()
  componentDidMount() {
    api.on('@layout::open:invoiceReviewer', this.handleOpenImagePlayer)
  }

  componentWillUnmount() {
    api.un('@layout::open:invoiceReviewer', this.handleOpenImagePlayer)
  }

  render() {
    const {
      index,
      items,
      visible,
      invoiceManageable,
      submitter,
      showValue,
      billState,
      riskData,
      isFlowEditable,
      modifyApproveMoney,
      source,
      isDetail
    } = this.state
    const itemData = items[index]
    const supportReviewedItems = showValue.filter(item => {
      // 过滤掉不支持审阅模式的发票类型
      const { master, type: entityType } = item
      const entityId = master && master.entityId
      const entity = entityId || entityType
      return ReviewedInvoiceTypes.indexOf(entity) > -1
    })
    let dataSource = supportReviewedItems[index]
    dataSource = calculateInvoiceTaxRate(dataSource)

    return (
      <InvoiceReviewer
        items={items}
        visible={visible}
        billState={billState}
        itemData={{ ...itemData }}
        currentIndex={index}
        total={items.length}
        invoiceManageable={invoiceManageable}
        submitter={submitter}
        onClose={this.handleCloseViewer}
        onNext={this.handleNext}
        onPrevious={this.handlePrev}
        onNavigate={this.handleNavigate}
        onDeduct={this.handleDeduct}
        dataSource={{ ...dataSource }}
        onEditTaxInfo={this.handleEditInfo}
        onOpenEditReviewAmount={this.handleEditReviewAmount}
        riskData={riskData}
        modifyApproveMoney={modifyApproveMoney}
        isFlowEditable={isFlowEditable}
        isDetail={isDetail}
        source={source}
      />
    )
  }
  /**
   * @description: 点击打开【审阅模式】预览发票明细
   * @return {*}
   */
  private handleOpenImagePlayer = (
    invoiceItems: OriginalInvoiceItem[],
    imageList: OriginalImage[],
    submitter: Submitter,
    invoiceManageable: boolean,
    showValue,
    flowId,
    billState,
    bus,
    riskData,
    invoices,
    modifyApproveMoney,
    isFlowEditable,
    details,
    source,
    isDetail
  ) => {
    const items = this.parseInvoice(invoiceItems, imageList, details)
    this.setState({
      visible: true,
      items,
      index: 0,
      invoiceManageable: invoiceManageable,
      submitter,
      showValue,
      flowId,
      billState,
      bus,
      riskData,
      invoices,
      modifyApproveMoney,
      isFlowEditable,
      source,
      isDetail
    })
  }

  /**
   * @description: 获取所有发票信息，当前费用的发票数组取自传过来的invoice（已排序），其他的从费用里按序拿
   * @param {*} details 所有费用明细
   * @param {*} invoices 当前费用下发票数组
   * @param {*} curDetailId 当前费用明细ID
   * @return {*} 返回组装好的所有发票数组
   */
  // getInvoiceList = (details, invoices, curDetailId) => {
  //   let invoiceList = details.reduce((preVal, curVal) => {
  //     let invoiceItem
  //     if (curVal.feeTypeForm?.detailId === curDetailId) {
  //       invoiceItem = invoices || []
  //     } else {
  //       invoiceItem = curVal?.feeTypeForm?.invoiceForm?.invoices ? curVal.feeTypeForm.invoiceForm.invoices : []
  //     }
  //     return preVal.concat(invoiceItem)
  //   }, [])
  //   return invoiceList
  // }

  private handleCloseViewer = () => {
    const { bus, source } = this.state
    if(source !== 'billDirectOpen') {
      bus.emit('viewer-close')
    }

    this.setState({ visible: false })
  }

  private parseInvoice = (invoiceItems: OriginalInvoiceItem[], imageList: OriginalImage[], details: any) => {
    const items: InvoiceItem[] = invoiceItems.map(item => {
      const itemInfo = getInvoiceInfo(item, imageList, details)
      return itemInfo
    })
    return items
  }

  private handleNext = () => {
    this.stepItem(1)
  }

  private handlePrev = () => {
    this.stepItem(-1)
  }

  private handleNavigate = (nextIndex: number) => {
    this.setState({ index: nextIndex })
  }

  private stepItem = (num: number) => {
    const { index, items } = this.state
    const nextIndex = index + num
    if (nextIndex < 0 || nextIndex > items.length - 1) {
      return
    }
    this.setState({ index: nextIndex })
  }

  private handleEditInfo = item => {
    const {
      master: { entityId, form },
      invoiceId,
      itemIds
    } = item
    const { flowId, bus, showValue, index } = this.state
    const params = {
      flowId,
      invoiceId: invoiceId,
      itemIds
    }
    const taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
    const taxRate = this.invoiceTaxInfo.getTaxRate(item)
    const newItem = Object.assign(showValue[index], {})
    api
      .open('@bills:InvoiceEditTaxModal', { entityId, taxAmount, taxRate, params, isApprove: true, invoiceForm:form })
      .then((data: Pick<CarouselInvoiceReviewerState, 'taxAmount' | 'taxRate'>) => {
        if (data.hasOwnProperty('taxAmount')) {
          newItem.taxAmount = data.taxAmount
        }
        if (data.hasOwnProperty('taxRate')) {
          newItem.taxRate = data.taxRate
        }
        this.setState({
          dataSource: { ...newItem }
        })
        bus.emit('update:billInfo')
      })
  }
  private handleEditReviewAmount = item => {
    const {
      master: { entityId, form },
      invoiceId,
      itemIds,
      comment
    } = item
    let { approveAmount } = item
    const { flowId, bus, showValue, index, invoices } = this.state
    const params = {
      flowId,
      invoiceId: invoiceId,
      itemIds
    }
    const supportReviewedItems = showValue.filter(item => {
      // 过滤掉不支持审阅模式的发票类型
      const { master, type: entityType } = item
      const entityId = master && master.entityId
      const entity = entityId || entityType
      return ReviewedInvoiceTypes.indexOf(entity) > -1
    })
    const newItem = Object.assign(supportReviewedItems[index], {})
    const totalMoney =
      entityId === i18n.get('system_发票主体')
        ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })].standard
        : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })].standard
    !approveAmount && (approveAmount = totalMoney)
    api
      .open('@bills:InvoiceApproveAmountModal', { params, approveAmount, comment, totalMoney, isUpdate: false })
      .then(async (data: Pick<CarouselInvoiceReviewerState, 'approveAmount' | 'comment'>) => {
        const newInvoice = invoices.find(line => line.master.id === item.master.id)
        newInvoice.approveAmount = standardValueMoney(data.approveAmount)
        newInvoice.comment = data.comment
        if (data.hasOwnProperty('approveAmount')) {
          newItem.approveAmount = standardValueMoney(data.approveAmount)
        }
        if (data.hasOwnProperty('comment')) {
          newItem.comment = data.comment
        }
        bus.emit('viewer-approve-amount-edit', invoices)

        this.setState({
          dataSource: { ...newItem }
        })
      })
  }
  private handleDeduct = (id: string, isDeduction: boolean) => {
    const params = { isDeductible: isDeduction, id }
    api.invokeService('@bills:invoice-isDeductible', params).then(() => {
      this.bus.emit('update:billInfo')
      const items = this.state.items.map(item => ({
        ...item,
        isDeduction: item.id === id ? isDeduction : item.isDeduction
      }))
      this.setState({ items: items })
    })
  }
}

export default CarouselInvoiceReviewer
