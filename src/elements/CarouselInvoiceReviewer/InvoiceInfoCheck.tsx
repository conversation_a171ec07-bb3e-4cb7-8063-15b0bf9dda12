import { Tooltip } from 'antd'
import React from 'react'
import styles from './InvoiceReviewer.module.less'

export interface InvoiceInfoCheckProps {
  title: string
  tooltip?: string
  className?: string
  icon?: string
  warningTitle?: string
  warningText?: string
}

export const InvoiceInfoCheck = (props: InvoiceInfoCheckProps) => {
  const { title, tooltip = '', className = '', icon = '', warningTitle = '', warningText = '' } = props
  return (
    <div className={`${styles['invoice-check-wrapper']} ${className}`}>
      <Tooltip placement="top" title={tooltip} overlayStyle={{ zIndex: 10000 }}>
        <div className={'invoice-check-value'}>
          <span>{title}</span>
          {icon && <img className={styles.icon} src={icon} />}
        </div>
      </Tooltip>
      {warningTitle && (
        <div className={styles.warning}>
          <span className={styles['warning-title']}>{warningTitle}</span>
          <span>{warningText}</span>
        </div>
      )}
    </div>
  )
}
