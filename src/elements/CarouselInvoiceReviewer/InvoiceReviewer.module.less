@import '~@ekuaibao/eui-styles/less/token';

.invoice-viewer-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  justify-content: center;

  .invoice-viewer {
    width: 100%;
    height: 100%;
    background-color: #fff;
    .invoice-viewer-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 17px;
      margin-bottom: 17px;
      .invoice-viewer-titleLeft {
        width: 50%;
        display: flex;
        line-height: 28px;
        align-items: center;
      }
      .title {
        font-weight: 500;
        color: rgba(29, 43, 61, 1);
        font-size: 20px;
        margin: 0 24px;
      }
      .invoice-viewer-center {
        display: flex;
        align-items: center;
        line-height: 8px;
        span {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
        }
      }
      .invoice-viewer-feeDetail {
        margin: 0;
      }
      .invoice-viewer-feename {
        display: inline-block;
        width: 300px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .invoice-viewer-divider {
        display: inline-block;
        width: 1px;
        height: 11px;
        background: rgba(29, 33, 41, 0.1);
        margin: 0 8px;
      }
      .prev,
      .next {
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
        cursor: pointer;
        padding: 2px 16px;
        border: 1px solid var(--brand-base);
        color: var(--brand-base);
        font-size: 14px;
        line-height: 22px;
        margin-right: 8px;
      }
      .disabled {
        color: rgba(29, 43, 61, 0.3);
        border-color: rgba(29, 43, 61, 0.3);
        cursor: not-allowed;
      }
      .close-icon {
        cursor: pointer;
        font-size: 18px;
        margin-left: 8px;
        margin-right: 32px;
      }
    }
    .viewer-layout {
      display: flex;
      width: 100%;
      height: calc(100vh - 51px);
    }
    .left {
      width: 50%;
    }
    .right {
      width: 50%;
      padding: 15px 16px 0 16px;
      border-top: 1px solid #f4f4f4;
      display: flex;
      flex-direction: column;
    }
  }

  .invoice-detail-wrapper {
    overflow-y: auto;
    padding: 4px 4px 4px 0;
    min-height: calc(100% - 15px);
    .invoice-detail {
      box-shadow: 0 1px 8px 0 rgba(29, 43, 61, 0.15);
      padding: 17px 16px 16px 16px;
      border-left: 2px solid;
      min-height: 100%;
    }

    .invoice-type {
      border-radius: 4px;
      display: inline-block;
      line-height: 20px;
      padding: 4px 8px;
      margin-bottom: 15px;
    }

    .tag-wrapper {
      display: flex;
      .real-card {
        .font-size-1;
        height: 26px;
        padding: @space-2 @space-4;
        line-height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f2faec;
        border-radius: 4px;
        color: #52c41a;
      }
      .no-real-card {
        .font-size-1;
        height: 26px;
        padding: @space-2 @space-4;
        line-height: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ecedee;
        border-radius: @space-2;
        color: rgb(20, 34, 52, 0.45);
      }
      .metaile-tag {
        .font-size-1;
        height: 26px;
        padding: @space-2 @space-4;
        line-height: 20px;
        border-radius: @space-2;
      }
      .signature-tag {
        .font-size-1;
        height: 26px;
        padding: @space-2 @space-4;
        line-height: 20px;
        border-radius: @space-2;
      }
    }

    .invoice-card-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .invoice-image-wrapper {
        background: rgba(242, 180, 142, 1);
        width: 42px;
        height: 42px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        .invoice-svg-image {
          width: 24px;
          height: 32px;
        }
        .invoice-image {
          width: 100%;
          height: 100%;
          border-radius: 4px;
        }
      }
      .invoice-title-texts {
        .invoice-title {
          font-size: 16px;
          font-weight: 500;
          color: rgba(29, 43, 61, 1);
          line-height: 24px;
        }
        .invoice-sub-title {
          color: rgba(29, 43, 61, 0.5);
          line-height: 22px;
          margin-top: 4px;
        }
        .warning {
          width: 14px;
          height: 14px;
          color: #fa8c16;
          margin: 0 0 0 14px;
        }
      }
    }
    .invoice-total {
      margin-bottom: 16px;
      .invoice-total-row {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
        justify-content: space-between;
        .text {
          color: rgba(29, 43, 61, 0.75);
          line-height: 22px;
          flex: 1 1 auto;
          img {
            width: 14px;
            margin-left: 5px;
          }
        }
        .invoice-show-txt {
          width: 70%;
          overflow: hidden;
          text-align: right;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .money {
          font-weight: 500;
          color: rgba(29, 43, 61, 1);
          line-height: 22px;
          font-size: 14px;
        }
      }
      .row-line {
        border-top: 1px dashed rgba(29, 43, 61, 0.15);
        margin: 16px 0;
      }
      .invoice-edit {
        font-size: 14px;
        display: flex;
        justify-content: flex-end;
        color: #1890ff;
        cursor: pointer;
      }
    }
    .invoice-deduction {
      padding-top: 8px;
      padding-bottom: 8px;
      border-top: 1px dashed rgba(29, 43, 61, 0.15);
    }
    .form-items-wrapper {
      border-top: 1px dashed rgba(29, 43, 61, 0.15);
      padding-top: 16px;
      padding-bottom: 16px;
      .form-item-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 22px;
        .form-title {
          flex-shrink: 0;
          width: 124px;
        }
        .form-value {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }

    .invoice-check-wrapper {
      .invoice-check-value {
        display: flex;
        align-items: center;
      }
      .icon {
        width: 15px;
        margin-left: 5px;
      }
      .warning {
        .warning-title {
          margin-right: 5px;
        }
      }
    }

    :global {
      .red {
        .invoice-check-value {
          color: #f17b7b;
        }
      }
    }

    .detail-items-wrapper {
      border-top: 1px dashed rgba(29, 43, 61, 0.15);
      padding-top: 16px;
      padding-bottom: 16px;
      font-size: 14px;
      .detail-item-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        .name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex: 1 1;
          height: 22px;
          line-height: 22px;
        }
        .number {
          width: 12%;
          text-align: right;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          height: 22px;
          line-height: 22px;
          display: inline-block;
        }
      }
    }
  }

  .source-item {
    color: #be445e;
    background-color: rgba(190, 68, 94, 0.1);
    border-radius: 4px;
    display: inline-block;
    line-height: 20px;
    padding: 4px 8px;
    margin-bottom: 15px;
    margin-left: 4px;
  }

  .invoice-yellow-theme {
    .invoice-detail {
      border-left-color: #d37b45;
    }
    .tag-wrapper {
      display: flex;
      .invoice-type {
        background-color: rgba(250, 164, 66, 0.1);
        color: rgba(250, 164, 66, 1);
      }
    }
    .form-items-wrapper {
      .form-item-row {
        .form-title {
          color: rgba(211, 123, 69, 1);
        }
      }
    }
    .detail-items-wrapper {
      .detail-header {
        color: #d37b45;
      }
      .detail-footer {
        .name {
          color: #d37b45;
        }
      }
    }
  }

  .invoice-green-theme {
    .invoice-detail {
      border-left-color: #18b694;
    }
    .invoice-type {
      background-color: rgba(24, 182, 148, 0.1);
      color: #18b694;
    }
    .form-items-wrapper {
      .form-item-row {
        .form-title {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
    .detail-items-wrapper {
      .detail-header {
        color: #18b694;
      }
      .detail-footer {
        .name {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
  }

  .invoice-violet-theme {
    .invoice-detail {
      border-left-color: rgba(102, 90, 181, 1);
    }
    .invoice-type {
      background-color: rgba(97, 78, 216, 0.1);
      color: rgba(102, 90, 181, 1);
    }
    .form-items-wrapper {
      .form-item-row {
        .form-title {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
    .detail-items-wrapper {
      .detail-header {
        color: rgba(102, 90, 181, 1);
      }
      .detail-footer {
        .name {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
  }

  .invoice-blue-theme {
    .invoice-detail {
      border-left-color: rgba(24, 144, 255, 1);
    }
    .invoice-image-wrapper {
      background: rgba(24, 144, 255, 1) !important;
    }
    .invoice-type {
      background-color: rgba(24, 144, 255, 0.1);
      color: rgba(24, 144, 255, 1);
    }
    .form-items-wrapper {
      .form-item-row {
        .form-title {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
    .detail-items-wrapper {
      .detail-header {
        color: rgba(24, 144, 255, 1);
      }
      .detail-footer {
        .name {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
  }
  .invoice-grey-theme {
    .invoice-detail {
      border-left-color: rgba(29, 43, 61, 0.3);
    }
    .invoice-type {
      background-color: rgba(29, 43, 61, 0.09);
      color: rgba(29, 43, 61, 0.75);
    }
    .form-items-wrapper {
      .form-item-row {
        .form-title {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
    .detail-items-wrapper {
      .detail-header {
        color: rgba(29, 43, 61, 0.75);
      }
      .detail-footer {
        .name {
          color: rgba(29, 43, 61, 0.75);
        }
      }
    }
  }
}

:global {
  .ant-popover {
    z-index: 10000;
  }
  .ant-tooltip {
    z-index: 10000;
  }
  .modify-invoice-field-wrapper {
    min-width: 300px;
  }
  .invoice-purple-tag{
    color: #D25F00!important;
    background: #FFF3DB!important;
  }
  .invoice-ai-summary {
    width: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--eui-primary-pri-50);
    height: 22px;
    padding: 1px 4px;
    border-radius: 6px;
    color: var(--eui-primary-pri-500);
    font-size: 12px;
    margin-top: 8px;
    justify-content: space-around;
  }
  .invoice-ai-summary-content {
    margin-top: 6px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    .invoice-ai-summary-content-row {
      margin-top: 2px;
      &:first-child {
        margin-top: 0;
      }
    }
  }
}
