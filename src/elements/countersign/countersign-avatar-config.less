.avatar-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 100%;
  overflow: hidden;

  .avatar-img {
    img {
      width: 100%;
    }
  }

  &.avatar-wrap-1 {
    .avatar-img {
      width: 100%;
      height: 100%;
    }
  }

  &.avatar-wrap-2 {
    .avatar-img {
      flex: 1;
      height: 100%;

      img {
        width: auto;
        height: 100%;
      }
    }
  }

  &.avatar-wrap-3 {
    position: relative;

    .avatar-img {
      display: flex;

      img {
        //margin : 1px;
        flex: 1;
      }

      &:nth-child(1) {
        position: absolute;
        top: 0;
        right: 50%;
        bottom: 0;
        width: 100%;
      }

      &:nth-child(2) {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 50%;
        left: 50%;
      }

      &:nth-child(3) {
        position: absolute;
        top: 50%;
        right: 0;
        bottom: 0;
        left: 50%;
      }
    }
  }

  &.avatar-wrap-4 {
    flex-wrap: wrap;
    align-items: inherit;
    justify-content: inherit;

    .avatar-img {
      display: flex;
      width: 50%;
      height: 50%;

      img {
        flex: 1;
        //margin : 1px;
      }
    }
  }
}