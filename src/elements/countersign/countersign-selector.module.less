.countersign-selector-wrapper {
  width: 100%;
  //height: 28px;
  display: flex;
  flex-direction: column;
  :global {
    .countersign-wrapper {
      display: flex;
      flex-direction: row;
      width: 100%;
      align-items: center;
      .countersign-selector {
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        border: 1px solid #dcdcdc;
        padding-left: 7px;
        border-radius: 6px;
        position: relative;
        &:hover {
          border: 1px solid var(--eui-primary-pri-500);
        }
        .content {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-title);
        }
        .selectColor {
          color: var(--eui-text-placeholder);
        }
      }
      .img {
        margin-left: 5px;
        height: 15px;
        width: 15px;
      }
      span {
        width: 100%;
      }
      .select_counters {
        cursor: pointer;
        display: inline-block;
      }
    }
    .non-match-defines {
      margin-top: 4px;
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
    }
  }
}

.countersign-tooltip-wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .header {
      height: 30px;
      display: flex;
      flex-direction: row;
      flex-shrink: 0;
      justify-content: space-between;
      margin-top: 5px;
      border-bottom: solid 1px #e0e5e5;
      .title {
        opacity: 0.85;
        font-size: 14px;
        line-height: 1.29;
        color: #000000;
      }
      .progress {
        opacity: 0.85;
        font-size: 14px;
        line-height: 1.29;
        text-align: right;
        color: #000000;
      }
    }
    .content {
      max-height: 260px;
      overflow: auto;
      position: relative;
      .countersign-tooltip-subTitle {
        margin-bottom: 8px;
        color: var(--eui-text-caption);
        font: var(--eui-font-note-r2);
      }
      .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        min-height: 56px;
        padding: 8px 0;
        .user {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-left: 12px;
          font: var(--eui-font-body-r1);
          .name-content {
            display: flex;
            flex-direction: row;
            .name {
              margin-right: 6px;
              color: var(--eui-text-title);
            }
            .user-signature {
              color: var(--eui-text-placeholder);
            }
          }
          .position {
            font: var(--eui-font-body-r1);
            color: var(--eui-text-placeholder);
          }
        }
        .done {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

.countersign-tooltip-header {
  height: 24px;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  :global {
    .title {
      font: var(--eui-font-body-b1);
      color: var(--eui-text-title);
    }
    .progress {
      opacity: 0.85;
      font-size: 14px;
      line-height: 1.29;
      text-align: right;
      color: #000000;
    }
  }
}

.countersign-staff-tooltip-container {
  width: 330px;
  max-height: 330px;
  :global {
    .ant-popover-content {
      .ant-popover-inner {
        .ant-popover-title {
          height: 45px;
          min-width: 330px;
          background: #ffffff;
        }
      }
    }
  }
}
