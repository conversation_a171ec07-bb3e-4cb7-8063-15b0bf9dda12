/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/27.
 */

import React, { PureComponent } from 'react'
import './countersign-avatar-config.less'

export default class CountersignAvatar extends PureComponent {
  renderClassName(imgList) {
    if (imgList.length < 4) {
      return `avatar-wrap avatar-wrap-${imgList.length}`
    }

    return 'avatar-wrap avatar-wrap-4'
  }

  renderImg(url, idx) {
    return (
      <div key={idx} className="avatar-img">
        <img src={url} />
      </div>
    )
  }

  render() {
    let imageList = this.props.imageList
    return (
      <div className={this.renderClassName.call(this, imageList)}>
        {imageList.map((el, idx) => this.renderImg(el, idx))}
      </div>
    )
  }
}
