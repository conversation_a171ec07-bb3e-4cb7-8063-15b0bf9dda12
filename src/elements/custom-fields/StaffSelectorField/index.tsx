import React, { Component } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import classnames from 'classnames'
import remove from 'lodash/remove'
import styles from './index.module.less'

const TagSelector = api.require('@elements/tag-selector')
const { getDeptItemsByIds, getItemByIds, getCheckedKeys } = api.require('@lib/lib-util')

interface IProps {
  [key: string]: any
}
interface IState {
  [key: string]: any
}

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
}))
export default class StaffSelector extends Component<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = {
      selectedValues: [],
      visibility: props.value || {},
    }
  }

  componentDidMount() {
    const { value } = this.props
    this.setState({
      selectedValues: this.valueParse(value),
      visibility: value,
    })
  }

  handleSelectStaffs = () => {
    const { visibility: { staffs = [], roles = [], departments = [], departmentsIncludeChildren = false } = {} } = this.state
    const { onlyDepartmentMember = false } = this.props
    const checkedList = [
      { type: 'department-member', multiple: this.props.multiple === undefined ? true : this.props.multiple, checkedKeys: staffs }
    ]
    if (!onlyDepartmentMember) {
      checkedList.splice(
        1, 
        0, 
        { type: 'role', multiple: this.props.multiple === undefined ? true : this.props.multiple, checkedKeys: roles },
        { type: 'department', multiple: this.props.multiple === undefined ? true : this.props.multiple, checkedKeys: departments }
      )
    }
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: checkedList,
        departmentsIncludeChildren: departmentsIncludeChildren,
      })
      .then((data) => {
        const { checkedList, departmentsIncludeChildren } = data as any
        const staffs = getCheckedKeys(checkedList, 'department-member')
        const roles = getCheckedKeys(checkedList, 'role')
        const departs = getCheckedKeys(checkedList, 'department')
        const params = {
          staffs,
          roles,
          departments: departs,
          departmentsIncludeChildren,
        }
        this.setState(
          {
            selectedValues: this.valueParse(params),
            visibility: params,
          },
          () => {
            this.props.onChange && this.props.onChange(params)
          },
        )
      })
  }

  handleTagChange = (data: any, deleteItem: any) => {
    const { visibility } = this.state
    const staffKeys = visibility.staffs || []
    const roleKeys = visibility.roles || []
    const departments = visibility.departments || []
    // eslint-disable-next-line jsx-control-statements/jsx-jcs-no-undef
    remove(staffKeys, (id: string) => id === deleteItem.id)
    remove(roleKeys, (id: string) => id === deleteItem.id)
    remove(departments, (id: string) => id === deleteItem.id)
    const params = {
      ...visibility,
      staffs: staffKeys,
      roles: roleKeys,
      departments: departments,
    }
    this.setState({ selectedValues: this.valueParse(params), visibility: params }, () => {
      this.props.onChange && this.props.onChange(params)
    })
  }

  valueParse = (value: any) => {
    if (!value) {
      return []
    }
    return [
      ...getDeptItemsByIds(this.props.departmentTree, value.departments || []),
      ...getItemByIds(this.props.roles, value.roles || []),
      ...getItemByIds(this.props.staffs, value.staffs || []),
    ]
  }

  render() {
    const { selectedValues = [] } = this.state
    const { placeholder, className, editable } = this.props
    const cn = editable ? classnames(styles['select-input'], className) : classnames(className, styles['select-input-disabled'],)
    return (
      <TagSelector
        value={selectedValues}
        editable={editable}
        className={cn}
        onClick={this.handleSelectStaffs}
        onChange={this.handleTagChange}
        placeholder={placeholder || i18n.get('请选择适用人员、角色或部门')}
      />
    )
  }
}
