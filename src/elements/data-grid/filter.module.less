.filter-content-wrapper {
  //height: 250px;
  width: 270px;
  :global {
    .eui-select-dropdown {
      box-shadow: none !important;
    }
  }
}

.filter-dropdown-render {
  background-color: var(--eui-bg-float);
  border-radius: 6px;
  outline: none;
  padding-top: 4px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  :global {
    .eui-tree-node-content-wrapper {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.list-wrapper {
  padding: 4px;
  overflow: auto;
  height: 200px;
}

.highlight {
  color: var(--eui-primary-pri-500);
  padding: 0 !important;
  background-color: transparent !important;
}

.list-item {
  display: flex;
  align-items: center;
  min-height: 32px;
  padding: 5px 8px;
  margin: 0 4px 4px;
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--eui-text-title);
  :global {
    .list-item-content {
      flex: auto;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  &:hover {
    background-color: var(--eui-fill-hover);
  }
}

.text-nowrap-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list-item-checked {
  color: var(--eui-primary-pri-500);
  background-color: var(--eui-fill-active);
}
