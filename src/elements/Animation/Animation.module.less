.loading-center-absolute {
  :global {
    position: relative;
    height: 10px;
    width: 100px;
    margin: 0 auto;
    @keyframes move {
      0% {
        left: 100px;
        top: 0;
      }
      80% {
        left: 0;
        top: 0;
      }
      85% {
        left: 0;
        top: -20px;
      }
      90% {
        width: 10px;
        height: 10px;
      }
      95% {
        left: 100px;
        top: -20px;
      }
      100% {
        left: 100px;
        top: 0;
      }
    }
    .circle {
      width: 0;
      height: 0;
      border: 5px solid var(--brand-base);
      border-radius: 50%;
      margin-right: 20px;
      margin-bottom: 20px;
      position: absolute;
    }
    .circle_one:local {
      animation: move 2s linear infinite;
    }
    .circle_two:local {
      left: 20px;
      animation: move 2s linear infinite -0.4s;
    }
    .circle_three:local {
      left: 40px;
      animation: move 2s linear infinite -0.8s;
    }
    .circle_four:local {
      left: 60px;
      animation: move 2s linear infinite -1.2s;
    }
    .circle_five:local {
      left: 80px;
      animation: move 2s linear infinite -1.6s;
    }
  }
}