import React, { memo } from 'react'
import <PERSON><PERSON> from 'react-lottie'
import animationData from './upload.json'

type OptionsType = {
  loop: boolean,
  autoplay: boolean
  animationData: any
}
type IProps = {
  width?: number
  height?: number
}

const defaultOptions: OptionsType = {
  loop: true,
  autoplay: true,
  animationData: animationData
}

const UncontrolledLottie:  React.FC<IProps> = memo(props => {
  return (
      <Lottie options={defaultOptions}
          isClickToPauseDisabled={true}
          height={props.height || 200}
          width={props.width || 140}
      />
  )
})

export default UncontrolledLottie