/**
 *  Created by <PERSON><PERSON> on 2018/9/25 下午4:34.
 */
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import CityComponent from './CityComponent'
import styles from './SelectCityModal.module.less'
import { Button, Icon } from 'antd'

interface SelectCityModalInterface {
  multiple?: boolean
  layer: any
}

@EnhanceModal({
  width: 776,
  height: 482,
  footer: [],
  className: 'custom-modal-layer'
})
export default class SelectCityModal extends PureComponent<SelectCityModalInterface> {
  list: any[]
  handleOnChange = (city: any[]) => {
    if (city) {
      this.list = city
    }
  }

  handleOK = () => {
    this.props.layer.emitOk(this.list)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    const { multiple } = this.props
    return (
      <div className={styles.select_city_wrapper}>
        <div className="modal-header new-header">
          <div className="flex title_font">{i18n.get('城市选择组件')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="city_component">
          <CityComponent onChange={this.handleOnChange} field={{ multiple, placeholder: i18n.get('请输入城市') }} />
        </div>
        <div className="tag_wrapper" />
        <div className="modal-footer ">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
