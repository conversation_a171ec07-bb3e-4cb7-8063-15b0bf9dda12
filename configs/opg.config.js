/**
 *  Created by pw on 2021/3/4 下午9:07.
 */
const path = require('path')

module.exports = function(config) {
  config.patch.defines({
    IS_STANDALONE: JSON.stringify(true),
    __DEV__: JSON.stringify(false),
    IS_SMG: JSON.stringify(true),
    IS_OPG: JSON.stringify(true)
  })
  config.patch.files({
    'favicon.ico': { path: path.join(__dirname, './opg.ico') }
  })
  config.patch.htmls({ '*': { favicon: path.join(__dirname, './opg.ico') } })
}
