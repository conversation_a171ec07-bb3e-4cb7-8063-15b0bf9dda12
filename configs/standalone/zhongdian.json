{"name": "@ekuaibao/entry-web", "version": "9.18.12", "description": "whispered-web", "author": "ekuaibao@team", "license": "MIT", "private": true, "monacoBuildVersion": "**********", "devDependencies": {"@ekuaibao/mobx-remotedev": "^0.5.0", "@types/jest": "^23.3.2", "@types/react-infinite-scroller": "^1.2.1", "@types/react-virtualized": "^9.21.2", "acorn": "^6.1.1", "babel-eslint": "^10.0.1", "babel-jest": "^23.6.0", "coa": "2.0.2", "eslint": "^5.6.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-prettier": "^3.0.0", "glob": "*", "jest": "^23.6.0", "linebyline": "^1.3.0", "prettier": "^1.14.2", "request": "^2.88.0", "start-server-and-test": "^1.9.1", "ts-jest": "^23.1.4", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0"}, "dependencies": {"@ekuaibao/3rd_resources": "1.0.0-beta.27", "@ekuaibao/async-component": "^1.0.5", "@ekuaibao/charts": "^1.0.0-beta.0", "@ekuaibao/collection-definition": "0.0.33-release", "@ekuaibao/datagrid": "1.3.6", "@ekuaibao/ekuaibao_404": "0.0.2", "@ekuaibao/ekuaibao_types": "^1.0.27", "@ekuaibao/enhance-layer-manager": "^5.5.2", "@ekuaibao/enhance-stacker-manager": "^3.1.2", "@ekuaibao/eui": "^1.3.3", "@ekuaibao/eui-isomorphic": "0.0.37", "@ekuaibao/eui-styles": "^2.1.0", "@ekuaibao/eui-web": "0.0.53", "@ekuaibao/fetch": "^0.5.20", "@ekuaibao/formula": "^1.0.30", "@ekuaibao/formula-patch": "^1.0.3", "@ekuaibao/helpers": "^1.1.10", "@ekuaibao/i18n": "^3.1.5", "@ekuaibao/image-player-manager": "^0.1.9", "@ekuaibao/keel": "^1.1.0", "@ekuaibao/lib": "^2.1.89", "@ekuaibao/loading": "^4.0.1", "@ekuaibao/markup-image": "0.0.4", "@ekuaibao/messagecenter": "5.1.0", "@ekuaibao/mobx-store": "^1.1.10", "@ekuaibao/monaco-editor": "^0.17.3000", "@ekuaibao/money-math": "^4.1.5", "@ekuaibao/navigator": "^1.0.12", "@ekuaibao/noekb": "^1.0.0", "@ekuaibao/painter": "0.0.64", "@ekuaibao/platform.is": "^1.0.9", "@ekuaibao/polyfill": "^1.0.2", "@ekuaibao/rpc": "^1.1.13", "@ekuaibao/sdk-bridge": "^1.5.35", "@ekuaibao/session-info": "^1.0.0", "@ekuaibao/show-util": "^1.0.1", "@ekuaibao/signature": "0.0.8", "@ekuaibao/springboard": "1.4.32-zhongdian.0", "@ekuaibao/store": "^1.0.7", "@ekuaibao/template": "5.0.6", "@ekuaibao/template-next": "^6.0.2", "@ekuaibao/theme-variables": "^1.3.12", "@ekuaibao/uploader": "3.1.40-zhong<PERSON>", "@ekuaibao/vendor-antd": "^3.9.1", "@ekuaibao/vendor-common": "^1.2.1", "@ekuaibao/vendor-lodash": "^4.17.13", "@ekuaibao/vendor-polyfill": "^1.0.3", "@ekuaibao/vendor-whispered": "^3.0.0-release.41", "@ekuaibao/web-theme-variables": "1.1.3", "@ekuaibao/whispered": "^5.7.1", "@sentry/browser": "^5.4.0", "@sentry/cli": "^1.54.0", "@sentry/integrations": "^5.23.0", "@sentry/webpack-plugin": "^1.7.0", "@types/classnames": "^2.2.7", "@types/lodash": "^4", "@types/minimatch": "^3.0.3", "@types/react": "^16", "@types/react-color": "^3.0.5", "@types/react-dom": "^16", "@types/react-grid-layout": "^0.16.7", "@types/react-infinite-scroller": "^1.2.1", "@types/react-virtualized": "^9.21.5", "antd": "^3.8.4", "big.js": "^5.2.2", "braft-editor": "^2.1.35", "children-dirs": "^2.0.0", "classnames": "^2.2.5", "cross-env": "^7.0.2", "echarts": "^3.7.2", "ekbc-datagrid": "5.2.0", "ekbc-list-layout": "^3.0.0", "ekbc-pro-ui": "^3.0.4", "ekbc-query-builder": "2.0.3", "ekbc-thirdParty-card": "^3.0.4", "html2canvas": "^1.0.0-rc.7", "image-promise": "^5.0.1", "isarray-polyfill-for-mobx4-observablearray": "^1.0.1", "jspdf": "^2.3.1", "lodash": "^4", "md5": "^2.2.1", "minimatch": "^3.0.4", "mobx": "^4.11.0", "mobx-cobweb": "0.0.58", "mobx-react-lite": "^2.2.2", "moment": "^2.22.2", "nanobar": "^0.4.2", "prop-types": "^15.6.2", "qrcode.react": "^0.9.3", "rc-editor-mention": "^1.1.11", "rc-progress": "^2.2.5", "rc-select": "^9.0.2", "react": "17.0.2", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.0.1", "react-dom": "^17.0.2", "react-grid-layout": "^0.16.6", "react-highlight-words": "^0.14.0", "react-infinite-scroller": "^1.2.4", "react-intl-universal": "^1.13.1", "react-lottie": "^1.2.3", "react-mentions": "^4.0.2", "react-sortable-hoc": "^0.8.3", "rimraf": "^3.0.2", "sa-sdk-javascript": "^1.11.6", "simple-i18n-cli": "^1.1.1", "string-width": "^4.1.0", "systemjs": "^0.21.6", "tslib": "^1", "victory": "^36.0.1", "whispered-build": "^3.3.0-beta.102"}, "config": {"testserver": "", "testcypresskey": ""}, "scripts": {"fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "build:pre:dev": "rimraf .dist && npm run fix-jszip-issue && node --max_old_space_size=4096 scripts/builds-dev", "build:pre:pro": "rimraf .dist && npm run fix-jszip-issue && node --max_old_space_size=4096 scripts/builds-pro", "start": "npm run build:pre:dev && npm run dev", "dev": "npm run fix-jszip-issue && npm run dev:server", "dev:server": "webpack-dev-server", "clean": "cross-env rm -rf ./build", "build:source": "node --max_old_space_size=4096 node_modules/.bin/webpack --config ./webpack.build.js --hash --compress --progress", "build": "npm run clean && npm run build:pre:pro && npm run build:source", "icons": "node compile-feetype-icon.js", "lint:es": "eslint --ext .jsx,.js src", "lint:ts": "tslint -p tsconfig.json", "publish2npm": "npm run build && npm publish dist", "test:watch": "jest -o --watch", "test": "jest --coverage", "cy:verify": "cypress verify", "e2e:run": "cypress run --record --key=$npm_package_config_testcypresskey --parallel", "e2e:test": "start-test test:server 9999 e2e:run", "local:open": "cypress open", "local:run": "cypress run", "bus:ast": "node ./busanalyzer/bus-ast.js", "bus:analyzer": "npm run bus:ast && http-server ./busanalyzer -p 3001 -o -c-1", "patch:i18n": "find ./src/i18n/locales -type f -name '*.all.json' | sed 's/.all.json//' | xargs -I % mv %.all.json %.json", "build:i18n": "i18n-cli wrap --dry-run false './src/**/*' && i18n-cli parse --output-dir ./src/i18n/locales --locales en-US,zh-CN './src/**/*' && npm run patch:i18n", "clear": "cross-env rm -rf ./packge-lock.json  && cross-env rm -rf ./node_modules && cross-env rm -rf ./dll && sudo npm i", "mfe:build": "/bin/bash build-mfe.sh", "mfe:restore": "/bin/bash restore_mfe.sh"}, "whispered": {"plugins": {}, "entryPlugins": {"app": ["account5"], "group": ["account5"], "ldap": ["account5"], "browser": ["account"], "huawei": ["account5"], "kdcloud": ["new-feature-kdcloud"], "thirdparty": ["account5"], "billentry": ["account5"], "nbbank": ["account"]}, "ignorePlugins": ["bi", "bi-manage", "changjiepayment", "custom-project", "home", "chanpay-payment"]}, "optionalDependencies": {"@ekuaibao/plugin-web-account": "1.0.4-release.5", "@ekuaibao/plugin-web-account5": "1.0.30-release-zhongdian.0", "@ekuaibao/plugin-web-aikeCRM": "1.0.3-release.5", "@ekuaibao/plugin-web-ali-pay": "1.0.2-release.5", "@ekuaibao/plugin-web-ali-trip": "1.0.3-release.5", "@ekuaibao/plugin-web-ant-ali-pay": "1.0.3-release.5", "@ekuaibao/plugin-web-apportion": "1.2.0-release.0", "@ekuaibao/plugin-web-assistance": "1.0.1-release.5", "@ekuaibao/plugin-web-audit": "1.3.1-release-zhongdian.1", "@ekuaibao/plugin-web-audit-log": "1.0.3-release.5", "@ekuaibao/plugin-web-auth-check": "1.0.7-release.0", "@ekuaibao/plugin-web-auth-manage": "1.0.16-release.0", "@ekuaibao/plugin-web-auto-expense-rule": "1.0.9-release.2", "@ekuaibao/plugin-web-bi": "1.0.5-release.5", "@ekuaibao/plugin-web-bi-manage": "1.0.3-release.5", "@ekuaibao/plugin-web-bill-invoice-manage": "1.3.1-release-zhongdian.5", "@ekuaibao/plugin-web-budget-adjust": "1.0.1-release.2", "@ekuaibao/plugin-web-budget-manage": "1.0.32-release.0", "@ekuaibao/plugin-web-budget-write": "1.0.13-release.0", "@ekuaibao/plugin-web-chanpay": "1.0.3-release.5", "@ekuaibao/plugin-web-check-in": "1.0.3-release.7", "@ekuaibao/plugin-web-city-settings": "1.0.10-release", "@ekuaibao/plugin-web-civil-service": "1.1.0-release.0", "@ekuaibao/plugin-web-common": "1.0.52-release-zhongdian.0", "@ekuaibao/plugin-web-contacts": "1.2.0-release.0", "@ekuaibao/plugin-web-corporation-info": "1.1.0-release.0", "@ekuaibao/plugin-web-credit": "1.1.1-release.0", "@ekuaibao/plugin-web-currency-manage": "1.0.13-release.1", "@ekuaibao/plugin-web-custom-dimension": "1.0.19-release.3", "@ekuaibao/plugin-web-custom-feetype": "1.1.0-release.0", "@ekuaibao/plugin-web-custom-flow": " 1.2.2-release-zhongdian.0", "@ekuaibao/plugin-web-custom-payment": "1.0.16-release.0", "@ekuaibao/plugin-web-custom-project": "1.0.2-release.5", "@ekuaibao/plugin-web-custom-setup": "1.0.5-release.0", "@ekuaibao/plugin-web-custom-specification": "1.4.1-release-zhongdian.1", "@ekuaibao/plugin-web-custom-triptype": "1.0.7-release.1", "@ekuaibao/plugin-web-dimension-map": "1.0.11-release.1", "@ekuaibao/plugin-web-enterprise": "1.0.2-release.5", "@ekuaibao/plugin-web-expansion-center": "1.0.36-release-zhongdian.2", "@ekuaibao/plugin-web-expense-manage": "1.0.19-release-zhongdian.1", "@ekuaibao/plugin-web-expense-standard": "1.1.1-release.0", "@ekuaibao/plugin-web-field-setting": "1.1.2-release.0", "@ekuaibao/plugin-web-financial": "1.0.2-release.5", "@ekuaibao/plugin-web-home": "1.0.2-release.5", "@ekuaibao/plugin-web-inter-connection": "1.0.4-release.6", "@ekuaibao/plugin-web-layout": "1.1.0-release.0", "@ekuaibao/plugin-web-layout5": "1.0.65-release-zhongdian.7", "@ekuaibao/plugin-web-loan-manage": "1.1.1-release-zhongdian.0", "@ekuaibao/plugin-web-mc-budget": "1.0.22-release-smg.1", "@ekuaibao/plugin-web-mc-business-object": "1.0.27-release-smg.1", "@ekuaibao/plugin-web-mc-city-settings": "1.0.1-release-smg.0", "@ekuaibao/plugin-web-mc-contacts": "1.0.37-release-smg.1", "@ekuaibao/plugin-web-mc-custom-dimension": "1.0.48-release-smg.1", "@ekuaibao/plugin-web-mc-custom-flow-distribute": "1.0.26-release-smg.0", "@ekuaibao/plugin-web-mc-custom-payment": "1.0.26-release-smg.1", "@ekuaibao/plugin-web-mc-dimension-map": "1.0.9-release-smg.2", "@ekuaibao/plugin-web-mc-expense-standard": "0.0.26-release-smg.5", "@ekuaibao/plugin-web-mc-feetype": "1.0.32-release-smg.2", "@ekuaibao/plugin-web-mc-invoice-import-setting": "0.0.10-release-smg.1", "@ekuaibao/plugin-web-mc-invoice-priceTax-separated": "0.0.10-release-smg.1", "@ekuaibao/plugin-web-mc-invoice-rule": "1.0.14-release-smg.1", "@ekuaibao/plugin-web-mc-payee-account": "1.0.31-release-smg.1", "@ekuaibao/plugin-web-mc-permission-distribute": "1.0.9-release-smg.2", "@ekuaibao/plugin-web-mc-role": "1.0.14-release-smg.1", "@ekuaibao/plugin-web-mc-specification": "0.0.38-release-smg.2", "@ekuaibao/plugin-web-mc-tenant-list": "1.0.18-release.5", "@ekuaibao/plugin-web-mybi": "1.0.3-release.9", "@ekuaibao/plugin-web-new-feature-kdcloud": "1.0.3-release.5", "@ekuaibao/plugin-web-new-home-page": "1.1.1-release.0", "@ekuaibao/plugin-web-openApi": "1.0.5-release", "@ekuaibao/plugin-web-order-manage": "1.0.12-release.2", "@ekuaibao/plugin-web-organization-management": "1.0.3-release.6", "@ekuaibao/plugin-web-outbound-message": "1.0.36-release.0", "@ekuaibao/plugin-web-payee-account": "1.0.18-release.0", "@ekuaibao/plugin-web-payments-method": "1.1.0-release.0", "@ekuaibao/plugin-web-permission": "1.0.13-release.0", "@ekuaibao/plugin-web-print": "1.1.0-release", "@ekuaibao/plugin-web-private-car": "1.0.14-release.0", "@ekuaibao/plugin-web-reconciliation": "1.0.5-release.0", "@ekuaibao/plugin-web-reconciliation-setting": "1.0.4-release.5", "@ekuaibao/plugin-web-record-expends": "1.0.15-release.3", "@ekuaibao/plugin-web-remuneration": "1.0.7-release.0", "@ekuaibao/plugin-web-report": "1.0.15-release.4", "@ekuaibao/plugin-web-reports": "1.0.11-release.0", "@ekuaibao/plugin-web-requisition-manage": "1.0.20-release-zhongdian.1", "@ekuaibao/plugin-web-role": "1.0.13-release.0", "@ekuaibao/plugin-web-search-engine": "1.0.3-release.6", "@ekuaibao/plugin-web-settlement-checking": "1.1.1-release.0", "@ekuaibao/plugin-web-supplier-file": "1.0.7-release.0", "@ekuaibao/plugin-web-supplier-payment-management": "1.1.0-release.1", "@ekuaibao/plugin-web-supplier-setting": "1.0.5-release.5", "@ekuaibao/plugin-web-third-party-manage": "1.3.0-release.2", "@ekuaibao/plugin-web-third-party-manage-center-v2": "1.3.5-release-zhongdian.10", "@ekuaibao/plugin-web-travel-manage-center": "1.0.3-release.5", "@ekuaibao/plugin-web-user-info": "1.1.1-release.0"}}