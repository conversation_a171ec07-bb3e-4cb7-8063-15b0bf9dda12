// 本地配置避免修改 dev.local 文件
// 本地拷贝一份到根目录
// cp ./configs/webpack.local.js webpack.local.js
module.exports = {
  LOCAL_PROXY: 'https://app.ekuaibao.com/',
  // LOCAL_PROXY: 'http://127.0.0.1:9990',
  // 使用 Nginx 秒级切换前端代理服务器 https://hose2019.feishu.cn/wiki/QASCwP8NnienGbkkhOWc81qvnyg
  LOCAL_ENTRY: {
    app: './src/hosting/app/index.ts'
    // application: './src/hosting/application/index.ts',
    // debugger: './src/hosting/browser/index.ts'
  }
}
